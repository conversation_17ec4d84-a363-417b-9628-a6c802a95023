### Configuration Properties for dev
###
### Spring Cloud Config value for profile=dev
###
### All properties in this profile can be used
### by setting up SPRING_PROFILES_ACTIVE=default.
### If you are using the API, use the name in profile.
### Add your business logic properties for this
### environment here.
###
### https://github.intuit.com/services-config/config-reference-app/wiki/Config-Properties-Setup

app:
  env: default
  description: This app properties was built by config-onboarding.

security:
  intuit:
    authZFlowEnabled: true
    iamTicketFlowEnabled: false

#Flag for region active or not. Useful for DR failover
region:
  active: true

db:
 urlPrefix: ********************************
 username: sas
 password: Intuit01


spring:
  reader:
    enabled: false
    url: **********************************************
  datasource:
    password: ${db.password}
    url: ${db.urlPrefix}/camunda
    username: ${db.username}
    hikari:
      schema: was
  jpa:
    open-in-view: false
    database-platform: org.hibernate.dialect.PostgreSQLDialect
  liquibase:
    enabled: true
    liquibase-schema: public
    change-log: classpath:db/changelog/db.changelog-local.yaml
  batch:
    table-prefix: public.batch_

access:
  host: access-e2e.platform.intuit.com

# WorkflowCore host endpoint
workflowcore:
  engine:
    host: "https://workflowenginecore-qal.api.intuit.com"
    endpoint: ${workflowcore.engine.host}/rest
    definition:
      deployment: "/deployment/create"
      deployment_crud: "/deployment"
    process:
      definition: "/process-definition"
      key: "/key"
      instance: "/process-instance"
      variables: "/variables"
      delete: "/delete"
    start:
      instance: "/start"
    decision:
      definition: "/decision-definition"
    evaluate:
      decision:
        definition: "/evaluate"
    suspend:
      instance: "/suspended"
    externalTask:
      endpoint: "/external-task"
      v1Endpoint: "/v1/external-task"
      complete: "/complete"
      failure: "/failure"
      extend-lock: "/extendLock"
    serviceTask:
      endpoint: "/v1/service-task/complete"
    history:
      endpoint: /history
      process-instance: /process-instance/
      process-variable-instance: /variable-instance
      external-task-log: /external-task-log
      external-task-log-count: /external-task-log/count
    execution:
      endpoint: /execution
      localVariables: /localVariables
    message-async: "/process-instance/message-async"


appconnect:
  host: https://e2e.api.intuit.com
  providerAppId: AP13072
  endpoints:
    connector: ${appconnect.host}/appconnect/connector
  workflow:
    pollingFrequency: 900
  mock:
    workflowId: 66666
  subscription:
    enableIdempotency: false

external-task:
  client:
    endpoint: ${workflowcore.engine.endpoint}
    backOffDisable: false
    backOffInitTime: 1000
    backOffFactor: 2
    backOffMaxTime: 60000
    errorBackOffInitTime: 4000
    errorBackOffFactor: 2
    errorBackOffMaxTime: 120000
    retryCount: 3
    retryTimer: 15000
    backoffStrategyName: 'ERROR_EXPONENTIAL'
    slidingWindow: 10
    backOffStatusCodes:
      - 502
      - 503
      - 504

  thread-pool:
    maxQueueSize: 10
    sharedMinThreads: 1
    sharedMaxThreads: 9
    individualMinThreads: 1
    individualMaxThreads: 3
    idleThreadTimeoutSec: 30
    allowedGracefulShutDownTimeSec: 30

  workers:
    test:
      disable: false
      topicName: test
      lockDuration: 300000
      maxTasks: 10
      asyncResponseTimeout: 10000
      extendedLockDuration: 3600000  # 1 hour
      workerId: test-worker
      useIdentity2: true
    test-worker-0:
      disable: false
      topicName: test-worker-0
      lockDuration: 300000
      maxTasks: 10
      asyncResponseTimeout: 10000
      extendedLockDuration: 3600000  # 1 hour
      workerId: test-worker-0
    overwatch-test-bkey__123456789:
      disable: true
      topicName: overwatch-test-bkey
      lockDuration: 300000  # 5 minutes This time is calculated after all retries are exhausted after an event is published
      maxTasks: 10
      asyncResponseTimeout: 25000 #should not be greater then gateway timeout
      extendedLockDuration: 3600000 # 1 hour
      workerId: overwatch-test-worker


qbo:
  wasApiClient:
    appId: "Intuit.appintgwkflw.wkflautomate.qbowasapiclient"

#Resiliency config
retry:
  statusCode:
    - 500
    - 502
    - 504
  asyncRetryProcessing:
    trigger:
      enabled : true
      workflows:
        - approval

resilience4j.retry:
  instances:
    wasDB:
      maxRetryAttempts: 3
      waitDuration: 100
      retryExceptions:
        - java.io.IOException
        - java.util.concurrent.TimeoutException
    v4_graphql_client:
      maxRetryAttempts: 3
      waitDuration: 500
      enableExponentialBackoff: true
      exponentialBackoffMultiplier: 2
      retryExceptions:
        - com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException
    http_client:
      maxRetryAttempts: 3
      waitDuration: 500
      enableExponentialBackoff: true
      exponentialBackoffMultiplier: 2
      retryExceptions:
        - com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException
    event_handler:
      maxRetryAttempts: 1
      waitDuration: 50
      enableExponentialBackoff: true
      exponentialBackoffMultiplier: 2
      retryExceptions:
        - com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException
    camunda:
      maxRetryAttempts: 3
      waitDuration: 100
      retryExceptions:
        - com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException
    camunda_signal:
      maxRetryAttempts: 3
      waitDuration: 20000
      retryExceptions:
        - com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException
    appConnect:
      maxRetryAttempts: 3
      waitDuration: 100
      retryExceptions:
        - com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException
    appConnect-workflow-task-handler:
      maxRetryAttempts: 3
      waitDuration: 100
      retryExceptions:
        - com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException

#Kafka Config
event:
  primary:
    bootStrapServers:
      - eventbus-local.pl-data-lake-qa.a.intuit.com:29701
      - eventbus-local.pl-data-lake-qa.a.intuit.com:29702
      - eventbus-local.pl-data-lake-qa.a.intuit.com:29801
      - eventbus-local.pl-data-lake-qa.a.intuit.com:29802
      - eventbus-local.pl-data-lake-qa.a.intuit.com:29901
      - eventbus-local.pl-data-lake-qa.a.intuit.com:29902
  security_protocol: SSL
  ssl_enabled_protocol: TLSv1.2
  idpsconfig:
    endpoint: kafkainfra-pre-production-l10maf.pd.idps.a.intuit.com
    api_secret_key: ../idps_config/key_v2-e3b373bd2284e.pem # remove ../ if you get Missing Private Key
    api_key_id: v2-e3b373bd2284e
    name: KeyedIDPS
  consumer:
    groupId: workflowAutomationService-local
    enable-secondary: false
    enablePublishController: true
    heartbeatIntervalMs: 6000
    retryConfig:
      retryMaxAttempts: 1 # max retry attempts on a topic including the main consumption call
      retryBackoffPeriod: 500 #fixed backoff period for retry
      dlqEnabled: false #control enabling/disabling of DLQ consumer
      dlqConcurrency: 4
      # retry config on DLQ error handler
      # DLQ retries will occur at cumulative intervals of 2min - 5min - 9.5min
      dlqBackoffInitialInterval: 120000 #exponential backoff initial interval: 2min
      dlqBackoffMultiplier: 1.5 #exponential backoff multiplier
      dlqRetryMaxAttempts: 4 # max retry attempts on dlq topic including the main consumption call

      #exponential backoff max interval. the default value is 30sec which is lower than dlqBackoffInitialInterval
      # any interval > maxInterval value is reset to the maxInterval
      # so the max interval value is increased to enable exponentially backed off retries
      dlqBackoffMaxInterval: 2100000
      # max poll interval set to 35min for DLQ to enable retries without consumer rebalance
      dlqMaxPollIntervalMs: 2100000
    enabledEntities:
      externalTask: false
      externalTaskTest: false
      definitionEvent: false
      trigger: true
      workflowTransitionEvents: true
      externalTaskPush: false
      scheduling: false
    entityTopicsMapping:
      externalTask:
        - workflow-externaltask-complete-vep
        - workflow-externaltask-complete-vep-dlq
        - workflow-externaltask-complete-test # to be used for testing
      trigger:
        - workflow-trigger-vep
        - workflow-trigger-vep-dlq
        - workflow-trigger-test
      incident:
        - camunda-incident-vep
      serviceTask:
        - camunda-service-task-assigned
      definitionEvent:
        - definition-event-test
      externalTaskTest:
        - workflow-externaltask-complete-vep
      workflowTransitionEvents:
        - camunda-transition-events-qalb
      externalTaskPush:
        - workflow-externaltask-assigned-push-test-qal
      scheduling:
         - was-scheduling-test

  producer:
    environment: qa
    enable-secondary: false
    entityTopicsMapping:
      externalTask: workflow-externaltask-assigned-vep
      externalTaskTest: workflow-externaltask-assigned-test # to be used for testing on local
      incident: workflow-incident-vep
      serviceTask: camunda-service-task-assigned
      workflowDefinition: workflow-defintion-slb
      workflowTransitionEvents: workflow-transition-events-qalb
      trigger: workflow-trigger-test
      scheduling: was-scheduling-test


offerings:
  filter:
    excludeUrlPatterns:
      - /health
      - /ping
      - /swagger
      - /metrics
      - /actuator
  default-offering: qbo
  allowedSystemUserUrls: #Include url patterns if API path is applicable for system-user auth context
    - /v1/history/process/*
    - /v1/processes
    - /v1/sendEvent
    - /v1/template/**
    - /v2/trigger
    - /v1/external-task/*

  #Note: Default dataSource and workflowCore config will be used for any offering which does not explicitly specifiy it
  downstreamServices:
    - offeringId: qbo #This offeringId is used to run liquibase on default DB
    -
     offeringId: demo-offering
     workflowCore:
       hostUrl: https://workflowenginecore-qal.api.intuit.com
       baseUrl: https://workflowenginecore-qal.api.intuit.com/rest
     dataSource:
       password: ${db.password}
       username: ${db.username}
       url: **********************************************


# Maximum value of history ttl in days
history:
  ttl: 30


#feature flag config required to enable extension attributes in ExternalTaskClient
extension-attributes:
  enabled: true

#Task Status event of ServiceTask and External task.
taskStatusEvent:
  allowedUpdateStatusEvent:
    - blocked
    - in-process

workflowtask:
  enable: true
  state-transition-config:
    maxResult: 100
  task-config:
    HUMAN_TASK:
      endpoint: https://accountantworkflow-e2e.api.intuit.com/v4/entities
      nonRetryErrorCodes:
        - INVALID_REQUEST
        - SYSTEM_ERROR
        - AUTHENTICATION_ERROR
        - AUTHORIZATION_ERROR
        - NOT_FOUND_ERROR
        - NOT_ACCEPTABLE_ERROR
        - UNSUPPORTED_MEDIA_TYPE_ERROR
        - VALIDATION_ERROR
        - STALE_STATE_ERROR
        - DUPLICATE_PROJECT_NAME_FOR_CLIENT
    NOTIFICATION_TASK:
      endpoint: https://consumernotification-e2e.api.intuit.com/v1/events
    BATCH_NOTIFICATION_TASK:
      endpoint: https://consumernotification-e2e.api.intuit.com/v1/events/batch

# Properties enabled only for overwatch automations, will be controlled via the flag
overwatchproperties:
  connectorWorkflowId: true

logging:
  level:
    com.intuit.platform.jsk.config: WARN

camunda:
  bpm:
    dmnFeelEnableLegacyBehavior: true
    dmnEnableCustomTransformer: true

batch-job:
  enabled: true
  cron: "0 0 18 * * *"
  jobName: "workflowBatchJob"
  threadPoolSize: 1
  chunkSize: 1 # Number of Items committed to DB as a batch . Value should be less than BatchSize. Since write takes so
  keepAliveTime: 300 # 5 mins. Since Some job step takes more than 1 minute (with retries)
  batchSize: 5 # Page Size for a Single Read. Starting with lesser value, since it also reads BPMN Data.
  stepConfig:
    cleanupDefinition:
      enabled: true
      isCascade: false
      templateName: ""
      cleanupStatus:
        - MARKED_FOR_DELETE
        - MARKED_FOR_DISABLE
        - STALE_DEFINITION
      cleanErrorProcess: true
      baseTimeJobStart: 15
      durationInMinutes: 15
      priority: 1
    userToSingleDefinitionMigration:
      enabled: true
      templateName: customReminder # Template name for which definition migration will happen
      templateVersion: "'15','16'" # List of Template versions for which definition migration will happen, keep an empty string for all versions
      recordTypes: "'invoice','bill'" #Comma separated list of recordTypes without space for which migration will be enabled
      baseTimeJobStart: 15
      durationInMinutes: 15
      priority: 2
    singleToSingleDefinitionMigration:
      enabled: true
      templateName: customReminder # Template name for which definition migration will happen
      templateVersion: "3" # List of Template versions for which definition migration will happen, keep an empty string for all versions
      recordTypes: "'invoice','bill'" #Comma separated list of recordTypes without space for which migration will be enabled
      durationInMinutes: 15
      priority: 3
    customReminderToESSMigration:
      enabled: false
      baseTimeJobStart: 15
      durationInMinutes: 15
      priority: 4
    cacheWarmup:
      enabled: false
      baseTimeJobStart: 15
      durationInMinutes: 15
      priority: 5
      threadPoolSize: 4
    scheduledActionsESSToSchedulingSvcMigration:
      enabled: false
      templateName: customScheduledActions
      recordTypes: "'statement','report'"
      templateIds: "'2d7720e9-f9bd-4ba5-b4da-75d9f5f2a119'"
      baseTimeJobStart: 1
      durationInMinutes: 15  # duration in minutes
      priority: 4
    schedulingSvcMigrationCleanup:
      enabled: false
      templateName: customScheduledActions
      recordTypes: "'statement','report'"
      templateIds: "'2d7720e9-f9bd-4ba5-b4da-75d9f5f2a119'"
      baseTimeJobStart: 1
      durationInMinutes: 15  # duration in minutes
      priority: 4
    cleanupProcess:
      enabled: false
      isCascade: false
      cleanupStatus:
        - ended
      baseTimeJobStart: 15
      durationInMinutes: 15
      priority: 6


authz:
  env: QAL #Default
  connectionTimeOutMs: 1000

oinp:
  send-notification:
    invoicecustomReminder:
      notificationTypes: IsMobile # Provide comma separated list of notification types
      ownerIds: -1 # -1 means all ownerIds are whitelisted. Provide comma separated list of ownerIds if required.
    estimatecustomReminder:
      notificationTypes: IsMobile
      ownerIds: -1
    invoiceapproval:
      notificationTypes: IsMobile
      ownerIds: -1

domain-event:
  topic:
    PROCESS:
      topic: qal.foundation.workflow.workflowautomation.process.v1
      enabled: true
    ACTIVITY:
      topic: qal.foundation.workflow.workflowautomation.activity.v1 #to be created later
      enabled: true
    ACTIVITY_RUNTIME:
      topic: qal.foundation.workflow.workflowautomation.activityruntime.v1
      enabled: true
    TEMPLATE:
      topic: qal.foundation.workflow.workflowautomation.template.v1
      enabled: true
    DEFINITION:
      topic: qal.foundation.workflow.workflowautomation.definition.v1
      enabled: true
  region: USW2
  enabled: true
  consumer:
    processNotificationWorkflow: false

definitionEventProcessor:
  definitionEventType:
    customReminderFetchTransactions:
      handlerIds:
        newCustomStart: /intuit-workflows/api/custom-reminder-start-process.json
        customWait: /intuit-workflows/api/custom-reminder-wait.json

throttle:
  definitionsPerWorkflowInTimeframe: true
  definitionsPerWorkflowPerRealm: true
  externalTasksPerActivity: true
  warnDiff: 10
  warnDiffCount:
    definitionsPerWorkflowInTimeframe: 10
    definitionsPerWorkflowPerRealm: 10
    externalTasksPerActivity: 10
  workflow:
    invoiceapprovalThrottleBreach: # For automation test
      threshold:
        definitionsPerWorkflowInTimeframe: 1
      timeframeMillis:
        definitionsPerWorkflowInTimeframe: 120000 # in milliseconds
    invoiceapprovalThrottleBreachUpdate: # For automation test
      threshold:
        definitionsPerWorkflowInTimeframe: 2
      timeframeMillis:
        definitionsPerWorkflowInTimeframe: 120000 # in milliseconds
    invoiceapprovalThrottleConfigDisable: # For automation test
      disable:
        definitionsPerWorkflowInTimeframe: true
    invoiceApprovalPerRealmCreateBreach:
      threshold:
        definitionsPerWorkflowPerRealm: 2
    invoiceApprovalPerRealmBreach:
      threshold:
        definitionsPerWorkflowPerRealm: 2
    invoiceApprovalPerRealmThrottleConfigDisable:
      disable:
        definitionsPerWorkflowPerRealm: true
    engagementloopBreach: # for overwatch test
      threshold:
        externalTasksPerActivity: 3
    engagementloopBreachDB: # for overwatch test
      threshold:
        externalTasksPerActivity: 4
    engagementloopThrottleConfigDisable: # for overwatch test
      disable:
        externalTasksPerActivity: true
    invoiceApprovalPerRealmMixedStatus:
      threshold:
        definitionsPerWorkflowPerRealm: 2
    invoiceApprovalPerRealmMFDThrottle:
      threshold:
        definitionsPerWorkflowPerRealm: 2


ess-sqs:
  enabled: ${sqs_enabled:false}
  assumeRoleArn: arn:aws:iam::862299615669:role/WkflAtmnSvcEss-SQSRoleQAL
  assumeRoleSession: WkflSqsSession
  sqsQueueUrl: overwatch-test-sqs
  endPoint: http://localhost:9324/
  suffix: queue/ # while publishing to local queue we have to append "queue" keyword after the endpoint.However, in case of listen we dont need to.
  retryCount: 5
  thread-pool:
    minThreads: 1
    maxThreads: 10
    keepAliveTimeInSec: 300
    queueSize: 10
  retry:
    statusCode:
      - 500
      - 502
      - 504
      - 202 #The request has been accepted for processing, but the processing has not been completed
      - 429 #Throttle

#Pagination on AppConnect Duzzit for Select Query from QBO
appconnect-duzzit-pagination:
  totalPages: 2
  recordsPerPage: 125
  maxResult: 250
  recordConfig:
    invoice:
       totalPages: 10
       recordsPerPage: 125
       maxResult: 250

# IXP Config
ixp:
  enabled : false
  env: PRE_PROD
  subEnv: QAL
  businessUnit: SBSEG
  applicationName: workflow-automation-service
  country: US
  clientVersion: 1.0
  clientName: workflow-automation-service
  pollingIntervalSeconds: 60
  dataServiceReadTimeout: 5000
  dataServiceCXTimeout: 5000

# Event Scheduler
event-scheduler:
  enabled: true
  url: https://paymentschedule-qal.api.intuit.com/v4/entities
  timezone: America/Los_Angeles
  workflow:
    customReminder:
      scheduleActions:
        customStart:
          #startDate: T # default value is today #FORMAT - yyyy-mm-dd
          noOfDaysToBeAddedToStartDate: 0
        customWait:
          #startDate: T # default value is today #FORMAT - yyyy-mm-dd
          noOfDaysToBeAddedToStartDate: 1
    customScheduledActions:
      scheduleActions:
        customStart:
          #startDate: T # default value is today #FORMAT - yyyy-mm-dd
          noOfDaysToBeAddedToStartDate: 0
    customUpdateEntity:
      scheduleActions:
        customStart:
          #startDate: T # default value is today #FORMAT - yyyy-mm-dd
          noOfDaysToBeAddedToStartDate: 0
    customSendEntity:
      scheduleActions:
        customStart:
          #startDate: T # default value is today #FORMAT - yyyy-mm-dd
          noOfDaysToBeAddedToStartDate: 0

# Scheduling Service
scheduling-svc:
  baseUrl: https://schedulingsvc-qal.api.intuit.com/v1/schedule
  migrationEnabled: true
  numaflowProcessingEnabled: false

# Multi Step config useful for multi-step definitions
multi-step:
  maxNumberOfSteps: 50
  maxNumberOfApprovers: 15

cache:
  enabled: false
  clusterMode: false
  hostUrls:
    - "redis://127.0.0.1:7000"
  scanInterval: 200

ucs:
  enabled: true
  url: https://contributions-qal.api.intuit.com
  verify-access: ${ucs.url}/v1/verify-access
  delete: ${ucs.url}/v1/template/
  delete-all: ${ucs.url}/templates

async-processing:
  enabled: false
  endpoints:
    - /v1/trigger
    - /v2/trigger
  workflows:
    - customapproval
    - engagementTTLiveFullServiceTestConsumer_PS

variability:
  connectionRequestTimeout : 10000
  socketTimeOut : 10000
  maxConnections : 50
  sslEnabled : false
  maxConnectionsPerRoute: 10
  connectTimeOutMilliSeconds: 10000
  connectionKeepAliveTimeSeconds : 280
  maxIdleConnectionTimeMilliSeconds : 25
  maxIdleConnectionTimeoutSeconds : 10
  circuitBreakerEnabled: false
  retryEnabled: true
  instancePropertiesEnabled: true
  instanceMaxAttempts: 1
  instanceExponentialBackoffEnabled: true
  enable: true
  enabledRealms:
    - 9130349382878586
    - -1

bpmn-initial-start-event-insertion:
    enabled: true

push-task:
  enabled: false
  topics:
    engagement-test:
      lockDuration: 150000
    engagement:
      lockDuration: 150000
  thread-pool:
    minThreads: 1
    maxThreads: 10
    keepAliveTimeInSec: 300
    queueSize: 10

rbac:
  definition:
    createEnabled: true #CREATE_WORKFLOW_DEFINITION
    readEnabled: true #READ_ALL_DEFINITION, READ_ONE_DEFINITION
    updateEnabled: true #UPDATE_WORKFLOW_DEFINITION
    deleteEnabled: true #DELETE_WORKFLOW_DEFINITION
    enableEnabled: true #ENABLE_WORKFLOW_DEFINITION
    disableEnabled: true #DISABLE_WORKFLOW_DEFINITION

    roleGroups:
      adminRoles: &adminRoles
        - Intuit.sb.accountant.masteradmin
        - MMa
        - Intuit.sb.accountant.companyadmin
        - CAa

      standardRoles: &standardRoles
        - Intuit.sb.accountant.masteradmin
        - MMa
        - Intuit.sb.accountant.companyadmin
        - CAa
        - Intuit.sb.accountant.allaccess
        #- Intuit.sb.accountant.regularuser

    policies:
      approval:
        permissions:
          CREATE: *adminRoles
          READ: *adminRoles
          UPDATE: *adminRoles
          DELETE: *adminRoles
          ENABLED: *adminRoles
          DISABLED: *adminRoles

      default:
        permissions:
          CREATE: *standardRoles
          READ: *standardRoles
          UPDATE: *standardRoles
          DELETE: *standardRoles
          ENABLED: *standardRoles
          DISABLED: *standardRoles

gc:
  serviceEnabled: true
  maxRetries: 4
  unmodified-since-seconds: 60
  max-records-to-be-fetched: 10
  cron: "0 * * * * *"
  thread-pool:
    minThreads: 1
    maxThreads: 10
    keepAliveTimeInSec: 300
    queueSize: 10
  consumer-group: Intuit.appintgwkflw.wkflautomate.wfas-qal-kr000
