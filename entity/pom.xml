<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
        <artifactId>workflow-automation-service-aggregator</artifactId>
        <version>1.1.20</version>
    </parent>

    <artifactId>was-entity</artifactId>

    <dependencies>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>was-aop</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>org.camunda.commons</groupId>
            <artifactId>camunda-commons-typed-values</artifactId>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
        </dependency>
        <dependency>
            <groupId>com.intuit.sb.api</groupId>
            <artifactId>v4-sdk-schema</artifactId>
        </dependency>
        <dependency>
            <groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
            <artifactId>was-telemetry</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.camunda.bpm</groupId>
            <artifactId>camunda-external-task-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.intuit.sb.api</groupId>
            <artifactId>v4-sdk-types</artifactId>
        </dependency>
    </dependencies>

</project>
