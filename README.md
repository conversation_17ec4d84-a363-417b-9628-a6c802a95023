### Title 
Workflow Automation Service 

### Description
WAS(Workflow Automation Service) is an orchestrator for Business Process Management. Workflow is a sequence of steps that define a process. The power of WAS is in defining and executing stateful workflows that have a series of triggers, evaluations and actions.

WAS provides workflow automation as a service to the entire Intuit Ecosystem, by leveraging Camunda Engine for state maintenance.
Basically, it acts as an interfacing layer for Intuit's services which can directly onboard to WAS and deploy their BPMNs seamlessly.
WAS is an orchestration service written as Spring boot application on top of Camunda Engine(which is also deployed as a separate springboot application).

### Badges
[![Build Status](https://build.intuit.com/qbo/buildStatus/buildIcon?job=appintgwkflw-wkflautomate/wkflatmnsvc/wkflatmnsvc-eks/develop)](https://build.intuit.com/qbo/job/appintgwkflw-wkflautomate/job/wkflatmnsvc/job/wkflatmnsvc-eks/job/develop/)
[![Code Coverage](https://codecov.tools.a.intuit.com/ghe/appintgwkflw-wkflautomate/workflow-automation-service/branch/develop/graph/badge.svg)](https://codecov.tools.a.intuit.com/ghe/appintgwkflw-wkflautomate/workflow-automation-service)
[![codecov](https://sonarqube.tools-k8s.a.intuit.com/api/project_badges/measure?project=com.intuit.appintgwkflw.wkflautomate%3Aworkflow-automation-service-aggregator&metric=alert_status)](https://sonarqube.tools-k8s.a.intuit.com/dashboard?id=com.intuit.appintgwkflw.wkflautomate%3Aworkflow-automation-service-aggregator)

### Usage

#### GraphQl and REST Resources
 [WAS Core](core/README.md) :
 ```Please refer to this code module for detailed information on various GraphQl and REST APIs used in the development for Creation, Updation, Deletion and Starting of the workflows.```
 
#### Postman Collections [```For GraphQL and Rest APIs```]
  [WAS API Collections](collections.json)

### Local Development
 
- [Workflow Automation Service Developer Guide](core/docs/Developer_Guide.md)
- [Set up a workflow](core/docs/Setup_Workflow.md) 
- [Api Documentation](https://appintgwkflw-qal-wkflatmnsvc-root.sbgqboppdusw2.iks2.a.intuit.com/swagger-ui.html)
- [Modules](modules.md)


### Contributing
Refer to [contribution guidline](CONTRIBUTING.md)

### Support
[FAQ's](https://github.intuit.com/pages/appintgwkflw-wkflautomate/workflow-platform-docs/#/support/faq)

[Reach out to us](https://github.intuit.com/pages/appintgwkflw-wkflautomate/workflow-platform-docs/#/support/support) 
