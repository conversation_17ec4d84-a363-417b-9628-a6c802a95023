<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="https://www.omg.org/spec/DMN/20191111/MODEL/" xmlns:biodi="http://bpmn.io/schema/dmn/biodi/1.0" xmlns:ns0="http://camunda.org/schema/1.0/dmn" id="Definitions_086839o" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" expressionLanguage="https://www.omg.org/spec/DMN/20191111/FEEL/" typeLanguage="https://www.omg.org/spec/DMN/20191111/FEEL/" exporter="Camunda Modeler" exporterVersion="5.5.0">
  <decision id="decisionElement" name="Invoice Approval">
    <extensionElements>
      <ns0:bounds height="80" width="180" x="150" y="150" />
    </extensionElements>
    <decisionTable id="decisionTable_1" hitPolicy="FIRST" preferredOrientation="Rule-as-Row">
      <input id="input0" label="Index" ns0:inputVariable="Index">
        <inputExpression id="inputExpression_0" typeRef="integer">
          <text>Index</text>
        </inputExpression>
      </input>
      <input id="input1" label="Class" ns0:inputVariable="Class">
        <inputExpression id="inputExpression_1" typeRef="list">
          <text>Class</text>
        </inputExpression>
      </input>
      <input id="input2" label="TxnAmount" ns0:inputVariable="TxnAmount">
        <inputExpression id="inputExpression_2" typeRef="double">
          <text>TxnAmount</text>
        </inputExpression>
      </input>
      <output id="output_1" label="decisionResult" name="decisionResult" typeRef="String" />
      <rule id="rule_551b5614-d805-47b5-bc03-5bdb440cb0e3">
        <description></description>
        <inputEntry id="inputEntry_47b64489-d26d-4705-bddf-519719761b03">
          <text>0</text>
        </inputEntry>
        <inputEntry id="inputEntry_4926c58d-1bda-41b6-bf3b-ccb9387e69ed">
          <text>containsAnyElement(["204100000000009617077","204100000000009617078"], Class)</text>
        </inputEntry>
        <inputEntry id="inputEntry_a9919e36-fce1-4f55-ac68-9fcfee9a7ee0">
          <text>[0..1000]</text>
        </inputEntry>
        <outputEntry id="outputEntry_df5cee5f-097e-420b-bb4c-10954b7ff369">
          <text>"sendForApproval-2"</text>
        </outputEntry>
      </rule>
      <rule id="rule_8e3e6828-1e63-411e-aa20-0ee16d4b2dcc">
        <description></description>
        <inputEntry id="inputEntry_7777b24b-c2b0-4ed6-9ef9-d5c437774575">
          <text>0</text>
        </inputEntry>
        <inputEntry id="inputEntry_cf92135e-636d-4096-8fb1-e2656abf6d9b">
          <text></text>
        </inputEntry>
        <inputEntry id="inputEntry_5aab1d06-031a-4e0e-999f-db6dd607e657">
          <text></text>
        </inputEntry>
        <outputEntry id="outputEntry_f4cbff1f-fec4-4095-8d16-1b7dcde208d9">
          <text>"2"</text>
        </outputEntry>
      </rule>
      <rule id="rule_e7bb92d7-d701-4f36-9407-60156d28eed0">
        <description></description>
        <inputEntry id="inputEntry_00bd9a54-d553-4bc6-9e5b-6ee4d26d6fd0">
          <text>2</text>
        </inputEntry>
        <inputEntry id="inputEntry_f9869ef2-047f-4708-ac7b-22ba101c01d4">
          <text>containsAnyElement(["204100000000009617079"], Class)</text>
        </inputEntry>
        <inputEntry id="inputEntry_658cfbe7-c6b3-442d-813e-f830d659cab2">
          <text></text>
        </inputEntry>
        <outputEntry id="outputEntry_dc40c86a-4310-46b0-9a14-9771aaf0cd31">
          <text>"sendForApproval-3"</text>
        </outputEntry>
      </rule>
      <rule id="rule_8b3fbaf9-2a1c-40c8-a1e7-c0f29910560d">
        <description></description>
        <inputEntry id="inputEntry_6665c4b9-fa29-4247-b41c-0a52fe7ed5a0">
          <text>2</text>
        </inputEntry>
        <inputEntry id="inputEntry_07eda953-200c-4b61-b11f-260db6eeb1eb">
          <text></text>
        </inputEntry>
        <inputEntry id="inputEntry_c5a35bab-da09-4de8-9bdf-bf619382cfc7">
          <text></text>
        </inputEntry>
        <outputEntry id="outputEntry_b013acb7-8e68-4e91-99dd-748c7e422846">
          <text>"false"</text>
        </outputEntry>
      </rule>
    </decisionTable>
  </decision>
</definitions>