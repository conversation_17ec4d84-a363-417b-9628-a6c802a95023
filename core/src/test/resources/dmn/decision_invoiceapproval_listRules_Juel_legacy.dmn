<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions xmlns="https://www.omg.org/spec/DMN/20191111/MODEL/" xmlns:dc="http://www.omg.org/spec/DMN/20180521/DC/" xmlns:dmndi="https://www.omg.org/spec/DMN/20191111/DMNDI/" xmlns:ns0="http://camunda.org/schema/1.0/dmn" exporter="Camunda Modeler" exporterVersion="3.3.1" expressionLanguage="https://www.omg.org/spec/DMN/20191111/FEEL/" id="Definitions_086839o" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" typeLanguage="https://www.omg.org/spec/DMN/20191111/FEEL/">

  <decision id="decisionElement_9130358263212016_5d962ff5-9f48-46e7-9e37-b9637c077438" name="Invoice reminder 16 May-2">

    <decisionTable hitPolicy="FIRST" id="decisionTable_1" preferredOrientation="Rule-as-Row">
      <input id="input1" label="TxnPaymentStatus" ns0:inputVariable="TxnPaymentStatus">
        <inputExpression id="inputExpression_1" typeRef="list">
          <text>${TxnPaymentStatus}</text>
        </inputExpression>
      </input>
      <input id="input3" label="TxnAmount" ns0:inputVariable="TxnAmount">
        <inputExpression id="inputExpression_3" typeRef="double">
          <text>${TxnAmount}</text>
        </inputExpression>
      </input>
      <input id="input4" label="Customer" ns0:inputVariable="Customer">
        <inputExpression id="inputExpression_4" typeRef="list">
          <text>${Customer}</text>
        </inputExpression>
      </input>

      <output id="output_1" label="decisionResult" name="approvalRequired" typeRef="boolean"/>
      <rule id="rule_511f3aba-5b11-4d75-8b4e-2dabdc1404b0">
        <description/>
        <inputEntry expressionLanguage="juel" id="inputEntry_65a787e0-347b-4f8f-836d-ba04907be391">
          <text>TxnPaymentStatus.equals("UNPAID")</text>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_69bbc749-624a-4f29-a7d0-e4f43b2bfa29">
          <text>TxnAmount &gt; 100</text>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_45af4527-d35f-40d6-8fe2-402a00fdaba9">
          <text>Customer.equals("1") || Customer.equals("12") || Customer.equals("2")</text>
        </inputEntry>
        <outputEntry id="outputEntry_764752f3-b5f1-47d1-8e62-a65937cb5116">
          <text>true</text>
        </outputEntry>
      </rule>
      <rule id="rule_6603dca1-3ce4-41f3-bcc5-733c5a475561">
        <description/>
        <inputEntry expressionLanguage="juel" id="inputEntry_0ca6e3fb-8626-4f52-bcf0-5848a0f534cc">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_7e891478-38e9-4b98-9f7c-84d7d6cde1d0">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_a0ebbfda-35cf-42a3-9e64-3070351fc7da">
          <text/>
        </inputEntry>
        <outputEntry id="outputEntry_044aeb49-8394-435c-af80-5825489d4ba5">
          <text>false</text>
        </outputEntry>
      </rule>

    </decisionTable>

  </decision>



</definitions>