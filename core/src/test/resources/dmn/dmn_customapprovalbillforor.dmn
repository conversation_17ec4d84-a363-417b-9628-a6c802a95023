<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="https://www.omg.org/spec/DMN/20191111/MODEL/" xmlns:dmndi="https://www.omg.org/spec/DMN/20191111/DMNDI/" xmlns:dc="http://www.omg.org/spec/DMN/20180521/DC/" xmlns:ns0="http://camunda.org/schema/1.0/dmn" id="Definitions_086839o" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" expressionLanguage="http://www.omg.org/spec/FEEL/20140401" typeLanguage="http://www.omg.org/spec/FEEL/20140401" exporter="Camunda Modeler" exporterVersion="3.3.1">
  <decision id="decisionElement_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" name="mybillworkflow">
     <decisionTable hitPolicy="FIRST" id="decisionTable_1" preferredOrientation="Rule-as-Row">
      <input id="input1" label="TxnAmount" ns0:inputVariable="TxnAmount">
        <inputExpression id="inputExpression_1" typeRef="double">
          <text>${TxnAmount}</text>
        </inputExpression>
      </input>
      <input id="input2" label="intuit_userid" ns0:inputVariable="intuit_userid">
        <inputExpression id="inputExpression_2" typeRef="string">
          <text>${intuit_userid}</text>
        </inputExpression>
      </input>
      <input id="input3" label="CFTxnBalanceAmount" ns0:inputVariable="CFTxnBalanceAmount">
        <inputExpression id="inputExpression_3" typeRef="double">
          <text>${CFTxnBalanceAmount}</text>
        </inputExpression>
      </input>
      <input id="input4" label="CFTxnBall" ns0:inputVariable="CFTxnBall">
        <inputExpression id="inputExpression_4" typeRef="double">
          <text>${CFTxnBall}</text>
        </inputExpression>
      </input>
      <input id="input5" label="CFid" ns0:inputVariable="CFid">
        <inputExpression id="inputExpression_5" typeRef="string">
          <text>${CFid}</text>
        </inputExpression>
      </input>

      <output id="output_1" label="decisionResult" name="decisionResult" typeRef="boolean"/>
      <rule id="rule_0677a60c-76b9-4cc9-b9d0-77d3c09289b2">
        <description/>
        <inputEntry expressionLanguage="juel" id="inputEntry_ca9752a3-fd5a-40d1-9e36-a963a55575e1">
          <text>TxnAmount &gt;= 0</text>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_fea9931a-99a5-452b-84bd-67515b2a3654">
          <text>!intuit_userid.equals("9130358394535786")</text>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_4c11331f-6ab6-4478-8ebb-f4de69841127">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_8dd21645-f02e-4b9a-8e1c-f5cb05f519a8">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_c6566804-f490-484b-b3ca-a1c2a3ab5b49">
          <text/>
        </inputEntry>
        <outputEntry id="outputEntry_06441479-f44e-416e-aa12-11cadcc99ff7">
          <text>true</text>
        </outputEntry>
      </rule>
      <rule id="rule_bdcfdfbb-f02a-40fd-bd97-2a906ba11d67">
        <description/>
        <inputEntry expressionLanguage="juel" id="inputEntry_591e8dcb-56c2-44bf-8738-5ec2c9331897">
          <text>TxnAmount &gt; 1</text>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_d806a55b-4521-48ac-a02d-896dc3244054">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_d173f18e-8fd5-4a90-918d-fe1367c77218">
          <text>CFTxnBalanceAmount &gt; 17</text>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_dd0d0af8-f1d6-48b4-be6a-a0df5459f93e">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_4be9ccaa-d967-48c3-a9bd-6592d4a3d296">
          <text/>
        </inputEntry>
        <outputEntry id="outputEntry_f0675d2e-5d40-4768-89ee-7ae728f501d8">
          <text>true</text>
        </outputEntry>
      </rule>
      <rule id="rule_51d999a8-70ea-49e1-824c-cbafb3ada4ae">
        <description/>
        <inputEntry expressionLanguage="juel" id="inputEntry_c0071778-f1e2-4b0f-86dd-db365f7dad34">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_3418e150-52ed-4f1d-aaa2-c74763554088">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_b7d2f8f7-bcca-4e05-a4fc-5804fc34a396">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_08c1dcfe-9bf3-4ddb-9ac1-3992f2fc448f">
          <text>CFTxnBall &gt; 0</text>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_b061192f-5a26-4078-b55d-40bd848037da">
          <text>!CFid.equals("123")</text>
        </inputEntry>
        <outputEntry id="outputEntry_c182c41e-b9de-4019-97dc-7b9dd889f656">
          <text>true</text>
        </outputEntry>
      </rule>
      <rule id="rule_5c939ffb-b648-415f-a852-4f0b4771d761">
        <description/>
        <inputEntry expressionLanguage="juel" id="inputEntry_53302ab5-ee3c-44d0-bdd8-4946f3d961ed">
          <text>TxnAmount &gt; 50</text>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_cbe54bbc-0e92-4c2b-8442-ea0bc9910b52">
          <text>!intuit_userid.equals("9130358394535786")</text>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_cce57879-3e43-4c0d-b31c-84bbb14593f2">
          <text>CFTxnBalanceAmount &gt; 0</text>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_9728767b-5a20-4040-8a7e-248931efd3df">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_a75dbfb3-1822-4da4-9dd7-8e05e3217e15">
          <text/>
        </inputEntry>
        <outputEntry id="outputEntry_26d498f4-8e48-46de-acfc-a3b7ca5cfea5">
          <text>true</text>
        </outputEntry>
      </rule>
      <rule id="rule_5f86aa78-05f2-49cc-b545-d183e1efbe43">
        <description/>
        <inputEntry expressionLanguage="juel" id="inputEntry_2cc74e3b-472c-4f92-974d-6e1c1760294a">
          <text>TxnAmount &gt; 50</text>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_f5b7be22-5ec9-4ace-94b2-fec3de6056d4">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_45a5ecdd-7224-47a2-aeee-ccbbce5016d7">
          <text>CFTxnBalanceAmount &gt; 0</text>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_5587b05b-d9c7-4225-89cb-5b84a609b8e4">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_cf752f03-8cf6-4660-bda0-565a7097a27c">
          <text>!CFid.equals("1234")</text>
        </inputEntry>
        <outputEntry id="outputEntry_c0e7cd13-141c-4b80-b8c1-0f8b09cb0b2e">
          <text>true</text>
        </outputEntry>
      </rule>
      <rule id="rule_edb86759-cfb0-41f6-b137-f04f6ad5fb3c">
        <description/>
        <inputEntry expressionLanguage="juel" id="inputEntry_862fd683-af5f-4860-8ad9-b9b3251f74ab">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_443b5ac9-561b-4753-a699-4b80aa59615c">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_32c77255-b996-48b3-8ed1-0a408a10c7ad">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_231c0d1d-219a-4052-8e01-e197befce969">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_91e69413-e385-426c-bea3-2b6a934083ec">
          <text/>
        </inputEntry>
        <outputEntry id="outputEntry_ff8f8ee1-8c3b-4677-bda4-5ba95f74df2b">
          <text>false</text>
        </outputEntry>
      </rule>

    </decisionTable>
  </decision>
  <dmndi:DMNDI>
    <dmndi:DMNDiagram id="DMNDiagram_12bv9q5">
      <dmndi:DMNShape id="DMNShape_140vmm3" dmnElementRef="decisionElement_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad">
        <dc:Bounds height="80" width="180" x="150" y="150" />
      </dmndi:DMNShape>
    </dmndi:DMNDiagram>
  </dmndi:DMNDI>
</definitions>