<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions xmlns:ns0="http://camunda.org/schema/1.0/dmn" exporter="Camunda Modeler" exporterVersion="4.0.0" id="definitions" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" xmlns="https://www.omg.org/spec/DMN/20191111/MODEL/">
  <decision id="businessRuleTask-1_b4cd037a6e9af7eb665de163aece22cc" name="Multi Condition Dynamic Decision">
    <decisionTable hitPolicy="FIRST" id="decisionTable_1">
      <input id="input0" label="Index" ns0:inputVariable="Index">
        <inputExpression id="inputExpression_0" typeRef="integer">
          <text>Index</text>
        </inputExpression>
      </input>
      <input id="input1" label="TxnAmount" ns0:inputVariable="TxnAmount">
        <inputExpression id="inputExpression_1" typeRef="double">
          <text>TxnAmount</text>
        </inputExpression>
      </input>
      <input id="input2" label="CF302300000000000329561" ns0:inputVariable="CF302300000000000329561">
        <inputExpression id="inputExpression_2" typeRef="list">
          <text>CF302300000000000329561</text>
        </inputExpression>
      </input>
      <input id="input3" label="CF302300000000000282303" ns0:inputVariable="CF302300000000000282303">
        <inputExpression id="inputExpression_3" typeRef="string">
          <text>CF302300000000000282303</text>
        </inputExpression>
      </input>
      <output id="output_1" label="decisionResult" name="decisionResult" typeRef="String"/>
      <rule id="rule_b4742f61-d790-48f2-84c9-2140a6b557be">
        <description/>
        <inputEntry expressionLanguage="feel" id="inputEntry_e2bd3edd-6c56-4917-9280-68d3eac8fb6d">
          <text>0</text>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_0b91857e-f1df-4595-9d8e-32edf4c1d995">
          <text>[0..1000]</text>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_448d747e-96f8-46e3-92f9-838e342e7693">
          <text>containsAnyElement(["1","2"], CF302300000000000329561)</text>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_ad7bd212-6995-4099-a25e-981b8f99dd3d">
          <text>"yash"</text>
        </inputEntry>
        <outputEntry id="outputEntry_99f87196-0363-4f04-ac33-7218feea0d65">
          <text>"callActivity-1"</text>
        </outputEntry>
      </rule>
      <rule id="rule_53366492-e98e-47ff-836b-dcb642154cc0">
        <description/>
        <inputEntry expressionLanguage="feel" id="inputEntry_d53ee4ee-ef80-44e0-8ecc-0c622db27623">
          <text>0</text>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_e989cbf8-b014-47a8-a2fb-686a5516d07e">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_9367a687-2b76-416e-8822-dfd2863e0715">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_acdb78a3-6232-43bb-bd0a-0c514add68ab">
          <text/>
        </inputEntry>
        <outputEntry id="outputEntry_469c819e-adab-4520-af4d-99185eecb5d3">
          <text>"callActivity-2"</text>
        </outputEntry>
      </rule>
    </decisionTable>
  </decision>
</definitions>
