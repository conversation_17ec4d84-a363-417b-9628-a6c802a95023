<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/DMN/20151101/dmn.xsd" xmlns:biodi="http://bpmn.io/schema/dmn/biodi/1.0" xmlns:camunda="http://camunda.org/schema/1.0/dmn" id="Definitions_1qui2om" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" exporter="Camunda Modeler" exporterVersion="3.2.1">
  <decision id="decision_billapproval_QBDT" name="Dec_in_app_v1_QBDT" camunda:historyTimeToLive="14">
    <extensionElements>
      <biodi:bounds x="157" y="81" width="180" height="80" />
    </extensionElements>
    <decisionTable id="decisionTable_1" hitPolicy="FIRST">
      <input id="input_1" label="Amount" camunda:inputVariable="Amount">
        <inputExpression id="inputExpression_1" typeRef="double">
          <text>${TotalAmt}</text>
        </inputExpression>
      </input>
      <input id="InputClause_13q46an" label="Vendor" camunda:inputVariable="Vendor">
        <inputExpression id="LiteralExpression_153c9i7" typeRef="string">
          <text>${VendorRef_value}</text>
        </inputExpression>
      </input>
      <input id="InputClause_10vl7tp" label="VendorType" camunda:inputVariable="VendorType">
        <inputExpression id="LiteralExpression_10svu8w" typeRef="string">
          <text>${VendorTypeRef_value}</text>
        </inputExpression>
      </input>
      <output id="output_1" label="approvalRequired" name="approvalRequired" typeRef="boolean" />
      <rule id="DecisionRule_1mj2h3y">
        <description>Amount:0</description>
        <inputEntry id="UnaryTests_04po3yu">
          <text></text>
        </inputEntry>
        <inputEntry id="UnaryTests_1id1996">
          <text></text>
        </inputEntry>
        <inputEntry id="UnaryTests_08x2x88">
          <text></text>
        </inputEntry>
        <outputEntry id="LiteralExpression_037m1jx">
          <text></text>
        </outputEntry>
      </rule>
    </decisionTable>
  </decision>
</definitions>