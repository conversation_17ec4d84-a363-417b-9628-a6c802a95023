<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="https://www.omg.org/spec/DMN/20191111/MODEL/" xmlns:dmndi="https://www.omg.org/spec/DMN/20191111/DMNDI/" xmlns:dc="http://www.omg.org/spec/DMN/20180521/DC/" xmlns:ns0="http://camunda.org/schema/1.0/dmn" id="Definitions_086839o" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" expressionLanguage="http://www.omg.org/spec/FEEL/20140401" typeLanguage="http://www.omg.org/spec/FEEL/20140401" exporter="Camunda Modeler" exporterVersion="3.3.1">
  <decision id="decisionElement_9130357471839386_84b7179a-e200-4c71-bb7d-8d511f2fb456" name="invoiceReminder112">
    <decisionTable id="decisionTable_1" hitPolicy="FIRST" preferredOrientation="Rule-as-Row">
      <input id="input1" label="TxnAmount" ns0:inputVariable="TxnAmount">
        <inputExpression id="inputExpression_1" typeRef="double">
          <text>${TxnAmount}</text>
        </inputExpression>
      </input>
      <input id="input2" label="TxnBalanceAmount" ns0:inputVariable="TxnBalanceAmount">
        <inputExpression id="inputExpression_2" typeRef="double">
          <text>${TxnBalanceAmount}</text>
        </inputExpression>
      </input>
      <input id="input3" label="TxnCreateDays" ns0:inputVariable="TxnCreateDays">
        <inputExpression id="inputExpression_3" typeRef="days">
          <text>${TxnCreateDays}</text>
        </inputExpression>
      </input>
      <output id="output_1" label="decisionResult" name="decisionResult" typeRef="boolean" />
      <rule id="rule_cc259a71-55b2-4a03-ba76-f71726f93f9e">
        <description></description>
        <inputEntry id="inputEntry_b26ae14a-7bdc-4e72-a749-a812ffa81d8c" expressionLanguage="juel">
          <text>TxnAmount &gt;= 340</text>
        </inputEntry>
        <inputEntry id="inputEntry_569a794a-5f10-4b3b-a9a2-efd83a5701ce" expressionLanguage="juel">
          <text>TxnBalanceAmount &gt;= 10</text>
        </inputEntry>
        <inputEntry id="inputEntry_e35451d1-6b66-4566-acbd-8e680e0fb745" expressionLanguage="juel">
          <text>TxnCreateDays == 0</text>
        </inputEntry>
        <outputEntry id="outputEntry_364bb717-7c4a-42f8-8080-a9baadedc413">
          <text>true</text>
        </outputEntry>
      </rule>
      <rule id="rule_c012d1fc-10e1-4112-b14b-301559a2c2c3">
        <description></description>
        <inputEntry id="inputEntry_f8fc6563-c9bd-434d-bd03-033fabd418a0" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <inputEntry id="inputEntry_011a3434-3aa4-4a76-a1c8-a04669673e0e" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <inputEntry id="inputEntry_545fd74b-70ce-453c-b8ee-77004e44d9ea" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <outputEntry id="outputEntry_8ee70957-67d7-42d8-9d61-39129c4f5cc5">
          <text>false</text>
        </outputEntry>
      </rule>
    </decisionTable>
  </decision>
  <dmndi:DMNDI>
    <dmndi:DMNDiagram id="DMNDiagram_17bscan">
      <dmndi:DMNShape id="DMNShape_03hnzyx" dmnElementRef="decisionElement_9130357471839386_84b7179a-e200-4c71-bb7d-8d511f2fb456">
        <dc:Bounds height="80" width="180" x="150" y="150" />
      </dmndi:DMNShape>
    </dmndi:DMNDiagram>
  </dmndi:DMNDI>
</definitions>
