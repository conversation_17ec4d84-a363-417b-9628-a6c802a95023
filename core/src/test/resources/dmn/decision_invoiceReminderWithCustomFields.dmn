<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions
	xmlns="https://www.omg.org/spec/DMN/20191111/MODEL/"
	xmlns:biodi="http://bpmn.io/schema/dmn/biodi/1.0"
	xmlns:ns0="http://camunda.org/schema/1.0/dmn" exporter="Camunda Modeler" exporterVersion="3.3.1" expressionLanguage="http://www.omg.org/spec/FEEL/20140401" id="Definitions_086839o" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" typeLanguage="http://www.omg.org/spec/FEEL/20140401">
	<decision id="decision_customField_invoiceapproval" name="test-poc-3">
		<extensionElements>
			<biodi:bounds height="80" width="180" x="150" y="150"/>
		</extensionElements>
		<decisionTable hitPolicy="FIRST" id="decisionTable_1" preferredOrientation="Rule-as-Row">
			<input id="input1" label="CF36000000000001557156" ns0:inputVariable="CF36000000000001557156">
				<inputExpression id="inputExpression_1" typeRef="string">
					<text>${CF36000000000001557156}</text>
				</inputExpression>
			</input>
			<input id="input2" label="CF3600000000000155715" ns0:inputVariable="CF3600000000000155715">
				<inputExpression id="inputExpression_2" typeRef="double">
					<text>${CF3600000000000155715}</text>
				</inputExpression>
			</input>
			<output id="output_1" label="decisionResult" name="decisionResult" typeRef="boolean"/>
			<rule id="rule_079dd7d0-86e5-4307-b95c-64721a2e031e">
				<description/>
				<inputEntry expressionLanguage="juel" id="inputEntry_3c627965-aabd-465a-9ea5-87b5ebc5431c">
					<text>CF36000000000001557156.equals("1") || CF36000000000001557156.equals("2") || CF36000000000001557156.equals("3")</text>
				</inputEntry>
				<inputEntry expressionLanguage="juel" id="inputEntry_96a1cfeb-10e6-4a5c-9b74-9c398fde255b">
					<text>CF3600000000000155715 &gt;= 500</text>
				</inputEntry>
				<outputEntry id="outputEntry_f135317b-97e7-4a69-8179-7e8f3aa3a41e">
					<text>true</text>
				</outputEntry>
			</rule>
			<rule id="rule_286ab275-50a4-46f6-9a67-5cdeecb902dd">
				<description/>
				<inputEntry expressionLanguage="juel" id="inputEntry_eb2a6c8b-ad81-4fdd-bfc8-7ab61a72801a">
					<text/>
				</inputEntry>
				<inputEntry expressionLanguage="juel" id="inputEntry_15693708-f572-466f-8e9f-afcb8eb82d3f">
					<text/>
				</inputEntry>
				<outputEntry id="outputEntry_eb7388d8-b053-4655-a2b7-ec2fe9d8a225">
					<text>false</text>
				</outputEntry>
			</rule>
		</decisionTable>
	</decision>
</definitions>
