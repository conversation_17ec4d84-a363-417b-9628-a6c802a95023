<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions xmlns="http://www.omg.org/spec/DMN/20151101/dmn.xsd" xmlns:biodi="http://bpmn.io/schema/dmn/biodi/1.0" xmlns:ns0="http://camunda.org/schema/1.0/dmn" exporter="Camunda Modeler" exporterVersion="3.3.1" expressionLanguage="http://www.omg.org/spec/FEEL/20140401" id="Definitions_086839o" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" typeLanguage="http://www.omg.org/spec/FEEL/20140401">

  <decision id="decision_customField_invoiceapproval" name="test-poc-4">

    <extensionElements>

      <biodi:bounds height="80" width="180" x="150" y="150"/>

    </extensionElements>

    <decisionTable hitPolicy="FIRST" id="decisionTable_1" preferredOrientation="Rule-as-Row">
      <input id="input1" label="TxnAmount" ns0:inputVariable="TxnAmount">
        <inputExpression id="inputExpression_1" typeRef="double">
          <text>${TxnAmount}</text>
        </inputExpression>
      </input>
      <input id="input2" label="CF36000000000001557156" ns0:inputVariable="CF36000000000001557156">
        <inputExpression id="inputExpression_2" typeRef="string">
          <text>${CF36000000000001557156}</text>
        </inputExpression>
      </input>

      <output id="output_1" label="decisionResult" name="decisionResult" typeRef="boolean"/>
      <rule id="rule_58df010e-e99d-47ca-a0bc-3c7c13c5c08c">
        <description/>
        <inputEntry expressionLanguage="juel" id="inputEntry_667f712d-8ad8-4190-96d7-39633fc326fc">
          <text>TxnAmount &gt;= 100</text>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_58aac45a-b38d-4372-9fc2-780b01a87fc9">
          <text>!CF36000000000001557156.equals("CONTAINS")</text>
        </inputEntry>
        <outputEntry id="outputEntry_41f3df69-14cb-4e94-b4eb-be4cae9d878b">
          <text>true</text>
        </outputEntry>
      </rule>
      <rule id="rule_e2ca41eb-ab5e-477c-8de2-812e65786f2d">
        <description/>
        <inputEntry expressionLanguage="juel" id="inputEntry_3a3b9fa2-fd03-410d-8512-012399324e32">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="juel" id="inputEntry_81f0fb29-7daa-4c7a-9bf8-2b5eb28eddb6">
          <text/>
        </inputEntry>
        <outputEntry id="outputEntry_7ea13e71-17f9-488b-a34c-8d0c2b412651">
          <text>false</text>
        </outputEntry>
      </rule>

    </decisionTable>

  </decision>

</definitions>
