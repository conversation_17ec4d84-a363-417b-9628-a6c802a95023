<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions xmlns:ns0="http://camunda.org/schema/1.0/dmn" exporter="Camunda Modeler" exporterVersion="4.0.0" id="definitions" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" xmlns="https://www.omg.org/spec/DMN/20191111/MODEL/">
  <decision id="businessRuleTask-1_0ab114a123300ae9bd2f0974a6032a70" name="Multi Condition Dynamic Decision">
    <decisionTable hitPolicy="FIRST" id="decisionTable_1">
      <input id="input0" label="Index" ns0:inputVariable="Index">
        <inputExpression id="inputExpression_0" typeRef="integer">
          <text>Index</text>
        </inputExpression>
      </input>
      <input id="input1" label="TxnAmount" ns0:inputVariable="TxnAmount">
        <inputExpression id="inputExpression_1" typeRef="double">
          <text>TxnAmount</text>
        </inputExpression>
      </input>
      <input id="input2" label="TxnDueDays" ns0:inputVariable="TxnDueDays">
        <inputExpression id="inputExpression_2" typeRef="days">
          <text>TxnDueDays</text>
        </inputExpression>
      </input>
      <output id="output_1" label="decisionResult" name="decisionResult" typeRef="String"/>
      <rule id="rule_65b837d4-a184-489f-81bc-cc6b9afe2233">
        <description/>
        <inputEntry expressionLanguage="feel" id="inputEntry_5fe4d42b-b9fe-4ad5-bb48-17cfeba9390b">
          <text>0</text>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_bfe0a84d-9504-4b5f-a87d-c4a8f90b6d8a">
          <text>[20000..20010]</text>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_8b780004-48b0-4840-861f-4b06f6092802">
          <text/>
        </inputEntry>
        <outputEntry id="outputEntry_272a1584-a147-4ae5-a5f6-096b8d62c235">
          <text>"1"</text>
        </outputEntry>
      </rule>
      <rule id="rule_ad224cc5-4717-417f-a3ab-d7f835881f88">
        <description/>
        <inputEntry expressionLanguage="feel" id="inputEntry_3cb55b69-523b-4159-ab33-9d96a06e34d8">
          <text>0</text>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_5d263e9e-ea35-4d6c-b78a-46fcc02d19b2">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_6254d954-ac94-4c50-959b-9bab095693b4">
          <text/>
        </inputEntry>
        <outputEntry id="outputEntry_4e031d67-c6bc-42ec-b34d-53bc1e1f51bd">
          <text>"false"</text>
        </outputEntry>
      </rule>
      <rule id="rule_616be61c-aa82-44ce-9eb5-28cb49a2edf2">
        <description/>
        <inputEntry expressionLanguage="feel" id="inputEntry_01d35801-544d-4f71-8cf7-bb37b9899a5b">
          <text>1</text>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_5ac386c1-8626-4900-b565-dda22a8646b0">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_70cd7dc2-7c9f-41a5-8d40-f9c5cc7ddd37">
          <text>-2</text>
        </inputEntry>
        <outputEntry id="outputEntry_22ae95cf-4c8e-45a8-aec8-b91b323a0ef3">
          <text>"callActivity-1"</text>
        </outputEntry>
      </rule>
      <rule id="rule_f5aad7de-802a-422f-bb51-c148881bbad7">
        <description/>
        <inputEntry expressionLanguage="feel" id="inputEntry_0d8696c1-e771-44c0-83bf-5d0b76f16a8e">
          <text>1</text>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_d239b485-8a07-47d8-b047-231577282a96">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_dc7abadd-d537-421e-a2c6-74d136f13829">
          <text/>
        </inputEntry>
        <outputEntry id="outputEntry_21b326f2-6a97-402d-b813-03af6edd7e98">
          <text>"false"</text>
        </outputEntry>
      </rule>
    </decisionTable>
  </decision>
</definitions>
