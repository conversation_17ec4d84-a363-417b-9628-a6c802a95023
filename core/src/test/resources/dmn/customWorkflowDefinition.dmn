<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/DMN/20151101/dmn.xsd" xmlns:biodi="http://bpmn.io/schema/dmn/biodi/1.0" xmlns:ns0="http://camunda.org/schema/1.0/dmn" id="Definitions_086839o" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" exporter="Camunda Modeler" exporterVersion="3.7.3">
  <decision id="decisionElement_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" name="jatin">
    <extensionElements>
      <biodi:bounds x="150" y="150" width="180" height="80" />
    </extensionElements>
    <decisionTable id="decisionTable_1" hitPolicy="FIRST">
      <input id="input1" label="Customer" ns0:inputVariable="Customer">
        <inputExpression id="inputExpression_1" typeRef="string">
          <text>${customer}</text>
        </inputExpression>
      </input>
      <input id="InputClause_1c1n68p" label="TxnAmount" ns0:inputVariable="TxnAmount">
        <inputExpression id="LiteralExpression_11bbu28" typeRef="double">
          <text>${amount}</text>
        </inputExpression>
      </input>
      <output id="output_1" label="decisionResult" name="decisionResult" typeRef="boolean" />
      <rule id="rule_3be65cd7-1a48-4423-92c5-ccc3d17248d6">
        <description></description>
        <inputEntry id="inputEntry_6f92c69c-5324-468f-83cd-08c195b5535c" expressionLanguage="juel">
          <text>Customer.equals("2") || Customer.equals("4") || Customer.equals("1")</text>
        </inputEntry>
        <inputEntry id="UnaryTests_15413x8">
          <text>TxnAmount &gt;= 100 &amp;&amp; TxnAmount &lt;= 200</text>
        </inputEntry>
        <outputEntry id="outputEntry_4dababc6-ed36-4bda-b952-eaf4a6dfa4a5">
          <text>true</text>
        </outputEntry>
      </rule>
      <rule id="rule_3f771bed-f077-4a5e-911e-551977759b35">
        <description></description>
        <inputEntry id="inputEntry_11b02223-dde9-4f9b-bcc2-856e5f12d6ab" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <inputEntry id="UnaryTests_1db8zu8">
          <text></text>
        </inputEntry>
        <outputEntry id="outputEntry_d42e6c76-2632-4a79-bc28-de6e241e94c2">
          <text>false</text>
        </outputEntry>
      </rule>
    </decisionTable>
  </decision>
</definitions>
