<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions xmlns="https://www.omg.org/spec/DMN/20191111/MODEL/" xmlns:dc="http://www.omg.org/spec/DMN/20180521/DC/" xmlns:dmndi="https://www.omg.org/spec/DMN/20191111/DMNDI/" xmlns:ns0="http://camunda.org/schema/1.0/dmn" exporter="Camunda Modeler" exporterVersion="3.3.1" expressionLanguage="https://www.omg.org/spec/DMN/20191111/FEEL/" id="Definitions_086839o" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" typeLanguage="https://www.omg.org/spec/DMN/20191111/FEEL/">

  <decision id="decisionElement_9130358263212016_964e74f4-5b6e-4980-9c65-40514cd8b27b" name="Invoice reminder 18 May-2">

    <decisionTable hitPolicy="FIRST" id="decisionTable_1" preferredOrientation="Rule-as-Row">
      <input id="input1" label="TxnPaymentStatus" ns0:inputVariable="TxnPaymentStatus">
        <inputExpression id="inputExpression_1" typeRef="list">
          <text>TxnPaymentStatus</text>
        </inputExpression>
      </input>
      <input id="input2" label="TxnDueDays" ns0:inputVariable="TxnDueDays">
        <inputExpression id="inputExpression_2" typeRef="days">
          <text>TxnDueDays</text>
        </inputExpression>
      </input>
      <input id="input3" label="TxnAmount" ns0:inputVariable="TxnAmount">
        <inputExpression id="inputExpression_3" typeRef="double">
          <text>TxnAmount</text>
        </inputExpression>
      </input>
      <input id="input4" label="Customer" ns0:inputVariable="Customer">
        <inputExpression id="inputExpression_4" typeRef="list">
          <text>Customer</text>
        </inputExpression>
      </input>
      <input id="input5" label="SalesRep" ns0:inputVariable="SalesRep">
        <inputExpression id="inputExpression_5" typeRef="string">
          <text>SalesRep</text>
        </inputExpression>
      </input>
      <input id="input6" label="DocNum" ns0:inputVariable="DocNum">
        <inputExpression id="inputExpression_6" typeRef="string">
          <text>DocNum</text>
        </inputExpression>
      </input>

      <output id="output_1" label="decisionResult" name="approvalRequired" typeRef="boolean"/>
      <rule id="rule_827bbe1b-1f81-44e5-9949-ff2d5e922900">
        <description/>
        <inputEntry expressionLanguage="feel" id="inputEntry_c9dc944c-fc7b-45b1-855c-829b2e130432">
          <text>containsAnyElement(["UNPAID"], TxnPaymentStatus)</text>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_9740c390-ae55-455a-9903-01e0e59ffc0f">
          <text>-2</text>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_d2cc8ece-3200-4890-8957-7be06e306252">
          <text>[100..600]</text>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_9348eee6-49d2-48a7-8597-7ebc6107299e">
          <text>containsAnyElement(["1","2","5","78","12"], Customer)</text>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_9348eee6-49d2-48a7-8597-7ebc61072819">
          <text>contains(SalesRep, "abc") or contains(SalesRep, "pqr")</text>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_9348eee6-49d2-48a7-8597-7ebc61072871">
          <text>not("inv001", "inv002")</text>
        </inputEntry>
        <outputEntry id="outputEntry_5707d10b-8a35-483a-be4a-f49ff5f68a7a">
          <text>true</text>
        </outputEntry>
      </rule>
      <rule id="rule_f091c99a-3877-4115-9bc6-f738a9c5fa9a">
        <description/>
        <inputEntry expressionLanguage="feel" id="inputEntry_675686ae-53b5-47f5-b27d-b5071b6f3470">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_f0cf2a44-c289-4035-8b56-82dfcb6b01e0">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_108062c2-68c3-4fe1-82ee-a21266e97376">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_108062c2-68c3-4fe1-82ee-a21266e97157">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_cf0f8ce1-4a33-4972-b84d-f75d7824243b">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_cf0f8ce1-4a33-4972-b84d-f75d7828713b">
          <text/>
        </inputEntry>
        <outputEntry id="outputEntry_67e2a0ac-50a4-45ef-a653-cd87a5faba29">
          <text>false</text>
        </outputEntry>
      </rule>

    </decisionTable>

  </decision>


</definitions>