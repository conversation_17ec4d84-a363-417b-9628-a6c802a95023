<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="https://www.omg.org/spec/DMN/20191111/MODEL/" xmlns:biodi_1="http://bpmn.io/schema/dmn/biodi/2.0" xmlns:biodi="http://bpmn.io/schema/dmn/biodi/1.0" xmlns:ns0="http://camunda.org/schema/1.0/dmn" id="Definitions_086839o" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" expressionLanguage="http://www.omg.org/spec/FEEL/20140401" typeLanguage="http://www.omg.org/spec/FEEL/20140401" exporter="Camunda Modeler" exporterVersion="5.7.0">
  <decision id="decision_customField_invoiceapproval" name="test-poc-3">
    <extensionElements>
      <ns0:bounds height="80" width="180" x="150" y="150" />
    </extensionElements>
    <decisionTable id="decisionTable_1" hitPolicy="FIRST" preferredOrientation="Rule-as-Row">
      <input id="input1" label="CF3600000000000155716" ns0:inputVariable="CF36000000000001557156">
        <inputExpression id="inputExpression_1" typeRef="string">
          <text>${CF3600000000000155716}</text>
        </inputExpression>
      </input>
      <input id="input2" label="CF3600000000000155715" ns0:inputVariable="CF3600000000000155715">
        <inputExpression id="inputExpression_2" typeRef="double">
          <text>${CF3600000000000155715}</text>
        </inputExpression>
      </input>
      <input id="InputClause_0mp0xqf" label="CF3600000000000155717" biodi_1:width="192">
        <inputExpression id="LiteralExpression_0arp3bu" typeRef="double">
          <text>${CF3600000000000155717}</text>
        </inputExpression>
      </input>
      <input id="InputClause_0umdp79" label="CF3600000000000155718">
        <inputExpression id="LiteralExpression_0bg33xq" typeRef="string">
          <text>${CF3600000000000155718}</text>
        </inputExpression>
      </input>
      <output id="output_1" label="decisionResult" name="decisionResult" typeRef="boolean" />
      <rule id="rule_079dd7d0-86e5-4307-b95c-64721a2e031e">
        <description></description>
        <inputEntry id="inputEntry_3c627965-aabd-465a-9ea5-87b5ebc5431c" expressionLanguage="juel">
          <text>CF3600000000000155716.equals("1") || CF3600000000000155716.equals("2") || CF3600000000000155716.equals("3")</text>
        </inputEntry>
        <inputEntry id="inputEntry_96a1cfeb-10e6-4a5c-9b74-9c398fde255b" expressionLanguage="juel">
          <text>CF3600000000000155715 &gt;= 500</text>
        </inputEntry>
        <inputEntry id="UnaryTests_0k62cyt">
          <text></text>
        </inputEntry>
        <inputEntry id="UnaryTests_1w7mgf9">
          <text></text>
        </inputEntry>
        <outputEntry id="outputEntry_f135317b-97e7-4a69-8179-7e8f3aa3a41e">
          <text>true</text>
        </outputEntry>
      </rule>
      <rule id="rule_286ab275-50a4-46f6-9a67-5cdeecb902dd">
        <description></description>
        <inputEntry id="inputEntry_eb2a6c8b-ad81-4fdd-bfc8-7ab61a72801a" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <inputEntry id="inputEntry_15693708-f572-466f-8e9f-afcb8eb82d3f" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <inputEntry id="UnaryTests_1kvr1ys">
          <text>CF3600000000000155717 &gt;= 50</text>
        </inputEntry>
        <inputEntry id="UnaryTests_15s3ck8">
          <text>CF3600000000000155718.equals("1") || CF3600000000000155718.equals("2")</text>
        </inputEntry>
        <outputEntry id="outputEntry_eb7388d8-b053-4655-a2b7-ec2fe9d8a225">
          <text>true</text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_03ofp8o">
        <inputEntry id="UnaryTests_0zp7ht6">
          <text></text>
        </inputEntry>
        <inputEntry id="UnaryTests_05dce1t">
          <text></text>
        </inputEntry>
        <inputEntry id="UnaryTests_0ak0mwp">
          <text></text>
        </inputEntry>
        <inputEntry id="UnaryTests_1x5oaau">
          <text></text>
        </inputEntry>
        <outputEntry id="LiteralExpression_18ue2qz">
          <text>false</text>
        </outputEntry>
      </rule>
    </decisionTable>
  </decision>
</definitions>