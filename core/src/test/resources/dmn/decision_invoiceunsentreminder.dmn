<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/DMN/20151101/dmn.xsd" xmlns:camunda="http://camunda.org/schema/1.0/dmn" id="Definitions_11zt9ct" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" exporter="Camunda Modeler" exporterVersion="3.3.5">
  <decision id="decision_invoiceunsentreminder" name="Decision Invoice unsent" camunda:historyTimeToLive="14">
    <decisionTable id="decisionTable_1" hitPolicy="FIRST">
      <input id="input_1" label="Customer" camunda:inputVariable="Customer">
        <inputExpression id="inputExpression_1" typeRef="string">
          <text>${CustomerRef_value}</text>
        </inputExpression>
      </input>
      <input id="InputClause_06c5k5m" label="Invoice Created" camunda:inputVariable="InvoiceCreated">
        <inputExpression id="LiteralExpression_120g09m" typeRef="days">
          <text>${DueDate}</text>
        </inputExpression>
      </input>
      <output id="output_1" label="sendReminder" name="sendReminder" typeRef="boolean" />
      <rule id="DecisionRule_0h84xh8">
        <description>Customer, Invoice Created:-1</description>
        <inputEntry id="UnaryTests_0hjmlp8">
          <text></text>
        </inputEntry>
        <inputEntry id="UnaryTests_1f9ta94">
          <text></text>
        </inputEntry>
        <outputEntry id="LiteralExpression_0ixhylo">
          <text></text>
        </outputEntry>
      </rule>
    </decisionTable>
  </decision>
</definitions>
