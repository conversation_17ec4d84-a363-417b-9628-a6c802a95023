<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/DMN/20151101/dmn.xsd" xmlns:biodi="http://bpmn.io/schema/dmn/biodi/1.0" id="Definitions_1qui2om" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" exporter="Camunda Modeler" exporterVersion="3.3.1">
  <decision id="invoiceApprovalDecision_invoiceApproval_companyId_uuid" name="invoiceSendDecision">
    <extensionElements>
      <biodi:bounds x="157" y="81" width="180" height="80" />
    </extensionElements>
    <decisionTable id="decisionTable_1" hitPolicy="FIRST">
      <input id="input_1" label="amountEvaluation">
        <inputExpression id="inputExpression_1" typeRef="double">
          <text>${Invoice.TotalAmt}</text>
        </inputExpression>
      </input>
      <input id="InputClause_13q46an" label="customers">
        <inputExpression id="LiteralExpression_153c9i7" typeRef="string">
          <text>${ Invoice.CustomerRef.value }</text>
        </inputExpression>
      </input>
      <input id="InputClause_10vl7tp" label="department">
        <inputExpression id="LiteralExpression_10svu8w" typeRef="double">
          <text>${ Invoice.DepartmentRef.value }</text>
        </inputExpression>
      </input>
      <output id="output_1" label="approvalRequired" name="sendAdminEmail" typeRef="boolean" />
      <rule id="DecisionRule_14gzk06">
        <inputEntry id="UnaryTests_11iesjo">
          <text>&gt; 500</text>
        </inputEntry>
        <inputEntry id="UnaryTests_1aglwpx" expressionLanguage="juel">
          <text>"1"</text>
        </inputEntry>
        <inputEntry id="UnaryTests_07f5osb" expressionLanguage="juel">
          <text>"2"</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_0x8ztxm">
          <text>true</text>
        </outputEntry>
      </rule>
    </decisionTable>
  </decision>
</definitions>
