<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions xmlns="http://www.omg.org/spec/DMN/20151101/dmn.xsd" xmlns:biodi="http://bpmn.io/schema/dmn/biodi/1.0" xmlns:ns0="http://camunda.org/schema/1.0/dmn" exporter="Camunda Modeler" exporterVersion="3.3.1" expressionLanguage="http://www.omg.org/spec/FEEL/20140401" id="Definitions_086839o" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" typeLanguage="http://www.omg.org/spec/FEEL/20140401">

    <decision id="decisionElement_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" name="mybillworkflow">

        <extensionElements>

            <biodi:bounds height="80" width="180" x="150" y="150"/>

        </extensionElements>

        <decisionTable hitPolicy="FIRST" id="decisionTable_1" preferredOrientation="Rule-as-Row">
            <input id="input1" label="TxnAmount" ns0:inputVariable="TxnAmount">
                <inputExpression id="inputExpression_1" typeRef="double">
                    <text>${TxnAmount}</text>
                </inputExpression>
            </input>
            <input id="input2" label="intuit_userid" ns0:inputVariable="intuit_userid">
                <inputExpression id="inputExpression_2" typeRef="string">
                    <text>${intuit_userid}</text>
                </inputExpression>
            </input>

            <output id="output_1" label="decisionResult" name="decisionResult" typeRef="boolean"/>
            <rule id="rule_7c81ea35-9ae8-4b29-9c0d-8ff049b7b88c">
                <description/>
                <inputEntry expressionLanguage="juel" id="inputEntry_d96a7777-654a-4bd2-940d-0e520ee24670">
                    <text>TxnAmount &gt;= 0</text>
                </inputEntry>
                <inputEntry expressionLanguage="juel" id="inputEntry_224ccd56-73de-46db-9192-9b3ffc1686a2">
                    <text>!intuit_userid.equals("9130356869118536")</text>
                </inputEntry>
                <outputEntry id="outputEntry_0d9f22b1-b5cb-4a59-a72c-4ebf2d33b5b7">
                    <text>true</text>
                </outputEntry>
            </rule>
            <rule id="rule_bc356172-5f2b-4c38-88b8-ac82e7eab17c">
                <description/>
                <inputEntry expressionLanguage="juel" id="inputEntry_3b434049-891c-4c99-9014-d69893ba79c7">
                    <text/>
                </inputEntry>
                <inputEntry expressionLanguage="juel" id="inputEntry_6779967b-3627-49ba-bd25-a7fe24ee5bbd">
                    <text/>
                </inputEntry>
                <outputEntry id="outputEntry_44af5e10-3752-46f0-a2b6-b381ea4e7023">
                    <text>false</text>
                </outputEntry>
            </rule>

        </decisionTable>

    </decision>

</definitions>
