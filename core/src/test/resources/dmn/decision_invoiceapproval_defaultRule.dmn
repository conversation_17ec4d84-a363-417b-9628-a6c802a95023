<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/DMN/20151101/dmn.xsd" xmlns:biodi="http://bpmn.io/schema/dmn/biodi/1.0" xmlns:camunda="http://camunda.org/schema/1.0/dmn" id="Definitions_1qui2om" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" exporter="Camunda Modeler" exporterVersion="3.4.1">
  <decision id="decision_invoiceapproval" name="Invoice approval" camunda:historyTimeToLive="14">
    <extensionElements>
      <biodi:bounds x="157" y="81" width="180" height="80" />
    </extensionElements>
    <decisionTable id="decisionTable_1" hitPolicy="FIRST">
      <input id="input_1" label="Amount" camunda:inputVariable="Amount">
        <inputExpression id="inputExpression_1" typeRef="double">
          <text>${TotalAmt}</text>
        </inputExpression>
      </input>
      <input id="InputClause_13q46an" label="Customer" camunda:inputVariable="Customer">
        <inputExpression id="LiteralExpression_153c9i7" typeRef="string">
          <text>${CustomerRef_value}</text>
        </inputExpression>
      </input>
      <input id="InputClause_10vl7tp" label="Department" camunda:inputVariable="Department">
        <inputExpression id="LiteralExpression_10svu8w" typeRef="string">
          <text>${DepartmentRef_value}</text>
        </inputExpression>
      </input>
      <output id="output_1" label="approvalRequired" name="approvalRequired" typeRef="boolean" />
      <rule id="rule_f274eeea-751a-430f-ac7c-1663a1a0ee92">
        <description>Amount:100,Department:2</description>
        <inputEntry id="inputEntry_318e1190-30e8-4130-8533-20bff3c59397" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <inputEntry id="inputEntry_e54c7c4a-4269-49fe-ac34-d80d22d0223a" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <inputEntry id="inputEntry_9c6f0607-c7b3-4833-abdb-7af4262dcd76" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <outputEntry id="outputEntry_fe5698c3-9985-4f11-a547-71bb05fb07a2">
          <text></text>
        </outputEntry>
      </rule>
    </decisionTable>
  </decision>
</definitions>
