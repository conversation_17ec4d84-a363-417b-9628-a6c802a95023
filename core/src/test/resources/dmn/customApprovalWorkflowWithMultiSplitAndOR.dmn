<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="https://www.omg.org/spec/DMN/20191111/MODEL/" xmlns:ns0="http://camunda.org/schema/1.0/dmn" xmlns:biodi="http://bpmn.io/schema/dmn/biodi/1.0" id="Definitions_086839o" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" expressionLanguage="https://www.omg.org/spec/DMN/20191111/FEEL/" typeLanguage="https://www.omg.org/spec/DMN/20191111/FEEL/" exporter="Camunda Modeler" exporterVersion="4.10.0">
  <decision id="decisionElement" name="lastCaseLast">
    <extensionElements>
      <ns0:bounds height="80" width="180" x="150" y="150" />
    </extensionElements>
    <decisionTable id="decisionTable_1" hitPolicy="FIRST" preferredOrientation="Rule-as-Row">
      <input id="input0" label="Index" ns0:inputVariable="Index">
        <inputExpression id="inputExpression_0" typeRef="integer">
          <text>${Index}</text>
        </inputExpression>
      </input>
      <input id="input1" label="TxnAmount" ns0:inputVariable="TxnAmount">
        <inputExpression id="inputExpression_1" typeRef="double">
          <text>${TxnAmount}</text>
        </inputExpression>
      </input>
      <output id="output_1" label="decisionResult" name="decisionResult" typeRef="String" />
      <rule id="rule_1d6d7d78-27b8-47f1-b76a-546112cad47d">
        <description></description>
        <inputEntry id="inputEntry_1f34939d-8207-42b5-abf6-45056cab706c" expressionLanguage="juel">
          <text>Index == 0</text>
        </inputEntry>
        <inputEntry id="inputEntry_bfd386f2-ba76-4a80-ab74-56aad905fcf8" expressionLanguage="juel">
          <text>TxnAmount &lt; 50.0</text>
        </inputEntry>
        <outputEntry id="outputEntry_dd26ca32-8f7f-4bfc-ba97-fc0b7b1e0d25">
          <text>'sendForApproval-2'</text>
        </outputEntry>
      </rule>
      <rule id="rule_2cb8cd22-ddec-45ce-9b7d-fb21f71880c5">
        <description></description>
        <inputEntry id="inputEntry_ec97e912-8ba0-4396-ad76-628242d1a2c5" expressionLanguage="juel">
          <text>Index == 0</text>
        </inputEntry>
        <inputEntry id="inputEntry_b3d938b3-75c0-4582-9c05-7664cc2b3ee4" expressionLanguage="juel">
          <text>TxnAmount &gt;= 50.0 &amp;&amp; TxnAmount &lt;= 75.0</text>
        </inputEntry>
        <outputEntry id="outputEntry_a9f7994e-a585-4d7b-ab6e-02bc6ade1b85">
          <text>'sendForApproval-3'</text>
        </outputEntry>
      </rule>
      <rule id="rule_12fe6d8d-4f6b-4180-b491-d20a834dfe40">
        <description></description>
        <inputEntry id="inputEntry_c1d5c9c1-445d-440a-951c-5de08ba1503e" expressionLanguage="juel">
          <text>Index == 0</text>
        </inputEntry>
        <inputEntry id="inputEntry_0748d872-6cea-47c7-bd88-c1c5a152c54d" expressionLanguage="juel">
          <text>TxnAmount &gt;= 75.0 &amp;&amp; TxnAmount &lt;= 100.0</text>
        </inputEntry>
        <outputEntry id="outputEntry_24ed7a0c-a2b5-48e9-a429-dfbaa76e2e53">
          <text>'sendForApproval-4'</text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_0ana2im">
        <inputEntry id="UnaryTests_1x7yttu">
          <text>Index == 0</text>
        </inputEntry>
        <inputEntry id="UnaryTests_1iyex1t">
          <text>TxnAmount &gt;= 1000</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_15ob2vq">
          <text>'sendForApproval-4'</text>
        </outputEntry>
      </rule>
      <rule id="rule_4ed72dea-4dc4-4270-a460-d27524127e53">
        <description></description>
        <inputEntry id="inputEntry_605616fb-4f2f-46d9-9c1d-67c93b7605e1" expressionLanguage="juel">
          <text>Index == 0</text>
        </inputEntry>
        <inputEntry id="inputEntry_faffb0fe-7d57-48bd-9a5c-208441e64cb9" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <outputEntry id="outputEntry_c87a4aa9-6ca3-44da-933e-9232c662a505">
          <text>false</text>
        </outputEntry>
      </rule>
    </decisionTable>
  </decision>
</definitions>