<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions xmlns="http://www.omg.org/spec/DMN/20151101/dmn.xsd" xmlns:biodi="http://bpmn.io/schema/dmn/biodi/1.0" xmlns:ns0="http://camunda.org/schema/1.0/dmn" exporter="Camunda Modeler" exporterVersion="3.3.1" expressionLanguage="http://www.omg.org/spec/FEEL/20140401" id="Definitions_086839o" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" typeLanguage="http://www.omg.org/spec/FEEL/20140401">
    
  <decision id="decisionElement_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" name="Testing Recurrence bpmn2">
        
    <extensionElements>
            
      <biodi:bounds height="80" width="180" x="150" y="150"/>
          
    </extensionElements>
        
    <decisionTable hitPolicy="FIRST" id="decisionTable_1" preferredOrientation="Rule-as-Row">
      <input id="input1" label="Customer" ns0:inputVariable="Customer">
        <inputExpression id="inputExpression_1" typeRef="string">
          <text>${Customer}</text>
        </inputExpression>
      </input>
            
      <output id="output_1" label="decisionResult" name="decisionResult" typeRef="boolean"/>
      <rule id="rule_e4cbc793-e8b0-48e7-888b-62d87608d0b7">
        <description/>
        <inputEntry expressionLanguage="juel" id="inputEntry_81d9b6c9-c679-447a-a432-4a700e9452bb">
          <text>Customer.equals("1")</text>
        </inputEntry>
        <outputEntry id="outputEntry_43ba7958-9cd1-483f-bc28-37aa01a54839">
          <text>true</text>
        </outputEntry>
      </rule>
      <rule id="rule_b2300856-d7df-4489-b931-9e19ec824435">
        <description/>
        <inputEntry expressionLanguage="juel" id="inputEntry_9565a8de-7d9a-4851-baa6-7f6eb6025fb9">
          <text/>
        </inputEntry>
        <outputEntry id="outputEntry_9b1f2939-097d-4a34-b6a7-0e0da905717b">
          <text>false</text>
        </outputEntry>
      </rule>
          
    </decisionTable>
      
  </decision>
  
</definitions>