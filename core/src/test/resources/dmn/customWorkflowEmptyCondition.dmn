<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="https://www.omg.org/spec/DMN/20191111/MODEL/" xmlns:ns0="http://camunda.org/schema/1.0/dmn" xmlns:biodi="http://bpmn.io/schema/dmn/biodi/1.0" id="Definitions_086839o" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" expressionLanguage="https://www.omg.org/spec/DMN/20191111/FEEL/" typeLanguage="https://www.omg.org/spec/DMN/20191111/FEEL/" exporter="Camunda Modeler" exporterVersion="5.1.0">
  <decision id="decisionElement" name="Invoice Multi Condition Approval - 3">
    <extensionElements />
    <decisionTable id="decisionTable_1" hitPolicy="FIRST" preferredOrientation="Rule-as-Row">
      <input id="input0" label="Index" ns0:inputVariable="Index">
        <inputExpression id="inputExpression_0" typeRef="integer">
          <text>${Index}</text>
        </inputExpression>
      </input>
      <output id="output_1" label="decisionResult" name="decisionResult" typeRef="String" />
      <rule id="rule_b29dfef1-74bc-43de-9a72-6b5b24c4a3b7">
        <description></description>
        <inputEntry id="inputEntry_822e0285-2022-48b2-8324-d9f1c3c02398" expressionLanguage="juel">
          <text>Index == 0</text>
        </inputEntry>
        <outputEntry id="outputEntry_d1c81a40-0bf5-4226-83f3-260f4e3029b0">
          <text>'action-3'</text>
        </outputEntry>
      </rule>
    </decisionTable>
  </decision>
</definitions>
