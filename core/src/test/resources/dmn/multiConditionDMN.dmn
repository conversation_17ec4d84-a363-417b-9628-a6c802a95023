<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="https://www.omg.org/spec/DMN/20191111/MODEL/" xmlns:dc="http://www.omg.org/spec/DMN/20180521/DC/" xmlns:dmndi="https://www.omg.org/spec/DMN/20191111/DMNDI/" xmlns:ns0="http://camunda.org/schema/1.0/dmn" id="Definitions_086839o" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" expressionLanguage="https://www.omg.org/spec/DMN/20191111/FEEL/" typeLanguage="https://www.omg.org/spec/DMN/20191111/FEEL/" exporter="Camunda Modeler" exporterVersion="5.1.0">
  <decision id="decisionElement" name="Multi Call Condition Def">
    <decisionTable id="decisionTable_1" hitPolicy="FIRST" preferredOrientation="Rule-as-Row">
      <input id="input0" label="Index" ns0:inputVariable="Index">
        <inputExpression id="inputExpression_0" typeRef="integer">
          <text>${Index}</text>
        </inputExpression>
      </input>
      <input id="input1" label="TxnAmount" ns0:inputVariable="TxnAmount">
        <inputExpression id="inputExpression_1" typeRef="double">
          <text>${TxnAmount}</text>
        </inputExpression>
      </input>
      <input id="input2" label="Location" ns0:inputVariable="Location">
        <inputExpression id="inputExpression_2" typeRef="list">
          <text>${Location}</text>
        </inputExpression>
      </input>
      <input id="input3" label="TxnBalanceAmount" ns0:inputVariable="TxnBalanceAmount">
        <inputExpression id="inputExpression_3" typeRef="double">
          <text>${TxnBalanceAmount}</text>
        </inputExpression>
      </input>
      <output id="output_1" label="decisionResult" name="decisionResult" typeRef="string" />
      <rule id="rule_4886d8a3-a6ca-4df4-b2da-582c816b470b">
        <description></description>
        <inputEntry id="inputEntry_701c4502-ab07-4c50-8b79-d622316fd996" expressionLanguage="juel">
          <text>Index == 0</text>
        </inputEntry>
        <inputEntry id="inputEntry_dca731c6-769d-4a14-aed4-2b8cb475bd98" expressionLanguage="juel">
          <text>TxnAmount &gt;= 100</text>
        </inputEntry>
        <inputEntry id="inputEntry_dc0931df-8d19-47f0-9f9e-80351bf11a6f" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <inputEntry id="inputEntry_d2c90fac-d256-47ba-98c0-57fb3848cb63" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <outputEntry id="outputEntry_717652e0-9baf-4d57-8567-50c9703c6924">
          <text>1</text>
        </outputEntry>
      </rule>
      <rule id="rule_6c4ec18e-eec8-4a1f-a86d-444016959e5d">
        <description></description>
        <inputEntry id="inputEntry_12be7916-9e6f-4afe-8d77-638256651201" expressionLanguage="juel">
          <text>Index == 0</text>
        </inputEntry>
        <inputEntry id="inputEntry_58e0a58c-8500-4762-9565-597c8c489b0e" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <inputEntry id="inputEntry_2e761db8-a4b5-496e-8d17-e0f7df2f3a3a" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <inputEntry id="inputEntry_1772f73b-00de-4fd4-8a11-44fc3ec31330" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <outputEntry id="outputEntry_b6d6a0da-309a-411e-a346-896ce41d0af2">
          <text>'action-2'</text>
        </outputEntry>
      </rule>
      <rule id="rule_0a017912-7945-4244-9a8a-7e1ff846ad9c">
        <description></description>
        <inputEntry id="inputEntry_d70b49a1-bc54-45b9-96ac-ee51a9860e6b" expressionLanguage="juel">
          <text>Index == 1</text>
        </inputEntry>
        <inputEntry id="inputEntry_6e97900a-9220-4566-8bd2-61a8d9a8a3b4" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <inputEntry id="inputEntry_5ceb2b20-be21-4899-8976-cd0f3221a54c" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <inputEntry id="inputEntry_be37357d-47ce-4d19-be63-62db81440608" expressionLanguage="juel">
          <text>TxnBalanceAmount &gt;= 1000</text>
        </inputEntry>
        <outputEntry id="outputEntry_e4b89b03-9b36-4086-918b-0a01e549f028">
          <text>'action-3'</text>
        </outputEntry>
      </rule>
      <rule id="rule_381e44e9-ab9d-47d1-9166-74a9f903af2e">
        <description></description>
        <inputEntry id="inputEntry_52eff526-87c3-41fe-97a4-0cc6da4336f7" expressionLanguage="juel">
          <text>Index == 1</text>
        </inputEntry>
        <inputEntry id="inputEntry_c01af142-6f91-4e7f-a36e-7cb00f646b9d" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <inputEntry id="inputEntry_8563d395-5d2e-4881-a3db-cb0d9884c190" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <inputEntry id="inputEntry_1afa79b1-ae2c-4958-8128-37a9671cf32a" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <outputEntry id="outputEntry_741e5b45-2ce3-4b74-915c-bb5f3eb43f0b">
          <text>4</text>
        </outputEntry>
      </rule>
      <rule id="rule_0177004f-a7f6-4345-8a2c-c5aa0aa71d0c">
        <description></description>
        <inputEntry id="inputEntry_e5845909-c399-4c54-8ba0-78a2d2f4de86" expressionLanguage="juel">
          <text>Index == 4</text>
        </inputEntry>
        <inputEntry id="inputEntry_85adb7ee-87fb-4b2b-be91-0c1f47127b72" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <inputEntry id="inputEntry_594eeaa5-e9ab-4155-87a2-b7b65dbfba14" expressionLanguage="juel">
          <text>Location.equals("1")</text>
        </inputEntry>
        <inputEntry id="inputEntry_0059c65b-938a-488b-ba70-2a7250612409" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <outputEntry id="outputEntry_b0813e35-ec57-4172-b558-31a7cf2fe7ea">
          <text>'action-5'</text>
        </outputEntry>
      </rule>
      <rule id="rule_3c59a38a-c634-404f-ae3d-f80854b6d3ac">
        <description></description>
        <inputEntry id="inputEntry_2807bfc1-3df7-4934-bc54-2d9615abac35" expressionLanguage="juel">
          <text>Index == 4</text>
        </inputEntry>
        <inputEntry id="inputEntry_5bba2ce1-4b17-42b1-9f21-8185f2557180" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <inputEntry id="inputEntry_77132486-cbae-421d-b5b9-b65b628ad898" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <inputEntry id="inputEntry_1dcd1fa9-70a3-4cf1-b756-0a478733310e" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <outputEntry id="outputEntry_144855e9-3e64-4d77-9744-3ef3b6791960">
          <text>'action-4'</text>
        </outputEntry>
      </rule>
    </decisionTable>
  </decision>
  <dmndi:DMNDI>
    <dmndi:DMNDiagram id="DMNDiagram_0wz3ah0">
      <dmndi:DMNShape id="DMNShape_0h7yp5z" dmnElementRef="decisionElement" isCollapsed="false">
        <dc:Bounds height="80" width="180" x="150" y="150" />
      </dmndi:DMNShape>
    </dmndi:DMNDiagram>
  </dmndi:DMNDI>
</definitions>
