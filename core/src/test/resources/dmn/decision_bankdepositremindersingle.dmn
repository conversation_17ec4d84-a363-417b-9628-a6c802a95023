<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="https://www.omg.org/spec/DMN/********/MODEL/" xmlns:dmndi="https://www.omg.org/spec/DMN/********/DMNDI/" xmlns:dc="http://www.omg.org/spec/DMN/********/DC/" xmlns:camunda="http://camunda.org/schema/1.0/dmn" id="Definitions_1voxg0d" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" exporter="Camunda Modeler" exporterVersion="3.3.5">
  <decision id="decision_bankdepositremindersingle" name="Decision BankDeposit reminder single" camunda:historyTimeToLive="14">
    <decisionTable id="decisionTable_1" hitPolicy="FIRST">
      <input id="input_1" label="Undeposited Funds" camunda:inputVariable="UndepositedFunds">
        <inputExpression id="inputExpression_1" typeRef="double">
          <text>${UndepositedFunds}</text>
        </inputExpression>
      </input>
      <output id="output_1" label="sendReminder" name="sendReminder" typeRef="boolean" />
      <rule id="DecisionRule_00iuwbl">
        <description></description>
        <inputEntry id="UnaryTests_1u39ecz">
          <text></text>
        </inputEntry>
        <outputEntry id="LiteralExpression_1t1yddc">
          <text></text>
        </outputEntry>
      </rule>
    </decisionTable>
  </decision>
  <dmndi:DMNDI>
    <dmndi:DMNDiagram id="DMNDiagram_1rbc3wi">
      <dmndi:DMNShape id="DMNShape_0hs4ari" dmnElementRef="decision_bankdepositremindersingle">
        <dc:Bounds height="80" width="180" x="150" y="150" />
      </dmndi:DMNShape>
    </dmndi:DMNDiagram>
  </dmndi:DMNDI>
</definitions>