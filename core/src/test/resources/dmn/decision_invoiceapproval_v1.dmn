<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/DMN/20151101/dmn.xsd" xmlns:biodi="http://bpmn.io/schema/dmn/biodi/1.0" xmlns:camunda="http://camunda.org/schema/1.0/dmn" id="Definitions_1qui2om" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" exporter="Camunda Modeler" exporterVersion="3.3.1">
  <decision id="decision_invoiceapproval" name="decision_invoiceapproval_dmn">
    <extensionElements>
      <biodi:bounds x="157" y="81" width="180" height="80" />
    </extensionElements>
    <decisionTable id="decisionTable_1" hitPolicy="FIRST">
      <input id="input_1" label="Amount" camunda:inputVariable="Amount">
        <inputExpression id="inputExpression_1" typeRef="double">
          <text>${TotalAmt}</text>
        </inputExpression>
      </input>
      <input id="InputClause_13q46an" label="Customer" camunda:inputVariable="Customer">
        <inputExpression id="LiteralExpression_153c9i7" typeRef="string">
          <text>${CustomerRef_value}</text>
        </inputExpression>
      </input>
      <input id="InputClause_10vl7tp" label="Department" camunda:inputVariable="Department">
        <inputExpression id="LiteralExpression_10svu8w" typeRef="string">
          <text>${DepartmentRef_value}</text>
        </inputExpression>
      </input>
      <output id="output_1" label="approvalRequired" name="approvalRequired" typeRef="boolean" />
      <rule id="DecisionRule_1mj2h3y">
        <description>Amount</description>
        <inputEntry id="UnaryTests_04po3yu">
          <text>Amount &gt; 500 &amp;&amp; Amount &lt; 1000</text>
        </inputEntry>
        <inputEntry id="UnaryTests_1id1996">
          <text>!Customer.equals("1") &amp;&amp; !Customer.equals("2") &amp;&amp; !Customer.equals("3")</text>
        </inputEntry>
        <inputEntry id="UnaryTests_08x2x88">
          <text>!Department.equlas("1")</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_037m1jx">
          <text></text>
        </outputEntry>
      </rule>
    </decisionTable>
  </decision>
</definitions>
