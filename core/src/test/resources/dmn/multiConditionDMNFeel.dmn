<?xml version="1.0" encoding="UTF-8"?><definitions xmlns="https://www.omg.org/spec/DMN/20191111/MODEL/" xmlns:biodi="http://bpmn.io/schema/dmn/biodi/1.0" xmlns:ns0="http://camunda.org/schema/1.0/dmn" exporter="Camunda Modeler" exporterVersion="3.3.1" expressionLanguage="https://www.omg.org/spec/DMN/20191111/FEEL/" id="Definitions_086839o" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" typeLanguage="https://www.omg.org/spec/DMN/20191111/FEEL/">
  <decision id="decisionElement" name="Invoice Approval-7">
    <extensionElements>
      <biodi:bounds height="80" width="180" x="150" y="150"/>
    </extensionElements>
    <decisionTable hitPolicy="FIRST" id="decisionTable_1" preferredOrientation="Rule-as-Row">
      <input id="input0" label="Index" ns0:inputVariable="Index">
        <inputExpression id="inputExpression_0" typeRef="integer">
          <text>Index</text>
        </inputExpression>
      </input>
      <input id="input1" label="TxnAmount" ns0:inputVariable="TxnAmount">
        <inputExpression id="inputExpression_1" typeRef="double">
          <text>TxnAmount</text>
        </inputExpression>
      </input>
      <input id="input2" label="Location" ns0:inputVariable="Location">
        <inputExpression id="inputExpression_2" typeRef="list">
          <text>Location</text>
        </inputExpression>
      </input>
      <output id="output_1" label="decisionResult" name="decisionResult" typeRef="String"/>
      <rule id="rule_2756b105-cac0-4819-a856-d07dcef2eeb1">
        <description/>
        <inputEntry expressionLanguage="feel" id="inputEntry_32fb0700-ad5c-48e2-8749-4a74dc4d0b0c">
          <text>0</text>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_cc774caf-b4bc-4597-8b23-3c25056e82a5">
          <text>[0..1000]</text>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_2ff3bf88-e038-4c93-99ed-247a1b5dcda0">
          <text/>
        </inputEntry>
        <outputEntry id="outputEntry_69bccdb6-0f3c-443e-aac7-fcb9ff745f2a">
          <text>"sendForApproval-2"</text>
        </outputEntry>
      </rule>
      <rule id="rule_c5c5968c-3303-4c65-9143-420cf1cec6e9">
        <description/>
        <inputEntry expressionLanguage="feel" id="inputEntry_7e9fa6fe-0de6-4b83-9df7-ef4487ea57ac">
          <text>0</text>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_b9beef33-3dcf-49ff-9096-97f6fee80932">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_4cf87637-5f7c-483a-8ab9-66b824bf6fbe">
          <text/>
        </inputEntry>
        <outputEntry id="outputEntry_28e73deb-3256-4fff-9d02-22ac069a3b87">
          <text>"2"</text>
        </outputEntry>
      </rule>
      <rule id="rule_b7eccd05-8d6a-425a-96b7-05ab6d3707cd">
        <description/>
        <inputEntry expressionLanguage="feel" id="inputEntry_38562637-6bf3-4a05-a4eb-4624651f5bd7">
          <text>2</text>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_cd2d4f75-fcb9-4e39-8754-9758a076d495">
          <text>[1001..5000]</text>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_6614c6db-5f21-4347-944d-512abc9377b8">
          <text>containsAnyElement(["1"], Location)</text>
        </inputEntry>
        <outputEntry id="outputEntry_8ea14f1f-7fb1-412b-9ed0-477d32da8e9f">
          <text>"sendForApproval-3"</text>
        </outputEntry>
      </rule>
      <rule id="rule_40417285-77b6-4e9f-a683-f29d956995e8">
        <description/>
        <inputEntry expressionLanguage="feel" id="inputEntry_1a68fe21-89e1-4968-b670-8dfd8738fb77">
          <text>2</text>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_89c38c82-3e5b-45cd-a598-5809c6d86eab">
          <text/>
        </inputEntry>
        <inputEntry expressionLanguage="feel" id="inputEntry_3a8f2aa6-c2d8-4af9-b2f7-cc9997132bd0">
          <text/>
        </inputEntry>
        <outputEntry id="outputEntry_14938e35-4e39-46cf-8794-620abec50705">
          <text>"false"</text>
        </outputEntry>
      </rule>
    </decisionTable>
  </decision>
</definitions>
