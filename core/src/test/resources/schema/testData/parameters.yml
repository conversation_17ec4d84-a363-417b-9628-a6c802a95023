custom-config-v2-parameters:
  - id: entityType
    name: entityType
    handlerFieldName: RecordType
    fieldType: string
    requiredByHandler: true
    valueType: PROCESS_VARIABLE
  - id: txnId
    name: Id
    handlerFieldName: TxnId
    fieldType: string
    valueType: PROCESS_VARIABLE
    requiredByHandler: true
  - id: LastUpdatedDate
    name: LastUpdatedDate
    handlerFieldName: Last Update Date
    fieldType: string
    valueType: PROCESS_VARIABLE
    requiredByHandler: true
  - id: syncToken
    name: SyncToken
    handlerFieldName: SyncToken
    fieldType: string
    requiredByHandler: true
    valueType: PROCESS_VARIABLE
  - id: realmId
    name: intuit_realmid
    handlerFieldName: RealmId
    fieldType: string
    valueType: PROCESS_VARIABLE
    requiredByHandler: true
  - id: sendTo
    name: SendTo
    handlerFieldName: To
    configurable: true
    requiredByHandler: true
    requiredByUI: true
    fieldType: string
  - id: notificationData
    name: notificationData
    configurable: false
    requiredByHandler: true
    fieldType: string
  - id: notificationDataType
    name: notificationDataType
    configurable: false
    requiredByHandler: true
    fieldType: string
  - id: notificationName
    name: notificationName
    configurable: false
    requiredByHandler: true
    fieldType: string
  - id: serviceName
    name: serviceName
    configurable: false
    requiredByHandler: true
    fieldType: string
  - id: cc
    name: CC
    fieldType: string
    configurable: true
    requiredByHandler: true
    requiredByUI: true
  - id: bcc
    name: BCC
    fieldType: string
    configurable: true
    requiredByHandler: true
    requiredByUI: true
  - id: message
    name: Message
    fieldType: string
    configurable: true
    requiredByHandler: true
    helpVariablesRequired: true
    requiredByUI: true
  - id: subject
    name: Subject
    fieldType: string
    configurable: true
    requiredByHandler: true
    helpVariablesRequired: true
    requiredByUI: true
  - id: isEmail
    name: IsEmail
    fieldType: boolean
    requiredByHandler: true
  - id: consolidateNotifications
    name: consolidateNotifications
    fieldType: boolean
    configurable: true
    requiredByUI: true
    requiredByHandler: true
    fieldValues:
      - "false"
  - id: notificationType
    name: NotificationType
    fieldType: string
    configurable: false
    requiredByUI: false
    requiredByHandler: true
  - id: sendAttachment
    name: SendAttachment
    fieldType: boolean
    requiredByHandler: true
    fieldValues:
      - "false"
  - id: notificationAction
    name: NotificationAction
    fieldType: string
    requiredByHandler: true
  - id: txnPaymentStatus
    name: TxnPaymentStatus
    handlerFieldName: TxnPaymentStatus
    fieldType: string
    requiredByHandler: true
    valueType: PROCESS_VARIABLE
  - id: txnSendStatus
    name: TxnSendStatus
    handlerFieldName: TxnSendStatus
    fieldType: string
    requiredByHandler: true
    valueType: PROCESS_VARIABLE
  - id: txnStatus
    name: TxnStatus
    handlerFieldName: TxnStatus
    fieldType: string
    requiredByHandler: true
    valueType: PROCESS_VARIABLE
  - id: txnDueDays
    name: TxnDueDays
    handlerFieldName: TxnDueDays
    fieldType: integer
    requiredByHandler: true
    valueType: PROCESS_VARIABLE
  - id: txnCreateDays
    name: TxnCreateDays
    handlerFieldName: TxnCreateDays
    fieldType: integer
    requiredByHandler: true
    valueType: PROCESS_VARIABLE
  - id: createdBy
    name: CreatedBy
    handlerFieldName: CreatedBy
    requiredByHandler: true
    fieldType: string
    configurable: true
    valueType: PROCESS_VARIABLE
  - id: lastTriggerDate
    name: lastTriggerDate
    handlerFieldName: startDate
    requiredByHandler: true
    fieldType: string
    configurable: true
    valueType: PROCESS_VARIABLE
  - id: statementType
    name: StatementType
    requiredByHandler: true
    fieldType: string
    configurable: true
    requiredByUI: false
    valueType: PROCESS_VARIABLE
  - id: reportId
    name: ReportId
    requiredByHandler: true
    fieldType: string
    configurable: true
    requiredByUI: false
    valueType: PROCESS_VARIABLE
  - id: customer
    name: Customer
    requiredByHandler: true
    fieldType: string
    configurable: true
    valueType: PROCESS_VARIABLE
  - id: memo
    name: Memo
    handlerFieldName: Memo
    configurable: true
    requiredByHandler: true
    requiredByUI: true
    fieldType: string
    helpVariablesRequired: true
  - id: memoAction
    name: MemoAction
    handlerFieldName: MemoAction
    configurable: true
    requiredByHandler: true
    requiredByUI: true
    fieldType: string
    possibleFieldValues:
      - PREPEND
      - APPEND
      - OVERWRITE
  - id: fieldsToUpdate
    name: FieldsToUpdate
    handlerFieldName: FieldsToUpdate
    configurable: true
    requiredByHandler: true
    requiredByUI: true
    fieldType: string
    possibleFieldValues:
      - Memo

