custom-config-v2-handlers:
  - id: wasSendNotification
    handlerDetail:
      taskHandler: appconnect
      handlerId: intuit-workflows/was-send-notification
      actionName: executeWorkflowAction
    duzzitRestHandlerDetail:
      taskHandler: appconnect
      handlerId: /intuit-workflows/api/was-send-notification.json
      actionName: executeDuzzitRestAction
  - id: wasCreateTask
    handlerDetail:
      taskHandler: appconnect
      handlerId: intuit-workflows/taskmanager-create-task
      actionName: executeWorkflowAction
      responseFields:
        - projectId
        - closeTaskRule
        - assigneeId
    duzzitRestHandlerDetail:
      taskHandler: appconnect
      handlerId: /intuit-workflows/api/taskmanager-create-task.json
      actionName: executeDuzzitRestAction
      responseFields:
        - projectId
        - closeTaskRule
        - assigneeId
  # This duzzit is used to add constraint and create task for the assignee
  - id: wasCreateTaskAndAddConstraint
    handlerDetail:
      taskHandler: appconnect
      handlerId: intuit-workflows/was-create-task-and-add-constraint
      actionName: executeWorkflowAction
      responseFields:
        - projectId
        - closeTaskRule
        - assigneeId
    duzzitRestHandlerDetail:
      taskHandler: appconnect
      handlerId: /intuit-workflows/api/was-create-task-and-add-constraint.json
      actionName: executeDuzzitRestAction
      responseFields:
        - projectId
        - closeTaskRule
        - assigneeId
  - id: wasSendStatement
    handlerDetail:
      taskHandler: appconnect
      handlerId: intuit-workflows/was-send-statement
      actionName: executeWorkflowAction
      responseFields:
        - lastTriggerDate
  - id: paymentRemittanceHandler
    handlerDetail:
      taskHandler: appconnect
      handlerId: intuit-workflows/entity-created-custom-workflow
      actionName: executeWorkflowAction
  - id: listEntityHandler
    handlerDetail:
      taskHandler: appconnect
      handlerId: intuit-workflows/process-list-entities
      actionName: executeWorkflowAction
  - id: projectProfitabilityHandler
    handlerDetail:
      taskHandler: appconnect
      handlerId: intuit-workflows/get-project-profitability
      actionName: executeWorkflowAction
  - id: notificationTaskHandler
    handlerDetail:
      taskHandler: was
      actionName: workflowCustomTaskHandler
  - id: getAmountToTransferHandler
    handlerDetail:
      taskHandler: appconnect
      handlerId: intuit-workflows/get-transfer-amount
      actionName: executeWorkflowAction
      responseFields:
        - totalTaxDue
        - TransferAmount
  - id: getTransferExecutionParamsHandler
    handlerDetail:
      taskHandler: appconnect
      handlerId: intuit-workflows/get-transfer-execution-params
      actionName: executeWorkflowAction
      responseFields:
        - insufficientFunds
        - TransferSourceEntityName
        - TransferDestinationEntityName
  - id: executeEnvelopeTransferHandler
    handlerDetail:
      taskHandler: appconnect
      handlerId: intuit-workflows/submit-envelope-transfer
      actionName: executeWorkflowAction
      responseFields:
        - isTransferSuccess
  - id: emailCustomReport
    handlerDetail:
      taskHandler: appconnect
      handlerId: intuit-workflows/emailCustomReport
      actionName: executeWorkflowAction
  - id: entityActionsHandler
    handlerDetail:
      taskHandler: appconnect
      handlerId: intuit-workflows/custom-reminder-start-process
      actionName: executeWorkflowAction
  - id: sendTransactionHandler
    handlerDetail:
      taskHandler: appconnect
      handlerId: intuit-workflows/was-send-qbo-transaction
      actionName: executeWorkflowAction
  - id: updateTransactionHandler
    handlerDetail:
      taskHandler: appconnect
      handlerId: intuit-workflows/was-update-qbo-transaction
      actionName: executeWorkflowAction
