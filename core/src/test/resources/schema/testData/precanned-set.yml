# Description: This file contains the precanned templates for the custom workflow feature.
# The precanned templates contains basics of the workflow like name, description, labels, attributes, actions, etc.
# The parameters associated to these actions are defined in the precanned parameter override set.
custom-config-v2-precanned-set:
- id: invoiceoverduereminder
  name: ~invoiceoverduereminder.name
  entity: invoice
  actionGroup: reminder
  multiconditionStatus: false
  description: ~invoiceoverduereminder.description
  labels:
    - name: Invoice
      value: ~Invoice
    - name: Reminder
      value: ~Reminder
  attributes:
    - id: txnPaymentStatus
      defaultValue: UNPAID
      defaultOperator: CONTAINS
      configurable: false
    - id: txnDueDays
      defaultValue: 3
      defaultOperator: BF
  selectedAction:
    - sendCompanyEmail
    - sendExternalEmail
    - createTask
    - sendPushNotification
- id: invoiceapproval-multicondition
  name: ~invoiceapproval-multicondition.name
  entity: Invoice
  actionGroup: approval
  multiconditionStatus: true
  description: ~invoiceapproval-multicondition.description
  labels:
  - name: Invoice
    value: ~Invoice
  - name: Approval
    value: ~Approval
  selectedAction:
    - sendCompanyEmail
    - createTask

