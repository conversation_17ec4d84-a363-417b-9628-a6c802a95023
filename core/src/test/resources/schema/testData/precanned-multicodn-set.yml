custom-config-v2-precanned-multicond-set:
  - id: invoiceapproval-multicondition-1
    precannedId: invoiceapproval-multicondition
    sequenceId: 1
    modelType: condition
    decisionType: boolean
    decisions:
      - id: txnAmount
        defaultValue: 0,1000
        defaultOperator: BTW
    nexts:
      - label: "yes"
        stepId: 2
      - label: "no"
        stepId: 3
  - id: invoiceapproval-multicondition-2
    precannedId: invoiceapproval-multicondition
    sequenceId: 2
    modelType: action
    action: sendForApproval
  - id: invoiceapproval-multicondition-3
    precannedId: invoiceapproval-multicondition
    sequenceId: 3
    modelType: condition
    decisionType: boolean
    decisions:
      - id: txnAmount
        defaultValue: 1001,5000
        defaultOperator: BTW
    nexts:
      - label: "yes"
        stepId: 4
      - label: "no"
        stepId: 5
  - id: invoiceapproval-multicondition-4
    precannedId: invoiceapproval-multicondition
    sequenceId: 4
    modelType: action
    action: sendForApproval
  - id: invoiceapproval-multicondition-5
    precannedId: invoiceapproval-multicondition
    sequenceId: 5
    modelType: condition
    decisionType: boolean
    decisions:
      - id: txnAmount
        defaultValue: 5001,10000
        defaultOperator: BTW
    nexts:
      - label: "yes"
        stepId: 6
      - label: "no"
        stepId: 7
  - id: invoiceapproval-multicondition-6
    precannedId: invoiceapproval-multicondition
    sequenceId: 6
    modelType: action
    action: sendForApproval
  - id: invoiceapproval-multicondition-7
    precannedId: invoiceapproval-multicondition
    sequenceId: 7
    modelType: action
    action: sendForApproval


