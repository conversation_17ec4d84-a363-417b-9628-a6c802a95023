# Data model for custom workflows. This is uber configuration for different records their attributes and actions supported for customized workflow
templateConfig:
  records:
  - id: invoice
    source: QBO
    # Default attributes. Rule lines are created based on these attributes
    defaultAttributes:
    - txnAmount
    # Attributes supported for this record type
    attributes:
    # we could have used list of attribute ids but we are using attribute object so that any field can be overridden for a particular record type
    - id: txnAmount
    - id: customer
    - id: txnPaymentStatus
    - id: term
    - id: location
    - id: txnBalanceAmount
    - id: txnApprovalStatus
    - id: txnSendStatus
    - id: txnUpdateStatus
    - id: txnEmailAvailabilityStatus
    - id: createDays
    - id: txnDays
    - id: txnSendDays
    - id: txnApprovalDays
    - id: txnDueDays
    - id: userId
    - id: stringCustomField
    - id: doubleCustomField
    - id: listCustomField
    # Supported help variables for action parameters
    helpVariables:
    # Help variable DisplayName:ProcessVariableName:DataType
    - Company Name:CompanyName:string
    - Company Email:CompanyEmail:string
    - Customer Name:CustomerName:string
    - Customer Email:CustomerEmail:string
    - Invoice Number:DocNumber:string
    - Total Amount:TxnAmount:double #Defined as attribute as well
    - Balance:TxnBalanceAmount:double #Defined as attribute as well
    - Due Date:TxnDueDate:string #We will get formatted value
    - Invoice Date:TxnDate:string #Attribue defines as days
    actionGroups:
    - id: reminder
      trigger:
        type: Scheduled
        handler:
          id: customReminderStartProcess
          handlerDetail:
            taskHandler: appconnect1
      actionIdMapper:
        actionId: sendForReminder
        subActionIds:
          - sendExternalEmail
          - sendCompanyEmail
          - createTask
          - sendPushNotification
      actionIds:
        - sendExternalEmail
        - sendCompanyEmail
        - createTask
        - sendPushNotification
      actions:
      - id: sendForReminder
        parameters:
        - name: FilterCloseTaskConditions
          fieldValues:
            - txn_sent
            - txn_paid
          possibleFieldValues:
            - txn_paid
            - txn_sent
      - id: sendExternalEmail
        name: Send a customer email
        parameters:
        - name: SendTo
          fieldValues:
          - "[[Customer Email]]"
      - id: createTask
        parameters:
        - name: CloseTask
          possibleFieldValues:
          - txn_paid
          - txn_sent
          - close_manually
      - id: sendPushNotification
        parameters:
        - name: Subject
          fieldValues:
          - ~invoice.reminder.sendPushNotification.Subject #An Invoice needs your attention
        - name: NotificationAction
          fieldValues:
          - qb001://open/invoice/?id=[[TxnId]]&companyid=[[RealmId]]
      unsupportedAttributes:
      - txnSendStatus
    - id: approval
      precannedTemplateId: invoiceapproval-multicondition
      actionIdMapper:
        actionId: sendForApproval
        subActionIds:
          - createTask
          - sendCompanyEmail
          - sendWhatsNotify
      onDemandActionIds:
        - createTask
        - sendCompanyEmail
      actions:
      - id: createTask
        # for invoice approval, to make create task action mandatory, marking required as true
        required: true
        parameters:
        - name: CloseTask
          # UI renders close task based on close task parameter sent as part of payload,
          # so marking requiredByUI as false to not send close task param for invoice approval
          requiredByUI: false
          fieldValues:
          - txn_approval_changed
      - id: sendCompanyEmail
        helpVariables:
        - Created By:intuit_userid:string
        parameters:
        - name: Message
          helpVariables:
            - Created By:intuit_userid:string
          fieldValues:
          - ~invoice.approval.sendCompanyEmail.Message #"Hi,\n\n%Record% [[%Record% Number]] is pending approval. Please approve it at the earliest using this task manager link - https://app.qal.qbo.intuit.com/app/taskmanager.\n\nNote that %record%s that are not approved for more than 30 days will be auto rejected.\n\nThanks,\n[[CompanyName]]"
        - name: Subject
          helpVariables:
            - Last Modified By:intuit_userid:string
      - id: sendPushNotification
        parameters:
        - name: Subject
          fieldValues:
          - ~invoice.reminder.sendPushNotification.Subject #An Invoice needs your attention
        - name: NotificationAction
          fieldValues:
          - qb001://open/invoice/?id=[[TxnId]]&companyid=[[RealmId]]
      unsupportedAttributes:
      - createDays
      - txnDays
      - txnDueDays
      - txnSendStatus
      - txnEmailAvailabilityStatus
      - txnPaymentStatus
      defaultAttributes:
      - txnAmount
    - id: notification
      actionIds:
        - sendNotification
      trigger:
        parameters:
          - name: entityOperation
            possibleFieldValues:
              - Create
              - Update
            fieldValues:
              - Create
        handler:
          id: paymentRemittanceHandler
      actions:
        - id: sendNotification
          name: Send an email
          parameters:
            - name: SendTo
              helpVariables:
                - Customer Email:CustomerEmail:string
                - Company Email:CompanyEmail:string
              configurable: true
            - name: SendAttachment
            - name: Subject
            - name: Message
            - name: CC
            - name: BCC
      unsupportedAttributes:
        - createDays
        - txnDays
        - txnDueDays
        - txnSendDays
        - txnApprovalDays
        - docNumber
        - memo
        - shipDays
        - doubleCustomField
        - listCustomField
        - stringCustomField
        - txnPrintStatus
        - savedByUser
        - class
        - item

  - id: bill
    source: QBO
    defaultAttributes:
    - txnAmount
    attributes:
    - id: txnAmount
    - id: vendor
    - id: txnPaymentStatus
    - id: txnUpdateStatus
    - id: location
    - id: term
    - id: createDays
    - id: txnDays
    - id: txnDueDays
    - id: userId
    helpVariables:
    #Help variable DisplayName:ProcessVariableName:DataType
    - Company Name:CompanyName:string
    - Company Email:CompanyEmail:string
    - Vendor Name:VendorName:string
    - Bill Number:DocNumber:string
    - Total Amount:TxnAmount:double #Defined as attribute as well
    - Balance Due:TxnBalanceAmount:double #Defined as attribute as well
    - Due Date:TxnDueDate:string
    - Bill Date:TxnDate:string
    - Due Days:TxnDueDays:integer #Defined as attribute as well
    actionGroups:
    - id: reminder
      actionIds:
      - createTask
      - sendCompanyEmail
      actionIdMapper:
        actionId: null
      actions:
      - id: sendPushNotification
      - id: sendExternalEmail
        name: Send a vendor email
      - id: createTask
        parameters:
        - name: CloseTask
          possibleFieldValues:
          - txn_paid
          - close_manually
    - id: approval
      onDemandActionIds:
      - createTask
      - sendCompanyEmail
      actions:
        - id: createTask
          required: true
          handler:
            id: wasCreateTaskAndAddConstraint
          parameters:
          - name: CloseTask
            fieldValues:
            - txn_approval_changed
        - id: sendCompanyEmail
          helpVariables:
            - Created By:intuit_userid:string
      unsupportedAttributes:
      - term
      - createDays
    - id: notification
      actionIds:
        - sendNotification
      trigger:
        parameters:
          - name: entityOperation
            possibleFieldValues:
              - Create
              - Update
            fieldValues:
              - Create
        handler:
          id: paymentRemittanceHandler
          handlerDetail:
            taskHandler: appconnect
            handlerId: intuit-workflows/entity-created-custom-workflow
            actionName: executeWorkflowAction
      actions:
        - id: sendNotification
          name: Send an email
          parameters:
            - name: SendTo
              helpVariables:
                - Vendor Email:VendorEmail:string
                - Company Email:CompanyEmail:string
              configurable: true
            - name: SendAttachment
            - name: Subject
            - name: Message
      unsupportedAttributes:
        - createDays
        - txnDays
        - txnDueDays


  - id: billpayment
    source: QBO
    defaultAttributes:
      - txnAmount
    attributes:
      - id: txnAmount
      - id: vendor
    helpVariables:
      #Help variable DisplayName:ProcessVariableName:DataType
      - Company Name:CompanyName:string
      - Company Email:CompanyEmail:string
      - Vendor Name:VendorName:string
      - Bill Payment Number:DocNumber:string
      - Total Amount:TxnAmount:double #Defined as attribute as well
      - Payment Date:TxnDate:string
      - Vendor Email:VendorEmail:string
    actionGroups:
      - id: notification
        trigger:
          parameters:
            - name: triggerValues
              possibleFieldValues:
                - create
                - update
                - delete
          handler:
            id: paymentRemittanceHandler
        actions:
          - id: sendNotification
            name: Send a vendor email
            parameters:
              - name: SendTo
                helpVariables:
                  - Vendor Email:VendorEmail:string
                fieldValues:
                  - ~billpayment.notification.sendNotification.SendTo
              - name: SendAttachment
                fieldValues:
                  - "false"
              - name: Subject
                fieldValues:
                  - ~billpayment.notification.sendNotification.Subject
              - name: Message
                fieldValues:
                  - ~billpayment.notification.sendNotification.Message
          - id: createTask
            parameters:
              - name: CloseTask
                requiredByUI: false
                fieldValues:
                  - close_manually
              - name: TaskName
                fieldValues:
                  - ~billpayment.notification.createTask.TaskName


  - id: estimate
    source: QBO
    defaultAttributes:
    - txnAmount
    attributes:
    - id: txnAmount
    - id: customer
    - id: txnStatus
      fieldValueOptions: GET_ESTIMATE_STATUS
    - id: location
    - id: txnEmailAvailabilityStatus
    - id: txnSendStatus
    - id: txnUpdateStatus
    - id: createDays
    - id: txnDays
    - id: txnSendDays
    - id: txnExpirationDays
    helpVariables:
    - Company Name:CompanyName:string
    - Company Email:CompanyEmail:string
    - Customer Name:CustomerName:string
    - Customer Email:CustomerEmail:string
    - Estimate Number:DocNumber:string
    - Estimate Total:TxnAmount:double #Defined as attribute as well
    - Estimate Date:TxnDate:string
    - Expiration Date:TxnExpirationDate:string
    - Estimate Status:TxnStatus:string
    actionGroups:
    - id: reminder
      actionIdMapper:
        actionId: sendForApproval
        subActionIds: null
      actions:
      - id: sendExternalEmail
        name: Send a customer email
        parameters:
        - name: SendTo
          fieldValues:
          - "[[Customer Email]]"
        - name: SendAttachment
          fieldValues:
          - "false"
        - name: Message
          fieldValues:
          - ~estimate.reminder.sendExternalEmail.Message #"Hi [[Customer Name]], \n%Record% [[%Record% Number]] needs your attention. Please take a look at the %record% and contact us if you have any questions. \n\nThanks,\n[[Company Name]]"
      - id: createTask
        parameters:
        - name: CloseTask
          possibleFieldValues:
          - txn_accepted
          - txn_sent
          - close_manually
      - id: sendPushNotification
        parameters:
        - name: Subject
          fieldValues:
          - ~estimate.reminder.sendPushNotification.Subject #An Estimate needs your attention
        - name: NotificationAction
          fieldValues:
          - qb001://open/estimate
    - id: notification
      actionIds:
        - sendNotification
      trigger:
        parameters:
          - name: entityOperation
            possibleFieldValues:
              - Create
              - Update
            fieldValues:
              - Create
        handler:
          id: paymentRemittanceHandler
          handlerDetail:
            taskHandler: appconnect
            handlerId: intuit-workflows/entity-created-custom-workflow
            actionName: executeWorkflowAction
      actions:
        - id: sendNotification
          name: Send an email
          parameters:
            - name: SendTo
              helpVariables:
                - Customer Email:CustomerEmail:string
                - Company Email:CompanyEmail:string
              configurable: true
            - name: SendAttachment
            - name: Subject
            - name: Message
      unsupportedAttributes:
        - createDays
        - txnDays
        - txnDueDays
  - id: purchaseorder
    source: QBO
    defaultAttributes:
    - txnAmount
    attributes:
    - id: txnAmount
    - id: vendor
    - id: txnSendDays
    - id: txnStatus
      fieldValueOptions: GET_PURCHASEORDER_STATUS
    - id: location
    - id: createDays
    - id: txnSendStatus
    - id: txnDays
    helpVariables:
    - Company Name:CompanyName:string
    - Company Email:CompanyEmail:string
    - Vendor Name:VendorName:string
    - Vendor Email:VendorEmail:string
    - Purchase order Number:DocNumber:string
    - Purchase order Total:TxnAmount:double
    - Purchase order Date:TxnDate:string
    actionGroups:
    - id: reminder
      actionIds:
      - createTask
      - sendCompanyEmail
      - sendExternalEmail
      actions:
      - id: sendExternalEmail
        name: Send a vendor email
        parameters:
        - name: SendTo
          helpVariables:
          - Vendor Email:VendorEmail:string
          fieldValues:
          - ~purchaseorder.reminder.sendExternalEmail.SendTo
        - name: SendAttachment
          fieldValues:
          - "false"
        - name: Message
          fieldValues:
          - ~purchaseorder.reminder.sendExternalEmail.Message
      - id: sendCompanyEmail
        parameters:
        - name: SendTo
          actionByUI: GET_USERS
          multiSelect: true
      - id: createTask
        parameters:
        - name: CloseTask
          possibleFieldValues:
          - txn_closed
          - txn_sent
          - close_manually
    - id: notification
      actionIds:
        - sendNotification
      trigger:
        parameters:
          - name: entityOperation
            possibleFieldValues:
              - Create
              - Update
            fieldValues:
              - Create
          - name: testParameter
            fieldValues:
              - testValue
        handler:
          id: paymentRemittanceHandler
          handlerDetail:
            taskHandler: appconnect
            handlerId: intuit-workflows/entity-created-custom-workflow
            actionName: executeWorkflowAction
      actions:
        - id: sendNotification
          name: Send an email
          parameters:
            - name: SendTo
              helpVariables:
                - Customer Email:CustomerEmail:string
                - Company Email:CompanyEmail:string
              configurable: true
            - name: SendAttachment
            - name: Subject
            - name: Message
      unsupportedAttributes:
        - createDays
        - txnDays
        - txnDueDays
  - id: statement
    source: QBO
    defaultAttributes:
    - customer
    attributes:
    - id: customer
    - id: statementType
    helpVariables:
    - Company Name:CompanyName:string
    - Company Email:CompanyEmail:string
    - Customer Name:CustomerName:string
    - Customer Email:CustomerEmail:string
    - Statement Date:StatementDate:string
    actionGroups:
    - id: scheduledActions
      actions:
      - id: scheduledAction
        required: true
        handler:
          id: wasSendStatement
        parameters:
        - name: Subject
        - name: Message
        - name: StatementType

  #List of all attributes supported by different records
  attributes:
  - id: txnAmount
    name: TxnAmount
    type: double
    configurable: true
    multiSelect: false
    defaultOperator: GTE
    defaultValue: 0
  - id: createDays
    name: TxnCreateDays
    type: days
    configurable: true
    multiSelect: false
    defaultOperator: AF
    defaultValue: DATE_TODAY
    unsupportedOperators:
    - BF
  - id: txnDays
    name: TxnDays
    type: days
    configurable: true
    hidden: false
    multiSelect: false
    defaultOperator: AF
    defaultValue: DATE_TODAY
    unsupportedOperators:
    - BF
  - id: txnSendDays
    name: TxnSendDays
    type: days
    configurable: true
    multiSelect: false
    defaultOperator: AF
    defaultValue: DATE_TODAY
    unsupportedOperators:
    - BF
  - id: txnApprovalDays
    name: TxnApprovalDays
    type: days
    configurable: true
    multiSelect: false
    defaultOperator: AF
    defaultValue: DATE_TODAY
    unsupportedOperators:
    - BF
  - id: txnApprovalStatus
    name: TxnApprovalStatus
    type: list
    configurable: true
    multiSelect: true
    fieldValueOptions: GET_APPROVAL_STATUS
  - id: term
    name: Term
    type: list
    configurable: true
    multiSelect: true
    fieldValueOptions: GET_TERM
  - id: class
    name: Class
    type: list
    configurable: true
    multiSelect: true
    fieldValueOptions: GET_CLASSES
  - id: location
    name: Location
    type: list
    configurable: true
    multiSelect: true
    fieldValueOptions: GET_DEPARTMENT
  - id: customer
    name: Customer
    type: list
    configurable: true
    multiSelect: true
    fieldValueOptions: GET_CUSTOMER
    defaultOperator: CONTAINS
  - id: vendor
    name: Vendor
    type: list
    configurable: true
    multiSelect: true
    fieldValueOptions: GET_VENDOR
    defaultOperator: CONTAINS
  - id: txnDueDays
    name: TxnDueDays
    type: days
    configurable: true
    multiSelect: false
    defaultOperator: BF
    defaultValue: -1
  - id: txnEmailAvailabilityStatus
    name: TxnEmailAvailabilityStatus
    fieldValueOptions: GET_CUSTOMER_EMAIL_STATUS
    type: string
    configurable: true
  - id: txnDepositStatus
    name: TxnDepositStatus
    fieldValueOptions: GET_DEPOSIT_STATUS
    type: list
    configurable: true
  - id: txnPaymentStatus
    name: TxnPaymentStatus
    fieldValueOptions: GET_PAYMENT_STATUS
    type: list
    configurable: true
  - id: txnSendStatus
    name: TxnSendStatus
    fieldValueOptions: GET_SEND_STATUS
    type: list
    configurable: true
  - id: txnUpdateStatus
    name: TxnUpdateStatus
    fieldValueOptions: GET_UPDATE_STATUS
    type: list
    configurable: true
  - id: txnBalanceAmount
    name: TxnBalanceAmount
    type: double
    configurable: true
  - id: txnStatus
    name: TxnStatus
    type: list
    configurable: true
    multiSelect: true
  - id: txnExpirationDays
    name: TxnExpirationDays
    type: days
    configurable: true
  - id: userId
    name: intuit_userid
    type: string
    configurable: true
    multiSelect: false
    defaultOperator: CONTAINS
    fieldValueOptions: GET_ADMINS_ID
    hidden: true
  - id: statementType
    name: StatementType
    type: list
    configurable: true
    multiSelect: false
    fieldValueOptions: GET_STATEMENT_TYPES
  - id: stringCustomField
    name: StringCustomField
    type: string
    configurable: true
    multiSelect: true
    defaultOperator: CONTAINS
  - id: doubleCustomField
    name:  DoubleCustomField
    type: double
    configurable: true
    multiSelect: true
    defaultOperator: GTE
  - id: listCustomField
    name: ListCustomField
    type: list
    configurable: true
    multiSelect: true
    fieldValueOptions: GET_CUSTOMFIELD_VALUES
    defaultOperator: CONTAINS

  # Various data types and the operators supported on them
  dataTypes:
  - type: date
    operators:
    - id: LT
      name: ~date.LT
    - id: GT
      name: ~date.GT
  - type: boolean
    operators:
    - id: is
      name: ~boolean.is
  - type: string
    operators:
    - id: CONTAINS
      name: ~string.CONTAINS
    - id: NOT_CONTAINS
      name: ~string.NOT_CONTAINS
  - type: double
    operators:
    - id: LT
      name: ~double.LT
    - id: GT
      name: ~double.GT
    - id: LTE
      name: ~double.LTE
    - id: GTE
      name: ~double.GTE
    - id: EQ
      name: ~double.EQ
    - id: BTW
      name: ~double.BTW
  - type: days
    # type inferred by process engine
    nativeType: integer
    operators:
    - id: BF
      name: Before
    - id: AF
      name: After
  - type: list
    nativeType: string
    operators:
      - id: CONTAINS
        name: ~string.CONTAINS
      - id: NOT_CONTAINS
        name: ~string.NOT_CONTAINS
  actionGroups:
  - id: reminder
    actionIds:
    - createTask
    - sendExternalEmail
    - sendCompanyEmail
    - sendPushNotification
  - id: approval
    actionIds:
    - createTask
    - sendCompanyEmail
  - id: scheduledActions
    actionIds:
    - scheduledAction

  parameters:
  - id: entityType
    name: entityType
    handlerFieldName: RecordType
    fieldType: string
    requiredByHandler: true
    valueType: PROCESS_VARIABLE
  - id: txnApprovalStatus
    name: TxnApprovalStatus
    handlerFieldName: TxnApprovalStatus
    fieldType: string
    valueType: PROCESS_VARIABLE
    requiredByHandler: true
    isOverridable: true
  - id: txnId
    name: Id
    handlerFieldName: TxnId
    fieldType: string
    valueType: PROCESS_VARIABLE
    requiredByHandler: true
  - id: syncToken
    name: SyncToken
    handlerFieldName: SyncToken
    fieldType: string
    requiredByHandler: true
    valueType: PROCESS_VARIABLE
  - id: realmId
    name: intuit_realmid
    handlerFieldName: RealmId
    fieldType: string
    valueType: PROCESS_VARIABLE
    requiredByHandler: true
  - id: sendTo
    name: SendTo
    handlerFieldName: To
    configurable: true
    requiredByHandler: true
    requiredByUI: true
    fieldType: string
  - id: cc
    name: CC
    fieldType: string
    configurable: true
    requiredByHandler: true
    requiredByUI: true
  - id: bcc
    name: BCC
    fieldType: string
    configurable: true
    requiredByHandler: true
    requiredByUI: true
  - id: message
    name: Message
    fieldType: string
    configurable: true
    requiredByHandler: true
    helpVariablesRequired: true
    requiredByUI: true
  - id: subject
    name: Subject
    fieldType: string
    configurable: true
    requiredByHandler: true
    helpVariablesRequired: true
    requiredByUI: true
  - id: isEmail
    name: IsEmail
    fieldType: boolean
    requiredByHandler: true
  - id: sendAttachment
    name: SendAttachment
    fieldType: boolean
    requiredByHandler: true
    fieldValues:
    - "false"
  - id: notificationAction
    name: NotificationAction
    fieldType: string
    requiredByHandler: true
  - id: statementType
    name: StatementType
    requiredByHandler: true
    fieldType: string
    configurable: true
    requiredByUI: false
    valueType: PROCESS_VARIABLE

  actions:
  - id: sendNotification
    name: Send Notification
    handler:
      id: wasSendNotification
    parameters:
      - id: sendTo
        configurable: false
      - id: subject
      - id: txnId
      - id: customer
      - id: LastUpdatedDate
      - id: message
      - id: isEmail
        fieldValues:
          - "true"
      - id: consolidateNotifications
        fieldValues:
          - "false"
      - id: notificationType
  - id: sendForApproval
    name: Send For Approval
    parameters:
    - name: Assignee
      configurable: true
      actionByUI: GET_ADMINS_ID
      requiredByHandler: true
      requiredByUI: true
      fieldType: string
  - id: sendForReminder
    name: Send For Reminder
    parameters:
      - name: isRecurring
        configurable: true
        requiredByHandler: true
        requiredByUI: true
        fieldType: boolean
      - name: recurFrequency
        configurable: true
        requiredByHandler: true
        requiredByUI: true
        fieldType: string
      - name: FilterCloseTaskConditions
        fieldType: string
        requiredByUI: true
        requiredByHandler: true
        configurable: true
      - name: maxScheduleCount
        fieldType: integer
        requiredByUI: true
        requiredByHandler: true
        configurable: true
  - id: createTask
    name: Create a task
    handler:
      id: wasCreateTask
    parameters:
    - id: txnId
    - id: realmId
    - name: ProjectType
      requiredByHandler: true
      fieldType: string
    - name: TaskType
      requiredByHandler: true
      fieldType: string
    - name: Assignee
      configurable: true
      actionByUI: GET_ADMINS_ID
      requiredByHandler: true
      requiredByUI: true
      fieldType: string
    - name: TaskName
      fieldType: string
      configurable: true
      requiredByUI: true
      requiredByHandler: true
      helpVariablesRequired: true
      fieldValues:
      - ~createTask.TaskName #"Review %Record% [[%Record% Number]]"
    - name: CloseTask
      fieldType: string
      requiredByUI: true
      requiredByHandler: true
      configurable: true
  - id: sendExternalEmail
    name: Send External Email
    handler:
      id: wasSendNotification
    parameters:
    - id: sendTo
      helpVariables:
      - Customer Email:CustomerEmail:string
    - id: cc
    - id: bcc
    - id: txnApprovalStatus
    - id: subject
      fieldValues:
      - ~sendExternalEmail.subject #"%Record% [[%Record% Number]] needs your attention"
    - id: message
      fieldValues:
      - ~sendExternalEmail.message #"Hi [[Customer Name]], \n%Record% [[%Record% Number]] needs your attention. Please take a look at the attached %record% and contact us if you have any questions. \n\nThanks,\n[[Company Name]]"
    - id: isEmail
      # Override field value if needed
      fieldValues:
      - "true"
    - id: sendAttachment
      fieldValues:
      - "true"
    - id: txnId
    - id: entityType
    - id: syncToken
  - id: sendCompanyEmail
    name: Send a company email
    handler:
      id: wasSendNotification
    parameters:
    - id: sendTo
      helpVariables:
      - Company Email:CompanyEmail:string
      fieldValues:
      - "[[Company Email]]"
    - id: cc
    - id: txnApprovalStatus
    - id: bcc
    - id: subject
      fieldValues:
      - ~sendCompanyEmail.subject #"Review %Record% [[%Record% Number]]"
    - id: message
      fieldValues:
      - ~sendCompanyEmail.message #"Hi, \n%Record% [[%Record% Number]] needs your attention. Please take a look at the %record% and complete any outstanding tasks. \n\nThanks,\n[[Company Name]]"
    - id: isEmail
      fieldValues:
      - "true"
  - id: sendPushNotification
    name: Send a push notification
    handler:
      id: wasSendNotification
    parameters:
    - id: subject
      configurable: false
    - id: notificationAction
    - id: txnId
    - id: message
      configurable: false
      fieldValues:
      - ~sendPushNotification.message #Go to QuickBooks to view it.
    - id: sendTo
    - id: realmId
    - name: IsMobile
      requiredByHandler: true
      fieldType: boolean
      configurable: false
      fieldValues:
      - "true"
  - id: scheduledAction
    name: Scheduled action

  handlers:
  - id: wasSendNotification
    handlerDetail:
      taskHandler: appconnect
      handlerId: intuit-workflows/was-send-notification
      actionName: executeWorkflowAction
  - id: wasCreateTask
    handlerDetail:
      taskHandler: appconnect
      handlerId: intuit-workflows/taskmanager-create-task
      actionName: executeWorkflowAction
      responseFields:
      - projectId
      - closeTaskRule
  - id: wasCreateTaskAndAddConstraint
    handlerDetail:
      taskHandler: appconnect
      handlerId: intuit-workflows/was-create-task-and-add-constraint
      actionName: executeWorkflowAction
      responseFields:
      - projectId
      - closeTaskRule
      - assigneeId
  - id: wasSendStatement
    handlerDetail:
      taskHandler: appconnect
      handlerId: intuit-workflows/was-send-statement
      actionName: executeWorkflowAction
  - id: customReminderStartProcess
    handlerDetail:
      taskHandler: appconnect
      handlerId: intuit-workflows/custom-reminder-start-process
      actionName: executeWorkflowAction


  #Old Templates
  configTemplates:
    - id: paymentDueReminder
      name: Payment Due Reminder
      description: Payment Due Reminder
      record: Invoice
      # Default attributes. Rule lines are created based on these attributes
      defaultAttributes:
        - txnPaymentStatus
        - txnDueDays
      attributes:
        - id: txnPaymentStatus
          defaultValue: UNPAID
          defaultOperator: CONTAINS
        - id: txnDueDays
          defaultValue: 3
          defaultOperator: BF
      actionGroups:
        - id: reminder
          actions:
            - id: sendExternalEmail
              name: Send a customer email
              selected: true
              parameters:
                - name: SendTo
                  fieldValues:
                    - "[[Customer Email]]"
            - id: createTask
              selected: true
              parameters:
                - name: CloseTask
                  fieldValues:
                    - txn_paid
                  possibleFieldValues:
                    - txn_paid
                    - txn_sent
                    - close_manually
            - id: sendPushNotification
              selected: true
              parameters:
                - name: Subject
                  fieldValues:
                    - ~invoice.reminder.sendPushNotification.Subject #An Invoice needs your attention

    - id: invoiceUnsentReminder
      name: ~invoiceUnsentReminder.name
      description: ~invoiceUnsentReminder.description
      record: Invoice
      # Default attributes. Rule lines are created based on these attributes
      defaultAttributes:
        - txnSendStatus
        - txnDueDays
      attributes:
        - id: txnSendStatus
          defaultValue: UNSENT
          defaultOperator: CONTAINS
        - id: txnDueDays
          defaultValue: 3
          defaultOperator: AF
      actionGroups:
        - id: reminder
          actions:
            - id: sendCompanyEmail
              selected: true
              name: Send a company email
              parameters:
                - name: SendTo
                  fieldValues:
                    - "[[Company Email]]"
            - id: createTask
              selected: true
              parameters:
                - name: CloseTask
                  fieldValues:
                    - txn_sent
                  possibleFieldValues:
                    - txn_paid
                    - txn_sent
                    - close_manually
            - id: sendPushNotification
              selected: true
              parameters:
                - name: Subject
                  fieldValues:
                    - ~invoice.reminder.sendPushNotification.Subject #An Invoice needs your attention

    - id: billVendorReminder
      name: ~billVendorReminder.name
      description: ~billVendorReminder.description
      record: Bill
      # Default attributes. Rule lines are created based on these attributes
      defaultAttributes:
        - txnPaymentStatus
        - txnDueDays
      attributes:
        - id: txnPaymentStatus
          defaultValue: UNPAID
          defaultOperator: CONTAINS
        - id: txnDueDays
          defaultValue: 1
          defaultOperator: BF
      actionGroups:
        - id: reminder
          actions:
            - id: sendCompanyEmail
              selected: true
              name: Send a company email
              parameters:
                - name: SendTo
                  fieldValues:
                    - "[[Company Email]]"
            - id: createTask
              selected: true
              parameters:
                - name: CloseTask
                  fieldValues:
                    - txn_sent
                  possibleFieldValues:
                    - txn_paid
                    - txn_sent
                    - close_manually

    - id: invoiceapproval-multicondition
      name: Invoice approval
      record: Invoice
      description: Invoice approval with multi-condition support
      steps:
        - stepId: 1
          stepType: condition
          attributes:
            - id: txnAmount
              defaultValue: 0,1000
              defaultOperator: BTW
          nexts:
            - label: "yes"
              stepId: 2
            - label: "no"
              stepId: 3
        - stepId: 2
          stepType: action
          action: sendForApproval
        - stepId: 3
          stepType: condition
          attributes:
            - id: txnAmount
              defaultValue: 1000,5000
              defaultOperator: BTW
          nexts:
            - label: "yes"
              stepId: 4
            - label: "no"
              stepId: 5
        - stepId: 4
          stepType: action
          action: sendForApproval
        - stepId: 5
          stepType: condition
          attributes:
            - id: txnAmount
              defaultValue: 5000,10000
              defaultOperator: BTW
          nexts:
            - label: "yes"
              stepId: 6
        - stepId: 6
          stepType: action
          action: sendForApproval
      labels:
        - name: Invoice
          value: ~Invoice
        - name: Approval
          value: ~Approval
      actionGroups:
        - id: approval
          actions:
            - id: sendCompanyEmail
              selected: true
            - id: createTask
              selected: true
              parameters:
                - name: CloseTask
                  fieldValues:
                    - txn_paid
                - name: Assignee
            - id: sendPushNotification
              selected: true
              parameters:
                - name: SendTo
                  fieldValues:
                    - CURRENT_USER_GLOBAL
    - id: billApproval
      name: ~billApproval.name
      record: Bill
      description: ~billApproval.description
      labels:
        - name: Bill
          value: ~Bill
        - name: Approval
          value: ~Approval
      attributes:
        - id: txnAmount
      actionGroups:
        - id: notification
          actions:
            - id: sendNotification

    # New Templates
    - id: estimateUnsentReminder
      name: ~estimateUnsentReminder.name
      description: ~estimateUnsentReminder.description
      record: Estimate
      # Default attributes. Rule lines are created based on these attributes
      defaultAttributes:
        - txnSendStatus
        - createDays
      attributes:
        - id: txnSendStatus
          defaultValue: UNSENT
          defaultOperator: CONTAINS
        - id: createDays
          defaultValue: 3
          defaultOperator: AF
      actionGroups:
        - id: reminder
          actions:
            - id: sendCompanyEmail
              selected: true
              name: Send a company email
              parameters:
                - name: SendTo
                  fieldValues:
                    - "[[Company Email]]"
            - id: createTask
              selected: true
              parameters:
                - name: CloseTask
                  fieldValues:
                    - txn_sent
                  possibleFieldValues:
                    - txn_accepted
                    - txn_sent
                    - close_manually
            - id: sendPushNotification
              selected: true
              parameters:
                - name: Subject
                  fieldValues:
                    - An Invoice needs your attention

    - id: estimateFollowupReminder #Name to be decided yet
      name: ~estimateFollowupReminder.name
      description: ~estimateFollowupReminder.description
      record: Estimate
      # Default attributes. Rule lines are created based on these attributes
      defaultAttributes:
        - txnSendStatus
        - txnStatus
        - txnExpirationDays
      attributes:
        - id: txnStatus
          defaultValue: pending
          defaultOperator: CONTAINS
        - id: txnSendStatus
          defaultValue: UNSENT
          defaultOperator: CONTAINS
        - id: txnExpirationDays
          defaultValue: 3
          defaultOperator: BF
      actionGroups:
        - id: reminder
          actions:
            - id: sendExternalEmail
              selected: true
              name: Send a customer email
              parameters:
                - name: SendTo
                  fieldValues:
                    - "[[Customer Email]]"
            - id: createTask
              selected: true
              parameters:
                - name: CloseTask
                  fieldValues:
                    - txn_accepted
                  possibleFieldValues:
                    - txn_accepted
                    - txn_sent
                    - close_manually
            - id: sendPushNotification
              selected: true
              parameters:
                - name: Subject
                  fieldValues:
                    - An Invoice needs your attention

    - id: openEstimateReminder
      name: ~openEstimateReminder.name
      description: ~openEstimateReminder.description
      record: Estimate
      # Default attributes. Rule lines are created based on these attributes
      defaultAttributes:
        - txnStatus
      attributes:
        - id: txnStatus
          defaultValue: pending,accepted
          defaultOperator: CONTAINS
      actionGroups:
        - id: reminder
          actions:
            - id: sendCompanyEmail
              selected: true
              name: Send a company email
              parameters:
                - name: SendTo
                  fieldValues:
                    - "[[Company Email]]"
            - id: createTask
              selected: true
              parameters:
                - name: CloseTask
                  fieldValues:
                    - txn_accepted
                  possibleFieldValues:
                    - txn_accepted
                    - txn_sent
                    - close_manually
            - id: sendPushNotification
              selected: true
              parameters:
                - name: Subject
                  fieldValues:
                    - An Invoice needs your attention

    - id: sendStatements
      name: ~sendStatements.name
      description: ~sendStatements.description
      record: statement
      attributes:
      - id: statementType
        defaultValue: BALANCE_FORWARD
        defaultOperator: CONTAINS
      actionGroups:
      - id: scheduledActions
        actions:
        - id: scheduledAction
          selected: true
      recurrenceRule:
        recurType: WEEKLY
        interval: 1
        daysOfWeek:
          - MONDAY


    - id: openPurchaseorderReminder
      name: ~openPurchaseorderReminder.name
      record: PurchaseOrder
      description: ~openPurchaseorderReminder.description
      attributes:
      - id: txnStatus
        defaultValue: OPEN
        defaultOperator: CONTAINS
        configurable: false
      - id: txnSendStatus
        defaultValue: SENT
        defaultOperator: CONTAINS
        configurable: false
      - id: txnSendDays
        defaultValue: 2
        defaultOperator: AF
      actionGroups:
      - id: reminder
        actions:
        - id: sendCompanyEmail
          selected: true
          parameters:
          - name: consolidateNotifications
            fieldValues:
            - "true"
          - name: SendTo
            fieldValues:
            - CURRENT_USER_GLOBAL
        - id: createTask
          selected: true
          parameters:
          - name: CloseTask
            fieldValues:
            - txn_closed
          - name: Assignee
            fieldValues:
            - CURRENT_USER_PERSONA

    - id: billUpdateNotification
      name: ~bill.notification.update.name
      record: Bill
      description: ~bill.notification.update.description
      labels:
        - name: Bill
          value: ~Bill
        - name: Notification
          value: ~Notification
      attributes:
        - id: txnAmount
          defaultValue: 0
          defaultOperator: GTE
      actionGroups:
        - id: notification
          trigger:
            parameters:
              - name: entityOperation
                fieldValues:
                  - Update
          actions:
            - id: sendNotification
              parameters:
                - name: Subject
                  helpVariables:
                    - Last Update Date:LastUpdatedDate:string
                  fieldValues:
                    - ~bill.notification.sendNotification.update.Subject
                - name: Message
                  helpVariables:
                    - Last Update Date:LastUpdatedDate:string
                  fieldValues:
                    - ~bill.notification.sendNotification.update.Message
              selected: true

    - id: estimateUpdateNotification
      name: ~estimate.notification.update.name
      record: Estimate
      description: ~estimate.notification.update.description
      labels:
        - name: Estimate
          value: ~Estimate
        - name: Notification
          value: ~Notification
      attributes:
        - id: txnAmount
          defaultValue: 0
          defaultOperator: GTE
      actionGroups:
        - id: notification
          trigger:
            parameters:
              - name: entityOperation
                fieldValues:
                  - Update
              - name: testParameter
                fieldValues:
                  - testValue
            handler:
              id: paymentRemittanceHandler
          actions:
            - id: sendNotification
              parameters:
                - name: Subject
                  helpVariables:
                    - Last Update Date:LastUpdatedDate:string
                  fieldValues:
                    - ~estimate.notification.sendNotification.update.Subject
                - name: Message
                  helpVariables:
                    - Last Update Date:LastUpdatedDate:string
                  fieldValues:
                    - ~estimate.notification.sendNotification.update.Message
              selected: true

    - id: purchaseorderUpdateNotification
      name: ~purchaseorder.notification.update.name
      record: Purchaseorder
      description: ~purchaseorder.notification.update.description
      labels:
        - name: Purchase Order
          value: ~PurchaseOrder
        - name: Notification
          value: ~Notification
      attributes:
        - id: txnAmount # default filter condition to show in the UI
          defaultValue: 0
          defaultOperator: GTE
      actionGroups:
        - id: notification
          trigger:
            parameters:
              - name: testParameter
                fieldValues:
                  - testValue1
            handler:
              id: paymentRemittanceHandler
              handlerDetail:
                taskHandler: appconnect
          actions:
            - id: sendNotification
              parameters:
                - name: Subject
                  fieldValues:
                    - ~purchaseorder.notification.sendNotification.update.Subject
                - name: Message
                  fieldValues:
                    - ~purchaseorder.notification.sendNotification.update.Message
                - name: SendTo
                  fieldValues:
                    - ~purchaseorder.notification.sendNotification.SendTo
              selected: true