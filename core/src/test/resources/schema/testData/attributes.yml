custom-config-v2-attributes:
  - id: taxStatus
    name: TaxStatus
    type: string
    configurable: true
    multiSelect: false
    defaultOperator: CONTAINS
    defaultValue: true
  - id: txnAmount
    name: TxnAmount
    type: double
    configurable: true
    multiSelect: false
    defaultOperator: GTE
    defaultValue: 0
  - id: undepositedFunds
    name: UndepositedFunds
    type: double
    configurable: true
    multiSelect: false
    defaultOperator: GTE
    defaultValue: 0
  - id: createDays
    name: TxnCreateDays
    type: days
    configurable: true
    multiSelect: false
    defaultOperator: AF
    defaultValue: DATE_TODAY
    unsupportedOperators:
      - BF
  - id: txnDays
    name: TxnDays
    type: days
    configurable: true
    multiSelect: false
    defaultOperator: AF
    defaultValue: DATE_TODAY
    unsupportedOperators:
      - BF
  - id: txnSendDays
    name: TxnSendDays
    type: days
    configurable: true
    multiSelect: false
    defaultOperator: AF
    defaultValue: DATE_TODAY
    unsupportedOperators:
      - BF
  - id: txnApprovalDays
    name: TxnApprovalDays
    type: days
    configurable: true
    multiSelect: false
    defaultOperator: AF
    defaultValue: DATE_TODAY
    unsupportedOperators:
      - BF
  - id: txnApprovalStatus
    name: TxnApprovalStatus
    type: string
    configurable: true
    multiSelect: true
    fieldValueOptions: GET_APPROVAL_STATUS
  - id: txnPrintStatus
    name: TxnPrintStatus
    type: list
    configurable: true
    multiSelect: true
    fieldValueOptions: GET_PRINT_STATUS # Expected Values: NotSet, NeedToPrint, PrintComplete
  - id: txnAcceptedBy
    name: TxnAcceptedBy
    type: string
    configurable: true
    multiSelect: true
  - id: term
    name: Term
    type: list
    configurable: true
    multiSelect: true
    fieldValueOptions: GET_TERM
  - id: class
    name: Class
    type: list
    configurable: true
    multiSelect: true
    fieldValueOptions: GET_CLASSES
  - id: location
    name: Location
    type: string
    configurable: true
    multiSelect: true
    fieldValueOptions: GET_DEPARTMENT
  - id: customer
    name: Customer
    type: list
    configurable: true
    multiSelect: true
    fieldValueOptions: GET_CUSTOMER
    defaultOperator: CONTAINS
  - id: vendor
    name: Vendor
    type: string
    configurable: true
    multiSelect: true
    fieldValueOptions: GET_VENDOR
    defaultOperator: CONTAINS
  - id: payee
    name: Payee
    type: list
    configurable: true
    multiSelect: true
    fieldValueOptions: GET_VENDOR
    defaultOperator: CONTAINS
  - id: txnDueDays
    name: TxnDueDays
    type: days
    configurable: true
    multiSelect: false
    defaultOperator: BF
    defaultValue: -1
  - id: txnEmailAvailabilityStatus
    name: TxnEmailAvailabilityStatus
    fieldValueOptions: GET_CUSTOMER_EMAIL_STATUS
    type: string
    configurable: true
  - id: txnDepositStatus
    name: TxnDepositStatus
    fieldValueOptions: GET_DEPOSIT_STATUS
    type: string
    configurable: true
  - id: txnPaymentStatus
    name: TxnPaymentStatus
    fieldValueOptions: GET_PAYMENT_STATUS
    type: string
    configurable: true
    defaultOperator: CONTAINS
    defaultValue: UNPAID
  - id: txnSendStatus
    name: TxnSendStatus
    fieldValueOptions: GET_SEND_STATUS
    type: string
    configurable: true
    defaultOperator: CONTAINS
    defaultValue: UNSENT
  - id: txnUpdateStatus
    name: TxnUpdateStatus
    fieldValueOptions: GET_UPDATE_STATUS
    type: string
    configurable: true
  - id: txnBalanceAmount
    name: TxnBalanceAmount
    type: double
    configurable: true
  - id: txnStatus
    name: TxnStatus
    type: string
    configurable: true
    multiSelect: true
  - id: txnExpirationDays
    name: TxnExpirationDays
    type: days
    configurable: true
  - id: savedByUser
    name: SavedBy
    type: string
    configurable: true
    multiSelect: true
    defaultOperator: CONTAINS
    fieldValueOptions: GET_USERS
  - id: reportId
    name: ReportId
    type: string
    configurable: true
    multiSelect: false
    fieldValueOptions: GET_CUSTOM_REPORTS
  - id: statementType
    name: StatementType
    type: string
    configurable: true
    multiSelect: false
    fieldValueOptions: GET_STATEMENT_TYPES
    defaultOperator: CONTAINS
    defaultValue: BALANCE_FORWARD
  - id: profitAmount
    name: ProfitAmount
    type: double
    configurable: true
    multiSelect: false
    defaultOperator: LTE
    defaultValue: 50
  - id: profitMargin
    name: ProfitMargin
    type: double
    configurable: true
    multiSelect: false
    defaultOperator: LTE
    defaultValue: 50
  - id: stringCustomField
    name: StringCustomField
    type: string
    configurable: true
    multiSelect: true
    defaultOperator: CONTAINS
  - id: doubleCustomField
    name: DoubleCustomField
    type: double
    configurable: true
    multiSelect: false
    defaultOperator: GTE
  - id: listCustomField
    name: ListCustomField
    type: list
    configurable: true
    multiSelect: true
    fieldValueOptions: GET_CUSTOMFIELD_VALUES
    defaultOperator: CONTAINS
  - id: transferAutomationType
    name: TransferAutomationType
    type: string
    defaultOperator: CONTAINS
  - id: transferSendToEmail
    name: TransferSendToEmail
    type: string
    defaultOperator: CONTAINS
  - id: transferCCEmail
    name: TransferCCEmail
    type: string
    defaultOperator: CONTAINS
  - id: transferSourceEntityId
    name: TransferSourceEntityId
    type: string
    defaultOperator: CONTAINS
  - id: transferSourceEntityName
    name: TransferSourceEntityName
    type: string
    defaultOperator: CONTAINS
  - id: transferDestinationEntityId
    name: TransferDestinationEntityId
    type: string
    defaultOperator: CONTAINS
  - id: transferDestinationEntityName
    name: TransferDestinationEntityName
    type: string
    defaultOperator: CONTAINS
  - id: transferAmount
    name: TransferAmount
    type: double
    defaultOperator: GT
    defaultValue: 0
  - id: isTransferSourceAWallet
    name: IsTransferSourceAWallet
    type: boolean
    defaultOperator: is
    defaultValue: false
  - id: pauseDurationForTranferExecution
    name: PauseDurationForTranferExecution
    type: string
    defaultOperator: CONTAINS
    defaultValue: PT24H
  - id: cronExpressionForTesting
    name: CronExpressionForTesting
    type: string
    defaultOperator: CONTAINS
  - id: docNumber
    name: DocNumber
    type: double
    configurable: true
    multiSelect: false
  - id: memo
    name: Memo
    type: string
  - id: shipDays
    name: ShipDays
    type: days
    configurable: true
    multiSelect: false
  - id: txnAcceptedDays
    name: TxnAcceptedDays
    type: days
    configurable: true
    multiSelect: false
    unsupportedOperators:
      - BF
    ## non-transactional attribute
  - id: accountNumber
    name: AccountNumber
    type: string
    configurable: true
    multiSelect: false
    defaultOperator: CONTAINS
  - id: active
    name: Active
    type: list
    configurable: true
    multiSelect: false
    fieldValueOptions: GET_ACTIVE
    defaultOperator: CONTAINS
    defaultValue: YES
  - id: accountType
    name: AccountType
    type: list
    configurable: true
    multiSelect: true
    fieldValueOptions: GET_ACCOUNT_TYPE
    defaultOperator: CONTAINS
    defaultValue: BANK
  - id: subAccount
    name: SubAccount
    type: list
    configurable: true
    multiSelect: false
    fieldValueOptions: GET_SUB_ACCOUNT
  - id: classification
    name: Classification
    type: list
    configurable: true
    multiSelect: true
    fieldValueOptions: GET_CLASSIFICATION
    defaultOperator: CONTAINS
  - id: accountSubType
    name: AccountSubType
    type: string
    configurable: true
    multiSelect: false
    defaultOperator: CONTAINS
  - id: currentBalance
    name: CurrentBalance
    type: string
  - id: currentBalanceWithSubAccounts
    name: CurrentBalanceWithSubAccounts
    type: string
  - id: primaryEmailAddress
    name: PrimaryEmailAddress
    type: string
    configurable: true
    defaultOperator: CONTAINS
  - id: balanceWithJobs
    name: BalanceWithJobs
    type: double
    defaultOperator: GT
    defaultValue: 0
  - id: billWithParent
    name: BillWithParent
    type: list
    configurable: true
    multiSelect: false
    fieldValueOptions: GET_BILL_WITH_PARENT
    defaultOperator: CONTAINS
  - id: job
    name: Job
    type: list
    configurable: true
    multiSelect: false
    fieldValueOptions: GET_JOB
    defaultOperator: CONTAINS
  - id: taxable
    name: Taxable
    type: list
    configurable: true
    multiSelect: false
    fieldValueOptions: GET_TAXABLE
    defaultOperator: CONTAINS
  - id: openBalanceDate
    name: OpenBalanceDate
    type: string
    defaultOperator: GT
  - id: companyName
    name: CompanyName
    type: string
    configurable: true
    multiSelect: false
    defaultOperator: CONTAINS
  - id: level
    name: Level
    type: double
    defaultOperator: GT
    defaultValue: 0
  - id: source
    name: Source
    type: string
    configurable: true
    multiSelect: false
    defaultOperator: CONTAINS
  - id: preferredDeliveryMethod
    name: PreferredDeliveryMethod
    type: list
    configurable: true
    multiSelect: true
    fieldValueOptions: GET_DELIVERY_METHOD
    defaultOperator: CONTAINS
  - id: paymentMethodRef
    name: PaymentMethod
    type: list
    configurable: true
    multiSelect: true
    fieldValueOptions: GET_PAYMENT_METHOD
    defaultOperator: CONTAINS
  - id: customerTypeRef
    name: CustomerType
    type: list
    configurable: true
    multiSelect: true
    fieldValueOptions: GET_CUSTOMER_TYPE
    defaultOperator: CONTAINS
  - id: costRate
    name: CostRate
    type: string
  - id: organization
    name: Organization
    type: list
    configurable: true
    multiSelect: false
    fieldValueOptions: GET_ORGANIZATION
    defaultOperator: CONTAINS
  - id: releasedDate
    name: ReleasedDate
    type: string
    defaultOperator: GT
  - id: hiredDate
    name: HiredDate
    type: string
  - id: employeeNumber
    name: EmployeeNumber
    type: string
    configurable: true
    multiSelect: false
    defaultOperator: CONTAINS
  - id: billableTime
    name: BillableTime
    type: list
    configurable: true
    multiSelect: false
    fieldValueOptions: GET_BILLABLE_TIME
    defaultOperator: CONTAINS
  - id: displayName
    name: DisplayName
    type: string
    configurable: true
    multiSelect: false
    defaultOperator: CONTAINS
  - id: vendor1099
    name: Vendor1099
    type: list
    configurable: true
    multiSelect: false
    fieldValueOptions: GET_VENDOR_1099
    defaultOperator: CONTAINS
  - id: billRate
    name: BillRate
    type: string
  - id: taxIdentifier
    name: TaxIdentifier
    type: string
    configurable: true
    multiSelect: false
    defaultOperator: CONTAINS
  - id: createTime
    name: CreateTime
    type: string
  - id: lastUpdateTime
    name: LastUpdateTime
    type: string
  - id: currencyRef
    name: Currency
    type: list
    configurable: true
    multiSelect: false
    fieldValueOptions: GET_CURRENCY
    defaultOperator: CONTAINS
  - id: isProject
    name: IsProject
    type: list
    configurable: true
    multiSelect: false
    fieldValueOptions: GET_IS_PROJECT
    defaultOperator: CONTAINS
  - id: employee
    name: Employee
    type: list
    configurable: true
    multiSelect: true
    fieldValueOptions: GET_EMPLOYEE
    defaultOperator: CONTAINS
  - id: expenseCategory
    name: ExpenseCategory
    type: list
    configurable: true
    multiSelect: true
    fieldValueOptions: GET_EXPENSE_CATEGORY
    defaultOperator: CONTAINS
  - id: txnHiredDays
    name: TxnHiredDays
    type: days
    configurable: true
    multiSelect: false
    defaultOperator: AF
    defaultValue: DATE_TODAY
  - id: txnReleaseDays
    name: TxnReleaseDays
    type: days
    configurable: true
    multiSelect: false
    defaultOperator: AF
    defaultValue: DATE_TODAY
  - id: txnBirthDays
    name: TxnBirthDays
    type: days
    configurable: true
    multiSelect: false
    defaultOperator: BF
    defaultValue: -1
    unsupportedOperators:
      - AF
  - id: txnAnniversaryDays
    name: TxnAnniversaryDays
    type: days
    configurable: true
    multiSelect: false
    defaultOperator: BF
    defaultValue: DATE_TODAY
    unsupportedOperators:
      - AF

