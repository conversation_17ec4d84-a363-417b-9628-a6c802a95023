custom-config-v2-datatypes:
  - type: date
    operators:
      - id: LT
        name: ~date.LT
      - id: GT
        name: ~date.GT
  - type: boolean
    operators:
      - id: is
        name: ~boolean.is
  - type: string
    operators:
      - id: CONTAINS
        name: ~string.CONTAINS
      - id: NOT_CONTAINS
        name: ~string.NOT_CONTAINS
      - id: ANY_MATCH
        name: ~string.ANY_MATCH
      - id: NO_MATCH
        name: ~string.NO_MATCH
  - type: double
    operators:
      - id: LT
        name: ~double.LT
      - id: GT
        name: ~double.GT
      - id: LTE
        name: ~double.LTE
      - id: GTE
        name: ~double.GTE
      - id: EQ
        name: ~double.EQ
      - id: BTW
        name: ~double.BTW
  - type: days
    # type inferred by process engine
    nativeType: integer
    operators:
      - id: BF
        name: ~days.BF
      - id: AF
        name: ~days.AF
  - type: list
    # type inferred by process engine
    nativeType: string
    operators:
      - id: CONTAINS
        name: ~string.CONTAINS
      - id: NOT_CONTAINS
        name: ~string.NOT_CONTAINS

