on-demand:
  actionConfig:
    createTask:
      bill:
        parameters:
          TaskName: "Approval due for Bill [[DocNumber]]"
          TaskType: "QB_BILL"
          ProjectType: "QB_BILL_APPROVAL"
      invoice:
        parameters:
          TaskName: "Approval due for Invoice [[DocNumber]]"
          TaskType: "QB_INVOICE"
          ProjectType: "QB_INVOICE_APPROVAL"
    sendCompanyEmail:
      bill:
        parameters:
          SendTo: "[[CompanyEmail]]"
          IsEmail: "true"
          Message: "Hi,\n\nBill [[DocNumber]] is pending approval. Please approve it at the earliest using this task manager link - https://app.e2e.qbo.intuit.com/app/taskmanager.\n\nNote that bills that are not approved for more than 30 days will be auto rejected.\n\nThanks,\n[[CompanyName]]"
          Subject: "Review Bill [[DocNumber]]"
          consolidateNotifications: "false"
      invoice:
        parameters:
          SendTo: "[[CompanyEmail]]"
          IsEmail: "true"
          Message: "Hi,\n\nInvoice [[DocNumber]] is pending approval. Please approve it at the earliest using this task manager link - https://app.e2e.qbo.intuit.com/app/taskmanager.\n\nNote that invoice that are not approved for more than 30 days will be auto rejected.\n\nThanks,\n[[CompanyName]]"
          Subject: "Review Invoice [[DocNumber]]"
          consolidateNotifications: "false"