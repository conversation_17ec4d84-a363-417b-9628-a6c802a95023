custom-config-v2-actions:
  - id: sendForApproval
    name: Send For Approval
    parameters:
    - name: approvalType
      configurable: true
      requiredByHandler: true
      requiredByUI: true
      fieldType: string
      possibleFieldValues:
        - SEQUENTIAL
        - PARALLEL
    - name: Assignee
      configurable: true
      actionByUI: GET_ADMINS_ID
      requiredByHandler: true
      requiredByUI: true
      fieldType: string
  - id: createTask
    name: Create a task
    handler:
      id: wasCreateTask
    parameters:
      - id: txnId
      - id: realmId
      - name: ProjectType
        requiredByHandler: true
        fieldType: string
      - name: TaskType
        requiredByHandler: true
        fieldType: string
      - name: Assignee
        configurable: true
        actionByUI: GET_ADMINS_ID
        requiredByHandler: true
        requiredByUI: true
        fieldType: string
      - name: TaskName
        fieldType: string
        configurable: true
        requiredByUI: true
        requiredByHandler: true
        helpVariablesRequired: true
        fieldValues:
          - ~createTask.TaskName
      - name: CloseTask
        fieldType: string
        requiredByUI: true
        requiredByHandler: true
        configurable: true
  - id: sendExternalEmail
    name: Send External Email
    handler:
      id: wasSendNotification
    parameters:
      - id: sendTo
        configurable: false
        helpVariables:
          - Customer Email:CustomerEmail:string
      - id: cc
      - id: bcc
      - id: customer
      - id: subject
        fieldValues:
          - ~sendExternalEmail.subject
      - id: message
        fieldValues:
          - ~sendExternalEmail.message
      - id: isEmail
        # Override field value if needed
        fieldValues:
          - "true"
      - id: sendAttachment
        fieldValues:
          - "true"
      - id: txnId
      - id: entityType
      - id: syncToken
  - id: sendCompanyEmail
    name: Send a company email
    handler:
      id: wasSendNotification
    parameters:
      - id: txnId
      - id: entityType
      - id: txnSendStatus
      - id: customer
      - id: sendTo
        helpVariables:
          - Company Email:CompanyEmail:string
        fieldValues:
          - ~sendCompanyEmail.sendTo
      - id: cc
      - id: bcc
      - id: subject
        fieldValues:
          - ~sendCompanyEmail.subject
      - id: message
        fieldValues:
          - ~sendCompanyEmail.message
      - id: isEmail
        fieldValues:
          - "true"
      - id: consolidateNotifications
        fieldValues:
          - "false"
  - id: sendTrayNotification
    name: Send Tray Notification
    handler:
      id: notificationTaskHandler
    parameters:
      - id: txnId
      - id: realmId
      - id: sendTo
      - id: subject
      - id: message
      - id: notificationData
      - id: notificationName
      - id: notificationDataType
      - id: serviceName
  - id: sendPushNotification
    name: Send a push notification
    handler:
      id: wasSendNotification
    parameters:
      - id: subject
        configurable: false
      - id: notificationAction
      - id: txnId
      - id: message
        configurable: false
        fieldValues:
          - ~sendPushNotification.message
      - id: sendTo
      - id: realmId
      - name: IsMobile
        requiredByHandler: true
        fieldType: boolean
        configurable: false
        fieldValues:
          - "true"
  - id: scheduledAction
    name: Scheduled action
  - id: sendEntity
    name: Send entity
    handler:
      id: sendTransactionHandler
    parameters:
      - id: sendTo
        configurable: false
        helpVariables:
          - Customer Email:CustomerEmail:string
      - id: cc
      - id: bcc
      - id: subject
        fieldValues:
          - ~sendExternalEmail.subject
      - id: message
        fieldValues:
          - ~sendExternalEmail.message
      - id: txnId
      - id: entityType
      - id: syncToken
  - id: updateEntity
    name: Update entity
    handler:
      id: updateTransactionHandler
    parameters:
      - id: fieldsToUpdate
      - id: memo
        fieldValues:
          - ~updateEntity.memo
      - id: memoAction
      - id: txnId
      - id: entityType
      - id: syncToken
  - id: sendNotification
    name: Send Notification
    handler:
      id: wasSendNotification
    parameters:
      - id: sendTo
        configurable: false
      - id: subject
      - id: txnId
      - id: LastUpdatedDate
      - id: message
      - id: isEmail
        fieldValues:
          - "true"
      - id: consolidateNotifications
        fieldValues:
          - "false"
      - id: notificationType
  - id: sendPreTransferEmail
    name: Send External Email
    handler:
      id: notificationTaskHandler
  - id: sendInsufficientFundsEmail
    name: Send insufficient funds Email
    handler:
      id: notificationTaskHandler
  - id: sendTransferSuccessEmail
    name: Send executed transfer email
    handler:
      id: notificationTaskHandler
  - id: sendTransferFailureEmail
    name: Send executed transfer email
    handler:
      id: notificationTaskHandler
    parameters:
      - id: sendTo
        helpVariables:
          - Customer Email:CustomerEmail:string
      - id: cc
      - id: bcc
      - id: subject
        fieldValues:
          - ~sendTransferFailureEmail.subject
      - id: message
        fieldValues:
          - ~sendTransferFailureEmail.message
      - id: isEmail
        # Override field value if needed
        fieldValues:
          - "true"
      - id: sendAttachment
        fieldValues:
          - "true"
      - id: txnId
      - id: entityType
      - id: syncToken
  - id: getAmountToTransfer
    name: Get amount need to be transfered
    handler:
      id: getAmountToTransferHandler
    parameters:
      - id: transferAutomationType
        name: TransferAutomationType
        handlerFieldName: transferType
        requiredByHandler: true
        fieldType: string
        valueType: PROCESS_VARIABLE
  - id: getTransferExecutionAmount
    name: Get amount need to be transfered
    handler:
      id: getAmountToTransferHandler
    parameters:
      - id: transferAutomationType
        name: TransferAutomationType
        handlerFieldName: transferType
        requiredByHandler: true
        fieldType: string
        valueType: PROCESS_VARIABLE
  - id: getTransferEntitiesDetails
    name: Get amount need to be transfered
    handler:
      id: getTransferExecutionParamsHandler
    parameters:
      - id: transferSourceEntityId
        name: TransferSourceEntityId
        handlerFieldName: sourceEnvelopeId
        requiredByHandler: true
        fieldType: string
        valueType: PROCESS_VARIABLE
      - id: transferDestinationEntityId
        name: TransferDestinationEntityId
        handlerFieldName: destinationEnvelopeId
        requiredByHandler: true
        fieldType: string
        valueType: PROCESS_VARIABLE
      - id: transferAmount
        name: TransferAmount
        handlerFieldName: transferAmount
        requiredByHandler: true
        fieldType: string
        valueType: PROCESS_VARIABLE
  - id: checkTransferFundsAvailability
    name: Get amount need to be transfered
    handler:
      id: getTransferExecutionParamsHandler
    parameters:
      - id: transferSourceEntityId
        name: TransferSourceEntityId
        handlerFieldName: sourceEnvelopeId
        requiredByHandler: true
        fieldType: string
        valueType: PROCESS_VARIABLE
      - id: transferDestinationEntityId
        name: TransferDestinationEntityId
        handlerFieldName: destinationEnvelopeId
        requiredByHandler: true
        fieldType: string
        valueType: PROCESS_VARIABLE
      - id: transferAmount
        name: TransferAmount
        handlerFieldName: transferAmount
        requiredByHandler: true
        fieldType: string
        valueType: PROCESS_VARIABLE
      - id: isTransferSourceAWallet
        name: IsTransferSourceAWallet
        handlerFieldName: isTransferSourceAWallet
        requiredByHandler: true
        fieldType: string
        valueType: PROCESS_VARIABLE
  - id: executeEnvelopeTransfer
    name: Get total tax liability
    handler:
      id: executeEnvelopeTransferHandler
    parameters:
      - id: transferSourceEntityId
        name: TransferSourceEntityId
        handlerFieldName: sourceEnvelopeId
        requiredByHandler: true
        fieldType: string
        valueType: PROCESS_VARIABLE
      - id: transferDestinationEntityId
        name: TransferDestinationEntityId
        handlerFieldName: destinationEnvelopeId
        requiredByHandler: true
        fieldType: string
        valueType: PROCESS_VARIABLE
      - id: transferAmount
        name: TransferAmount
        handlerFieldName: transferAmount
        requiredByHandler: true
        fieldType: string
        valueType: PROCESS_VARIABLE

