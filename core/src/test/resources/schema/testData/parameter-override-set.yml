# Description: This file contains the parameter override set for custom workflows.
# Different workflow have different actions and parameters. The parameter override set is used to override the default parameters of the actions.
# The parameter override set is used to prepopulate the parameter values in the UI and the workflow payload.
custom-config-v2-parameter-override-set:
  - entityId: invoice
    actionGroup: reminder
    actions:
    - id: createTask
      parameters:
      - name: CloseTask
        possibleFieldValues:
        - txn_paid
        - txn_sent
        - close_manually
    - id: sendExternalEmail
      name: Send a customer email
      parameters:
      - name: SendTo
        fieldValues:
        - ~invoice.reminder.sendExternalEmail.SendTo
    - id: sendCompanyEmail
      parameters:
      - name: SendTo
        actionByUI: GET_USERS
        multiSelect: true
      - name: TxnPaymentStatus
      - name: TxnDueDays
    - id: sendPushNotification
      parameters:
      - name: Subject
        fieldValues:
        - ~invoice.reminder.sendPushNotification.Subject
      - name: NotificationAction
        fieldValues:
        - qb001://open/invoice/?id=[[TxnId]]&companyid=[[RealmId]]
  - entityId: invoice
    actionGroup: approval
    actions:
    - id: createTask
      # for invoice approval, to make create task action mandatory, marking required as true
      required: true
      parameters:
      - name: CloseTask
        # UI renders close task based on close task parameter sent as part of payload,
        # so marking requiredByUI as false to not send close task param for invoice approval
        requiredByUI: false
        fieldValues:
          - txn_approval_changed
      - name: TaskName
        fieldValues:
        - ~invoice.approval.createTask.TaskName
    - id: sendCompanyEmail
      helpVariables:
        # Help variable DisplayName:ProcessVariableName:DataType
        - Created By - Full Name:intuit_userid:string
      parameters:
      - name: Message
        fieldValues:
        - ~invoice.approval.sendCompanyEmail.Message
    - id: sendPushNotification
      parameters:
      - name: Subject
        fieldValues:
        - ~invoice.approval.sendPushNotification.Subject
      - name: NotificationAction
        fieldValues:
        - qb001://open/invoice/?id=[[TxnId]]&companyid=[[RealmId]]

  - entityId: invoice
    actionGroup: send
    actions:
    - id: sendEntity
      name: Send invoice to customer
      required: true
      parameters:
      - name: SendTo
        fieldValues:
        - ~invoice.reminder.sendExternalEmail.SendTo
      - name: Subject
        fieldValues:
        - ~invoice.send.sendEntity.Subject
      - name: Message
        fieldValues:
        - ~invoice.send.sendEntity.Message
  - entityId: invoice
    actionGroup: update
    actions:
    - id: updateEntity
      name: Update invoice
      required: true
  - entityId: invoice
    actionGroup: notification
    actions:
    - id: sendNotification
      name: Send an email
      parameters:
      - name: SendTo
        helpVariables:
          - Customer Email:CustomerEmail:string
          - Company Email:CompanyEmail:string
        fieldValues:
        - "[[Company Email]]"
        configurable: true
      - name: SendAttachment
        fieldValues:
        - "false"
      - name: Subject
        fieldValues:
        - "Invoice [[%Record% Number]] Created/Updated on [[%Record% Date]]"
      - name: Message
        fieldValues:
        - "Hello [[Company Name]], \n\n[[Customer Name]] has created/updated invoice [[%Record% Number]] with [[Total Amount]] on [[%Record% Date]].\nPlease click the <a href=https://app.e2e.qbo.intuit.com/app/invoice?txnId=[[TxnId]]>Invoice Link</a> to review the invoice. \n\nThanks, \n[[Company Name]]"
      - name: CC
      - name: BCC

  - entityId: bill
    actionGroup: reminder
    actions:
    - id: sendPushNotification
    - id: sendExternalEmail
      name: Send a vendor email
    - id: createTask
      parameters:
      - name: CloseTask
        possibleFieldValues:
          - txn_paid
          - close_manually
    - id: sendCompanyEmail
      parameters:
      - name: SendTo
        actionByUI: GET_USERS
        multiSelect: true
      - name: TxnPaymentStatus
      - name: TxnDueDays
  - entityId: bill
    actionGroup: approval
    actions:
    - id: createTask
      # for bill approval, to make create task action mandatory, marking required as true
      required: true
      handler:
        id: wasCreateTaskAndAddConstraint
      parameters:
      - name: CloseTask
        # UI renders close task based on close task parameter sent as part of payload,
        # so marking requiredByUI as false to not send close task param for bill approval
        requiredByUI: false
        fieldValues:
        - txn_approval_changed
      - name: TaskName
        fieldValues:
        - ~bill.approval.createTask.TaskName
    - id: sendCompanyEmail
      parameters:
      - name: Message
        fieldValues:
        - ~bill.approval.sendCompanyEmail.Message
  - entityId: bill
    actionGroup: notification
    actions:
    - id: sendNotification
      name: Send an email
      parameters:
      - name: SendTo
        helpVariables:
          - Vendor Email:VendorEmail:string
          - Company Email:CompanyEmail:string
        fieldValues:
        - "test sendto"
        configurable: true
      - name: SendAttachment
        fieldValues:
        - "false"
      - name: Subject
        fieldValues:
        - "test subject"
      - name: Message
        fieldValues:
        - "test message"
      - name: CC
      - name: BCC


