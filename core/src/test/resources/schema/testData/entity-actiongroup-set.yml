custom-config-v2-entity-actiongroup-set:
  - entityId: invoice
    actionGroup: reminder
    enabledActions:
      - createTask
      - sendExternalEmail
      - sendCompanyEmail
      - sendPushNotification
    enabledAttributes:
      - txnAmount
      - customer
      - txnPaymentStatus
      - term
      - location
      - txnBalanceAmount
      - txnEmailAvailabilityStatus
      - createDays
      - txnDays
      - txnSendDays
      - txnApprovalDays
      - txnDueDays
      - userId
      - stringCustomField
      - doubleCustomField
      - listCustomField
      - docNumber
      - memo
      - shipDays
      - txnPrintStatus
      - class
  - entityId: invoice
    actionGroup: approval
    precannedTemplateId: invoiceapproval-multicondition
    actionIdMapper:
      actionId: sendForApproval
      subActionIds:
        - createTask
        - sendCompanyEmail
        - sendPushNotification
    enabledActions:
      - createTask
      - sendPushNotification
      - sendCompanyEmail
    enabledAttributes:
      - txnAmount
      - customer
      - txnPaymentStatus
      - term
      - location
      - txnBalanceAmount
      - txnSendStatus
      - savedByUser
      - txnDueDays
      - userId
      - class
    defaultAttributes:
      - txnAmount
  - entityId: invoice
    actionGroup: send
    trigger:
      handler:
        id: entityActionsHandler
    enabledActions:
      - sendEntity
    enabledAttributes:
      - txnAmount
      - customer
      - txnPaymentStatus
      - term
      - location
      - txnBalanceAmount
      - txnEmailAvailabilityStatus
      - createDays
      - txnDays
      - txnSendDays
      - txnApprovalDays
      - txnDueDays
      - userId
      - stringCustomField
      - doubleCustomField
      - listCustomField
      - docNumber
      - memo
      - shipDays
      - txnPrintStatus
      - class
    defaultAttributes:
      - txnSendStatus
  - entityId: invoice
    actionGroup: update
    trigger:
      handler:
        id: entityActionsHandler
    enabledActions:
      - updateEntity
    enabledAttributes:
      - txnAmount
      - customer
      - txnPaymentStatus
      - term
      - location
      - txnBalanceAmount
      - txnEmailAvailabilityStatus
      - createDays
      - txnDays
      - txnSendDays
      - txnApprovalDays
      - txnDueDays
      - userId
      - stringCustomField
      - doubleCustomField
      - listCustomField
      - docNumber
      - memo
      - shipDays
      - txnPrintStatus
      - class
  - entityId: invoice
    actionGroup: notification
    enabledActions:
      - sendNotification
    trigger:
      parameters:
        - name: entityOperation
          possibleFieldValues:
            - Create
            - Update
          fieldValues:
            - Create
      handler:
        id: paymentRemittanceHandler
    enabledAttributes:
      - txnAmount
      - customer
      - txnPaymentStatus
      - term
      - location
      - txnBalanceAmount
      - txnEmailAvailabilityStatus
      - createDays
      - txnDays
      - txnSendDays
      - txnApprovalDays
      - txnDueDays
      - userId
      - stringCustomField
      - doubleCustomField
      - listCustomField
      - docNumber
      - memo
      - shipDays
      - txnPrintStatus
      - class
  - entityId: bill
    actionGroup: reminder
    enabledActions:
      - createTask
      - sendCompanyEmail
    enabledAttributes:
      - txnAmount
      - vendor
      - txnPaymentStatus
      - location
      - term
      - createDays
      - txnDays
      - txnDueDays
      - userId
      - txnApprovalStatus
      - stringCustomField
      - doubleCustomField
      - listCustomField
      - docNumber
    defaultAttributes:
      - txnPaymentStatus
  - entityId: bill
    actionGroup: approval
    precannedTemplateId: billApproval-multicondition
    actionIdMapper:
      actionId: sendForApproval
      subActionIds:
        - createTask
        - sendCompanyEmail
    enabledActions:
      - createTask
      - sendCompanyEmail
    enabledAttributes:
      - txnAmount
      - vendor
      - txnPaymentStatus
      - location
      - term
      - userId
      - savedByUser
  - entityId: bill
    actionGroup: notification
    enabledActions:
      - sendNotification
    trigger:
      parameters:
        - name: entityOperation
          possibleFieldValues:
            - Create
            - Update
          fieldValues:
            - Create
      handler:
        id: paymentRemittanceHandler
    enabledAttributes:
      - txnAmount
      - vendor
      - txnPaymentStatus
      - location
      - term
      - userId
      - savedByUser
      - txnApprovalStatus




