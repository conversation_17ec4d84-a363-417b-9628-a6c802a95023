custom-config-v2-entity-set:
  - id: invoice
    attributes:
    - txnAmount
    - customer
    - txnPaymentStatus
    - term
    - location
    - txnBalanceAmount
    - txnEmailAvailabilityStatus
    - createDays
    - txnDays
    - txnSendDays
    - txnApprovalStatus
    - txnSendStatus
    - savedByUser
    - txnApprovalDays
    - txnDueDays
    - userId
    - stringCustomField
    - doubleCustomField
    - listCustomField
    - docNumber
    - memo
    - shipDays
    - txnPrintStatus
    - class
    defaultAttributes:
    - txnPaymentStatus
    helpVariables:
    # Help variable DisplayName:ProcessVariableName:DataType
    - Company Name:CompanyName:string
    - Company Email:CompanyEmail:string
    - Customer Name:CustomerName:string
    - Customer Email:CustomerEmail:string
    - Invoice Number:DocNumber:string
    - Total Amount:TxnAmount:double # Defined as attribute as well
    - Balance:TxnBalanceAmount:double # Defined as attribute as well
    - Due Date:TxnDueDate:string # We will get formatted value
    - Invoice Date:TxnDate:string # Attribute defines as days
  - id: bill
    attributes:
    - txnAmount
    - vendor
    - txnPaymentStatus
    - location
    - term
    - createDays
    - txnDays
    - txnDueDays
    - userId
    - savedByUser
    - txnApprovalStatus
    - stringCustomField
    - doubleCustomField
    - listCustomField
    - docNumber
    defaultAttributes:
    - txnPaymentStatus
    helpVariables:
    # Help variable DisplayName:ProcessVariableName:DataType
    - Company Name:CompanyName:string
    - Company Email:CompanyEmail:string
    - Vendor Name:VendorName:string
    - Bill Number:DocNumber:string
    - Total Amount:TxnAmount:double # Defined as attribute as well
    - Balance Due:TxnBalanceAmount:double # Defined as attribute as well
    - Due Date:TxnDueDate:string
    - Bill Date:TxnDate:string
    - Due Days:TxnDueDays:integer # Defined as attribute as well
    - Vendor Email:VendorEmail:string
