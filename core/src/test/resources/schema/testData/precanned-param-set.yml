# This file is used to override the default precanned parameter values for the workflow actions.
# The precanned parameter values are used to prepopulate the parameter values in the UI.
# The precanned parameter values are also used to prepopulate the parameter values in the workflow payload.
# The changes made in this file will be reflected in the UI and the workflow payload.

custom-config-v2-precanned-param-set:
  - id: invoiceoverduereminder
    actions:
    - id: sendCompanyEmail
      parameters:
      - name: consolidateNotifications
        fieldValues:
          - "true"
      - name: SendTo
        fieldValues:
          - CURRENT_USER_GLOBAL
    - id: sendExternalEmail
      selected: true
    - id: createTask
      selected: true
      parameters:
      - name: CloseTask
        fieldValues:
          - txn_paid
      - name: Assignee
        fieldValues:
          - CURRENT_USER_PERSONA
    - id: sendPushNotification
      selected: true
      parameters:
      - name: SendTo
        fieldValues:
        - CURRENT_USER_GLOBAL
  - id: invoiceUnsentReminder
    actions:
    - id: sendCompanyEmail
      selected: false
      parameters:
      - name: consolidateNotifications
        fieldValues:
        - "true"
      - name: SendTo
        fieldValues:
        - CURRENT_USER_GLOBAL
    - id: createTask
      selected: true
      parameters:
      - name: CloseTask
        fieldValues:
        - txn_sent
      - name: Assignee
        fieldValues:
        - CURRENT_USER_PERSONA
    - id: sendPushNotification
      selected: false
      parameters:
      - name: SendTo
        fieldValues:
        - CURRENT_USER_GLOBAL
  - id: invoiceApproval
    actions:
    - id: sendCompanyEmail
      selected: true
    - id: createTask
      selected: true
      parameters:
      - name: CloseTask
        fieldValues:
        - txn_paid
      - name: Assignee
    - id: sendPushNotification
      selected: true
      parameters:
      - name: SendTo
        fieldValues:
        - CURRENT_USER_GLOBAL
  - id: invoiceCreateNotification
    actions:
    - id: sendNotification
      parameters:
      - name: Subject
        fieldValues:
        - ~invoice.notification.sendNotification.create.Subject
      - name: Message
        fieldValues:
        - ~invoice.notification.sendNotification.create.Message
      - name: SendTo
        fieldValues:
        - ~invoice.notification.sendNotification.SendTo
      selected: true
  - id: invoiceUpdateNotification
    actions:
    - id: sendNotification
      parameters:
      - name: Subject
        helpVariables:
        - Last Update Date:LastUpdatedDate:string
        fieldValues:
        - ~invoice.notification.sendNotification.update.Subject
      - name: Message
        helpVariables:
        - Last Update Date:LastUpdatedDate:string
        fieldValues:
        - ~invoice.notification.sendNotification.update.Message
      - name: SendTo
        fieldValues:
        - ~invoice.notification.sendNotification.SendTo
      selected: true
  - id: automaticallySendUnsentInvoices
    actions:
      - id: sendEntity
        selected: true
  - id: overdueInvoiceMemo
    actions:
    - id: updateEntity
      selected: true
      parameters:
      - name: MemoAction
        fieldValues:
        - APPEND
      - name: FieldsToUpdate
        fieldValues:
        - Memo

  - id: billVendorReminder
    actions:
    - id: sendCompanyEmail
      selected: false
      parameters:
      - name: consolidateNotifications
        fieldValues:
        - "true"
      - name: SendTo
        fieldValues:
        - CURRENT_USER_GLOBAL
    - id: createTask
      selected: true
      parameters:
      - name: CloseTask
        fieldValues:
        - txn_paid
      - name: Assignee
        fieldValues:
        - CURRENT_USER_PERSONA

  - id: billapproval
    actions:
    - id: sendCompanyEmail
      selected: true

  - id: billCreateNotification
    actions:
    - id: sendNotification
      parameters:
      - name: Subject
        fieldValues:
          - ~bill.notification.sendNotification.create.Subject
      - name: Message
        fieldValues:
          - ~bill.notification.sendNotification.create.Message
      - name: SendTo
        fieldValues:
          - ~bill.notification.sendNotification.SendTo
      selected: true

  - id: billUpdateNotification
    actions:
      - id: sendNotification
        parameters:
        - name: Subject
          helpVariables:
          - Last Update Date:LastUpdatedDate:string
          fieldValues:
          - ~bill.notification.sendNotification.update.Subject
        - name: Message
          helpVariables:
          - Last Update Date:LastUpdatedDate:string
          fieldValues:
          - ~bill.notification.sendNotification.update.Message
        - name: SendTo
          fieldValues:
          - ~bill.notification.sendNotification.SendTo
        selected: true
  - id: invoiceapproval-multicondition
    actions:
    - id: sendCompanyEmail
      selected: true
    - id: createTask
      selected: true
      parameters:
      - name: CloseTask
        fieldValues:
          - txn_paid




