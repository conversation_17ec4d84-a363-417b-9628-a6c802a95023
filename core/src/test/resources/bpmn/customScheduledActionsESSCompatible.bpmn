<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_0ghypro" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.7.0">
  <bpmn:process id="customScheduledActions" isExecutable="true" camunda:historyTimeToLive="30">
    <bpmn:extensionElements>
      <camunda:properties>
        <camunda:property name="workflowName" value="customScheduledActions" />
      </camunda:properties>
    </bpmn:extensionElements>
    <bpmn:sequenceFlow id="Flow_07dv1jk" sourceRef="customStartEvent" targetRef="decisionElement" />
    <bpmn:businessRuleTask id="decisionElement" name="DMN Rule Processor" camunda:type="external" camunda:topic="custom-approval-nam">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{ "taskHandler": "was","handlerId":"was_dmn_evaluator","actionName": "evaluateDMN"}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_07dv1jk</bpmn:incoming>
      <bpmn:outgoing>ruleEvaluationTrue</bpmn:outgoing>
      <bpmn:outgoing>ruleEvaluationFalse</bpmn:outgoing>
    </bpmn:businessRuleTask>
    <bpmn:startEvent id="customStartEvent" name="start process">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="currentStepDetails" value="{ &#34;required&#34;: true }" />
          <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
          <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;was&#34;}" />
          <camunda:property name="startableEvents" value="[&#34;created&#34;]" />
          <camunda:property name="processVariablesDetails" value="[{     &#34;variableName&#34;: &#34;entityType&#34;,     &#34;variableType&#34;: &#34;String&#34;   },   {     &#34;variableName&#34;: &#34;entityChangeType&#34;,     &#34;variableType&#34;: &#34;String&#34;   },   {     &#34;variableName&#34;: &#34;Id&#34;,     &#34;variableType&#34;: &#34;String&#34;   },   {     &#34;variableName&#34;: &#34;intuit_userid&#34;,     &#34;variableType&#34;: &#34;String&#34;   },   {     &#34;variableName&#34;: &#34;intuit_realmid&#34;,     &#34;variableType&#34;: &#34;String&#34;   } ]" />
          <camunda:property name="stepDetails" value="{   &#34;customStartEvent&#34;: [     &#34;customStartEvent&#34;,     &#34;decisionElement&#34;,     &#34;scheduledAction&#34;   ] }" />
          <camunda:property name="recurrenceRule" value="{}" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_07dv1jk</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sendTask id="scheduledAction" name="Schedule Action" camunda:type="external" camunda:topic="custom-approval-nam">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="parameterDetails" />
          <camunda:inputParameter name="handlerDetails">{}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>ruleEvaluationTrue</bpmn:incoming>
      <bpmn:outgoing>Flow_1upx9on</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:sequenceFlow id="ruleEvaluationTrue" name="Rule evaluation true" sourceRef="decisionElement" targetRef="scheduledAction">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="EndEvent_0ayhj3y" name="End Process Event">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
              "taskHandler": "was",
              "handlerId": "",
              "actionName": "updateProcessStatus"
              }</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1upx9on</bpmn:incoming>
      <bpmn:messageEventDefinition messageRef="Message_0n66iem" camunda:type="external" camunda:topic="custom-approval-nam" />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1upx9on" sourceRef="scheduledAction" targetRef="EndEvent_0ayhj3y" />
    <bpmn:endEvent id="Event_1i1egkz" name="DMN evaluation false: End process">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
              "taskHandler": "was",
              "handlerId": "",
              "actionName": "updateProcessStatus"
              }</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>ruleEvaluationFalse</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_021bobf" messageRef="Message_0n66iem" camunda:type="external" camunda:topic="custom-approval-nam" />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="ruleEvaluationFalse" name="Rule evaluation false" sourceRef="decisionElement" targetRef="Event_1i1egkz">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmn:message id="Message_10uji9x" name="approved_rejected" />
  <bpmn:message id="Message_1lsccjr" name="customWait" />
  <bpmn:message id="Message_1vqffdi" name="end_process" />
  <bpmn:escalation id="Escalation_1f2mfy4" name="close_task" escalationCode="closetask" />
  <bpmn:message id="Message_0n66iem" name="process_ended_message" />
  <bpmn:escalation id="Escalation_0aq0q52" name="end_process" escalationCode="endprocess" />
  <bpmn:message id="Message_0tschbo" name="deleted_voided_disable" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="customScheduledActions">
      <bpmndi:BPMNShape id="Activity_0io4hr8_di" bpmnElement="decisionElement">
        <dc:Bounds x="300" y="250" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_08rwoar_di" bpmnElement="customStartEvent">
        <dc:Bounds x="172" y="272" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="158" y="248" width="63" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="SendTask_1mez1bd_di" bpmnElement="scheduledAction">
        <dc:Bounds x="550" y="250" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_19xjaph_di" bpmnElement="EndEvent_0ayhj3y">
        <dc:Bounds x="782" y="272" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="769" y="315" width="63" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1cnetou" bpmnElement="Event_1i1egkz">
        <dc:Bounds x="382" y="132" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="360" y="80" width="79" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_07dv1jk_di" bpmnElement="Flow_07dv1jk">
        <di:waypoint x="208" y="290" />
        <di:waypoint x="300" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05vmr6s_di" bpmnElement="ruleEvaluationTrue">
        <di:waypoint x="400" y="290" />
        <di:waypoint x="550" y="290" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="439" y="296" width="76" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1upx9on_di" bpmnElement="Flow_1upx9on">
        <di:waypoint x="650" y="290" />
        <di:waypoint x="782" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_039msd4_di" bpmnElement="ruleEvaluationFalse">
        <di:waypoint x="350" y="250" />
        <di:waypoint x="350" y="150" />
        <di:waypoint x="382" y="150" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="272" y="186" width="76" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
