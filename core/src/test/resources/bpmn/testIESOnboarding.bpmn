<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0tdkq4p" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.26.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.15.0">
  <bpmn:process id="testIESOnboarding" name="IES Onboarding Workflow" isExecutable="true" camunda:historyTimeToLive="3">
    <bpmn:startEvent id="startEvent" name="startEvent">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;:&#34;was&#34;,&#34;recordType&#34;:&#34;test&#34;}" />
          <camunda:property name="stepDetails" value="{&#34;startEvent&#34;:[&#34;startEvent&#34;,&#34;Account-Manager&#34;] }" />
          <camunda:property name="processVariablesDetails" value="[{&#34;variableName&#34;:&#34;userId&#34;,&#34;variableType&#34;:&#34;String&#34;},{&#34;variableName&#34;:&#34;assigneeId&#34;,&#34;variableType&#34;:&#34;String&#34;}]" />
          <camunda:property name="startableEvents" value="[&#34;created&#34;, &#34;updated&#34;]" />
          <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
          <camunda:property name="currentStepDetails" value="{ &#34;required&#34;: true }" />
          <camunda:property name="ignoreProcessVariablesDetails" value="true" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_13hxirh</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:subProcess id="Account-Manager" name="Account Manager">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="events" value="[&#34;start&#34;,&#34;end&#34;]" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_13hxirh</bpmn:incoming>
      <bpmn:outgoing>Flow_0rnmt4u</bpmn:outgoing>
      <bpmn:startEvent id="Event_0twfjqf">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="events" value="[&#34;start&#34;,&#34;customStart&#34;]" />
            <camunda:property name="beginStage" value="Account Manager" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:outgoing>Flow_01epbt8</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:serviceTask id="Signs-Contract" name="Signs Contract" camunda:type="external" camunda:topic="overwatch-test">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;:&#34;was&#34;,&#34;handlerId&#34;:&#34;intuit-workflows/test&#34;,&#34;actionName&#34;:&#34;test&#34;}" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_01epbt8</bpmn:incoming>
        <bpmn:outgoing>Flow_15bqlkg</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_01epbt8" sourceRef="Event_0twfjqf" targetRef="Signs-Contract" />
      <bpmn:serviceTask id="Company-Creation" name="Company Creation" camunda:type="external" camunda:topic="overwatch-test">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;:&#34;was&#34;,&#34;handlerId&#34;:&#34;intuit-workflows/test&#34;,&#34;actionName&#34;:&#34;test&#34;}" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_15bqlkg</bpmn:incoming>
        <bpmn:outgoing>Flow_1yuvbpp</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:endEvent id="Event_0tn59cb">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="events" value="[&#34;start&#34;]" />
            <camunda:property name="endStage" value="Account Manager" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1yuvbpp</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="Flow_1yuvbpp" sourceRef="Company-Creation" targetRef="Event_0tn59cb" />
      <bpmn:sequenceFlow id="Flow_15bqlkg" sourceRef="Signs-Contract" targetRef="Company-Creation" />
    </bpmn:subProcess>
    <bpmn:sequenceFlow id="Flow_13hxirh" sourceRef="startEvent" targetRef="Account-Manager" />
    <bpmn:subProcess id="Professional-Services" name="Professional Services">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="events" value="[&#34;start&#34;,&#34;end&#34;]" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0rnmt4u</bpmn:incoming>
      <bpmn:outgoing>Flow_0l28k1u</bpmn:outgoing>
      <bpmn:startEvent id="Event_183cvn7">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="events" value="[&#34;start&#34;]" />
            <camunda:property name="beginStage" value="Professional Services" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:outgoing>Flow_0jnxj3q</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:serviceTask id="Data-Migration" name="Data Migration" camunda:type="external" camunda:topic="overwatch-test">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;:&#34;was&#34;,&#34;handlerId&#34;:&#34;intuit-workflows/test&#34;,&#34;actionName&#34;:&#34;test&#34;}" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0jnxj3q</bpmn:incoming>
        <bpmn:outgoing>Flow_1brbyjt</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:endEvent id="Event_119sw88">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="events" value="[&#34;start&#34;]" />
            <camunda:property name="endStage" value="Professional Services" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0qnln8y</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:serviceTask id="Other-Services" name="Other Services" camunda:type="external" camunda:topic="overwatch-test">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;:&#34;was&#34;,&#34;handlerId&#34;:&#34;intuit-workflows/test&#34;,&#34;actionName&#34;:&#34;test&#34;}" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1brbyjt</bpmn:incoming>
        <bpmn:outgoing>Flow_0qnln8y</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_0jnxj3q" sourceRef="Event_183cvn7" targetRef="Data-Migration" />
      <bpmn:sequenceFlow id="Flow_0qnln8y" sourceRef="Other-Services" targetRef="Event_119sw88" />
      <bpmn:sequenceFlow id="Flow_1brbyjt" sourceRef="Data-Migration" targetRef="Other-Services" />
    </bpmn:subProcess>
    <bpmn:sequenceFlow id="Flow_0rnmt4u" sourceRef="Account-Manager" targetRef="Professional-Services" />
    <bpmn:subProcess id="CSM" name="CSM">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="events" value="[&#34;start&#34;,&#34;end&#34;]" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0l28k1u</bpmn:incoming>
      <bpmn:outgoing>Flow_16h23zo</bpmn:outgoing>
      <bpmn:startEvent id="Event_1d5h89m">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="events" value="[&#34;start&#34;]" />
            <camunda:property name="beginStage" value="CSM" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:outgoing>Flow_0rkwvva</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:serviceTask id="FMS-Guided-Setup" name="FMS Guided Setup" camunda:type="external" camunda:topic="overwatch-test">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;:&#34;was&#34;,&#34;handlerId&#34;:&#34;intuit-workflows/test&#34;,&#34;actionName&#34;:&#34;test&#34;}" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0rkwvva</bpmn:incoming>
        <bpmn:outgoing>Flow_0rfpw18</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:endEvent id="Event_0qxh979">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="events" value="[&#34;start&#34;]" />
            <camunda:property name="endStage" value="CSM" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0rfpw18</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="Flow_0rkwvva" sourceRef="Event_1d5h89m" targetRef="FMS-Guided-Setup" />
      <bpmn:sequenceFlow id="Flow_0rfpw18" sourceRef="FMS-Guided-Setup" targetRef="Event_0qxh979" />
    </bpmn:subProcess>
    <bpmn:sequenceFlow id="Flow_0l28k1u" sourceRef="Professional-Services" targetRef="CSM" />
    <bpmn:subProcess id="Onboarding-Product-Experts" name="Onboarding Product Experts">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="events" value="[&#34;start&#34;,&#34;end&#34;]" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_16h23zo</bpmn:incoming>
      <bpmn:outgoing>Flow_0n8sdpp</bpmn:outgoing>
      <bpmn:startEvent id="Event_1xwo60k">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="events" value="[&#34;start&#34;]" />
            <camunda:property name="beginStage" value="Onboarding Product Experts" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:outgoing>Flow_1o96bar</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:serviceTask id="Payroll-Elite-assisted-setup" name="Payroll Elite assisted setup" camunda:type="external" camunda:topic="overwatch-test">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;:&#34;was&#34;,&#34;handlerId&#34;:&#34;intuit-workflows/test&#34;,&#34;actionName&#34;:&#34;test&#34;}" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1o96bar</bpmn:incoming>
        <bpmn:outgoing>Flow_008y5gk</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:endEvent id="Event_1iu924a">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="events" value="[&#34;start&#34;]" />
            <camunda:property name="endStage" value="Onboarding Product Experts" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1ko7tos</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:serviceTask id="Activity_0ejmblc" name="Payment Bill Pay Setup" camunda:type="external" camunda:topic="overwatch-test">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;:&#34;was&#34;,&#34;handlerId&#34;:&#34;intuit-workflows/test&#34;,&#34;actionName&#34;:&#34;test&#34;}" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_008y5gk</bpmn:incoming>
        <bpmn:outgoing>Flow_1eae5ss</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_1o96bar" sourceRef="Event_1xwo60k" targetRef="Payroll-Elite-assisted-setup" />
      <bpmn:sequenceFlow id="Flow_1eae5ss" sourceRef="Activity_0ejmblc" targetRef="Mailchimp-assisted-setup" />
      <bpmn:sequenceFlow id="Flow_008y5gk" sourceRef="Payroll-Elite-assisted-setup" targetRef="Activity_0ejmblc" />
      <bpmn:serviceTask id="Mailchimp-assisted-setup" name="Mailchimp assisted setup" camunda:type="external" camunda:topic="overwatch-test">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;:&#34;was&#34;,&#34;handlerId&#34;:&#34;intuit-workflows/test&#34;,&#34;actionName&#34;:&#34;test&#34;}" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1eae5ss</bpmn:incoming>
        <bpmn:outgoing>Flow_1ko7tos</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_1ko7tos" sourceRef="Mailchimp-assisted-setup" targetRef="Event_1iu924a" />
    </bpmn:subProcess>
    <bpmn:sequenceFlow id="Flow_16h23zo" sourceRef="CSM" targetRef="Onboarding-Product-Experts" />
    <bpmn:subProcess id="Pooled-product-support" name="Pooled product support">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="events" value="[&#34;start&#34;,&#34;end&#34;]" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0n8sdpp</bpmn:incoming>
      <bpmn:outgoing>Flow_1eu00mc</bpmn:outgoing>
      <bpmn:startEvent id="Event_0ham5i3">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="events" value="[&#34;start&#34;]" />
            <camunda:property name="beginStage" value="Pooled product support" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:outgoing>Flow_0sinkqz</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:serviceTask id="Ongoing-support-and-proactive-outreach" name="Ongoing support and proactive outreach" camunda:type="external" camunda:topic="overwatch-test">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;:&#34;was&#34;,&#34;handlerId&#34;:&#34;intuit-workflows/test&#34;,&#34;actionName&#34;:&#34;test&#34;}" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0sinkqz</bpmn:incoming>
        <bpmn:outgoing>Flow_16apmiu</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:endEvent id="Event_1buip71">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="events" value="[&#34;start&#34;]" />
            <camunda:property name="endStage" value="Pooled product support" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_16apmiu</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="Flow_0sinkqz" sourceRef="Event_0ham5i3" targetRef="Ongoing-support-and-proactive-outreach" />
      <bpmn:sequenceFlow id="Flow_16apmiu" sourceRef="Ongoing-support-and-proactive-outreach" targetRef="Event_1buip71" />
    </bpmn:subProcess>
    <bpmn:sequenceFlow id="Flow_0n8sdpp" sourceRef="Onboarding-Product-Experts" targetRef="Pooled-product-support" />
    <bpmn:endEvent id="Event_0n6wtib" name="End">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1eu00mc</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1eu00mc" sourceRef="Pooled-product-support" targetRef="Event_0n6wtib" />
  </bpmn:process>
  <bpmn:message id="Message_0vz4vdy" name="process_ended_message" />
  <bpmn:message id="Message_0ric24b" name="approval_message" />
  <bpmn:message id="Message_1qsrxjc" name="isApproved" />
  <bpmn:message id="Message_05nttfg" name="approval" />
  <bpmn:message id="Message_1dwpbhh" name="process_ended_message" />
  <bpmn:message id="Message_18x561j" name="process_ended_message" />
  <bpmn:message id="Message_0wjcwyw" name="process_ended_message" />
  <bpmn:message id="Message_1utfbdd" name="process_ended_message" />
  <bpmn:message id="Message_0vaxstw" name="process_ended_message" />
  <bpmn:message id="Message_0testzn" name="process_ended_message" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="testIESOnboarding">
      <bpmndi:BPMNShape id="BPMNShape_0hrfa9k" bpmnElement="startEvent">
        <dc:Bounds x="202" y="122" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="195" y="161" width="50" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0u32cfc_di" bpmnElement="Account-Manager" isExpanded="true">
        <dc:Bounds x="340" y="80" width="530" height="130" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0twfjqf_di" bpmnElement="Event_0twfjqf">
        <dc:Bounds x="361.**************" y="132" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1k7a7dg" bpmnElement="Signs-Contract">
        <dc:Bounds x="470" y="110" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_13vavj6" bpmnElement="Company-Creation">
        <dc:Bounds x="650" y="110" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0tn59cb_di" bpmnElement="Event_0tn59cb">
        <dc:Bounds x="802" y="132" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_01epbt8_di" bpmnElement="Flow_01epbt8">
        <di:waypoint x="398" y="150" />
        <di:waypoint x="470" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1yuvbpp_di" bpmnElement="Flow_1yuvbpp">
        <di:waypoint x="750" y="150" />
        <di:waypoint x="802" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15bqlkg_di" bpmnElement="Flow_15bqlkg">
        <di:waypoint x="570" y="150" />
        <di:waypoint x="650" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="BPMNShape_0kl7oq6" bpmnElement="Professional-Services" isExpanded="true">
        <dc:Bounds x="340" y="250" width="530" height="130" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_19m8j9g" bpmnElement="Event_183cvn7">
        <dc:Bounds x="362" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0qd7a7c" bpmnElement="Data-Migration">
        <dc:Bounds x="470" y="280" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_00a919l" bpmnElement="Event_119sw88">
        <dc:Bounds x="802" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0p2rpwv" bpmnElement="Other-Services">
        <dc:Bounds x="650" y="280" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_0cpgjjf" bpmnElement="Flow_0jnxj3q">
        <di:waypoint x="398" y="320" />
        <di:waypoint x="470" y="320" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1q5znbj" bpmnElement="Flow_0qnln8y">
        <di:waypoint x="750" y="320" />
        <di:waypoint x="802" y="320" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1ckpgfj" bpmnElement="Flow_1brbyjt">
        <di:waypoint x="570" y="320" />
        <di:waypoint x="650" y="320" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="BPMNShape_0lavxu4" bpmnElement="CSM" isExpanded="true">
        <dc:Bounds x="415" y="420" width="380" height="130" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0metet3" bpmnElement="Event_1d5h89m">
        <dc:Bounds x="437" y="472" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0873rlo" bpmnElement="FMS-Guided-Setup">
        <dc:Bounds x="545" y="450" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1whnxdq" bpmnElement="Event_0qxh979">
        <dc:Bounds x="707" y="472" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_1dbt4dy" bpmnElement="Flow_0rkwvva">
        <di:waypoint x="473" y="490" />
        <di:waypoint x="545" y="490" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1wfcfx9" bpmnElement="Flow_0rfpw18">
        <di:waypoint x="645" y="490" />
        <di:waypoint x="707" y="490" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="BPMNShape_1u59ft4" bpmnElement="Onboarding-Product-Experts" isExpanded="true">
        <dc:Bounds x="330" y="580" width="550" height="130" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ncpzaw" bpmnElement="Event_1xwo60k">
        <dc:Bounds x="352" y="632" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0wr1b2u" bpmnElement="Payroll-Elite-assisted-setup">
        <dc:Bounds x="420" y="610" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0jj4nty" bpmnElement="Event_1iu924a">
        <dc:Bounds x="812" y="632" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1kze585" bpmnElement="Activity_0ejmblc">
        <dc:Bounds x="550" y="610" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0aul8zq" bpmnElement="Mailchimp-assisted-setup">
        <dc:Bounds x="680" y="610" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_0rxtiyl" bpmnElement="Flow_1o96bar">
        <di:waypoint x="388" y="650" />
        <di:waypoint x="420" y="650" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1gel0hb" bpmnElement="Flow_1eae5ss">
        <di:waypoint x="650" y="650" />
        <di:waypoint x="680" y="650" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0au6g49" bpmnElement="Flow_008y5gk">
        <di:waypoint x="520" y="650" />
        <di:waypoint x="550" y="650" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ko7tos_di" bpmnElement="Flow_1ko7tos">
        <di:waypoint x="780" y="650" />
        <di:waypoint x="812" y="650" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="BPMNShape_1v7fyr5" bpmnElement="Pooled-product-support" isExpanded="true">
        <dc:Bounds x="400" y="740" width="380" height="130" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1752tln" bpmnElement="Event_0ham5i3">
        <dc:Bounds x="422" y="792" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1fe4as4" bpmnElement="Ongoing-support-and-proactive-outreach">
        <dc:Bounds x="530" y="770" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0nila2p" bpmnElement="Event_1buip71">
        <dc:Bounds x="692" y="792" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_1avtdzi" bpmnElement="Flow_0sinkqz">
        <di:waypoint x="458" y="810" />
        <di:waypoint x="530" y="810" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1hrj64k" bpmnElement="Flow_16apmiu">
        <di:waypoint x="630" y="810" />
        <di:waypoint x="692" y="810" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_0n6wtib_di" bpmnElement="Event_0n6wtib">
        <dc:Bounds x="962" y="787" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="971" y="830" width="20" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_13hxirh_di" bpmnElement="Flow_13hxirh">
        <di:waypoint x="238" y="140" />
        <di:waypoint x="340" y="140" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rnmt4u_di" bpmnElement="Flow_0rnmt4u">
        <di:waypoint x="605" y="210" />
        <di:waypoint x="605" y="250" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0l28k1u_di" bpmnElement="Flow_0l28k1u">
        <di:waypoint x="605" y="380" />
        <di:waypoint x="605" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16h23zo_di" bpmnElement="Flow_16h23zo">
        <di:waypoint x="595" y="550" />
        <di:waypoint x="595" y="580" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n8sdpp_di" bpmnElement="Flow_0n8sdpp">
        <di:waypoint x="590" y="710" />
        <di:waypoint x="590" y="740" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1eu00mc_di" bpmnElement="Flow_1eu00mc">
        <di:waypoint x="780" y="805" />
        <di:waypoint x="962" y="805" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
