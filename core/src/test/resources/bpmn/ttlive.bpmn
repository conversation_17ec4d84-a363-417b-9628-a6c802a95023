<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:bioc="http://bpmn.io/schema/bpmn/biocolor/1.0" id="Definitions_1fho3ib" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="3.4.1">
  <bpmn:message id="Message_1fy3zel" name="assign_engagement_true" />
  <bpmn:collaboration id="TTLiveFullServiceWFF">
    <bpmn:participant id="Participant_0no1f6f" name="Full Service" processRef="engagementvepfstest1" />
  </bpmn:collaboration>
  <bpmn:process id="engagementvepfstest1" name="engagementvepfstest1" isExecutable="true">
    <bpmn:laneSet id="LaneSet_1tj65xu" />
    <bpmn:startEvent id="startEvent" name="startEvent" camunda:asyncBefore="true">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{ &#34;recordType&#34;: &#34;engagement&#34;}" />
          <camunda:property name="stepDetails" value="{   &#34;startEvent&#34;: [     &#34;startEvent&#34;   ] }" />
          <camunda:property name="startableEvents" value="[&#34;created&#34;]" />
          <camunda:property name="processVariablesDetails" value="[{&#34;variableName&#34;:&#34;engagementId&#34;,&#34;variableType&#34;:&#34;String&#34;},{&#34;variableName&#34;:&#34;firmId&#34;,&#34;variableType&#34;:&#34;String&#34;},{&#34;variableName&#34;:&#34;customerAccountId&#34;,&#34;variableType&#34;:&#34;String&#34;}]" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>SequenceFlow_1b3hr1x</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:subProcess id="Activity_0gwf49y" name="Gathering Info">
      <bpmn:incoming>SequenceFlow_1b3hr1x</bpmn:incoming>
      <bpmn:outgoing>Flow_02y45dv</bpmn:outgoing>
      <bpmn:serviceTask id="Activity_1vkbba0" name="Create user tasks for gathering info" camunda:type="external" camunda:topic="engagement">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-test",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="projectConfig">
              <camunda:map>
                <camunda:entry key="TY19 Full Service #1 . B. Gathering Info . A. Client Intake">{     "alias": "TY19 Full Service #1 . B. Gathering Info . A. Client Intake",     "templateId": [       {         "env": "qal",         "value": "559573"       },       {         "env": "qa",         "value": "559573"       },       {         "env": "e2e",         "value": "559573"       },       {         "env": "prod",         "value": "559573"       }     ],     "projectName": [       {         "env": "qal",         "value": "559573. Full Service Client Intake"       },       {         "env": "qa",         "value": "559573. Full Service Client Intake"       },       {         "env": "e2e",         "value": "559573. Full Service Client Intake"       },       {         "env": "prod",         "value": "559573. Client Intake"       }     ]   }</camunda:entry>
                <camunda:entry key="TY19 Full Service #1 . B. Gathering Info . B. Client Verification">{     "alias": "TY19 Full Service #1 . B. Gathering Info . B. Client Verification",     "templateId": [       {         "env": "qal",         "value": "559573"       },       {         "env": "qa",         "value": "559573"       },       {         "env": "e2e",         "value": "559573"       },       {         "env": "prod",         "value": "559573"       }     ],     "projectName": [       {         "env": "qal",         "value": "559573. Full Service Client Verification"       },       {         "env": "qa",         "value": "559573. Full Service Client Verification"       },       {         "env": "e2e",         "value": "559573. Full Service Client Verification"       },       {         "env": "prod",         "value": "559573. Client Verification"       }     ]   }</camunda:entry>
                <camunda:entry key="TY19 Full Service #1 . B. Gathering Info . C. Scope">{     "alias": "TY19 Full Service #1 . B. Gathering Info . C. Scope",     "templateId": [       {         "env": "qal",         "value": "559573"       },       {         "env": "qa",         "value": "559573"       },       {         "env": "e2e",         "value": "559573"       },       {         "env": "prod",         "value": "559573"       }     ],     "projectName": [       {         "env": "qal",         "value": "559573. Full Service Scope"       },       {         "env": "qa",         "value": "559573. Full Service Scope"       },       {         "env": "e2e",         "value": "559573. Full Service Scope"       },       {         "env": "prod",         "value": "559573. Scope"       }     ]   }</camunda:entry>
                <camunda:entry key="TY19 Full Service #1 . B. Gathering Info . D. Data Collection (24 hrs)">{     "alias": "TY19 Full Service #1 . B. Gathering Info . D. Data Collection (24 hrs)",     "templateId": [       {         "env": "qal",         "value": "559573"       },       {         "env": "qa",         "value": "559573"       },       {         "env": "e2e",         "value": "559573"       },       {         "env": "prod",         "value": "559573"       }     ],     "projectName": [       {         "env": "qal",         "value": "559573 Service Data Collection"       },       {         "env": "qa",         "value": "559573 Service Data Collection"       },       {         "env": "e2e",         "value": "559573 Service Data Collection"       },       {         "env": "prod",         "value": "559573. Data Collection (24 hrs)"       }     ]   }</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1unyim9</bpmn:incoming>
        <bpmn:outgoing>Flow_1aexlc0</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:endEvent id="Event_0wbl7oj">
        <bpmn:incoming>Flow_0e5jaig</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:intermediateCatchEvent id="Event_0bvsmsg" name="IEP Milestone Complete">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="engagementMessageEventName">SHOULD_NOT_MATCH</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1aexlc0</bpmn:incoming>
        <bpmn:outgoing>Flow_0e5jaig</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_102m65w" messageRef="Message_1ji3lo4" />
      </bpmn:intermediateCatchEvent>
      <bpmn:startEvent id="Event_1htumfp">
        <bpmn:outgoing>Flow_1unyim9</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:sequenceFlow id="Flow_1unyim9" sourceRef="Event_1htumfp" targetRef="Activity_1vkbba0" />
      <bpmn:sequenceFlow id="Flow_0e5jaig" sourceRef="Event_0bvsmsg" targetRef="Event_0wbl7oj" />
      <bpmn:sequenceFlow id="Flow_1aexlc0" sourceRef="Activity_1vkbba0" targetRef="Event_0bvsmsg" />
      <bpmn:textAnnotation id="TextAnnotation_17cdzpc">
        <bpmn:text>topic =  engagement</bpmn:text>
      </bpmn:textAnnotation>
      <bpmn:association id="Association_0xowj2s" sourceRef="Activity_1vkbba0" targetRef="TextAnnotation_17cdzpc" />
    </bpmn:subProcess>
    <bpmn:serviceTask id="Activity_0w5be17" name="Update Milestone and Status" camunda:type="external" camunda:topic="engagement">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
              <camunda:entry key="method">PATCH</camunda:entry>
              <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{milestoneStatus}"}</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1738l9i</bpmn:incoming>
      <bpmn:outgoing>Flow_00u7ie3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="Event_0taocf1" name="Milestone:- Gathering info complete">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="milestone">Gathering Info</camunda:outputParameter>
          <camunda:outputParameter name="milestoneStatus">COMPLETED</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1p5jjvf</bpmn:incoming>
      <bpmn:incoming>Flow_02y45dv</bpmn:incoming>
      <bpmn:outgoing>Flow_1738l9i</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_1p5jjvf" sourceRef="Event_1kkktpi" targetRef="Event_0taocf1" />
    <bpmn:sequenceFlow id="Flow_1738l9i" sourceRef="Event_0taocf1" targetRef="Activity_0w5be17" />
    <bpmn:subProcess id="Activity_1kamh9f" name="Prepapre Return">
      <bpmn:incoming>Flow_1hdj1vo</bpmn:incoming>
      <bpmn:outgoing>Flow_0n0fseg</bpmn:outgoing>
      <bpmn:serviceTask id="Activity_1v1v4cv" name="Create user tasks for Preparing Return" camunda:type="external" camunda:topic="engagement">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-test",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="projectConfig">
              <camunda:map>
                <camunda:entry key="TY19 Full Service #1 . C. Preparing Return . A. Return Preparation">{     "alias": "TY19 Full Service #1 . C. Preparing Return . A. Return Preparation",     "templateId": [       {         "env": "qal",         "value": "559573"       },       {         "env": "qa",         "value": "559573"       },       {         "env": "e2e",         "value": "559573"       },       {         "env": "prod",         "value": "559573"       }     ],     "projectName": [       {         "env": "qal",         "value": "559573. Full Service Return Preparation"       },       {         "env": "qa",         "value": "559573. Full Service Return Preparation"       },       {         "env": "e2e",         "value": "559573. Full Service Return Preparation"       },       {         "env": "prod",         "value": "559573. Return Preparation"       }     ]   }</camunda:entry>
                <camunda:entry key="TY19 Full Service #1 . C. Preparing Return . B. Preparation Checklist">{     "alias": "TY19 Full Service #1 . C. Preparing Return . B. Preparation Checklist",     "templateId": [       {         "env": "qal",         "value": "559573"       },       {         "env": "qa",         "value": "559573"       },       {         "env": "e2e",         "value": "559573"       },       {         "env": "prod",         "value": "559573"       }     ],     "projectName": [       {         "env": "qal",         "value": "559573. Full Service Preparation Checklist"       },       {         "env": "qa",         "value": "559573. Full Service Preparation Checklist"       },       {         "env": "e2e",         "value": "559573. Full Service Preparation Checklist"       },       {         "env": "prod",         "value": "559573. Preparation Checklist"       }     ]   }</camunda:entry>
                <camunda:entry key="TY19 Full Service #1 . C. Preparing Return . C. Self Review">{     "alias": "TY19 Full Service #1 . C. Preparing Return . C. Self Review",     "templateId": [       {         "env": "qal",         "value": "559573"       },       {         "env": "qa",         "value": "559573"       },       {         "env": "e2e",         "value": "559573"       },       {         "env": "prod",         "value": "559573"       }     ],     "projectName": [       {         "env": "qal",         "value": "559573. Full Service Self Review"       },       {         "env": "qa",         "value": "559573. Full Service Self Review"       },       {         "env": "e2e",         "value": "559573. Full Service Self Review"       },       {         "env": "prod",         "value": "559573. Self Review"       }     ]   }</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1xs03yy</bpmn:incoming>
        <bpmn:outgoing>Flow_0lbyg6f</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:endEvent id="Event_1g8e9kq">
        <bpmn:incoming>Flow_05oaonr</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:intermediateCatchEvent id="Event_10heztf" name="IEP Milestone Complete">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="engagementMessageEventName">SHOULD_NOT_MATCH</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0lbyg6f</bpmn:incoming>
        <bpmn:outgoing>Flow_05oaonr</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_1sy9y7d" messageRef="Message_1ji3lo4" />
      </bpmn:intermediateCatchEvent>
      <bpmn:sequenceFlow id="Flow_0lbyg6f" sourceRef="Activity_1v1v4cv" targetRef="Event_10heztf" />
      <bpmn:sequenceFlow id="Flow_05oaonr" sourceRef="Event_10heztf" targetRef="Event_1g8e9kq" />
      <bpmn:startEvent id="Event_12rs4pk">
        <bpmn:outgoing>Flow_1xs03yy</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:sequenceFlow id="Flow_1xs03yy" sourceRef="Event_12rs4pk" targetRef="Activity_1v1v4cv" />
    </bpmn:subProcess>
    <bpmn:subProcess id="Activity_0xiheb7" name="Evaluation">
      <bpmn:incoming>Flow_0k69urg</bpmn:incoming>
      <bpmn:outgoing>Flow_14wzwvf</bpmn:outgoing>
      <bpmn:serviceTask id="Activity_0cujhu8" name="Create user tasks for Evaluation" camunda:type="external" camunda:topic="engagement">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-test",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="projectConfig">
              <camunda:map>
                <camunda:entry key="TY19 Full Service #1 . D. Evaluation . A. Return Evaluation - SET Checklist">{     "alias": "TY19 Full Service #1 . D. Evaluation . A. Return Evaluation - SET Checklist",     "templateId": [       {         "env": "qal",         "value": "559573"       },       {         "env": "qa",         "value": "559573"       },       {         "env": "e2e",         "value": "559573"       },       {         "env": "prod",         "value": "559573"       }     ],     "projectName": [       {         "env": "qal",         "value": "559573. Full Service SET Checklist"       },       {         "env": "qa",         "value": "559573. Full Service SET Checklist"       },       {         "env": "e2e",         "value": "559573. Full Service SET Checklist"       },       {         "env": "prod",         "value": "559573. Return Evaluation - SET Checklist"       }     ]   }</camunda:entry>
                <camunda:entry key="TY19 Full Service #1 . D. Evaluation . B. Signer Revisions">{     "alias": "TY19 Full Service #1 . D. Evaluation . B. Signer Revisions",     "templateId": [       {         "env": "qal",         "value": "559573"       },       {         "env": "qa",         "value": "559573"       },       {         "env": "e2e",         "value": "559573"       },       {         "env": "prod",         "value": "559573"       }     ],     "projectName": [       {         "env": "qal",         "value": "559573. Full Service Signer Revisions"       },       {         "env": "qa",         "value": "559573. Full Service Signer Revisions"       },       {         "env": "e2e",         "value": "559573. Full Service Signer Revisions"       },       {         "env": "prod",         "value": "559573. Signer Revisions"       }     ]   }</camunda:entry>
                <camunda:entry key="TY19 Full Service #1 . D. Evaluation . C. SET Approval">{     "alias": "TY19 Full Service #1 . D. Evaluation . C. SET Approval",     "templateId": [       {         "env": "qal",         "value": "559573"       },       {         "env": "qa",         "value": "559573"       },       {         "env": "e2e",         "value": "559573"       },       {         "env": "prod",         "value": "559573"       }     ],     "projectName": [       {         "env": "qal",         "value": "559573. Full Service SET Approval"       },       {         "env": "qa",         "value": "559573. Full Service SET Approval"       },       {         "env": "e2e",         "value": "559573. Full Service SET Approval"       },       {         "env": "prod",         "value": "559573. SET Approval"       }     ]   }</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1t60fls</bpmn:incoming>
        <bpmn:outgoing>Flow_0hde8v9</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:endEvent id="Event_0wyy4qy">
        <bpmn:incoming>Flow_0c9vu39</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:startEvent id="Event_09ibsht">
        <bpmn:outgoing>Flow_1t60fls</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:sequenceFlow id="Flow_1t60fls" sourceRef="Event_09ibsht" targetRef="Activity_0cujhu8" />
      <bpmn:intermediateCatchEvent id="Event_0eek1gg" name="IEP Milestone Complete">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="engagementMessageEventName">SHOULD_NOT_MATCH</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0hde8v9</bpmn:incoming>
        <bpmn:outgoing>Flow_0c9vu39</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_1mwnkho" messageRef="Message_1ji3lo4" />
      </bpmn:intermediateCatchEvent>
      <bpmn:sequenceFlow id="Flow_0c9vu39" sourceRef="Event_0eek1gg" targetRef="Event_0wyy4qy" />
      <bpmn:sequenceFlow id="Flow_0hde8v9" sourceRef="Activity_0cujhu8" targetRef="Event_0eek1gg" />
    </bpmn:subProcess>
    <bpmn:sequenceFlow id="Flow_00u7ie3" sourceRef="Activity_0w5be17" targetRef="Event_0lpdkco" />
    <bpmn:intermediateThrowEvent id="Event_0lpdkco" name="Milestone:- Preparing Return In Progress">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="milestone">Preparing Return</camunda:outputParameter>
          <camunda:outputParameter name="milestoneStatus">IN_PROGRESS</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_00u7ie3</bpmn:incoming>
      <bpmn:outgoing>Flow_0bp5mx5</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_0bp5mx5" sourceRef="Event_0lpdkco" targetRef="Activity_1frxv9a" />
    <bpmn:serviceTask id="Activity_1frxv9a" name="Update Milestone and Status" camunda:type="external" camunda:topic="engagement">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
              <camunda:entry key="method">PATCH</camunda:entry>
              <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{milestoneStatus}"}</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0bp5mx5</bpmn:incoming>
      <bpmn:outgoing>Flow_1hdj1vo</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1hdj1vo" sourceRef="Activity_1frxv9a" targetRef="Activity_1kamh9f" />
    <bpmn:intermediateThrowEvent id="Event_16j46em" name="Milestone:- Preparing Return complete">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="milestone">Preparing Return</camunda:outputParameter>
          <camunda:outputParameter name="milestoneStatus">COMPLETED</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0n0fseg</bpmn:incoming>
      <bpmn:outgoing>Flow_0sp1wsl</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:serviceTask id="Activity_0un42m9" name="Update Milestone and Status" camunda:type="external" camunda:topic="engagement">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
              <camunda:entry key="method">PATCH</camunda:entry>
              <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{milestoneStatus}"}</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0sp1wsl</bpmn:incoming>
      <bpmn:outgoing>Flow_1x365jd</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="Event_0vg7u0d" name="Milestone:- Evaluation: In Progress">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="milestone">Evaluation</camunda:outputParameter>
          <camunda:outputParameter name="milestoneStatus">IN_PROGRESS</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1x365jd</bpmn:incoming>
      <bpmn:outgoing>Flow_1aisklg</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:serviceTask id="Activity_1vysmsx" name="Update Milestone and Status" camunda:type="external" camunda:topic="engagement">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
              <camunda:entry key="method">PATCH</camunda:entry>
              <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{milestoneStatus}"}</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1aisklg</bpmn:incoming>
      <bpmn:outgoing>Flow_0k69urg</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0sp1wsl" sourceRef="Event_16j46em" targetRef="Activity_0un42m9" />
    <bpmn:sequenceFlow id="Flow_1x365jd" sourceRef="Activity_0un42m9" targetRef="Event_0vg7u0d" />
    <bpmn:sequenceFlow id="Flow_1aisklg" sourceRef="Event_0vg7u0d" targetRef="Activity_1vysmsx" />
    <bpmn:sequenceFlow id="Flow_0n0fseg" sourceRef="Activity_1kamh9f" targetRef="Event_16j46em" />
    <bpmn:sequenceFlow id="Flow_0k69urg" sourceRef="Activity_1vysmsx" targetRef="Activity_0xiheb7" />
    <bpmn:subProcess id="Activity_0qlxed6" triggeredByEvent="true">
      <bpmn:endEvent id="Event_0jc3rf5">
        <bpmn:incoming>Flow_0yhgy8t</bpmn:incoming>
        <bpmn:terminateEventDefinition id="TerminateEventDefinition_0ri87iv" />
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="Flow_0yhgy8t" sourceRef="Activity_1anpgzg" targetRef="Event_0jc3rf5" />
      <bpmn:serviceTask id="Activity_1anpgzg" name="Close&#10;Workflow&#10;(StepFn)" camunda:type="external" camunda:topic="engagement">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="engagementRequest">
              <camunda:map>
                <camunda:entry key="path">/v1/engagement/#{engagementId}/deep</camunda:entry>
                <camunda:entry key="method">GET</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_04wkft9</bpmn:incoming>
        <bpmn:outgoing>Flow_0yhgy8t</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:startEvent id="Event_02ztdgm" name="Close Engagement">
        <bpmn:outgoing>Flow_04wkft9</bpmn:outgoing>
        <bpmn:escalationEventDefinition id="EscalationEventDefinition_0vu93ih" escalationRef="Escalation_188xam6" />
      </bpmn:startEvent>
      <bpmn:sequenceFlow id="Flow_04wkft9" sourceRef="Event_02ztdgm" targetRef="Activity_1anpgzg" />
    </bpmn:subProcess>
    <bpmn:subProcess id="Activity_0z8v9a5" triggeredByEvent="true">
      <bpmn:serviceTask id="Activity_1omyshr" name="Revoke&#10;Workflow&#10;(StepFn)" camunda:type="external" camunda:topic="engagement">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="engagementRequest">
              <camunda:map>
                <camunda:entry key="path">/v1/engagement/#{engagementId}/deep</camunda:entry>
                <camunda:entry key="method">GET</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0i248x5</bpmn:incoming>
        <bpmn:outgoing>Flow_0x20x9u</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_0x20x9u" sourceRef="Activity_1omyshr" targetRef="Event_1ry2j2j" />
      <bpmn:endEvent id="Event_1ry2j2j">
        <bpmn:incoming>Flow_0x20x9u</bpmn:incoming>
        <bpmn:terminateEventDefinition id="TerminateEventDefinition_1hmntax" />
      </bpmn:endEvent>
      <bpmn:startEvent id="Event_13ii4wq" name="Revoke Engagement">
        <bpmn:outgoing>Flow_0i248x5</bpmn:outgoing>
        <bpmn:escalationEventDefinition id="EscalationEventDefinition_1ik1upk" escalationRef="Escalation_0x6jr3b" />
      </bpmn:startEvent>
      <bpmn:sequenceFlow id="Flow_0i248x5" sourceRef="Event_13ii4wq" targetRef="Activity_1omyshr" />
    </bpmn:subProcess>
    <bpmn:intermediateThrowEvent id="Event_15yweic" name="Close Engagement">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="closeReason">CLOSED</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0obllbu</bpmn:incoming>
      <bpmn:escalationEventDefinition id="EscalationEventDefinition_1v06q4g" escalationRef="Escalation_188xam6" />
    </bpmn:intermediateThrowEvent>
    <bpmn:intermediateCatchEvent id="Event_1o2yi3w" name="Close Engagement">
      <bpmn:incoming>Flow_0kwctl1</bpmn:incoming>
      <bpmn:outgoing>Flow_0obllbu</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_17qi9d5" messageRef="Message_0ya9e3t" />
    </bpmn:intermediateCatchEvent>
    <bpmn:subProcess id="Activity_1c8ggpq" triggeredByEvent="true">
      <bpmn:intermediateThrowEvent id="Event_1ldgoxs" name="Revoke Engagement">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:outputParameter name="closeReason">CLOSED</camunda:outputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0b6qnmh</bpmn:incoming>
        <bpmn:escalationEventDefinition id="EscalationEventDefinition_051ye0e" escalationRef="Escalation_0x6jr3b" />
      </bpmn:intermediateThrowEvent>
      <bpmn:sequenceFlow id="Flow_0b6qnmh" sourceRef="Event_05ibtcw" targetRef="Event_1ldgoxs" />
      <bpmn:startEvent id="Event_05ibtcw" name="Revoke Engagement">
        <bpmn:outgoing>Flow_0b6qnmh</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_1v497v0" messageRef="Message_0mwl4zo" />
      </bpmn:startEvent>
    </bpmn:subProcess>
    <bpmn:sequenceFlow id="Flow_0obllbu" sourceRef="Event_1o2yi3w" targetRef="Event_15yweic" />
    <bpmn:boundaryEvent id="Event_1kkktpi" name="Override Milestone" attachedToRef="Activity_0gwf49y">
      <bpmn:outgoing>Flow_1p5jjvf</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1net6ht" messageRef="Message_1qijmpo" />
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_02y45dv" sourceRef="Activity_0gwf49y" targetRef="Event_0taocf1" />
    <bpmn:subProcess id="Activity_003o1lo" name="Client Review">
      <bpmn:incoming>Flow_1usso3c</bpmn:incoming>
      <bpmn:outgoing>Flow_19c3nll</bpmn:outgoing>
      <bpmn:endEvent id="Event_1a3brgt">
        <bpmn:incoming>Flow_0a3e3z6</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:startEvent id="Event_0qlelft">
        <bpmn:outgoing>Flow_17mkdmk</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:intermediateCatchEvent id="Event_12eg1vj" name="IEP Milestone Complete">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="engagementMessageEventName">SHOULD_NOT_MATCH</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_09fddpw</bpmn:incoming>
        <bpmn:outgoing>Flow_0a3e3z6</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_0xrj542" messageRef="Message_1ji3lo4" />
      </bpmn:intermediateCatchEvent>
      <bpmn:sequenceFlow id="Flow_0a3e3z6" sourceRef="Event_12eg1vj" targetRef="Event_1a3brgt" />
      <bpmn:sequenceFlow id="Flow_17mkdmk" sourceRef="Event_0qlelft" targetRef="Activity_1qs4o2e" />
      <bpmn:serviceTask id="Activity_1qs4o2e" name="Create user tasks for Client Review" camunda:type="external" camunda:topic="engagement">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-test",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="projectConfig">
              <camunda:map>
                <camunda:entry key="TY19 Full Service #1 . D. Evaluation . C. SET Approval">{     "alias": "TY19 Full Service #1 . D. Evaluation . C. SET Approval",     "templateId": [       {         "env": "qal",         "value": "559573"       },       {         "env": "qa",         "value": "559573"       },       {         "env": "e2e",         "value": "559573"       },       {         "env": "prod",         "value": "559573"       }     ],     "projectName": [       {         "env": "qal",         "value": "559573. Full Service SET Approval"       },       {         "env": "qa",         "value": "559573. Full Service SET Approval"       },       {         "env": "e2e",         "value": "559573. Full Service SET Approval"       },       {         "env": "prod",         "value": "559573. SET Approval"       }     ]   }</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_17mkdmk</bpmn:incoming>
        <bpmn:outgoing>Flow_09fddpw</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_09fddpw" sourceRef="Activity_1qs4o2e" targetRef="Event_12eg1vj" />
    </bpmn:subProcess>
    <bpmn:subProcess id="Activity_0oyxp3l" name="E-file authorization">
      <bpmn:incoming>Flow_19c3nll</bpmn:incoming>
      <bpmn:outgoing>Flow_0491mpp</bpmn:outgoing>
      <bpmn:endEvent id="Event_17p6cmx">
        <bpmn:incoming>Flow_0ew70t7</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:startEvent id="Event_09oxpla">
        <bpmn:outgoing>Flow_0ze4cnc</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:intermediateCatchEvent id="Event_1avadqc" name="IEP Milestone Complete">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="engagementMessageEventName">SHOULD_NOT_MATCH</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_08n9r7o</bpmn:incoming>
        <bpmn:outgoing>Flow_0ew70t7</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_0jnyk1r" messageRef="Message_1ji3lo4" />
      </bpmn:intermediateCatchEvent>
      <bpmn:sequenceFlow id="Flow_0ew70t7" sourceRef="Event_1avadqc" targetRef="Event_17p6cmx" />
      <bpmn:sequenceFlow id="Flow_0ze4cnc" sourceRef="Event_09oxpla" targetRef="Activity_0rhjtjw" />
      <bpmn:serviceTask id="Activity_0rhjtjw" name="Setup&#10;(StepFn)" camunda:type="external" camunda:topic="engagement">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-stepfunctions",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="engagementRequest">
              <camunda:map>
                <camunda:entry key="path">/v1/engagement/#{engagementId}/deep</camunda:entry>
                <camunda:entry key="method">GET</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0ze4cnc</bpmn:incoming>
        <bpmn:outgoing>Flow_08n9r7o</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_08n9r7o" sourceRef="Activity_0rhjtjw" targetRef="Event_1avadqc" />
    </bpmn:subProcess>
    <bpmn:subProcess id="Activity_10pisli" name="Ready to file">
      <bpmn:incoming>Flow_00zzglj</bpmn:incoming>
      <bpmn:outgoing>Flow_00701rn</bpmn:outgoing>
      <bpmn:endEvent id="Event_0u3v7ul">
        <bpmn:incoming>Flow_0z28h9d</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:startEvent id="Event_1scxnxc">
        <bpmn:outgoing>Flow_0fcssqj</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:intermediateCatchEvent id="Event_0y2jobd" name="IEP Milestone Complete">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="engagementMessageEventName">SHOULD_NOT_MATCH</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0ociqaw</bpmn:incoming>
        <bpmn:outgoing>Flow_0z28h9d</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_082tqf1" messageRef="Message_1ji3lo4" />
      </bpmn:intermediateCatchEvent>
      <bpmn:sequenceFlow id="Flow_0z28h9d" sourceRef="Event_0y2jobd" targetRef="Event_0u3v7ul" />
      <bpmn:sequenceFlow id="Flow_0fcssqj" sourceRef="Event_1scxnxc" targetRef="Activity_1re7q2s" />
      <bpmn:serviceTask id="Activity_1re7q2s" name="Setup&#10;(StepFn)" camunda:type="external" camunda:topic="engagement">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-stepfunctions",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="engagementRequest">
              <camunda:map>
                <camunda:entry key="path">/v1/engagement/#{engagementId}/deep</camunda:entry>
                <camunda:entry key="method">GET</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0fcssqj</bpmn:incoming>
        <bpmn:outgoing>Flow_0ociqaw</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_0ociqaw" sourceRef="Activity_1re7q2s" targetRef="Event_0y2jobd" />
      <bpmn:textAnnotation id="TextAnnotation_14ncbqk">
        <bpmn:text>Complete E-Sig Stepfunc</bpmn:text>
      </bpmn:textAnnotation>
      <bpmn:association id="Association_1o1w79d" sourceRef="Activity_1re7q2s" targetRef="TextAnnotation_14ncbqk" />
    </bpmn:subProcess>
    <bpmn:subProcess id="Activity_0toojjz" name="Filing">
      <bpmn:incoming>Flow_00701rn</bpmn:incoming>
      <bpmn:outgoing>Flow_1dcczkj</bpmn:outgoing>
      <bpmn:endEvent id="Event_1u60xye">
        <bpmn:incoming>Flow_1px2th3</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:startEvent id="Event_0toqm0l">
        <bpmn:outgoing>Flow_1npd6gg</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:intermediateCatchEvent id="Event_12u5p74" name="Filing Success">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="engagementMessageEventName">SHOULD_NOT_MATCH</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1npd6gg</bpmn:incoming>
        <bpmn:outgoing>Flow_1px2th3</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_03t3532" messageRef="Message_0ya9e3t" />
      </bpmn:intermediateCatchEvent>
      <bpmn:sequenceFlow id="Flow_1px2th3" sourceRef="Event_12u5p74" targetRef="Event_1u60xye" />
      <bpmn:sequenceFlow id="Flow_1npd6gg" sourceRef="Event_0toqm0l" targetRef="Event_12u5p74" />
    </bpmn:subProcess>
    <bpmn:subProcess id="Activity_14j9dp5" name="Post File">
      <bpmn:incoming>Flow_00krvl5</bpmn:incoming>
      <bpmn:outgoing>Flow_0kwctl1</bpmn:outgoing>
      <bpmn:serviceTask id="Activity_1mrgl3t" name="Create user tasks for Post File" camunda:type="external" camunda:topic="engagement">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-test",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="projectConfig">
              <camunda:map>
                <camunda:entry key="TY19 Full Service #1 . D. Evaluation . A. Return Evaluation - SET Checklist">{     "alias": "TY19 Full Service #1 . D. Evaluation . A. Return Evaluation - SET Checklist",     "templateId": [       {         "env": "qal",         "value": "559573"       },       {         "env": "qa",         "value": "559573"       },       {         "env": "e2e",         "value": "559573"       },       {         "env": "prod",         "value": "559573"       }     ],     "projectName": [       {         "env": "qal",         "value": "559573. Full Service SET Checklist"       },       {         "env": "qa",         "value": "559573. Full Service SET Checklist"       },       {         "env": "e2e",         "value": "559573. Full Service SET Checklist"       },       {         "env": "prod",         "value": "559573. Return Evaluation - SET Checklist"       }     ]   }</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1isbelj</bpmn:incoming>
        <bpmn:outgoing>Flow_1hs6lne</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:endEvent id="Event_1yukpet">
        <bpmn:incoming>Flow_0bkq76t</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:startEvent id="Event_16aqp0r">
        <bpmn:outgoing>Flow_1isbelj</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:intermediateCatchEvent id="Event_0fcoetx" name="IEP Milestone Complete">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="engagementMessageEventName">SHOULD_NOT_MATCH</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1hs6lne</bpmn:incoming>
        <bpmn:outgoing>Flow_0bkq76t</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_1aj2rcv" messageRef="Message_1ji3lo4" />
      </bpmn:intermediateCatchEvent>
      <bpmn:sequenceFlow id="Flow_1hs6lne" sourceRef="Activity_1mrgl3t" targetRef="Event_0fcoetx" />
      <bpmn:sequenceFlow id="Flow_0bkq76t" sourceRef="Event_0fcoetx" targetRef="Event_1yukpet" />
      <bpmn:sequenceFlow id="Flow_1isbelj" sourceRef="Event_16aqp0r" targetRef="Activity_1mrgl3t" />
    </bpmn:subProcess>
    <bpmn:sequenceFlow id="Flow_14wzwvf" sourceRef="Activity_0xiheb7" targetRef="Event_01hx2qe" />
    <bpmn:serviceTask id="Activity_0snmt16" name="Update Milestone and Status" camunda:type="external" camunda:topic="engagement">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
              <camunda:entry key="method">PATCH</camunda:entry>
              <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{milestoneStatus}"}</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1ojix3p</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="Event_19u0m3r" name="Milestone:- Evaluation Complete">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="milestone">Evaluation</camunda:outputParameter>
          <camunda:outputParameter name="milestoneStatus">COMPLETED</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_1ojix3p</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_1ojix3p" sourceRef="Event_19u0m3r" targetRef="Activity_0snmt16" />
    <bpmn:intermediateThrowEvent id="Event_01hx2qe" name="Milestone:- Client Review In Progress">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="milestone">Client Review</camunda:outputParameter>
          <camunda:outputParameter name="milestoneStatus">IN_PROGRESS</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_14wzwvf</bpmn:incoming>
      <bpmn:outgoing>Flow_0apq8kk</bpmn:outgoing>
      <bpmn:outgoing>Flow_1usso3c</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:serviceTask id="Activity_0vtyrj9" name="Update Milestone and Status" camunda:type="external" camunda:topic="engagement">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
              <camunda:entry key="method">PATCH</camunda:entry>
              <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{milestoneStatus}"}</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0apq8kk</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0apq8kk" sourceRef="Event_01hx2qe" targetRef="Activity_0vtyrj9" />
    <bpmn:sequenceFlow id="Flow_1usso3c" sourceRef="Event_01hx2qe" targetRef="Activity_003o1lo" />
    <bpmn:sequenceFlow id="Flow_19c3nll" sourceRef="Activity_003o1lo" targetRef="Activity_0oyxp3l" />
    <bpmn:intermediateThrowEvent id="Event_1hie2g9" name="Milestone:- Client Review Complete">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="milestone">Evaluation</camunda:outputParameter>
          <camunda:outputParameter name="milestoneStatus">COMPLETED</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_1nnotqn</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:serviceTask id="Activity_13o8pw0" name="Update Milestone and Status" camunda:type="external" camunda:topic="engagement">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
              <camunda:entry key="method">PATCH</camunda:entry>
              <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{milestoneStatus}"}</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1nnotqn</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="Event_0aeqn13" name="Milestone:- E-file authorization In Progress">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="milestone">E-file authorization</camunda:outputParameter>
          <camunda:outputParameter name="milestoneStatus">IN_PROGRESS</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_1mmf9ok</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:serviceTask id="Activity_04oocme" name="Update Milestone and Status" camunda:type="external" camunda:topic="engagement">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
              <camunda:entry key="method">PATCH</camunda:entry>
              <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{milestoneStatus}"}</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1mmf9ok</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1nnotqn" sourceRef="Event_1hie2g9" targetRef="Activity_13o8pw0" />
    <bpmn:sequenceFlow id="Flow_1mmf9ok" sourceRef="Event_0aeqn13" targetRef="Activity_04oocme" />
    <bpmn:sequenceFlow id="Flow_0491mpp" sourceRef="Activity_0oyxp3l" targetRef="Event_0lh57zs" />
    <bpmn:intermediateThrowEvent id="Event_12qqblu" name="Milestone:- E-file authorization Complete">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="milestone">E-file authorization</camunda:outputParameter>
          <camunda:outputParameter name="milestoneStatus">COMPLETED</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_17sttrj</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:serviceTask id="Activity_1qnbfat" name="Update Milestone and Status" camunda:type="external" camunda:topic="engagement">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
              <camunda:entry key="method">PATCH</camunda:entry>
              <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{milestoneStatus}"}</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_17sttrj</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="Event_0q8zzzo" name="Milestone:- Ready to file In Progress">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="milestone">Ready to file</camunda:outputParameter>
          <camunda:outputParameter name="milestoneStatus">IN_PROGRESS</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0hje5cv</bpmn:incoming>
      <bpmn:outgoing>Flow_0lmrlzs</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:serviceTask id="Activity_0itjeua" name="Update Milestone and Status" camunda:type="external" camunda:topic="engagement">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
              <camunda:entry key="method">PATCH</camunda:entry>
              <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{milestoneStatus}"}</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0lmrlzs</bpmn:incoming>
      <bpmn:outgoing>Flow_00zzglj</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_00701rn" sourceRef="Activity_10pisli" targetRef="Activity_0toojjz" />
    <bpmn:intermediateThrowEvent id="Event_1nv1gnt" name="Milestone:- E-file authorization Complete">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="milestone">E-file authorization</camunda:outputParameter>
          <camunda:outputParameter name="milestoneStatus">COMPLETED</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_0lrki6g</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:serviceTask id="Activity_0pjcr6d" name="Update Milestone and Status" camunda:type="external" camunda:topic="engagement">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
              <camunda:entry key="method">PATCH</camunda:entry>
              <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{milestoneStatus}"}</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0lrki6g</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="Event_1kvxi4x" name="Milestone:- Filing In Progress">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="milestone">Filing</camunda:outputParameter>
          <camunda:outputParameter name="milestoneStatus">IN_PROGRESS</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_008mewy</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:serviceTask id="Activity_1j0j0yj" name="Update Milestone and Status" camunda:type="external" camunda:topic="engagement">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
              <camunda:entry key="method">PATCH</camunda:entry>
              <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{milestoneStatus}"}</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_008mewy</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0lrki6g" sourceRef="Event_1nv1gnt" targetRef="Activity_0pjcr6d" />
    <bpmn:sequenceFlow id="Flow_008mewy" sourceRef="Event_1kvxi4x" targetRef="Activity_1j0j0yj" />
    <bpmn:sequenceFlow id="Flow_1dcczkj" sourceRef="Activity_0toojjz" targetRef="Event_1fvw15m" />
    <bpmn:intermediateThrowEvent id="Event_1fvw15m" name="Milestone:- Filing Complete">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="milestone">Filing</camunda:outputParameter>
          <camunda:outputParameter name="milestoneStatus">COMPLETED</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1dcczkj</bpmn:incoming>
      <bpmn:outgoing>Flow_1lvc49p</bpmn:outgoing>
      <bpmn:outgoing>Flow_14cufwa</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:serviceTask id="Activity_19zohgj" name="Update Milestone and Status" camunda:type="external" camunda:topic="engagement">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
              <camunda:entry key="method">PATCH</camunda:entry>
              <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{milestoneStatus}"}</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1lvc49p</bpmn:incoming>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="Event_13ke950" name="Milestone:- Post File In Progress">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="milestone">Post File</camunda:outputParameter>
          <camunda:outputParameter name="milestoneStatus">IN_PROGRESS</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_14cufwa</bpmn:incoming>
      <bpmn:outgoing>Flow_1dx4bqy</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:serviceTask id="Activity_1gawg7e" name="Update Milestone and Status" camunda:type="external" camunda:topic="engagement">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
              <camunda:entry key="method">PATCH</camunda:entry>
              <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{milestoneStatus}"}</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1dx4bqy</bpmn:incoming>
      <bpmn:outgoing>Flow_00krvl5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1lvc49p" sourceRef="Event_1fvw15m" targetRef="Activity_19zohgj" />
    <bpmn:sequenceFlow id="Flow_1dx4bqy" sourceRef="Event_13ke950" targetRef="Activity_1gawg7e" />
    <bpmn:sequenceFlow id="Flow_00krvl5" sourceRef="Activity_1gawg7e" targetRef="Activity_14j9dp5" />
    <bpmn:sequenceFlow id="Flow_14cufwa" sourceRef="Event_1fvw15m" targetRef="Event_13ke950" />
    <bpmn:sequenceFlow id="Flow_0kwctl1" sourceRef="Activity_14j9dp5" targetRef="Event_1o2yi3w" />
    <bpmn:sequenceFlow id="Flow_00zzglj" sourceRef="Activity_0itjeua" targetRef="Activity_10pisli" />
    <bpmn:sequenceFlow id="Flow_0lmrlzs" sourceRef="Event_0q8zzzo" targetRef="Activity_0itjeua" />
    <bpmn:sequenceFlow id="Flow_17sttrj" sourceRef="Event_12qqblu" targetRef="Activity_1qnbfat" />
    <bpmn:intermediateCatchEvent id="Event_0lh57zs" name="E-Signature Complete">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="engagementMessageEventName">SHOULD_NOT_MATCH</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0491mpp</bpmn:incoming>
      <bpmn:outgoing>Flow_0hje5cv</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1sn2opw" messageRef="Message_0otezgn" />
    </bpmn:intermediateCatchEvent>
    <bpmn:sequenceFlow id="Flow_0hje5cv" sourceRef="Event_0lh57zs" targetRef="Event_0q8zzzo" />
    <bpmn:sequenceFlow id="SequenceFlow_1b3hr1x" sourceRef="startEvent" targetRef="Activity_0gwf49y" />
  </bpmn:process>
  <bpmn:message id="Message_10mibh2" name="assign_engagement_true" />
  <bpmn:message id="Message_1ji3lo4" name="approved" />
  <bpmn:escalation id="Escalation_188xam6" name="Close Engagement" escalationCode="engagement_close" />
  <bpmn:message id="Message_1qijmpo" name="voided" />
  <bpmn:message id="Message_0ya9e3t" name="deposited" />
  <bpmn:message id="Message_0mwl4zo" name="disconnected" />
  <bpmn:escalation id="Escalation_0x6jr3b" name="Revoke Engagement" escalationCode="engagement_revoke" />
  <bpmn:error id="Error_0jlaxyu" name="Any Exception" errorCode="com.intuit.spp.vep.integrations.core.error.ProjectException" />
  <bpmn:message id="Message_0nrohfn" name="sent" />
  <bpmn:message id="Message_0otezgn" name="updated" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="engagementvepfstest1">
      <bpmndi:BPMNEdge id="Flow_0hje5cv_di" bpmnElement="Flow_0hje5cv">
        <di:waypoint x="5676" y="384" />
        <di:waypoint x="5732" y="384" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_17sttrj_di" bpmnElement="Flow_17sttrj">
        <di:waypoint x="5438" y="384" />
        <di:waypoint x="5480" y="384" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lmrlzs_di" bpmnElement="Flow_0lmrlzs">
        <di:waypoint x="5768" y="384" />
        <di:waypoint x="5800" y="384" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00zzglj_di" bpmnElement="Flow_00zzglj">
        <di:waypoint x="5900" y="384" />
        <di:waypoint x="5980" y="384" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kwctl1_di" bpmnElement="Flow_0kwctl1">
        <di:waypoint x="7910" y="384" />
        <di:waypoint x="8002" y="384" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14cufwa_di" bpmnElement="Flow_14cufwa">
        <di:waypoint x="7188" y="374" />
        <di:waypoint x="7372" y="374" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00krvl5_di" bpmnElement="Flow_00krvl5">
        <di:waypoint x="7550" y="374" />
        <di:waypoint x="7610" y="374" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1dx4bqy_di" bpmnElement="Flow_1dx4bqy">
        <di:waypoint x="7408" y="374" />
        <di:waypoint x="7450" y="374" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1lvc49p_di" bpmnElement="Flow_1lvc49p">
        <di:waypoint x="7188" y="374" />
        <di:waypoint x="7220" y="374" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1dcczkj_di" bpmnElement="Flow_1dcczkj">
        <di:waypoint x="7130" y="374" />
        <di:waypoint x="7152" y="374" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_008mewy_di" bpmnElement="Flow_008mewy">
        <di:waypoint x="6598" y="374" />
        <di:waypoint x="6660" y="374" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lrki6g_di" bpmnElement="Flow_0lrki6g">
        <di:waypoint x="6348" y="374" />
        <di:waypoint x="6390" y="374" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00701rn_di" bpmnElement="Flow_00701rn">
        <di:waypoint x="6270" y="374" />
        <di:waypoint x="6830" y="374" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0491mpp_di" bpmnElement="Flow_0491mpp">
        <di:waypoint x="5350" y="384" />
        <di:waypoint x="5640" y="384" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mmf9ok_di" bpmnElement="Flow_1mmf9ok">
        <di:waypoint x="4828" y="384" />
        <di:waypoint x="4890" y="384" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1nnotqn_di" bpmnElement="Flow_1nnotqn">
        <di:waypoint x="4578" y="384" />
        <di:waypoint x="4620" y="384" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19c3nll_di" bpmnElement="Flow_19c3nll">
        <di:waypoint x="4500" y="384" />
        <di:waypoint x="5050" y="384" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1usso3c_di" bpmnElement="Flow_1usso3c">
        <di:waypoint x="3928" y="384" />
        <di:waypoint x="4200" y="384" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0apq8kk_di" bpmnElement="Flow_0apq8kk">
        <di:waypoint x="3928" y="384" />
        <di:waypoint x="3990" y="384" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ojix3p_di" bpmnElement="Flow_1ojix3p">
        <di:waypoint x="3678" y="384" />
        <di:waypoint x="3720" y="384" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14wzwvf_di" bpmnElement="Flow_14wzwvf">
        <di:waypoint x="3580" y="384" />
        <di:waypoint x="3892" y="384" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02y45dv_di" bpmnElement="Flow_02y45dv">
        <di:waypoint x="1830" y="400" />
        <di:waypoint x="1882" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0obllbu_di" bpmnElement="Flow_0obllbu">
        <di:waypoint x="8038" y="384" />
        <di:waypoint x="8112" y="384" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0k69urg_di" bpmnElement="Flow_0k69urg">
        <di:waypoint x="3210" y="384" />
        <di:waypoint x="3280" y="384" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n0fseg_di" bpmnElement="Flow_0n0fseg">
        <di:waypoint x="2690" y="384" />
        <di:waypoint x="2792" y="384" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1aisklg_di" bpmnElement="Flow_1aisklg">
        <di:waypoint x="3068" y="384" />
        <di:waypoint x="3110" y="384" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1x365jd_di" bpmnElement="Flow_1x365jd">
        <di:waypoint x="2990" y="384" />
        <di:waypoint x="3032" y="384" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0sp1wsl_di" bpmnElement="Flow_0sp1wsl">
        <di:waypoint x="2828" y="384" />
        <di:waypoint x="2890" y="384" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hdj1vo_di" bpmnElement="Flow_1hdj1vo">
        <di:waypoint x="2300" y="400" />
        <di:waypoint x="2370" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bp5mx5_di" bpmnElement="Flow_0bp5mx5">
        <di:waypoint x="2158" y="400" />
        <di:waypoint x="2200" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00u7ie3_di" bpmnElement="Flow_00u7ie3">
        <di:waypoint x="2080" y="400" />
        <di:waypoint x="2122" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1738l9i_di" bpmnElement="Flow_1738l9i">
        <di:waypoint x="1918" y="400" />
        <di:waypoint x="1980" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1p5jjvf_di" bpmnElement="Flow_1p5jjvf">
        <di:waypoint x="1848" y="330" />
        <di:waypoint x="1900" y="330" />
        <di:waypoint x="1900" y="382" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_1gj7am4_di" bpmnElement="startEvent">
        <dc:Bounds x="149" y="382" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="143" y="425" width="50" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0gwf49y_di" bpmnElement="Activity_0gwf49y" isExpanded="true">
        <dc:Bounds x="1510" y="110" width="320" height="579" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1aexlc0_di" bpmnElement="Flow_1aexlc0">
        <di:waypoint x="1660" y="280" />
        <di:waypoint x="1660" y="509" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0e5jaig_di" bpmnElement="Flow_0e5jaig">
        <di:waypoint x="1678" y="527" />
        <di:waypoint x="1772" y="527" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1unyim9_di" bpmnElement="Flow_1unyim9">
        <di:waypoint x="1568" y="240" />
        <di:waypoint x="1610" y="240" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_1vkbba0_di" bpmnElement="Activity_1vkbba0">
        <dc:Bounds x="1610" y="200" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0wbl7oj_di" bpmnElement="Event_0wbl7oj">
        <dc:Bounds x="1772" y="509" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0bvsmsg_di" bpmnElement="Event_0bvsmsg">
        <dc:Bounds x="1642" y="509" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1556" y="503" width="68" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1htumfp_di" bpmnElement="Event_1htumfp">
        <dc:Bounds x="1532" y="222" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_17cdzpc_di" bpmnElement="TextAnnotation_17cdzpc">
        <dc:Bounds x="1600" y="130" width="100" height="39" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Association_0xowj2s_di" bpmnElement="Association_0xowj2s">
        <di:waypoint x="1655" y="200" />
        <di:waypoint x="1653" y="169" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_0w5be17_di" bpmnElement="Activity_0w5be17" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="1980" y="360" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0taocf1_di" bpmnElement="Event_0taocf1" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="1882" y="382" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1865" y="428" width="70" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1kamh9f_di" bpmnElement="Activity_1kamh9f" isExpanded="true">
        <dc:Bounds x="2370" y="107" width="320" height="586" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1xs03yy_di" bpmnElement="Flow_1xs03yy">
        <di:waypoint x="2428" y="235" />
        <di:waypoint x="2475" y="235" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05oaonr_di" bpmnElement="Flow_05oaonr">
        <di:waypoint x="2543" y="515" />
        <di:waypoint x="2632" y="515" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lbyg6f_di" bpmnElement="Flow_0lbyg6f">
        <di:waypoint x="2525" y="275" />
        <di:waypoint x="2525" y="497" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_1v1v4cv_di" bpmnElement="Activity_1v1v4cv">
        <dc:Bounds x="2475" y="195" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1g8e9kq_di" bpmnElement="Event_1g8e9kq">
        <dc:Bounds x="2632" y="497" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_10heztf_di" bpmnElement="Event_10heztf">
        <dc:Bounds x="2507" y="497" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2486" y="543" width="68" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_12rs4pk_di" bpmnElement="Event_12rs4pk">
        <dc:Bounds x="2392" y="217" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0xiheb7_di" bpmnElement="Activity_0xiheb7" isExpanded="true">
        <dc:Bounds x="3280" y="100" width="300" height="568" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0hde8v9_di" bpmnElement="Flow_0hde8v9">
        <di:waypoint x="3430" y="268" />
        <di:waypoint x="3430" y="500" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0c9vu39_di" bpmnElement="Flow_0c9vu39">
        <di:waypoint x="3448" y="518" />
        <di:waypoint x="3502" y="518" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1t60fls_di" bpmnElement="Flow_1t60fls">
        <di:waypoint x="3338" y="228" />
        <di:waypoint x="3380" y="228" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_0cujhu8_di" bpmnElement="Activity_0cujhu8">
        <dc:Bounds x="3380" y="188" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0wyy4qy_di" bpmnElement="Event_0wyy4qy">
        <dc:Bounds x="3502" y="500" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_09ibsht_di" bpmnElement="Event_09ibsht">
        <dc:Bounds x="3302" y="210" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0eek1gg_di" bpmnElement="Event_0eek1gg">
        <dc:Bounds x="3412" y="500" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3395" y="546" width="68" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0lpdkco_di" bpmnElement="Event_0lpdkco" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="2122" y="382" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2098" y="320" width="84" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1frxv9a_di" bpmnElement="Activity_1frxv9a" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="2200" y="360" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_16j46em_di" bpmnElement="Event_16j46em" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="2792" y="366" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2768" y="304" width="84" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0un42m9_di" bpmnElement="Activity_0un42m9" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="2890" y="344" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0vg7u0d_di" bpmnElement="Event_0vg7u0d" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="3032" y="366" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3018" y="304" width="67" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1vysmsx_di" bpmnElement="Activity_1vysmsx" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="3110" y="344" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1wa6ipz_di" bpmnElement="Activity_0qlxed6" isExpanded="true">
        <dc:Bounds x="160" y="770" width="500" height="200" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_04wkft9_di" bpmnElement="Flow_04wkft9">
        <di:waypoint x="236" y="870" />
        <di:waypoint x="360" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yhgy8t_di" bpmnElement="Flow_0yhgy8t">
        <di:waypoint x="460" y="870" />
        <di:waypoint x="592" y="870" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_017zlez_di" bpmnElement="Event_0jc3rf5">
        <dc:Bounds x="592" y="852" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1anpgzg_di" bpmnElement="Activity_1anpgzg" bioc:stroke="rgb(229, 57, 53)" bioc:fill="rgb(255, 205, 210)">
        <dc:Bounds x="360" y="830" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0bjl1yq_di" bpmnElement="Event_02ztdgm">
        <dc:Bounds x="200" y="852" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="188" y="895" width="63" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1h05dsb_di" bpmnElement="Activity_0z8v9a5" isExpanded="true">
        <dc:Bounds x="160" y="1030" width="500" height="200" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0i248x5_di" bpmnElement="Flow_0i248x5">
        <di:waypoint x="238" y="1130" />
        <di:waypoint x="360" y="1130" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0x20x9u_di" bpmnElement="Flow_0x20x9u">
        <di:waypoint x="460" y="1130" />
        <di:waypoint x="592" y="1130" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_1omyshr_di" bpmnElement="Activity_1omyshr" bioc:stroke="rgb(229, 57, 53)" bioc:fill="rgb(255, 205, 210)">
        <dc:Bounds x="360" y="1090" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1752qvy_di" bpmnElement="Event_1ry2j2j">
        <dc:Bounds x="592" y="1112" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_13ii4wq_di" bpmnElement="Event_13ii4wq">
        <dc:Bounds x="202" y="1112" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="190" y="1155" width="63" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_15qm403_di" bpmnElement="Event_15yweic">
        <dc:Bounds x="8112" y="366" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="8099" y="409" width="63" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1o2yi3w_di" bpmnElement="Event_1o2yi3w">
        <dc:Bounds x="8002" y="366" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7989" y="409" width="63" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0xtvh2x_di" bpmnElement="Activity_1c8ggpq" isExpanded="true">
        <dc:Bounds x="160" y="1260" width="500" height="200" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0b6qnmh_di" bpmnElement="Flow_0b6qnmh">
        <di:waypoint x="238" y="1350" />
        <di:waypoint x="562" y="1350" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_1ldgoxs_di" bpmnElement="Event_1ldgoxs">
        <dc:Bounds x="562" y="1332" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="549" y="1375" width="63" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_15bsgnx_di" bpmnElement="Event_05ibtcw">
        <dc:Bounds x="202" y="1332" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="189" y="1375" width="63" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_003o1lo_di" bpmnElement="Activity_003o1lo" isExpanded="true">
        <dc:Bounds x="4200" y="100" width="300" height="568" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_09fddpw_di" bpmnElement="Flow_09fddpw">
        <di:waypoint x="4350" y="268" />
        <di:waypoint x="4350" y="500" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_17mkdmk_di" bpmnElement="Flow_17mkdmk">
        <di:waypoint x="4258" y="228" />
        <di:waypoint x="4300" y="228" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0a3e3z6_di" bpmnElement="Flow_0a3e3z6">
        <di:waypoint x="4368" y="518" />
        <di:waypoint x="4422" y="518" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_1a3brgt_di" bpmnElement="Event_1a3brgt">
        <dc:Bounds x="4422" y="500" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0qlelft_di" bpmnElement="Event_0qlelft">
        <dc:Bounds x="4222" y="210" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_12eg1vj_di" bpmnElement="Event_12eg1vj">
        <dc:Bounds x="4332" y="500" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4315" y="546" width="68" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1qs4o2e_di" bpmnElement="Activity_1qs4o2e">
        <dc:Bounds x="4300" y="188" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0oyxp3l_di" bpmnElement="Activity_0oyxp3l" isExpanded="true">
        <dc:Bounds x="5050" y="100" width="300" height="568" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_08n9r7o_di" bpmnElement="Flow_08n9r7o">
        <di:waypoint x="5200" y="268" />
        <di:waypoint x="5200" y="500" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ze4cnc_di" bpmnElement="Flow_0ze4cnc">
        <di:waypoint x="5108" y="228" />
        <di:waypoint x="5150" y="228" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ew70t7_di" bpmnElement="Flow_0ew70t7">
        <di:waypoint x="5218" y="518" />
        <di:waypoint x="5272" y="518" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_17p6cmx_di" bpmnElement="Event_17p6cmx">
        <dc:Bounds x="5272" y="500" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_09oxpla_di" bpmnElement="Event_09oxpla">
        <dc:Bounds x="5072" y="210" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1avadqc_di" bpmnElement="Event_1avadqc">
        <dc:Bounds x="5182" y="500" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5165" y="546" width="68" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0rhjtjw_di" bpmnElement="Activity_0rhjtjw" bioc:stroke="rgb(229, 57, 53)" bioc:fill="rgb(255, 205, 210)">
        <dc:Bounds x="5150" y="188" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_10pisli_di" bpmnElement="Activity_10pisli" isExpanded="true">
        <dc:Bounds x="5980" y="80" width="290" height="588" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0ociqaw_di" bpmnElement="Flow_0ociqaw">
        <di:waypoint x="6130" y="268" />
        <di:waypoint x="6130" y="500" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fcssqj_di" bpmnElement="Flow_0fcssqj">
        <di:waypoint x="6038" y="228" />
        <di:waypoint x="6080" y="228" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0z28h9d_di" bpmnElement="Flow_0z28h9d">
        <di:waypoint x="6148" y="518" />
        <di:waypoint x="6202" y="518" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_0u3v7ul_di" bpmnElement="Event_0u3v7ul">
        <dc:Bounds x="6202" y="500" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1scxnxc_di" bpmnElement="Event_1scxnxc">
        <dc:Bounds x="6002" y="210" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0y2jobd_di" bpmnElement="Event_0y2jobd">
        <dc:Bounds x="6112" y="500" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6095" y="546" width="68" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1re7q2s_di" bpmnElement="Activity_1re7q2s" bioc:stroke="rgb(229, 57, 53)" bioc:fill="rgb(255, 205, 210)">
        <dc:Bounds x="6080" y="188" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_14ncbqk_di" bpmnElement="TextAnnotation_14ncbqk">
        <dc:Bounds x="6030" y="130" width="99.99305040770942" height="40.02965159377317" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Association_1o1w79d_di" bpmnElement="Association_1o1w79d">
        <di:waypoint x="6106" y="188" />
        <di:waypoint x="6095" y="170" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_0toojjz_di" bpmnElement="Activity_0toojjz" isExpanded="true">
        <dc:Bounds x="6830" y="80" width="300" height="578" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1npd6gg_di" bpmnElement="Flow_1npd6gg">
        <di:waypoint x="6888" y="370" />
        <di:waypoint x="6962" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1px2th3_di" bpmnElement="Flow_1px2th3">
        <di:waypoint x="6998" y="370" />
        <di:waypoint x="7072" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_1u60xye_di" bpmnElement="Event_1u60xye">
        <dc:Bounds x="7072" y="352" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0toqm0l_di" bpmnElement="Event_0toqm0l">
        <dc:Bounds x="6852" y="352" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_12u5p74_di" bpmnElement="Event_12u5p74">
        <dc:Bounds x="6962" y="352" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6944" y="398" width="71" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_14j9dp5_di" bpmnElement="Activity_14j9dp5" isExpanded="true">
        <dc:Bounds x="7610" y="90" width="300" height="568" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1isbelj_di" bpmnElement="Flow_1isbelj">
        <di:waypoint x="7668" y="218" />
        <di:waypoint x="7710" y="218" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bkq76t_di" bpmnElement="Flow_0bkq76t">
        <di:waypoint x="7778" y="508" />
        <di:waypoint x="7832" y="508" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hs6lne_di" bpmnElement="Flow_1hs6lne">
        <di:waypoint x="7760" y="258" />
        <di:waypoint x="7760" y="490" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_1mrgl3t_di" bpmnElement="Activity_1mrgl3t">
        <dc:Bounds x="7710" y="178" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1yukpet_di" bpmnElement="Event_1yukpet">
        <dc:Bounds x="7832" y="490" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_16aqp0r_di" bpmnElement="Event_16aqp0r">
        <dc:Bounds x="7632" y="200" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0fcoetx_di" bpmnElement="Event_0fcoetx">
        <dc:Bounds x="7742" y="490" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7725" y="536" width="68" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0snmt16_di" bpmnElement="Activity_0snmt16" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="3720" y="344" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_19u0m3r_di" bpmnElement="Event_19u0m3r" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="3642" y="366" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3635" y="304" width="54" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_01hx2qe_di" bpmnElement="Event_01hx2qe" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="3892" y="366" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3872" y="304" width="80" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0vtyrj9_di" bpmnElement="Activity_0vtyrj9" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="3990" y="344" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1hie2g9_di" bpmnElement="Event_1hie2g9" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="4542" y="366" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4529" y="304" width="68" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_13o8pw0_di" bpmnElement="Activity_13o8pw0" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="4620" y="344" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0aeqn13_di" bpmnElement="Event_0aeqn13" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="4792" y="366" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4774" y="304" width="76" height="53" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_04oocme_di" bpmnElement="Activity_04oocme" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="4890" y="344" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_12qqblu_di" bpmnElement="Event_12qqblu" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="5402" y="366" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5391" y="304" width="63" height="53" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1qnbfat_di" bpmnElement="Activity_1qnbfat" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="5480" y="344" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0q8zzzo_di" bpmnElement="Event_0q8zzzo" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="5732" y="366" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5715" y="304" width="74" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0itjeua_di" bpmnElement="Activity_0itjeua" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="5800" y="344" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1nv1gnt_di" bpmnElement="Event_1nv1gnt" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="6312" y="356" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6301" y="294" width="63" height="53" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0pjcr6d_di" bpmnElement="Activity_0pjcr6d" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="6390" y="334" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1kvxi4x_di" bpmnElement="Event_1kvxi4x" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="6562" y="356" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6539" y="294" width="86" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1j0j0yj_di" bpmnElement="Activity_1j0j0yj" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="6660" y="334" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1fvw15m_di" bpmnElement="Event_1fvw15m" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="7152" y="356" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7134" y="294" width="77" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_19zohgj_di" bpmnElement="Activity_19zohgj" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="7220" y="334" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_13ke950_di" bpmnElement="Event_13ke950" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="7372" y="356" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7364" y="294" width="56" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1gawg7e_di" bpmnElement="Activity_1gawg7e" bioc:stroke="rgb(30, 136, 229)" bioc:fill="rgb(187, 222, 251)">
        <dc:Bounds x="7450" y="334" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0lh57zs_di" bpmnElement="Event_0lh57zs">
        <dc:Bounds x="5640" y="366" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5628" y="412" width="59" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_15j7q8r_di" bpmnElement="Event_1kkktpi">
        <dc:Bounds x="1812" y="312" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1756" y="316" width="48" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1b3hr1x_di" bpmnElement="SequenceFlow_1b3hr1x">
        <di:waypoint x="185" y="400" />
        <di:waypoint x="1510" y="400" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
