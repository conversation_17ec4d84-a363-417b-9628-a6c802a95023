<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/********/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI" xmlns:dc="http://www.omg.org/spec/DD/********/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/********/DI" id="Definitions_143oe0r" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.4.0">
    <bpmn:process id="depositbankremindersingle" name="Bank deposit reminder" isExecutable="true" camunda:historyTimeToLive="14">
        <bpmn:extensionElements>
            <camunda:properties>
                <camunda:property name="workflowName" value="bankreminder" />
                <camunda:property name="description" value="Remind yourself or your team to record bank deposits." />
            </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:startEvent id="newEvent_bankdepositremindersingle" name="Undeposited fund event">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;: &#34;appconnect&#34;, &#34;handlerId&#34;: &#34;intuit-workflows/undeposited-funds-start&#34;, &#34;actionName&#34;: &#34;executeWorkflowAction&#34;, &#34;recordType&#34;: &#34;deposit&#34; }" />
                    <camunda:property name="stepDetails" value=" {   &#34;newEvent_bankdepositremindersingle&#34;: [&#34;newEvent_bankdepositremindersingle&#34;,&#34;decision_bankdepositremindersingle&#34;,     &#34;sendNotification_bankdepositremindersingle&#34;, &#34;createProjectServiceTask_bankdepositremindersingle&#34; ], &#34;waitForDeposit_bankdepositremindersingle&#34;: [&#34;waitForDeposit_bankdepositremindersingle&#34;] }" />
                    <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
                    <camunda:property name="currentStepDetails" value="{&#34;required&#34;: true}" />
                    <camunda:property name="processVariablesDetails" value="[{&#34;variableName&#34;:&#34;UndepositedFunds&#34;, &#34;variableType&#34;: &#34;Double&#34;}, {&#34;variableName&#34;:&#34;entityChangeType&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;Id&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;intuit_userid&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;intuit_realmid&#34;, &#34;variableType&#34;: &#34;String&#34;},{&#34;variableName&#34;:&#34;Company Name&#34;, &#34;variableType&#34;: &#34;String&#34;},{&#34;variableName&#34;:&#34;Company Email&#34;, &#34;variableType&#34;: &#34;String&#34;}]" />
                    <camunda:property name="startableEvents" value="[&#34;updated&#34;]" />
                    <camunda:property name="targetApi" value="evaluate-and-trigger" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:outgoing>SequenceFlow_194fy7b</bpmn:outgoing>
        </bpmn:startEvent>
        <bpmn:businessRuleTask id="decision_bankdepositremindersingle" name="Evaluate funds" camunda:type="external" camunda:topic="bankdepositaaggarwal1">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="parameterDetails">{
                        "resultVariable":  {
                        "fieldValue": ["decisionResult"],
                        "requiredByHandler": true
                        }
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
                    <camunda:inputParameter name="handlerDetails">{ "taskHandler": "was","handlerId":"decision_customReminder","actionName": "evaluateDMN"}</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_194fy7b</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_09kebvy</bpmn:outgoing>
            <bpmn:outgoing>SequenceFlow_13q91qp</bpmn:outgoing>
            <bpmn:outgoing>SequenceFlow_11lx0rw</bpmn:outgoing>
        </bpmn:businessRuleTask>
        <bpmn:sequenceFlow id="SequenceFlow_194fy7b" sourceRef="newEvent_bankdepositremindersingle" targetRef="decision_bankdepositremindersingle" />
        <bpmn:sequenceFlow id="SequenceFlow_09kebvy" sourceRef="decision_bankdepositremindersingle" targetRef="sendNotification_bankdepositremindersingle">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${sendReminder == true}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="SequenceFlow_13q91qp" sourceRef="decision_bankdepositremindersingle" targetRef="createProjectServiceTask_bankdepositremindersingle">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${sendReminder == true}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="SequenceFlow_1y8t9k9" sourceRef="createProjectServiceTask_bankdepositremindersingle" targetRef="ExclusiveGateway_0qqiix7" />
        <bpmn:sendTask id="sendNotification_bankdepositremindersingle" name="Send notification" camunda:type="external" camunda:topic="bankdepositaaggarwal1">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="parameterDetails">{
                        "Approver #1": {
                        "fieldValue": [],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": "GET_ADMINS_ID",
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Send To": {
                        "fieldValue": [],
                        "configurable": true,
                        "actionByUI": "GET_ADMINS_ID",
                        "handlerFieldName": "To",
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "multiSelect": true,
                        "fieldType": "string"
                        },
                        "To Name": {
                        "fieldValue": [
                        "[[Company Name]]"
                        ],
                        "handlerFieldName": "To Name",
                        "configurable": true,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType":"PROCESS_VARIABLE"
                        },
                        "CC": {
                        "fieldValue": [],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "helpVariables": [],
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "BCC": {
                        "fieldValue": [],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "helpVariables": [],
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Subject": {
                        "fieldValue": [
                        "Reminder: You’ve got bank deposits to record"
                        ],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "helpVariables": [
                        "Company Name",
                        "UndepositedFunds"
                        ],
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Message": {
                        "fieldValue": [
                        "Hi [[Company Name]],\n\nYou’ve got bank deposits to record.Review them now: https://silver-develop.qbo.intuit.com/login?pagereq=https://silver-develop.qbo.intuit.com/app/deposit"
                        ],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "helpVariables": [
                        "Company Name",
                        "UndepositedFunds"
                        ],
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Send email": {
                        "fieldValue": [
                        "true"
                        ],
                        "handlerFieldName": "Is Email",
                        "configurable": true,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "boolean"
                        },
                        "Send notification": {
                        "fieldValue": [
                        "true"
                        ],
                        "handlerFieldName": "Is Mobile",
                        "configurable": true,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "boolean"
                        },
                        "Workflow Type": {
                        "fieldValue": [
                        "QBOAdvBankDepositMobile"
                        ],
                        "configurable": false,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Company Name": {
                        "fieldValue": [
                        "[[Company Name]]"
                        ],
                        "configurable": false,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType":"PROCESS_VARIABLE"
                        },
                        "Id": {
                        "fieldValue": [
                        "[[Id]]"
                        ],
                        "configurable": false,
                        "requiredByHandler": true,
                        "handlerFieldName": "txnId",
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType":"PROCESS_VARIABLE"
                        },
                        "UndepositedFunds": {
                        "fieldValue": [],
                        "configurable": false,
                        "requiredByHandler": false,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType":"PROCESS_VARIABLE"
                        }
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/was-reminder-send-notification",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_09kebvy</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_03c1uji</bpmn:outgoing>
        </bpmn:sendTask>
        <bpmn:serviceTask id="createProjectServiceTask_bankdepositremindersingle" name="Create project Service task" camunda:type="external" camunda:topic="bankdepositaaggarwal1">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="parameterDetails">{
                        "Id": {
                        "fieldValue": [],
                        "handlerFieldName": "txnId",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType":"PROCESS_VARIABLE"
                        },
                        "intuit_realmid": {
                        "fieldValue": [],
                        "handlerFieldName": "realmId",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType":"PROCESS_VARIABLE"
                        },
                        "Name": {
                        "fieldValue": [
                        "Bank Deposit Reminder"
                        ],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "helpVariables": [],
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Assignee": {
                        "fieldValue": [],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": "GET_ADMINS_ID",
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "ProjectType": {
                        "fieldValue": [
                        "QB_BANK_DEPOSIT_REMINDER"
                        ],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "helpVariables": [],
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "TaskType": {
                        "fieldValue": [
                        "QB_ACCOUNT"
                        ],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "helpVariables": [],
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Due date": {
                        "fieldValue": [],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "helpVariables": [],
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        }
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/was-create-task",
                        "actionName": "executeWorkflowAction",
                        "responseFields": ["taskId", "projectId"]
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{ "required": true}</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_13q91qp</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_1y8t9k9</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="SequenceFlow_03c1uji" sourceRef="sendNotification_bankdepositremindersingle" targetRef="ExclusiveGateway_0qqiix7" />
        <bpmn:sequenceFlow id="SequenceFlow_1dgx8ng" sourceRef="ExclusiveGateway_0qqiix7" targetRef="waitForDeposit_bankdepositremindersingle" />
        <bpmn:parallelGateway id="ExclusiveGateway_0qqiix7" camunda:asyncBefore="true">
            <bpmn:incoming>SequenceFlow_03c1uji</bpmn:incoming>
            <bpmn:incoming>SequenceFlow_1y8t9k9</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_1dgx8ng</bpmn:outgoing>
        </bpmn:parallelGateway>
        <bpmn:sequenceFlow id="SequenceFlow_11lx0rw" sourceRef="decision_bankdepositremindersingle" targetRef="end_conditionFailed_bankdepositremindersingle">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${sendReminder == false}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:endEvent id="end_conditionFailed_bankdepositremindersingle" name="Condition not satisfied">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "handlerId": "",
                        "actionName": "updateProcessStatus"
                        }</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_11lx0rw</bpmn:incoming>
            <bpmn:messageEventDefinition messageRef="Message_1ouh1mg" camunda:type="external" camunda:topic="workflows-sumit" />
        </bpmn:endEvent>
        <bpmn:subProcess id="Activity_0acnps6" triggeredByEvent="true">
            <bpmn:startEvent id="delete_void_bankdepositremindersingle" name="Delete void event">
                <bpmn:extensionElements>
                    <camunda:properties>
                        <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;was&#34;,&#34;recordType&#34;: &#34;deposit&#34;}" />
                    </camunda:properties>
                </bpmn:extensionElements>
                <bpmn:outgoing>Flow_0j41ni2</bpmn:outgoing>
                <bpmn:messageEventDefinition id="MessageEventDefinition_0yu7b51" messageRef="Message_02nwm28" />
            </bpmn:startEvent>
            <bpmn:endEvent id="Event_1b5h5qc" name="Delete void">
                <bpmn:extensionElements>
                    <camunda:inputOutput>
                        <camunda:inputParameter name="handlerDetails">{
                            "taskHandler": "was",
                            "handlerId": "",
                            "actionName": "updateProcessStatus"
                            }</camunda:inputParameter>
                    </camunda:inputOutput>
                </bpmn:extensionElements>
                <bpmn:incoming>Flow_0j41ni2</bpmn:incoming>
                <bpmn:escalationEventDefinition id="EscalationEventDefinition_1abcxat" escalationRef="Escalation_1iiofa9" />
            </bpmn:endEvent>
            <bpmn:sequenceFlow id="Flow_0j41ni2" sourceRef="delete_void_bankdepositremindersingle" targetRef="Event_1b5h5qc" />
        </bpmn:subProcess>
        <bpmn:subProcess id="Activity_09r7la8" triggeredByEvent="true">
            <bpmn:serviceTask id="Activity_14t6fjy" name="Close Project service task" camunda:type="external" camunda:topic="bankdepositaaggarwal1">
                <bpmn:extensionElements>
                    <camunda:inputOutput>
                        <camunda:inputParameter name="handlerDetails">{
                            "taskHandler": "appconnect",
                            "handlerId": "intuit-workflows/was-update-task",
                            "actionName": "executeWorkflowAction"
                            }</camunda:inputParameter>
                        <camunda:inputParameter name="parameterDetails">{
                            "taskId": {
                            "fieldValue": [],
                            "possibleFieldValues": [],
                            "configurable": true,
                            "actionByUI": null,
                            "handlerFieldName": "Task",
                            "requiredByHandler": true,
                            "helpVariables": [],
                            "requiredByUI": false,
                            "multiSelect": false,
                            "fieldType": "string",
                            "valueType":"PROCESS_VARIABLE"
                            },
                            "projectId": {
                            "fieldValue": [],
                            "possibleFieldValues": [],
                            "configurable": true,
                            "actionByUI": null,
                            "handlerFieldName": "Project",
                            "requiredByHandler": true,
                            "helpVariables": [],
                            "requiredByUI": false,
                            "multiSelect": false,
                            "fieldType": "string",
                            "valueType":"PROCESS_VARIABLE"
                            },
                            "Status": {
                            "fieldValue": [
                            "Complete"
                            ],
                            "possibleFieldValues": [],
                            "configurable": true,
                            "actionByUI": null,
                            "requiredByHandler": true,
                            "requiredByUI": false,
                            "multiSelect": false,
                            "fieldType": "string"
                            }
                            }</camunda:inputParameter>
                        <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
                    </camunda:inputOutput>
                </bpmn:extensionElements>
                <bpmn:incoming>Flow_1t1mvx8</bpmn:incoming>
                <bpmn:outgoing>Flow_0ajg5xw</bpmn:outgoing>
            </bpmn:serviceTask>
            <bpmn:startEvent id="Event_1t3r6w7" name="Close task">
                <bpmn:outgoing>Flow_1t1mvx8</bpmn:outgoing>
                <bpmn:escalationEventDefinition id="EscalationEventDefinition_0xrn360" escalationRef="Escalation_19s5vxr" />
            </bpmn:startEvent>
            <bpmn:endEvent id="Event_1dnijp8" name="End process">
                <bpmn:extensionElements>
                    <camunda:inputOutput>
                        <camunda:inputParameter name="handlerDetails">{
                            "taskHandler": "was",
                            "handlerId": "",
                            "actionName": "updateProcessStatus"
                            }</camunda:inputParameter>
                    </camunda:inputOutput>
                </bpmn:extensionElements>
                <bpmn:incoming>Flow_0ajg5xw</bpmn:incoming>
                <bpmn:messageEventDefinition id="MessageEventDefinition_1o2n31j" messageRef="Message_19vad9e" camunda:type="external" camunda:topic="workflows-sumit" />
            </bpmn:endEvent>
            <bpmn:sequenceFlow id="Flow_0ajg5xw" sourceRef="Activity_14t6fjy" targetRef="Event_1dnijp8" />
            <bpmn:sequenceFlow id="Flow_1t1mvx8" sourceRef="Event_1t3r6w7" targetRef="Activity_14t6fjy" />
        </bpmn:subProcess>
        <bpmn:receiveTask id="waitForDeposit_bankdepositremindersingle" name="Wait for deposit" messageRef="Message_1c3cwf5">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "recordType": "deposit",
                        "handlerId": "intuit-workflows/undeposited-funds-wait"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{"required": false}</camunda:inputParameter>
                    <camunda:inputParameter name="currentStepDetails">{"required": false}</camunda:inputParameter>
                    <camunda:inputParameter name="targetApi">trigger</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_1dgx8ng</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_0utsed6</bpmn:outgoing>
        </bpmn:receiveTask>
        <bpmn:sequenceFlow id="SequenceFlow_0utsed6" sourceRef="waitForDeposit_bankdepositremindersingle" targetRef="end_bankdepositremindersingle" />
        <bpmn:endEvent id="end_bankdepositremindersingle" name="Funds deposited">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "handlerId": "",
                        "actionName": "updateProcessStatus"
                        }</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_0utsed6</bpmn:incoming>
            <bpmn:escalationEventDefinition id="EscalationEventDefinition_0o66j16" escalationRef="Escalation_19s5vxr" />
        </bpmn:endEvent>
        <bpmn:boundaryEvent id="waitTimer_bankdepositremindersingle" attachedToRef="waitForDeposit_bankdepositremindersingle">
            <bpmn:outgoing>Flow_0vo0v15</bpmn:outgoing>
            <bpmn:timerEventDefinition id="TimerEventDefinition_042j54q">
                <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">P30D</bpmn:timeDuration>
            </bpmn:timerEventDefinition>
        </bpmn:boundaryEvent>
        <bpmn:sequenceFlow id="Flow_0vo0v15" sourceRef="waitTimer_bankdepositremindersingle" targetRef="Event_0j14srb" />
        <bpmn:endEvent id="Event_0j14srb" name="Expired">
            <bpmn:incoming>Flow_0vo0v15</bpmn:incoming>
            <bpmn:escalationEventDefinition id="EscalationEventDefinition_1vc4z7f" escalationRef="Escalation_19s5vxr" />
        </bpmn:endEvent>
    </bpmn:process>
    <bpmn:message id="Message_1c3cwf5" name="deposited" />
    <bpmn:message id="Message_1ouh1mg" name="process_ended_message" />
    <bpmn:message id="Message_02nwm28" name="deleted_voided_disable" />
    <bpmn:escalation id="Escalation_1iiofa9" name="close_task" escalationCode="closetask" />
    <bpmn:escalation id="Escalation_19s5vxr" name="close_task" escalationCode="closetask" />
    <bpmn:message id="Message_19vad9e" name="process_ended_message" />
    <bpmndi:BPMNDiagram id="BPMNDiagram_1">
        <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="depositbankremindersingle">
            <bpmndi:BPMNEdge id="Flow_0vo0v15_di" bpmnElement="Flow_0vo0v15">
                <di:waypoint x="820" y="305" />
                <di:waypoint x="820" y="362" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_0utsed6_di" bpmnElement="SequenceFlow_0utsed6">
                <di:waypoint x="870" y="247" />
                <di:waypoint x="992" y="247" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_11lx0rw_di" bpmnElement="SequenceFlow_11lx0rw">
                <di:waypoint x="340" y="207" />
                <di:waypoint x="340" y="100" />
                <di:waypoint x="482" y="100" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_1dgx8ng_di" bpmnElement="SequenceFlow_1dgx8ng">
                <di:waypoint x="675" y="247" />
                <di:waypoint x="770" y="247" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_03c1uji_di" bpmnElement="SequenceFlow_03c1uji">
                <di:waypoint x="560" y="247" />
                <di:waypoint x="625" y="247" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_1y8t9k9_di" bpmnElement="SequenceFlow_1y8t9k9">
                <di:waypoint x="560" y="390" />
                <di:waypoint x="650" y="390" />
                <di:waypoint x="650" y="272" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_13q91qp_di" bpmnElement="SequenceFlow_13q91qp">
                <di:waypoint x="340" y="290" />
                <di:waypoint x="340" y="390" />
                <di:waypoint x="460" y="390" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_09kebvy_di" bpmnElement="SequenceFlow_09kebvy">
                <di:waypoint x="390" y="247" />
                <di:waypoint x="460" y="247" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_194fy7b_di" bpmnElement="SequenceFlow_194fy7b">
                <di:waypoint x="215" y="247" />
                <di:waypoint x="290" y="247" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="newEvent_bankdepositremindersingle">
                <dc:Bounds x="179" y="229" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="154" y="272" width="87" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BusinessRuleTask_0iwgxdu_di" bpmnElement="decision_bankdepositremindersingle">
                <dc:Bounds x="290" y="207" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="SendTask_1nky2ka_di" bpmnElement="sendNotification_bankdepositremindersingle">
                <dc:Bounds x="460" y="207" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="ServiceTask_0ga7c88_di" bpmnElement="createProjectServiceTask_bankdepositremindersingle">
                <dc:Bounds x="460" y="350" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="ParallelGateway_0a6bfbw_di" bpmnElement="ExclusiveGateway_0qqiix7">
                <dc:Bounds x="625" y="222" width="50" height="50" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="EndEvent_0aivdtm_di" bpmnElement="end_conditionFailed_bankdepositremindersingle">
                <dc:Bounds x="482" y="82" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="470" y="125" width="65" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0acnps6_di" bpmnElement="Activity_0acnps6" isExpanded="true">
                <dc:Bounds x="210" y="470" width="350" height="200" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="Flow_0j41ni2_di" bpmnElement="Flow_0j41ni2">
                <di:waypoint x="286" y="570" />
                <di:waypoint x="452" y="570" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="Event_0j004b3_di" bpmnElement="delete_void_bankdepositremindersingle">
                <dc:Bounds x="250" y="552" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="226" y="595" width="85" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_1b5h5qc_di" bpmnElement="Event_1b5h5qc">
                <dc:Bounds x="452" y="552" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="442" y="595" width="56" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_09r7la8_di" bpmnElement="Activity_09r7la8" isExpanded="true">
                <dc:Bounds x="210" y="700" width="350" height="200" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="Flow_1t1mvx8_di" bpmnElement="Flow_1t1mvx8">
                <di:waypoint x="286" y="800" />
                <di:waypoint x="330" y="800" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0ajg5xw_di" bpmnElement="Flow_0ajg5xw">
                <di:waypoint x="430" y="800" />
                <di:waypoint x="482" y="800" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="Activity_14t6fjy_di" bpmnElement="Activity_14t6fjy">
                <dc:Bounds x="330" y="760" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_1t3r6w7_di" bpmnElement="Event_1t3r6w7">
                <dc:Bounds x="250" y="782" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="242" y="825" width="52" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_1dnijp8_di" bpmnElement="Event_1dnijp8">
                <dc:Bounds x="482" y="782" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="469" y="825" width="62" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="ReceiveTask_1rcsmn8_di" bpmnElement="waitForDeposit_bankdepositremindersingle">
                <dc:Bounds x="770" y="207" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_18lhftp_di" bpmnElement="end_bankdepositremindersingle">
                <dc:Bounds x="992" y="229" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="969" y="272" width="82" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_0gn1dfo_di" bpmnElement="Event_0j14srb">
                <dc:Bounds x="802" y="362" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="802" y="405" width="38" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_04i1ucg_di" bpmnElement="waitTimer_bankdepositremindersingle">
                <dc:Bounds x="802" y="269" width="36" height="36" />
            </bpmndi:BPMNShape>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</bpmn:definitions>