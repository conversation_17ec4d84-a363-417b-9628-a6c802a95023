<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" exporter="Camunda Modeler" exporterVersion="4.11.1" expressionLanguage="http://www.w3.org/1999/XPath" id="Definitions_1ueeqzk" targetNamespace="http://bpmn.io/schema/bpmn" typeLanguage="http://www.w3.org/2001/XMLSchema">

  <bpmn:process camunda:historyTimeToLive="14" id="customReminder_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" isClosed="false" isExecutable="true" name="test-poc-3" processType="None">

    <bpmn:extensionElements>

      <camunda:properties>

        <camunda:property name="workflowName" value="customReminder"/>

      </camunda:properties>

    </bpmn:extensionElements>

    <bpmn:startEvent id="customStartEvent_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" isInterrupting="true" name="start process" parallelMultiple="false">

      <bpmn:extensionElements>

        <camunda:properties>

          <camunda:property name="stepDetails" value="{&quot;customStartEvent_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86&quot;:[&quot;sendExternalEmail_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86&quot;,&quot;customStartEvent_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86&quot;,&quot;createTask_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86&quot;,&quot;sendCompanyEmail_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86&quot;,&quot;inclusiveGateway_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86&quot;,&quot;decisionElement_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86&quot;,&quot;sendPushNotification_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86&quot;],&quot;customWorkflowWaitEvent_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86&quot;:[&quot;customWorkflowWaitEvent_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86&quot;]}"/>

          <camunda:property name="taskDetails" value="{ &quot;required&quot;: true }"/>

          <camunda:property name="currentStepDetails" value="{ &quot;required&quot;: true }"/>

          <camunda:property name="processVariablesDetails" value="[{&quot;variableName&quot;:&quot;CF36000000000001557156&quot;,&quot;variableType&quot;:&quot;string&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;entityChangeType&quot;,&quot;variableType&quot;:&quot;String&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;entityType&quot;,&quot;variableType&quot;:&quot;String&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;TxnDate&quot;,&quot;variableType&quot;:&quot;string&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;CompanyEmail&quot;,&quot;variableType&quot;:&quot;string&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;TxnAmount&quot;,&quot;variableType&quot;:&quot;double&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;intuit_userid&quot;,&quot;variableType&quot;:&quot;String&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;TxnBalanceAmount&quot;,&quot;variableType&quot;:&quot;double&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;SyncToken&quot;,&quot;variableType&quot;:&quot;String&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;CompanyName&quot;,&quot;variableType&quot;:&quot;string&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;TxnDueDate&quot;,&quot;variableType&quot;:&quot;string&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;CustomerEmail&quot;,&quot;variableType&quot;:&quot;string&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;DocNumber&quot;,&quot;variableType&quot;:&quot;string&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;CF3600000000000155715&quot;,&quot;variableType&quot;:&quot;double&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;Id&quot;,&quot;variableType&quot;:&quot;String&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;CustomerName&quot;,&quot;variableType&quot;:&quot;string&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;intuit_realmid&quot;,&quot;variableType&quot;:&quot;String&quot;,&quot;overrideIfAbsent&quot;:true}]"/>

          <camunda:property name="handlerDetails" value="{&quot;taskHandler&quot;: &quot;appconnect&quot;, &quot;handlerId&quot;: &quot;intuit-workflows/custom-reminder-start-process&quot;, &quot;actionName&quot;: &quot;executeWorkflowAction&quot;}"/>

          <camunda:property name="parameterDetails" value="{&quot;FilterCloseTaskConditions&quot;:{&quot;fieldValue&quot;:[&quot;txn_paid&quot;],&quot;configurable&quot;:false,&quot;requiredByHandler&quot;:false,&quot;requiredByUI&quot;:false,&quot;multiSelect&quot;:false},&quot;FilterCondition&quot;:{&quot;fieldValue&quot;:[&quot;{\&quot;rules\&quot;:[{\&quot;parameterType\&quot;:\&quot;STRING\&quot;,\&quot;parameterName\&quot;:\&quot;36000000000001557156\&quot;,\&quot;conditionalExpression\&quot;:\&quot;CONTAINS 1,2,3\&quot;,\&quot;$sdk_validated\&quot;:true},{\&quot;parameterType\&quot;:\&quot;DOUBLE\&quot;,\&quot;parameterName\&quot;:\&quot;3600000000000155715\&quot;,\&quot;conditionalExpression\&quot;:\&quot;GTE 500\&quot;,\&quot;$sdk_validated\&quot;:true}]}&quot;],&quot;configurable&quot;:false,&quot;requiredByHandler&quot;:true,&quot;requiredByUI&quot;:false,&quot;multiSelect&quot;:false},&quot;FilterRecordType&quot;:{&quot;fieldValue&quot;:[&quot;invoice&quot;],&quot;configurable&quot;:false,&quot;requiredByHandler&quot;:true,&quot;requiredByUI&quot;:false,&quot;multiSelect&quot;:false}}"/>

          <camunda:property name="startableEvents" value="[&quot;newCustomStart&quot;]"/>

          <camunda:property name="targetApi" value="evaluate-and-trigger"/>

        </camunda:properties>

      </bpmn:extensionElements>

      <bpmn:outgoing>Flow_0uuma6l_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:outgoing>

    </bpmn:startEvent>

    <bpmn:sequenceFlow id="Flow_0uuma6l_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" sourceRef="customStartEvent_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" targetRef="decisionElement_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86"/>

    <bpmn:businessRuleTask camunda:decisionRef="decisionElement_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" camunda:topic="test-ritesh" camunda:type="external" completionQuantity="1" id="decisionElement_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" implementation="##unspecified" isForCompensation="false" name="DMN Rule Processor" startQuantity="1">

      <bpmn:extensionElements>

        <camunda:inputOutput>

          <camunda:inputParameter name="handlerDetails">{ "taskHandler": "was","handlerId":"was_dmn_evaluator","actionName": "evaluateDMN"}</camunda:inputParameter>

          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>

        </camunda:inputOutput>

      </bpmn:extensionElements>

      <bpmn:incoming>Flow_0uuma6l_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:incoming>

      <bpmn:outgoing>Flow_0e5omq2_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:outgoing>

      <bpmn:outgoing>Sequence_decision_result_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:outgoing>

    </bpmn:businessRuleTask>

    <bpmn:sequenceFlow id="Flow_1jh72q4_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" sourceRef="Gateway_1rgsx47_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" targetRef="Event_1fgu7ic_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86"/>

    <bpmn:sendTask camunda:topic="test-ritesh" camunda:type="external" completionQuantity="1" id="sendExternalEmail_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" implementation="##WebService" isForCompensation="false" name="Send external email" startQuantity="1">

      <bpmn:extensionElements>

        <camunda:inputOutput>

          <camunda:inputParameter name="parameterDetails">{"Message":{"fieldValue":["Hi [[CustomerName]],\n\nInvoice [[DocNumber]] needs your attention. Please take a look at the attached invoice and contact us if you have any questions.\n\nThanks,\n[[CompanyName]]"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"SendAttachment":{"fieldValue":["true"],"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"boolean"},"entityType":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"RecordType"},"SendTo":{"fieldValue":["[[CustomerEmail]]"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string","handlerFieldName":"To"},"TxnDate":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnDate"},"CompanyEmail":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CompanyEmail"},"TxnAmount":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"double","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnAmount"},"Subject":{"fieldValue":["Invoice [[DocNumber]] needs your attention"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"TxnBalanceAmount":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"double","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnBalanceAmount"},"CompanyName":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CompanyName"},"IsEmail":{"fieldValue":["true"],"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"boolean"},"SyncToken":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"SyncToken"},"TxnDueDate":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnDueDate"},"CustomerEmail":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CustomerEmail"},"DocNumber":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"DocNumber"},"CustomerName":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CustomerName"},"Id":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnId"}}</camunda:inputParameter>

          <camunda:inputParameter name="handlerDetails">{"taskHandler":"appconnect","actionName":"executeWorkflowAction","handlerId":"intuit-workflows/was-send-notification","recordType":null,"responseFields":null,"handlerScope":null}</camunda:inputParameter>

          <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>

        </camunda:inputOutput>

      </bpmn:extensionElements>

      <bpmn:incoming>SequenceFlow_sendExternalEmail_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:incoming>

      <bpmn:outgoing>SequenceFlow_1xjrp7d_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:outgoing>

    </bpmn:sendTask>

    <bpmn:serviceTask camunda:topic="test-ritesh" camunda:type="external" completionQuantity="1" id="createTask_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" implementation="##WebService" isForCompensation="false" name="Create task" startQuantity="1">

      <bpmn:extensionElements>

        <camunda:inputOutput>

          <camunda:inputParameter name="parameterDetails">{"Assignee":{"fieldValue":["9130356958496036"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"TxnDate":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnDate"},"CompanyEmail":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CompanyEmail"},"TxnAmount":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"double","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnAmount"},"TxnBalanceAmount":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"double","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnBalanceAmount"},"CompanyName":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CompanyName"},"CloseTask":{"fieldValue":["txn_paid"],"possibleFieldValues":["txn_paid","txn_sent","close_manually"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"TxnDueDate":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnDueDate"},"TaskName":{"fieldValue":["Review Invoice [[DocNumber]]"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"CustomerEmail":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CustomerEmail"},"DocNumber":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"DocNumber"},"ProjectType":{"fieldValue":["QB_INVOICE_DUE_REMINDER"],"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string"},"TaskType":{"fieldValue":["QB_INVOICE"],"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string"},"CustomerName":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CustomerName"},"Id":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnId"},"intuit_realmid":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"RealmId"}}</camunda:inputParameter>

          <camunda:inputParameter name="handlerDetails">{"taskHandler":"appconnect","actionName":"executeWorkflowAction","handlerId":"intuit-workflows/taskmanager-create-task","recordType":null,"responseFields":["projectId","closeTaskRule"],"handlerScope":null}</camunda:inputParameter>

          <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>

        </camunda:inputOutput>

      </bpmn:extensionElements>

      <bpmn:incoming>SequenceFlow_createTask_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:incoming>

      <bpmn:outgoing>SequenceFlow_137bn44_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:outgoing>

    </bpmn:serviceTask>

    <bpmn:receiveTask completionQuantity="1" id="customWorkflowWaitEvent_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" implementation="##WebService" instantiate="false" isForCompensation="false" messageRef="Message_064b9px" name="Wait for state change" startQuantity="1">

      <bpmn:extensionElements>

        <camunda:inputOutput>

          <camunda:inputParameter name="handlerDetails">{"taskHandler": "appconnect", "handlerId": "intuit-workflows/custom-reminder-wait", "actionName": "executeWorkflowAction" }</camunda:inputParameter>

          <camunda:inputParameter name="currentStepDetails">{ "required": false }</camunda:inputParameter>

          <camunda:inputParameter name="parameterDetails">{"FilterCloseTaskConditions":{"fieldValue":["txn_paid"],"configurable":false,"requiredByHandler":false,"requiredByUI":false,"multiSelect":false},"FilterCondition":{"fieldValue":["{\"rules\":[{\"parameterType\":\"STRING\",\"parameterName\":\"36000000000001557156\",\"conditionalExpression\":\"CONTAINS 1,2,3\",\"$sdk_validated\":true},{\"parameterType\":\"DOUBLE\",\"parameterName\":\"3600000000000155715\",\"conditionalExpression\":\"GTE 500\",\"$sdk_validated\":true}]}"],"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false},"FilterRecordType":{"fieldValue":["invoice"],"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false}}</camunda:inputParameter>

          <camunda:inputParameter name="targetApi">trigger</camunda:inputParameter>

        </camunda:inputOutput>

      </bpmn:extensionElements>

      <bpmn:incoming>Flow_1necc4x_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:incoming>

      <bpmn:outgoing>Flow_1xevg2t_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:outgoing>

    </bpmn:receiveTask>

    <bpmn:endEvent id="Event_1fgu7ic_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" name="End the process if create task is false">

      <bpmn:extensionElements>

        <camunda:inputOutput>

          <camunda:inputParameter name="handlerDetails">{
            "taskHandler": "was",
            "handlerId": "",
            "actionName": "updateProcessStatus"
            }</camunda:inputParameter>

        </camunda:inputOutput>

      </bpmn:extensionElements>

      <bpmn:incoming>Flow_1jh72q4_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:incoming>

      <bpmn:messageEventDefinition camunda:topic="test-ritesh" camunda:type="external" id="MessageEventDefinition_1qq1zcv" messageRef="Message_0p07vw4"/>

    </bpmn:endEvent>

    <bpmn:sequenceFlow id="Flow_0e5omq2_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" name="Conditions unmatched" sourceRef="decisionElement_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" targetRef="Event_1dx97ea_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86">

      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == false}</bpmn:conditionExpression>

    </bpmn:sequenceFlow>

    <bpmn:endEvent id="Event_1dx97ea_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" name="DMN condition not satisfied: End process">

      <bpmn:extensionElements>

        <camunda:inputOutput>

          <camunda:inputParameter name="handlerDetails">{
              "taskHandler": "was",
              "handlerId": "",
              "actionName": "updateProcessStatus"
              }</camunda:inputParameter>

        </camunda:inputOutput>

      </bpmn:extensionElements>

      <bpmn:incoming>Flow_0e5omq2_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:incoming>

      <bpmn:messageEventDefinition camunda:topic="test-ritesh" camunda:type="external" id="MessageEventDefinition_0swqckt" messageRef="Message_0p07vw4"/>

    </bpmn:endEvent>

    <bpmn:sequenceFlow id="Flow_1xevg2t_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" sourceRef="customWorkflowWaitEvent_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" targetRef="Event_1hhbrmg_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86"/>

    <bpmn:endEvent id="Event_1hhbrmg_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" name="State change occured">

      <bpmn:incoming>Flow_1xevg2t_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:incoming>

      <bpmn:escalationEventDefinition escalationRef="Escalation_0tyjh9j" id="EscalationEventDefinition_0ay2jrq"/>

    </bpmn:endEvent>

    <bpmn:subProcess completionQuantity="1" id="Activity_108jjaa_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" isForCompensation="false" startQuantity="1" triggeredByEvent="true">

      <bpmn:serviceTask camunda:topic="test-ritesh" camunda:type="external" completionQuantity="1" id="closeTask_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" implementation="##WebService" isForCompensation="false" name="Close Project service task" startQuantity="1">

        <bpmn:extensionElements>

          <camunda:inputOutput>

            <camunda:inputParameter name="handlerDetails">{
              "taskHandler": "appconnect",
              "handlerId": "intuit-workflows/taskmanager-update-task",
              "actionName": "executeWorkflowAction"
              }</camunda:inputParameter>

            <camunda:inputParameter name="parameterDetails">{
              "projectId": {
              "fieldValue": [],
              "handlerFieldName": "Project",
              "requiredByHandler": true,
              "requiredByUI": false,
              "fieldType": "string",
              "valueType": "PROCESS_VARIABLE"
              },
              "Status": {
              "fieldValue": [
              "Complete"
              ],
              "requiredByHandler": true,
              "requiredByUI": false,
              "multiSelect": false,
              "fieldType": "string"
              }
              }</camunda:inputParameter>

            <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>

          </camunda:inputOutput>

        </bpmn:extensionElements>

        <bpmn:incoming>Flow_16ihqz2_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:incoming>

        <bpmn:outgoing>Flow_0pxn6yi_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:outgoing>

      </bpmn:serviceTask>

      <bpmn:startEvent id="Event_0vepyso_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" isInterrupting="true" name="Close task" parallelMultiple="false">

        <bpmn:outgoing>Flow_16ihqz2_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:outgoing>

        <bpmn:escalationEventDefinition escalationRef="Escalation_0tyjh9j" id="EscalationEventDefinition_0ghanme"/>

      </bpmn:startEvent>

      <bpmn:endEvent id="Event_0ey8tt4_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" name="End process">

        <bpmn:extensionElements>

          <camunda:inputOutput>

            <camunda:inputParameter name="handlerDetails">{
              "taskHandler": "was",
              "handlerId": "",
              "actionName": "updateProcessStatus"
              }</camunda:inputParameter>

          </camunda:inputOutput>

        </bpmn:extensionElements>

        <bpmn:incoming>Flow_0pxn6yi_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:incoming>

        <bpmn:messageEventDefinition camunda:topic="test-ritesh" camunda:type="external" id="MessageEventDefinition_07qnodz" messageRef="Message_0p07vw4"/>

      </bpmn:endEvent>

      <bpmn:sequenceFlow id="Flow_0pxn6yi_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" sourceRef="closeTask_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" targetRef="Event_0ey8tt4_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86"/>

      <bpmn:sequenceFlow id="Flow_16ihqz2_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" sourceRef="Event_0vepyso_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" targetRef="closeTask_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86"/>

    </bpmn:subProcess>

    <bpmn:boundaryEvent attachedToRef="customWorkflowWaitEvent_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" cancelActivity="true" id="Event_1tx361d_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" name="Expiry Event" parallelMultiple="false">

      <bpmn:outgoing>Flow_1fep73j_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:outgoing>

      <bpmn:timerEventDefinition id="TimerEventDefinition_1dgqopk">

        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">P15D</bpmn:timeDuration>

      </bpmn:timerEventDefinition>

    </bpmn:boundaryEvent>

    <bpmn:sequenceFlow id="Flow_1fep73j_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" sourceRef="Event_1tx361d_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" targetRef="Event_0kb35fk_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86"/>

    <bpmn:endEvent id="Event_0kb35fk_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" name="End process after the process expiration.">

      <bpmn:extensionElements>

        <camunda:inputOutput>

          <camunda:inputParameter name="handlerDetails">{
            "taskHandler": "was",
            "handlerId": "",
            "actionName": "updateProcessStatus"
            }</camunda:inputParameter>

        </camunda:inputOutput>

      </bpmn:extensionElements>

      <bpmn:incoming>Flow_1fep73j_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:incoming>

      <bpmn:messageEventDefinition camunda:topic="test-ritesh" camunda:type="external" id="MessageEventDefinition_0vwg5fd" messageRef="Message_0p07vw4"/>

    </bpmn:endEvent>

    <bpmn:inclusiveGateway default="Flow_1jh72q4_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" gatewayDirection="Unspecified" id="Gateway_1rgsx47_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" name="create task?">

      <bpmn:incoming>SequenceFlow_137bn44_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:incoming>

      <bpmn:incoming>SequenceFlow_1xjrp7d_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:incoming>

      <bpmn:incoming>SequenceFlow_0st8w12_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:incoming>

      <bpmn:incoming>SequenceFlow_02eopl1_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:incoming>

      <bpmn:outgoing>Flow_1jh72q4_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:outgoing>

      <bpmn:outgoing>Flow_1necc4x_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:outgoing>

    </bpmn:inclusiveGateway>

    <bpmn:sendTask camunda:topic="test-ritesh" camunda:type="external" completionQuantity="1" id="sendCompanyEmail_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" implementation="##WebService" isForCompensation="false" name="Send company email" startQuantity="1">

      <bpmn:extensionElements>

        <camunda:inputOutput>

          <camunda:inputParameter name="parameterDetails">{"Message":{"fieldValue":["Hi,\n\nInvoice [[DocNumber]] needs your attention. Please take a look at the invoice and complete any outstanding tasks.\n\nThanks,\n[[CompanyName]]"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"entityType":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"RecordType"},"SendTo":{"fieldValue":["9130356951078586"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":true,"fieldType":"string","handlerFieldName":"To"},"TxnDate":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnDate"},"CompanyEmail":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CompanyEmail"},"TxnAmount":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"double","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnAmount"},"Subject":{"fieldValue":["Review Invoice [[DocNumber]]"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"TxnBalanceAmount":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"double","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnBalanceAmount"},"CompanyName":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CompanyName"},"IsEmail":{"fieldValue":["true"],"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"boolean"},"consolidateNotifications":{"fieldValue":["true"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"boolean"},"TxnDueDate":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnDueDate"},"CustomerEmail":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CustomerEmail"},"DocNumber":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"DocNumber"},"CustomerName":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CustomerName"},"Id":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnId"}}</camunda:inputParameter>

          <camunda:inputParameter name="handlerDetails">{"taskHandler":"appconnect","actionName":"executeWorkflowAction","handlerId":"intuit-workflows/was-send-notification","recordType":null,"responseFields":null,"handlerScope":null}</camunda:inputParameter>

          <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>

        </camunda:inputOutput>

      </bpmn:extensionElements>

      <bpmn:incoming>SequenceFlow_sendCompanyEmail_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:incoming>

      <bpmn:outgoing>SequenceFlow_0st8w12_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:outgoing>

    </bpmn:sendTask>

    <bpmn:sendTask camunda:topic="test-ritesh" camunda:type="external" completionQuantity="1" id="sendPushNotification_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" implementation="##WebService" isForCompensation="false" name="Send push notification" startQuantity="1">

      <bpmn:extensionElements>

        <camunda:inputOutput>

          <camunda:inputParameter name="parameterDetails">{"Message":{"fieldValue":["Go to QuickBooks to view it."],"configurable":false,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"Subject":{"fieldValue":["An Invoice needs your attention"],"configurable":false,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"}}</camunda:inputParameter>

          <camunda:inputParameter name="handlerDetails">{}</camunda:inputParameter>

          <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>

        </camunda:inputOutput>

      </bpmn:extensionElements>

      <bpmn:incoming>SequenceFlow_sendPushNotification_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:incoming>

      <bpmn:outgoing>SequenceFlow_02eopl1_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:outgoing>

    </bpmn:sendTask>

    <bpmn:sequenceFlow id="Sequence_decision_result_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" name="Rule Evaluation Passed" sourceRef="decisionElement_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" targetRef="inclusiveGateway_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86">

      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == true}</bpmn:conditionExpression>

    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="Flow_1necc4x_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" sourceRef="Gateway_1rgsx47_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" targetRef="customWorkflowWaitEvent_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86">

      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('projectId') != null &amp;&amp; execution.getVariable('closeTaskRule') != "close_manually"}</bpmn:conditionExpression>

    </bpmn:sequenceFlow>

    <bpmn:inclusiveGateway gatewayDirection="Unspecified" id="inclusiveGateway_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" name="Gateway">

      <bpmn:extensionElements>

        <camunda:properties>

          <camunda:property name="taskDetails" value="{ &quot;required&quot;: true }"/>

        </camunda:properties>

      </bpmn:extensionElements>

      <bpmn:incoming>Sequence_decision_result_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:incoming>

      <bpmn:outgoing>SequenceFlow_createTask_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:outgoing>

      <bpmn:outgoing>SequenceFlow_sendCompanyEmail_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:outgoing>

      <bpmn:outgoing>SequenceFlow_sendExternalEmail_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:outgoing>

      <bpmn:outgoing>SequenceFlow_sendPushNotification_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:outgoing>

    </bpmn:inclusiveGateway>

    <bpmn:sequenceFlow id="SequenceFlow_createTask_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" name="Create Task" sourceRef="inclusiveGateway_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" targetRef="createTask_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86">

      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${true}</bpmn:conditionExpression>

    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="SequenceFlow_sendCompanyEmail_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" name="Send Company Email" sourceRef="inclusiveGateway_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" targetRef="sendCompanyEmail_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86">

      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${true}</bpmn:conditionExpression>

    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="SequenceFlow_137bn44_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" sourceRef="createTask_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" targetRef="Gateway_1rgsx47_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86"/>

    <bpmn:sequenceFlow id="SequenceFlow_1xjrp7d_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" sourceRef="sendExternalEmail_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" targetRef="Gateway_1rgsx47_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86"/>

    <bpmn:sequenceFlow id="SequenceFlow_0st8w12_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" sourceRef="sendCompanyEmail_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" targetRef="Gateway_1rgsx47_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86"/>

    <bpmn:sequenceFlow id="SequenceFlow_02eopl1_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" sourceRef="sendPushNotification_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" targetRef="Gateway_1rgsx47_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86"/>

    <bpmn:sequenceFlow id="SequenceFlow_sendExternalEmail_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" name="Send External Email" sourceRef="inclusiveGateway_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" targetRef="sendExternalEmail_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86">

      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${true}</bpmn:conditionExpression>

    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="SequenceFlow_sendPushNotification_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" name="Send Push Notification" sourceRef="inclusiveGateway_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" targetRef="sendPushNotification_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86">

      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${false}</bpmn:conditionExpression>

    </bpmn:sequenceFlow>

    <bpmn:subProcess completionQuantity="1" id="SubProcess_0g6pybq_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" isForCompensation="false" startQuantity="1" triggeredByEvent="true">

      <bpmn:startEvent id="customDeleteVoidTransactionEvent_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" isInterrupting="true" name="Downgrade" parallelMultiple="false">

        <bpmn:outgoing>SequenceFlow_10dp848_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:outgoing>

        <bpmn:outgoing>SequenceFlow_0y51f29_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:outgoing>

        <bpmn:messageEventDefinition id="MessageEventDefinition_02j8ti4" messageRef="Message_1o1rayd"/>

      </bpmn:startEvent>

      <bpmn:endEvent id="EndEvent_1adbxdq_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" name="End process">

        <bpmn:extensionElements>

          <camunda:inputOutput>

            <camunda:inputParameter name="handlerDetails">{
              "taskHandler": "was",
              "handlerId": "",
              "actionName": "updateProcessStatus"
              }</camunda:inputParameter>

          </camunda:inputOutput>

        </bpmn:extensionElements>

        <bpmn:incoming>SequenceFlow_10dp848_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:incoming>

        <bpmn:incoming>SequenceFlow_0y51f29_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:incoming>

        <bpmn:messageEventDefinition camunda:topic="test-ritesh" camunda:type="external" id="MessageEventDefinition_0dzw8q3" messageRef="Message_0p07vw4"/>

      </bpmn:endEvent>

      <bpmn:sequenceFlow id="SequenceFlow_10dp848_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" sourceRef="customDeleteVoidTransactionEvent_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" targetRef="EndEvent_1adbxdq_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86"/>

      <bpmn:sequenceFlow id="SequenceFlow_0y51f29_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" sourceRef="customDeleteVoidTransactionEvent_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" targetRef="EndEvent_1adbxdq_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86"/>

    </bpmn:subProcess>

    <bpmn:subProcess completionQuantity="1" id="SubProcess_0jw9z9g_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" isForCompensation="false" startQuantity="1" triggeredByEvent="true">

      <bpmn:startEvent id="StartEvent_06qdmrv_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" isInterrupting="true" name="Entity Delete" parallelMultiple="false">

        <bpmn:extensionElements>

          <camunda:properties>

            <camunda:property name="handlerDetails" value="{&quot;taskHandler&quot;: &quot;appconnect&quot;, &quot;handlerId&quot;: &quot;intuit-workflows/entity-deleted-custom-workflow&quot;, &quot;actionName&quot;: &quot;executeWorkflowAction&quot; }"/>

            <camunda:property name="targetApi" value="trigger"/>

          </camunda:properties>

        </bpmn:extensionElements>

        <bpmn:outgoing>SequenceFlow_0yb8p84_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:outgoing>

        <bpmn:messageEventDefinition id="MessageEventDefinition_19pdfcz" messageRef="Message_1qdj486"/>

      </bpmn:startEvent>

      <bpmn:sequenceFlow id="SequenceFlow_0yb8p84_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" sourceRef="StartEvent_06qdmrv_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" targetRef="EndEvent_02ic7kr_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86"/>

      <bpmn:endEvent id="EndEvent_02ic7kr_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" name="End process">

        <bpmn:extensionElements>

          <camunda:inputOutput>

            <camunda:inputParameter name="handlerDetails">{
              "taskHandler": "was",
              "handlerId": "",
              "actionName": "updateProcessStatus"
              }</camunda:inputParameter>

          </camunda:inputOutput>

        </bpmn:extensionElements>

        <bpmn:incoming>SequenceFlow_0yb8p84_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86</bpmn:incoming>

        <bpmn:escalationEventDefinition escalationRef="Escalation_0tyjh9j"/>

      </bpmn:endEvent>

    </bpmn:subProcess>

  </bpmn:process>

  <bpmn:escalation escalationCode="closetask" id="Escalation_0tyjh9j" name="close_task"/>

  <bpmn:message id="Message_0p07vw4" name="process_ended_message"/>

  <bpmn:message id="Message_064b9px" name="customWait"/>

  <bpmn:message id="Message_1o1rayd" name="deleted_voided_disable"/>

  <bpmn:message id="Message_172hcy9" name="cleanup"/>

  <bpmn:message id="Message_1qdj486" name="custom_deleted"/>

  <bpmndi:BPMNDiagram id="BPMNDiagram_1">

    <bpmndi:BPMNPlane bpmnElement="customReminder_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="BPMNPlane_1">

      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_sendPushNotification_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="SequenceFlow_1nuvc6b_di">

        <di:waypoint x="575" y="455"/>

        <di:waypoint x="575" y="560"/>

        <di:waypoint x="720" y="560"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="27" width="55" x="732" y="606"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_sendExternalEmail_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="SequenceFlow_0n3artf_di">

        <di:waypoint x="575" y="405"/>

        <di:waypoint x="575" y="280"/>

        <di:waypoint x="720" y="280"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="27" width="70" x="735" y="336"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_02eopl1_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="SequenceFlow_02eopl1_di">

        <di:waypoint x="820" y="550"/>

        <di:waypoint x="980" y="550"/>

        <di:waypoint x="980" y="445"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_0st8w12_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="SequenceFlow_0st8w12_di">

        <di:waypoint x="820" y="420"/>

        <di:waypoint x="955" y="420"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_1xjrp7d_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="SequenceFlow_1xjrp7d_di">

        <di:waypoint x="820" y="280"/>

        <di:waypoint x="980" y="280"/>

        <di:waypoint x="980" y="395"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_137bn44_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="SequenceFlow_137bn44_di">

        <di:waypoint x="820" y="160"/>

        <di:waypoint x="980" y="160"/>

        <di:waypoint x="980" y="395"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_sendCompanyEmail_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="SequenceFlow_13dpi54_di">

        <di:waypoint x="600" y="430"/>

        <di:waypoint x="720" y="430"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="27" width="76" x="732" y="478"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_createTask_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="SequenceFlow_0gdfajo_di">

        <di:waypoint x="575" y="405"/>

        <di:waypoint x="575" y="160"/>

        <di:waypoint x="720" y="160"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="59" x="740" y="213"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_1necc4x_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Flow_1necc4x_di">

        <di:waypoint x="1005" y="420"/>

        <di:waypoint x="1080" y="420"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="22" x="1033" y="402"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Sequence_decision_result_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Flow_0o8v5j8_di">

        <di:waypoint x="400" y="430"/>

        <di:waypoint x="550" y="430"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="27" width="78" x="428" y="448"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_1fep73j_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Flow_1fep73j_di">

        <di:waypoint x="1130" y="478"/>

        <di:waypoint x="1130" y="552"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_1xevg2t_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Flow_1xevg2t_di">

        <di:waypoint x="1180" y="420"/>

        <di:waypoint x="1262" y="420"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_0e5omq2_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Flow_0e5omq2_di">

        <di:waypoint x="350" y="390"/>

        <di:waypoint x="350" y="290"/>

        <di:waypoint x="402" y="290"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="27" width="55" x="282" y="319"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_1jh72q4_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Flow_1jh72q4_di">

        <di:waypoint x="1005" y="420"/>

        <di:waypoint x="1020" y="420"/>

        <di:waypoint x="1020" y="280"/>

        <di:waypoint x="1142" y="280"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_0uuma6l_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Flow_0uuma6l_di">

        <di:waypoint x="208" y="430"/>

        <di:waypoint x="300" y="430"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNShape bpmnElement="customStartEvent_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="_BPMNShape_StartEvent_2">

        <dc:Bounds height="36" width="36" x="172" y="412"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="63" x="159" y="455"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="decisionElement_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Activity_109vnno_di">

        <dc:Bounds height="80" width="100" x="300" y="390"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="sendExternalEmail_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Activity_0a7dlcb_di">

        <dc:Bounds height="80" width="100" x="720" y="240"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="createTask_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Activity_1sjt7qd_di">

        <dc:Bounds height="80" width="100" x="720" y="120"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="customWorkflowWaitEvent_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Activity_1guy81g_di">

        <dc:Bounds height="80" width="100" x="1080" y="380"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Event_1fgu7ic_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Event_0xbnmwr_di">

        <dc:Bounds height="36" width="36" x="1142" y="262"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="40" width="89" x="1115" y="219"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Event_1dx97ea_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Event_1f1cf2c_di">

        <dc:Bounds height="36" width="36" x="402" y="272"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="40" width="85" x="377" y="229"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Event_1hhbrmg_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Event_00zaz0i_di">

        <dc:Bounds height="36" width="36" x="1262" y="402"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="27" width="65" x="1248" y="445"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Activity_108jjaa_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Activity_108jjaa_di" isExpanded="true">

        <dc:Bounds height="200" width="350" x="160" y="550"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNEdge bpmnElement="Flow_16ihqz2_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Flow_16ihqz2_di">

        <di:waypoint x="236" y="650"/>

        <di:waypoint x="280" y="650"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_0pxn6yi_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Flow_0pxn6yi_di">

        <di:waypoint x="380" y="650"/>

        <di:waypoint x="432" y="650"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNShape bpmnElement="closeTask_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Activity_1t3pzu3_di">

        <dc:Bounds height="80" width="100" x="280" y="610"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Event_0vepyso_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Event_0vepyso_di">

        <dc:Bounds height="36" width="36" x="200" y="632"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="52" x="192" y="675"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Event_0ey8tt4_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Event_0ey8tt4_di">

        <dc:Bounds height="36" width="36" x="432" y="632"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="62" x="419" y="675"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Event_0kb35fk_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Event_1risnpd_di">

        <dc:Bounds height="36" width="36" x="1112" y="552"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="40" width="87" x="1087" y="595"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Gateway_1rgsx47_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Gateway_168ziyq_di">

        <dc:Bounds height="50" width="50" x="955" y="395"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="60" x="910" y="445"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="sendCompanyEmail_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Activity_0p4g82p_di">

        <dc:Bounds height="80" width="100" x="720" y="380"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="sendPushNotification_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Activity_0vgc65h_di">

        <dc:Bounds height="80" width="100" x="720" y="510"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="inclusiveGateway_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Gateway_0xoew6n_di">

        <dc:Bounds height="50" width="50" x="550" y="405"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="44" x="528" y="383"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="SubProcess_0g6pybq_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="SubProcess_0g6pybq_di" isExpanded="true">

        <dc:Bounds height="200" width="350" x="160" y="780"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_0y51f29_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="SequenceFlow_0y51f29_di">

        <di:waypoint x="236" y="880"/>

        <di:waypoint x="412" y="880"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNShape bpmnElement="customDeleteVoidTransactionEvent_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="StartEvent_1cl4dtq_di">

        <dc:Bounds height="36" width="36" x="200" y="862"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="57" x="191" y="905"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="EndEvent_1adbxdq_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="EndEvent_1adbxdq_di">

        <dc:Bounds height="36" width="36" x="412" y="862"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="62" x="399" y="905"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="SubProcess_0jw9z9g_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="SubProcess_0jw9z9g_di" isExpanded="true">

        <dc:Bounds height="200" width="350" x="595" y="700"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_0yb8p84_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="SequenceFlow_0yb8p84_di">

        <di:waypoint x="671" y="800"/>

        <di:waypoint x="847" y="800"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNShape bpmnElement="StartEvent_06qdmrv_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="StartEvent_06qdmrv_di">

        <dc:Bounds height="36" width="36" x="635" y="782"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="63" x="623" y="825"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="EndEvent_02ic7kr_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="EndEvent_11fhe8y_di">

        <dc:Bounds height="36" width="36" x="847" y="782"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="62" x="834" y="825"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Event_1tx361d_9130357193086756_df495f46-5186-4af2-8a2c-a52df0b67e86" id="Event_1fte3ra_di">

        <dc:Bounds height="36" width="36" x="1112" y="442"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="62" x="1101" y="485"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

    </bpmndi:BPMNPlane>

  </bpmndi:BPMNDiagram>

</bpmn:definitions>
