<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_09ch9f6" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="3.3.1">
    <bpmn:process id="invoiceapproval" name="Invoice Approval" isExecutable="true" camunda:versionTag="1">
        <bpmn:extensionElements>
            <camunda:properties>
                <camunda:property name="description" value="Invoice approval" />
                <camunda:property name="workflowName" value="approval" />
            </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:sendTask id="sendForApproval_invoiceapproval" name="Send for Approval" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:type="external" camunda:topic="workflow">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/521057",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails">{
                        "Approver #1": {
                        "fieldValue": [],
                        "possibleFieldValues": [],
                        "handlerFieldName": "To",
                        "configurable": true,
                        "actionByUI": "GET_ADMINS_ID",
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "multiSelect": true,
                        "fieldType": "string"
                        },
                        "Send To": {
                        "fieldValue": [
                        "Approver #1 email"
                        ],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": "GET_ADMINS_ID",
                        "requiredByHandler": false,
                        "requiredByUI": true,
                        "multiSelect": true,
                        "fieldType": "string"
                        },
                        "Subject": {
                        "fieldValue": [
                        "Invoice [[DocNumber]] requires approval"
                        ],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "helpVariables": [
                        "CustomerName",
                        "Amount",
                        "DocNumber",
                        "InvoiceDate",
                        "DueDate",
                        "Balance",
                        "My Company Email"
                        ],
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Message": {
                        "fieldValue": [
                        "Invoice for [[CustomerName]] for $[[Amount]] requires approval.Click here https://silver-release.qbo.intuit.com/app/taskmanager to approve. \nRegards"
                        ],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "helpVariables": [
                        "CustomerName",
                        "Amount",
                        "DocNumber",
                        "InvoiceDate",
                        "DueDate",
                        "Balance",
                        "My Company Email"
                        ],
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Id": {
                        "fieldValue": [],
                        "handlerFieldName": "invoiceLocalId",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType":"PROCESS_VARIABLE"
                        },
                        "intuit_realmid": {
                        "fieldValue": [],
                        "handlerFieldName": "realmId",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType":"PROCESS_VARIABLE"
                        },
                        "Send email": {
                        "fieldValue": [
                        "true"
                        ],
                        "handlerFieldName": "Is Email",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "boolean"
                        },
                        "Send notification": {
                        "fieldValue": [
                        "true"
                        ],
                        "handlerFieldName": "Is Mobile",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "boolean"
                        },
                        "CC": {
                        "fieldValue": [],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "helpVariables": [
                        "My Company Email"
                        ],
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "BCC": {
                        "fieldValue": [],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "helpVariables": [
                        "Customer Email"
                        ],
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Workflow Type": {
                        "fieldValue": [
                        "InvoiceApproval"
                        ],
                        "possibleFieldValues": [],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Mobile Notification Title": {
                        "fieldValue": ["A new invoice is waiting for approval"],
                        "possibleFieldValues": [],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Mobile Notification Action": {
                        "fieldValue": ["qb001://open/invoice/?id=[[invoiceLocalId]]&amp;companyId=[[realmId]]"],
                        "possibleFieldValues": [],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Mobile Notification Subject": {
                        "fieldValue": ["Review and approve it so it can be sent to the customer"],
                        "possibleFieldValues": [],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        }
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
                    <camunda:outputParameter name="outputRsponse" />
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>sequenceToApproval_invoiceApproval</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_0e5o342</bpmn:outgoing>
        </bpmn:sendTask>
        <bpmn:receiveTask id="waitForApproval1_invoiceApproval" name="Wait for approval" messageRef="Message_0upynr5">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "recordType": "invoice"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{"required": false}</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails">{
                        "possibleModesForAction" : {
                        "fieldValue" : ["Send email","Send notification"],
                        "possibleFieldValues":["Send email","Send notification"],
                        "configurable" : true,
                        "actionByUI" : null,
                        "requiredByHandler" : true,
                        "requiredByUI": true,
                        "helpVariables": ["CompanyName"],
                        "multiSelect": true,
                        "fieldType": "string"
                        },
                        "Approver #1": {
                        "fieldValue" : ["Approver #1 name"],
                        "possibleFieldValues":[],
                        "configurable" : true,
                        "actionByUI" : "GET_ADMINS_ID",
                        "requiredByHandler" : true,
                        "requiredByUI": true,
                        "multiSelect": true,
                        "fieldType": "string"
                        },
                        "Send To": {
                        "fieldValue" : ["Approver #1 email"],
                        "possibleFieldValues":[],
                        "configurable" : true,
                        "actionByUI" : "GET_ADMINS_EMAIL",
                        "requiredByHandler" : true,
                        "requiredByUI": true,
                        "multiSelect": true,
                        "fieldType": "string"
                        },
                        "Subject": {
                        "fieldValue" : ["Invoice [[QB:DocNumber]] requires approval"],
                        "possibleFieldValues":[],
                        "configurable" : true,
                        "actionByUI" : null,
                        "requiredByHandler" : true,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Message" : {
                        "fieldValue" : ["Invoice for [[QB:CustomerName]] for $[[QB:Amount]] requires approval.\nRegards"],
                        "possibleFieldValues":[],
                        "configurable" : true,
                        "actionByUI" : null,
                        "requiredByHandler" : true,
                        "requiredByUI": true,
                        "helpVariables": ["QBOCompanyName"],
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Id": {
                        "fieldValue" : ["Temp value for ID"],
                        "configurable" : false,
                        "actionByUI" : null,
                        "requiredByhandler" : true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType":"PROCESS_VARIABLE"
                        }
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="currentStepDetails">{"required": false}</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_0e5o342</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_0vidx1u</bpmn:outgoing>
        </bpmn:receiveTask>
        <bpmn:boundaryEvent id="waitForTimerToElapse1_invoiceApproval" attachedToRef="waitForApproval1_invoiceApproval">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;camunda&#34;}" />
                    <camunda:property name="taskDetails" value="{&#34;required&#34;: true}" />
                    <camunda:property name="currentStepDetails" value="{ &#34;required&#34; : true}" />
                    <camunda:property name="parameterDetails" value="{   &#34;waitTime&#34;: {     &#34;fieldValue&#34;: [       &#34;5&#34;     ],     &#34;configurable&#34;: true,     &#34;actionByUI&#34;: null,     &#34;requiredByhandler&#34;: false,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;integer&#34;   },   &#34;Not Approved&#34;: {     &#34;fieldValue&#34;: [       &#34;Not Approved&#34;     ],     &#34;possibleFieldValues&#34;: [       &#34;Not Approved&#34;     ],     &#34;configurable&#34;: false,     &#34;actionByUI&#34;: null,     &#34;requiredByhandler&#34;: false,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: true,     &#34;fieldType&#34;: &#34;string&#34;   },   &#34;send reminder to approver&#34;: {     &#34;fieldValue&#34;: [       &#34;send reminder to approver&#34;     ],     &#34;possibleFieldValues&#34;: [       &#34;send reminder to approver&#34;,       &#34;Auto approve invoice&#34;     ],     &#34;configurable&#34;: true,     &#34;actionByUI&#34;: null,     &#34;requiredByhandler&#34;: false,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   },   &#34;mappings&#34;: {     &#34;fieldValue&#34;: [       &#34;send reminder to approver:sendReminderEmail_invoiceApproval&#34;,       &#34;Auto approve invoice:autoUpdateAsApproved_invoiceApproval&#34;     ],     &#34;required&#34;: true,     &#34;configurable&#34;: true,     &#34;actionByUI&#34;: null,     &#34;requiredByhandler&#34;: false,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;possibleFieldValues&#34;: [       &#34;send reminder to approver:sendReminderEmail_invoiceApproval&#34;,       &#34;Auto approve invoice:autoUpdateAsApproved_invoiceApproval&#34;     ]   } }" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:outgoing>SequenceFlow_0nfqqec</bpmn:outgoing>
            <bpmn:timerEventDefinition>
                <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">${waitTime}</bpmn:timeDuration>
            </bpmn:timerEventDefinition>
        </bpmn:boundaryEvent>
        <bpmn:sequenceFlow id="sequenceSendReminder_invoiceApproval" name="Reminder action chosen by user" sourceRef="evaluateUserDefinedAction_invoiceApproval" targetRef="sendReminderEmail_invoiceApproval">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${userDefined.sendApprovalReminder == true}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sendTask id="sendReminderEmail_invoiceApproval" name="Send reminder" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:type="external" camunda:topic="workflow">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/521057",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails">{
                        "Approver #1": {
                        "fieldValue": [],
                        "possibleFieldValues": [],
                        "handlerFieldName": "To",
                        "configurable": true,
                        "actionByUI": "GET_ADMINS_ID",
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "multiSelect": true,
                        "fieldType": "string"
                        },
                        "Send To": {
                        "fieldValue": [
                        "Approver #1 email"
                        ],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": "GET_ADMINS_ID",
                        "requiredByHandler": false,
                        "requiredByUI": true,
                        "multiSelect": true,
                        "fieldType": "string"
                        },
                        "Subject": {
                        "fieldValue": [
                        "Invoice [[DocNumber]] requires approval"
                        ],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "helpVariables": [
                        "CustomerName",
                        "Amount",
                        "DocNumber",
                        "InvoiceDate",
                        "DueDate",
                        "Balance",
                        "My Company Email"
                        ],
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Message": {
                        "fieldValue": [
                        "Invoice for [[CustomerName]] for $[[Amount]] requires approval.Click here https://silver-release.qbo.intuit.com/app/taskmanager to approve. \nRegards"
                        ],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "helpVariables": [
                        "CustomerName",
                        "Amount",
                        "DocNumber",
                        "InvoiceDate",
                        "DueDate",
                        "Balance",
                        "My Company Email"
                        ],
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Id": {
                        "fieldValue": [],
                        "handlerFieldName": "invoiceLocalId",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType":"PROCESS_VARIABLE"
                        },
                        "intuit_realmid": {
                        "fieldValue": [],
                        "handlerFieldName": "realmId",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType":"PROCESS_VARIABLE"
                        },
                        "Send email": {
                        "fieldValue": [
                        "true"
                        ],
                        "handlerFieldName": "Is Email",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "boolean"
                        },
                        "Send notification": {
                        "fieldValue": [
                        "true"
                        ],
                        "handlerFieldName": "Is Mobile",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "boolean"
                        },
                        "CC": {
                        "fieldValue": [],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "helpVariables": [
                        "My Company Email"
                        ],
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "BCC": {
                        "fieldValue": [],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "helpVariables": [
                        "Customer Email"
                        ],
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Workflow Type": {
                        "fieldValue": [
                        "InvoiceApproval"
                        ],
                        "possibleFieldValues": [],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Mobile Notification Title": {
                        "fieldValue": ["An invoice is waiting for approval"],
                        "possibleFieldValues": [],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Mobile Notification Action": {
                        "fieldValue": ["qb001://open/invoice/?id=[[invoiceLocalId]]&amp;companyId=[[realmId]]"],
                        "possibleFieldValues": [],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Mobile Notification Subject": {
                        "fieldValue": ["Review and approve it so it can be sent to the customer"],
                        "possibleFieldValues": [],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        }
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{ "uiVisibility": true }</camunda:inputParameter>
                    <camunda:outputParameter name="outputRsponse" />
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>sequenceSendReminder_invoiceApproval</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_0f9ufa8</bpmn:outgoing>
        </bpmn:sendTask>
        <bpmn:sequenceFlow id="SequenceFlow_0f9ufa8" sourceRef="sendReminderEmail_invoiceApproval" targetRef="waitForApproval2_invoiceApproval" />
        <bpmn:receiveTask id="waitForApproval2_invoiceApproval" name="Wait for approval" messageRef="Message_0upynr5">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "recordType": "invoice"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails" />
                    <camunda:inputParameter name="currentStepDetails">{"required" : false}</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_0f9ufa8</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_1t8fodz</bpmn:outgoing>
        </bpmn:receiveTask>
        <bpmn:exclusiveGateway id="evaluateUserDefinedAction_invoiceApproval">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;: &#34;UI&#34;}" />
                    <camunda:property name="taskDetails" value="{&#34;required&#34;: true}" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_0nfqqec</bpmn:incoming>
            <bpmn:outgoing>sequenceSendReminder_invoiceApproval</bpmn:outgoing>
            <bpmn:outgoing>sequenceAutoUpdate_invoiceApproval</bpmn:outgoing>
        </bpmn:exclusiveGateway>
        <bpmn:sequenceFlow id="SequenceFlow_0nfqqec" sourceRef="waitForTimerToElapse1_invoiceApproval" targetRef="evaluateUserDefinedAction_invoiceApproval" />
        <bpmn:sequenceFlow id="sequenceAutoUpdate_invoiceApproval" sourceRef="evaluateUserDefinedAction_invoiceApproval" targetRef="autoUpdateAsApproved_invoiceApproval">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${userDefined.autoUpdate == true}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:serviceTask id="autoUpdateAsApproved_invoiceApproval" name="Auto approve invoice status" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:type="external" camunda:topic="workflow">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/520402",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails">{
                        "Id": {
                        "fieldValue": [],
                        "handlerFieldName": "invoiceId",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType": "PROCESS_VARIABLE"
                        },
                        "invoiceStatus": {
                        "fieldValue": [
                        "WORKFLOW_AUTO_APPROVED"
                        ],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "intuit_realmid": {
                        "fieldValue": [],
                        "handlerFieldName": "realmId",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType": "PROCESS_VARIABLE"
                        }
                        }</camunda:inputParameter>
                    <camunda:outputParameter name="outputResponse" />
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>sequenceAutoUpdate_invoiceApproval</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_0necl5h</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:boundaryEvent id="waitForTimerToElapse2_invoiceApproval" attachedToRef="waitForApproval2_invoiceApproval">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;camunda&#34;}" />
                    <camunda:property name="taskDetails" value="{&#34;required&#34;: false}" />
                    <camunda:property name="currentStepDetails" value="{&#34;required&#34;: false}" />
                    <camunda:property name="parameterDetails" value="{  &#34;waitTime&#34;: {     &#34;fieldValue&#34; : [&#34;30&#34;],    &#34;configurable&#34; : true,     &#34;actionByUI&#34; : null,     &#34;requiredByhandler&#34; : false,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;integer&#34;   }}" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:outgoing>SequenceFlow_reminderTimerElapsed</bpmn:outgoing>
            <bpmn:timerEventDefinition>
                <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">${approvalReminderWaitTime}</bpmn:timeDuration>
            </bpmn:timerEventDefinition>
        </bpmn:boundaryEvent>
        <bpmn:sequenceFlow id="SequenceFlow_reminderTimerElapsed" sourceRef="waitForTimerToElapse2_invoiceApproval" targetRef="autoRejectInvoice_invoiceApproval" />
        <bpmn:sequenceFlow id="SequenceFlow_0041dgc" sourceRef="sendAutoRejectNotification_invoiceApproval" targetRef="end4_invoiceApproval" />
        <bpmn:exclusiveGateway id="exclusiveGateway_invoiceApproval" name="approved?" default="sequenceRejected_invoiceApproval">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="handlerDetails" value="{                         &#34;taskHandler&#34;: &#34;appconnect&#34;,                         &#34;handlerId&#34;: &#34;1234&#34;,                         &#34;handlerName&#34;: &#34;sendEmail&#34;                         }" />
                    <camunda:property name="parameterDetails" value="{                         &#34;invoice.status&#34;: {                         &#34;fieldValue&#34; : [&#34;approved&#34;],                         &#34;configurable&#34; : false,                         &#34;actionByUI&#34; : null,                         &#34;requiredByhandler&#34; : false,                         &#34;requiredByUI&#34;: false,                         &#34;multiSelect&#34;: false,                         &#34;fieldType&#34;: &#34;string&#34;                         }}" />
                    <camunda:property name="taskDetails" value="{&#34;required&#34;: true}" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_0vidx1u</bpmn:incoming>
            <bpmn:incoming>SequenceFlow_1t8fodz</bpmn:incoming>
            <bpmn:outgoing>sequenceApproved_invoiceApproval</bpmn:outgoing>
            <bpmn:outgoing>sequenceRejected_invoiceApproval</bpmn:outgoing>
        </bpmn:exclusiveGateway>
        <bpmn:sequenceFlow id="SequenceFlow_0vidx1u" sourceRef="waitForApproval1_invoiceApproval" targetRef="exclusiveGateway_invoiceApproval" />
        <bpmn:sequenceFlow id="sequenceApproved_invoiceApproval" name="yes" sourceRef="exclusiveGateway_invoiceApproval" targetRef="sendApproveNotification_invoiceApproval">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${entityChangeType == 'approved'}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:startEvent id="newInvoiceCreated_invoiceapproval" name="Invoice create event" camunda:asyncAfter="true">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;was&#34;, &#34;recordType&#34;: &#34;invoice&#34;}" />
                    <camunda:property name="stepDetails" value="{   &#34;newInvoiceCreated_invoiceapproval&#34;: [     &#34;newInvoiceCreated_invoiceapproval&#34;,     &#34;decision_invoiceapproval&#34;,     &#34;sendForApproval_invoiceapproval&#34;   ],  &#34;waitForApproval1_invoiceApproval&#34;: [     &#34;waitForApproval1_invoiceApproval&#34;,     &#34;exclusiveGateway_invoiceApproval&#34;,     &#34;sendRejectNotification_invoiceApproval&#34;,     &#34;sendApproveNotification_invoiceApproval&#34;   ],   &#34;waitForTimerToElapse1_invoiceApproval&#34;: [     &#34;waitForTimerToElapse1_invoiceApproval&#34;,     &#34;evaluateUserDefinedAction_invoiceApproval&#34;,     &#34;sendReminderEmail_invoiceApproval&#34;,     &#34;autoUpdateAsApproved_invoiceApproval&#34;,     &#34;sendNotificationToCreator_invoiceapproval&#34;   ],   &#34;waitForApproval2_invoiceApproval&#34;: [     &#34;waitForApproval2_invoiceApproval&#34;,     &#34;exclusiveGateway_invoiceApproval&#34;,     &#34;sendApproveNotification_invoiceApproval&#34;,     &#34;sendRejectNotification_invoiceApproval&#34;   ],   &#34;waitForTimerToElapse2_invoiceApproval&#34;: [     &#34;waitForTimerToElapse2_invoiceApproval&#34;,     &#34;autoRejectInvoice_invoiceApproval&#34;,     &#34;sendAutoRejectNotification_invoiceApproval&#34;   ] }" />
                    <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
                    <camunda:property name="currentStepDetails" value="{&#34;required&#34;: true}" />
                    <camunda:property name="processVariablesDetails" value="[{&#34;variableName&#34;:&#34;TotalAmt&#34;, &#34;variableType&#34;: &#34;Double&#34;}, {&#34;variableName&#34;:&#34;CustomerRef_value&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;DepartmentRef_value&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;entityChangeType&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;Id&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;intuit_userid&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;intuit_realmid&#34;, &#34;variableType&#34;: &#34;String&#34;}]" />
                    <camunda:property name="startableEvents" value="[&#34;created&#34;, &#34;updated&#34;]" />
                    <camunda:property name="recurrenceDetails" value="{   &#34;recurrenceStartDate&#34;:&#34;waitForTimerToElapse1_invoiceApproval&#34;,   &#34;recurrenceSchedule&#34;:&#34;waitForTimerToElapse2_invoiceApproval&#34; }" />
                    <camunda:property name="recurrenceRule" value="{}" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:outgoing>SequenceFlow_09y5vl1</bpmn:outgoing>
        </bpmn:startEvent>
        <bpmn:sequenceFlow id="SequenceFlow_1t8fodz" sourceRef="waitForApproval2_invoiceApproval" targetRef="exclusiveGateway_invoiceApproval" />
        <bpmn:sequenceFlow id="SequenceFlow_0o7eg9h" sourceRef="sendRejectNotification_invoiceApproval" targetRef="end5_invoiceApproval" />
        <bpmn:sequenceFlow id="SequenceFlow_0v85vpc" sourceRef="sendApproveNotification_invoiceApproval" targetRef="end6_invoiceApproval" />
        <bpmn:serviceTask id="autoRejectInvoice_invoiceApproval" name="Auto reject invoice" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:type="external" camunda:topic="workflow">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/520402",
                        "actionName": "executeDummyAction"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{
                        "required": true
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails">{
                        "Id": {
                        "fieldValue": [],
                        "handlerFieldName": "invoiceId",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType": "PROCESS_VARIABLE"
                        },
                        "invoiceStatus": {
                        "fieldValue": [
                        "WORKFLOW_AUTO_REJECTED"
                        ],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "intuit_realmid": {
                        "fieldValue": [],
                        "handlerFieldName": "realmId",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType": "PROCESS_VARIABLE"
                        }
                        }</camunda:inputParameter>
                    <camunda:outputParameter name="outputResponse" />
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_reminderTimerElapsed</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_1mh685w</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:serviceTask id="sendRejectNotification_invoiceApproval" name="Send notification to creator of invoice" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:type="external" camunda:topic="workflow">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/520508",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails">{
                        "intuit_userid": {
                        "fieldValue": [],
                        "handlerFieldName": "To",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType":"PROCESS_VARIABLE"
                        },
                        "Subject": {
                        "fieldValue": [
                        "Invoice [[InvoiceNumber]] is rejected"
                        ],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "helpVariables": [
                        "CustomerName",
                        "Amount",
                        "InvoiceNumber",
                        "InvoiceDate"
                        ],
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Message": {
                        "fieldValue": [
                        "Hi,\n\nInvoice for [[CustomerName]] for $[[Amount]] got rejected.\n\nWarm regards,\nQBO"
                        ],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "helpVariables": [
                        "CustomerName",
                        "Amount",
                        "InvoiceNumber",
                        "InvoiceDate",
                        "ApproverName",
                        "ApprovalStatusChangedDate"
                        ],
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Id": {
                        "fieldValue": [],
                        "handlerFieldName": "invoiceLocalId",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType":"PROCESS_VARIABLE"
                        },
                        "intuit_realmid": {
                        "fieldValue": [],
                        "handlerFieldName": "realmId",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType":"PROCESS_VARIABLE"
                        },
                        "Send email": {
                        "fieldValue": [
                        "true"
                        ],
                        "handlerFieldName": "isEmail",
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "boolean"
                        },
                        "Send notification": {
                        "fieldValue": [
                        "true"
                        ],
                        "handlerFieldName": "isMobile",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "boolean"
                        },
                        "CC": {
                        "fieldValue": [],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "helpVariables": [
                        "My Company Email"
                        ],
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "BCC": {
                        "fieldValue": [],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "helpVariables": [
                        "Customer Email"
                        ],
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Workflow Type": {
                        "fieldValue": [
                        "InvoiceApproval"
                        ],
                        "possibleFieldValues": [],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Mobile Notification Title": {
                        "fieldValue": ["Invoice [[InvoiceNumber]] wasn't approved"],
                        "possibleFieldValues": [],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Mobile Notification Action": {
                        "fieldValue": ["qb001://open/invoice/?id=[[invoiceLocalId]]&amp;companyId=[[realmId]]"],
                        "possibleFieldValues": [],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Mobile Notification Subject": {
                        "fieldValue": ["You can edit it and request approval again."],
                        "possibleFieldValues": [],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        }
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{ "required": false }</camunda:inputParameter>
                    <camunda:outputParameter name="outputRsponse" />
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>sequenceRejected_invoiceApproval</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_0o7eg9h</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:serviceTask id="sendApproveNotification_invoiceApproval" name="Send notification to creator of invoice" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:type="external" camunda:topic="workflow">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/520508",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails">{
                        "intuit_userid": {
                        "fieldValue": [],
                        "handlerFieldName": "To",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType":"PROCESS_VARIABLE"
                        },
                        "Subject": {
                        "fieldValue": [
                        "Invoice [[InvoiceNumber]] is approved"
                        ],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "helpVariables": [
                        "CustomerName",
                        "Amount",
                        "InvoiceNumber",
                        "InvoiceDate"
                        ],
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Message": {
                        "fieldValue": [
                        "Hi,\n\nInvoice for [[CustomerName]] for $[[Amount]] got approved.\n\nWarm regards,\nQBO"
                        ],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "helpVariables": [
                        "CustomerName",
                        "Amount",
                        "InvoiceNumber",
                        "InvoiceDate",
                        "ApproverName",
                        "ApprovalStatusChangedDate"
                        ],
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Id": {
                        "fieldValue": [],
                        "handlerFieldName": "invoiceLocalId",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType":"PROCESS_VARIABLE"
                        },
                        "intuit_realmid": {
                        "fieldValue": [],
                        "handlerFieldName": "realmId",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType":"PROCESS_VARIABLE"
                        },
                        "Send email": {
                        "fieldValue": [
                        "true"
                        ],
                        "handlerFieldName": "isEmail",
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "boolean"
                        },
                        "Send notification": {
                        "fieldValue": [
                        "true"
                        ],
                        "handlerFieldName": "isMobile",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "boolean"
                        },
                        "CC": {
                        "fieldValue": [],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "helpVariables": [
                        "My Company Email"
                        ],
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "BCC": {
                        "fieldValue": [],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "helpVariables": [
                        "Customer Email"
                        ],
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Workflow Type": {
                        "fieldValue": [
                        "InvoiceApproval"
                        ],
                        "possibleFieldValues": [],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Mobile Notification Title": {
                        "fieldValue": ["Invoice [[InvoiceNumber]] was approved"],
                        "possibleFieldValues": [],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Mobile Notification Action": {
                        "fieldValue": ["qb001://open/invoice/?id=[[invoiceLocalId]]&amp;companyId=[[realmId]]"],
                        "possibleFieldValues": [],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Mobile Notification Subject": {
                        "fieldValue": ["Now you can send it to your customer"],
                        "possibleFieldValues": [],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        }
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{ "required": false }</camunda:inputParameter>
                    <camunda:outputParameter name="outputRsponse" />
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>sequenceApproved_invoiceApproval</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_0v85vpc</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="SequenceFlow_0e5o342" sourceRef="sendForApproval_invoiceapproval" targetRef="waitForApproval1_invoiceApproval" />
        <bpmn:businessRuleTask id="decision_invoiceapproval" name="Invoice approval rule evaluation" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:resultVariable="decision" camunda:decisionRef="decision_invoiceapproval" camunda:mapDecisionResult="singleResult">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{ "taskHandler": "was"}</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_09y5vl1</bpmn:incoming>
            <bpmn:outgoing>sequenceToApproval_invoiceApproval</bpmn:outgoing>
            <bpmn:outgoing>SequenceFlow_0gmat5b</bpmn:outgoing>
        </bpmn:businessRuleTask>
        <bpmn:sequenceFlow id="SequenceFlow_09y5vl1" sourceRef="newInvoiceCreated_invoiceapproval" targetRef="decision_invoiceapproval" />
        <bpmn:sequenceFlow id="sequenceToApproval_invoiceApproval" sourceRef="decision_invoiceapproval" targetRef="sendForApproval_invoiceapproval">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decision.approvalRequired == true}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="SequenceFlow_0necl5h" sourceRef="autoUpdateAsApproved_invoiceApproval" targetRef="sendNotificationToCreator_invoiceapproval" />
        <bpmn:sequenceFlow id="SequenceFlow_04ggzbx" sourceRef="sendNotificationToCreator_invoiceapproval" targetRef="end3_invoiceapproval" />
        <bpmn:serviceTask id="sendNotificationToCreator_invoiceapproval" name="Send notification to creator of invoice" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:type="external" camunda:topic="workflow">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/520508",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails">{
                        "intuit_userid": {
                        "fieldValue": [],
                        "handlerFieldName": "To",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType":"PROCESS_VARIABLE"
                        },
                        "Subject": {
                        "fieldValue": [
                        "Invoice [[InvoiceNumber]] is approved"
                        ],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "helpVariables": [
                        "CustomerName",
                        "Amount",
                        "InvoiceNumber",
                        "InvoiceDate"
                        ],
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Message": {
                        "fieldValue": [
                        "Hi,\n\nInvoice for [[CustomerName]] for $[[Amount]] got approved.\n\nWarm regards,\nQBO"
                        ],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "helpVariables": [
                        "CustomerName",
                        "Amount",
                        "InvoiceNumber",
                        "InvoiceDate",
                        "ApproverName",
                        "ApprovalStatusChangedDate"
                        ],
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Id": {
                        "fieldValue": [],
                        "handlerFieldName": "invoiceLocalId",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType":"PROCESS_VARIABLE"
                        },
                        "intuit_realmid": {
                        "fieldValue": [],
                        "handlerFieldName": "realmId",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType":"PROCESS_VARIABLE"
                        },
                        "Send email": {
                        "fieldValue": [
                        "true"
                        ],
                        "handlerFieldName": "isEmail",
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "boolean"
                        },
                        "Send notification": {
                        "fieldValue": [
                        "true"
                        ],
                        "handlerFieldName": "isMobile",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "boolean"
                        },
                        "CC": {
                        "fieldValue": [],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "helpVariables": [
                        "My Company Email"
                        ],
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "BCC": {
                        "fieldValue": [],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "helpVariables": [
                        "Customer Email"
                        ],
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Workflow Type": {
                        "fieldValue": [
                        "InvoiceApproval"
                        ],
                        "possibleFieldValues": [],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Mobile Notification Title": {
                        "fieldValue": ["Invoice [[InvoiceNumber]] was auto-approved"],
                        "possibleFieldValues": [],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Mobile Notification Action": {
                        "fieldValue": ["qb001://open/invoice/?id=[[invoiceLocalId]]&amp;companyId=[[realmId]]"],
                        "possibleFieldValues": [],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Mobile Notification Subject": {
                        "fieldValue": ["Now you can send it to your customer"],
                        "possibleFieldValues": [],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        }
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{ "required": false }</camunda:inputParameter>
                    <camunda:outputParameter name="outputRsponse" />
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_0necl5h</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_04ggzbx</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="SequenceFlow_1mh685w" sourceRef="autoRejectInvoice_invoiceApproval" targetRef="sendAutoRejectNotification_invoiceApproval" />
        <bpmn:endEvent id="end5_invoiceApproval" name="Invoice rejected">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "handlerId": "",
                        "actionName": "updateProcessStatus"
                        }</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_0o7eg9h</bpmn:incoming>
            <bpmn:messageEventDefinition messageRef="Message_0n540t5" camunda:type="external" camunda:topic="workflow" />
        </bpmn:endEvent>
        <bpmn:endEvent id="end6_invoiceApproval" name="Invoice approved">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "handlerId": "",
                        "actionName": "updateProcessStatus"
                        }</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_0v85vpc</bpmn:incoming>
            <bpmn:messageEventDefinition messageRef="Message_0n540t5" camunda:type="external" camunda:topic="workflow" />
        </bpmn:endEvent>
        <bpmn:endEvent id="end4_invoiceApproval" name="invoice auto rejected">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "handlerId": "",
                        "actionName": "updateProcessStatus"
                        }</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_0041dgc</bpmn:incoming>
            <bpmn:messageEventDefinition messageRef="Message_0n540t5" camunda:type="external" camunda:topic="workflow" />
        </bpmn:endEvent>
        <bpmn:endEvent id="end3_invoiceapproval" name="Invoice auto approved because of time out">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "handlerId": "",
                        "actionName": "updateProcessStatus"
                        }</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_04ggzbx</bpmn:incoming>
            <bpmn:messageEventDefinition messageRef="Message_0n540t5" camunda:type="external" camunda:topic="workflow" />
        </bpmn:endEvent>
        <bpmn:serviceTask id="sendAutoRejectNotification_invoiceApproval" name="Send notification to creator of invoice" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:type="external" camunda:topic="workflow">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/520508",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails">{
                        "intuit_userid": {
                        "fieldValue": [],
                        "handlerFieldName": "To",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType":"PROCESS_VARIABLE"
                        },
                        "Subject": {
                        "fieldValue": [
                        "Invoice [[InvoiceNumber]] is rejected"
                        ],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "helpVariables": [
                        "CustomerName",
                        "Amount",
                        "InvoiceNumber",
                        "InvoiceDate"
                        ],
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Message": {
                        "fieldValue": [
                        "Hi,\n\nInvoice for [[CustomerName]] for $[[Amount]] got rejected.\n\nWarm regards,\nQBO"
                        ],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "helpVariables": [
                        "CustomerName",
                        "Amount",
                        "InvoiceNumber",
                        "InvoiceDate",
                        "ApproverName",
                        "ApprovalStatusChangedDate"
                        ],
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Id": {
                        "fieldValue": [],
                        "handlerFieldName": "invoiceLocalId",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType":"PROCESS_VARIABLE"
                        },
                        "intuit_realmid": {
                        "fieldValue": [],
                        "handlerFieldName": "realmId",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType":"PROCESS_VARIABLE"
                        },
                        "Send email": {
                        "fieldValue": [
                        "true"
                        ],
                        "handlerFieldName": "isEmail",
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "boolean"
                        },
                        "Send notification": {
                        "fieldValue": [
                        "true"
                        ],
                        "handlerFieldName": "isMobile",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "boolean"
                        },
                        "CC": {
                        "fieldValue": [],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "helpVariables": [
                        "My Company Email"
                        ],
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "BCC": {
                        "fieldValue": [],
                        "possibleFieldValues": [],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": true,
                        "helpVariables": [
                        "Customer Email"
                        ],
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Workflow Type": {
                        "fieldValue": [
                        "InvoiceApproval"
                        ],
                        "possibleFieldValues": [],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Mobile Notification Title": {
                        "fieldValue": ["Invoice [[InvoiceNumber]] wasn't approved"],
                        "possibleFieldValues": [],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Mobile Notification Action": {
                        "fieldValue": ["qb001://open/invoice/?id=[[invoiceLocalId]]&amp;companyId=[[realmId]]"],
                        "possibleFieldValues": [],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Mobile Notification Subject": {
                        "fieldValue": ["You can edit it and request approval again"],
                        "possibleFieldValues": [],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        }
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{ "required": false }</camunda:inputParameter>
                    <camunda:outputParameter name="outputRsponse" />
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_1mh685w</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_0041dgc</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="sequenceRejected_invoiceApproval" name="no" sourceRef="exclusiveGateway_invoiceApproval" targetRef="sendRejectNotification_invoiceApproval" />
        <bpmn:subProcess id="deletedVoidedSubProcess_invoiceapproval" name="deleted voided handler subprocess" triggeredByEvent="true">
            <bpmn:startEvent id="deleted_voided_invoiceapproval" name="deleted voided invoiceapproval" camunda:asyncBefore="true" camunda:asyncAfter="true">
                <bpmn:outgoing>SequenceFlow_0ttx468</bpmn:outgoing>
                <bpmn:messageEventDefinition messageRef="Message_02ejj4p" />
            </bpmn:startEvent>
            <bpmn:endEvent id="end2_invoiceApproval" name="Invoice deleted or voided">
                <bpmn:extensionElements>
                    <camunda:inputOutput>
                        <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>
                        <camunda:inputParameter name="handlerDetails">{
                            "taskHandler": "was",
                            "handlerId": "",
                            "actionName": "updateProcessStatus"
                            }</camunda:inputParameter>
                    </camunda:inputOutput>
                </bpmn:extensionElements>
                <bpmn:incoming>SequenceFlow_0ttx468</bpmn:incoming>
                <bpmn:messageEventDefinition messageRef="Message_0n540t5" camunda:type="external" camunda:topic="workflow" />
            </bpmn:endEvent>
            <bpmn:sequenceFlow id="SequenceFlow_0ttx468" sourceRef="deleted_voided_invoiceapproval" targetRef="end2_invoiceApproval" />
        </bpmn:subProcess>
        <bpmn:sequenceFlow id="SequenceFlow_0gmat5b" sourceRef="decision_invoiceapproval" targetRef="Task_0mbpm8l">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decision.approvalRequired == false}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:serviceTask id="Task_0mbpm8l" name="Approve Invoice" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:type="external" camunda:topic="workflow">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/520402",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails">{
                        "Id": {
                        "fieldValue": [],
                        "handlerFieldName": "invoiceId",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType": "PROCESS_VARIABLE"
                        },
                        "invoiceStatus": {
                        "fieldValue": [
                        "WORKFLOW_AUTO_APPROVED"
                        ],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "intuit_realmid": {
                        "fieldValue": [],
                        "handlerFieldName": "realmId",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType": "PROCESS_VARIABLE"
                        }
                        }</camunda:inputParameter>
                    <camunda:outputParameter name="outputResponse" />
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_0gmat5b</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_0mrs5zp</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="SequenceFlow_0mrs5zp" sourceRef="Task_0mbpm8l" targetRef="EndEvent_08z2oxy" />
        <bpmn:endEvent id="EndEvent_08z2oxy" name="Invoice Auto Approved">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "handlerId": "",
                        "actionName": "updateProcessStatus"
                        }</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_0mrs5zp</bpmn:incoming>
            <bpmn:messageEventDefinition messageRef="Message_0n540t5" camunda:type="external" camunda:topic="workflow" />
        </bpmn:endEvent>
    </bpmn:process>
    <bpmn:message id="Message_02ejj4p" name="deleted_voided" />
    <bpmn:message id="Message_0upynr5" name="approved_rejected" />
    <bpmn:message id="Message_0n540t5" name="process_ended_message" />
    <bpmndi:BPMNDiagram id="BPMNDiagram_1">
        <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="invoiceapproval">
            <bpmndi:BPMNShape id="SendTask_0cbehcx_di" bpmnElement="sendForApproval_invoiceapproval">
                <dc:Bounds x="500" y="313" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="ReceiveTask_0u4i7br_di" bpmnElement="waitForApproval1_invoiceApproval">
                <dc:Bounds x="694" y="313" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BoundaryEvent_0nb8g8w_di" bpmnElement="waitForTimerToElapse1_invoiceApproval">
                <dc:Bounds x="720" y="375" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="811" y="347" width="4" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_109yat4_di" bpmnElement="sequenceSendReminder_invoiceApproval">
                <di:waypoint x="861" y="507" />
                <di:waypoint x="938" y="507" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="852" y="476" width="81" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="SendTask_1g3ea5h_di" bpmnElement="sendReminderEmail_invoiceApproval">
                <dc:Bounds x="938" y="467" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_0f9ufa8_di" bpmnElement="SequenceFlow_0f9ufa8">
                <di:waypoint x="988" y="467" />
                <di:waypoint x="988" y="404" />
                <di:waypoint x="1118" y="404" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="ReceiveTask_10ztdcz_di" bpmnElement="waitForApproval2_invoiceApproval">
                <dc:Bounds x="1118" y="356" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="ExclusiveGateway_026vdsv_di" bpmnElement="evaluateUserDefinedAction_invoiceApproval" isMarkerVisible="true">
                <dc:Bounds x="811" y="482" width="50" height="50" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_0nfqqec_di" bpmnElement="SequenceFlow_0nfqqec">
                <di:waypoint x="738" y="411" />
                <di:waypoint x="738" y="507" />
                <di:waypoint x="811" y="507" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_0sz88v2_di" bpmnElement="sequenceAutoUpdate_invoiceApproval">
                <di:waypoint x="836" y="532" />
                <di:waypoint x="836" y="583" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="ServiceTask_0fvublp_di" bpmnElement="autoUpdateAsApproved_invoiceApproval">
                <dc:Bounds x="786" y="583" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BoundaryEvent_1gb6h60_di" bpmnElement="waitForTimerToElapse2_invoiceApproval">
                <dc:Bounds x="1152" y="418" width="36" height="36" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_09zaj8t_di" bpmnElement="SequenceFlow_reminderTimerElapsed">
                <di:waypoint x="1169" y="454" />
                <di:waypoint x="1168" y="512" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_0041dgc_di" bpmnElement="SequenceFlow_0041dgc">
                <di:waypoint x="1318" y="592" />
                <di:waypoint x="1318" y="698" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="ExclusiveGateway_1gey5dp_di" bpmnElement="exclusiveGateway_invoiceApproval" isMarkerVisible="true">
                <dc:Bounds x="1081" y="218" width="50" height="50" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1040" y="255" width="52" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_0vidx1u_di" bpmnElement="SequenceFlow_0vidx1u">
                <di:waypoint x="794" y="353" />
                <di:waypoint x="1011" y="353" />
                <di:waypoint x="1011" y="243" />
                <di:waypoint x="1081" y="243" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_0y6uvoi_di" bpmnElement="sequenceApproved_invoiceApproval">
                <di:waypoint x="1131" y="243" />
                <di:waypoint x="1237" y="243" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1129" y="227" width="18" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="StartEvent_053q9wi_di" bpmnElement="newInvoiceCreated_invoiceapproval">
                <dc:Bounds x="198" y="297" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="183" y="340" width="69" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_1t8fodz_di" bpmnElement="SequenceFlow_1t8fodz">
                <di:waypoint x="1168" y="356" />
                <di:waypoint x="1168" y="312" />
                <di:waypoint x="1106" y="312" />
                <di:waypoint x="1106" y="268" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_0o7eg9h_di" bpmnElement="SequenceFlow_0o7eg9h">
                <di:waypoint x="1368" y="121" />
                <di:waypoint x="1465" y="121" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_0v85vpc_di" bpmnElement="SequenceFlow_0v85vpc">
                <di:waypoint x="1337" y="257" />
                <di:waypoint x="1465" y="257" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="ServiceTask_1c9aodz_di" bpmnElement="autoRejectInvoice_invoiceApproval">
                <dc:Bounds x="1118" y="512" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="ServiceTask_0tfr5e9_di" bpmnElement="sendRejectNotification_invoiceApproval">
                <dc:Bounds x="1268" y="81" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="ServiceTask_1od76nh_di" bpmnElement="sendApproveNotification_invoiceApproval">
                <dc:Bounds x="1237" y="203" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_0e5o342_di" bpmnElement="SequenceFlow_0e5o342">
                <di:waypoint x="600" y="353" />
                <di:waypoint x="694" y="353" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="BusinessRuleTask_06zco86_di" bpmnElement="decision_invoiceapproval">
                <dc:Bounds x="280" y="285" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_09y5vl1_di" bpmnElement="SequenceFlow_09y5vl1">
                <di:waypoint x="234" y="315" />
                <di:waypoint x="280" y="315" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_0utyhtt_di" bpmnElement="sequenceToApproval_invoiceApproval">
                <di:waypoint x="380" y="353" />
                <di:waypoint x="500" y="353" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_0necl5h_di" bpmnElement="SequenceFlow_0necl5h">
                <di:waypoint x="828" y="663" />
                <di:waypoint x="828" y="715" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_04ggzbx_di" bpmnElement="SequenceFlow_04ggzbx">
                <di:waypoint x="778" y="755" />
                <di:waypoint x="638" y="755" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="ServiceTask_01jstbc_di" bpmnElement="sendNotificationToCreator_invoiceapproval">
                <dc:Bounds x="778" y="715" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_1mh685w_di" bpmnElement="SequenceFlow_1mh685w">
                <di:waypoint x="1218" y="552" />
                <di:waypoint x="1268" y="552" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="EndEvent_04q54dt_di" bpmnElement="end5_invoiceApproval">
                <dc:Bounds x="1465" y="103" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1515" y="114" width="78" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="EndEvent_1og7yue_di" bpmnElement="end6_invoiceApproval">
                <dc:Bounds x="1465" y="239" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1507" y="250" width="84" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="EndEvent_0m024uu_di" bpmnElement="end4_invoiceApproval">
                <dc:Bounds x="1300" y="698" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1292" y="741" width="59" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="EndEvent_1wq7q0w_di" bpmnElement="end3_invoiceapproval">
                <dc:Bounds x="602" y="737" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="581" y="780" width="78" height="53" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="EndEvent_13f0aau_di" bpmnElement="end2_invoiceApproval">
                <dc:Bounds x="338" y="696" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="312" y="659" width="87" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="ServiceTask_1le18uv_di" bpmnElement="sendAutoRejectNotification_invoiceApproval">
                <dc:Bounds x="1268" y="512" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_08y2sd8_di" bpmnElement="sequenceRejected_invoiceApproval">
                <di:waypoint x="1106" y="218" />
                <di:waypoint x="1106" y="121" />
                <di:waypoint x="1268" y="121" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1089" y="190" width="13" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="SubProcess_0v780n2_di" bpmnElement="deletedVoidedSubProcess_invoiceapproval" isExpanded="true">
                <dc:Bounds x="120" y="600" width="350" height="200" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="StartEvent_1yipiqv_di" bpmnElement="deleted_voided_invoiceapproval">
                <dc:Bounds x="157" y="696" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="140" y="739" width="77" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_0ttx468_di" bpmnElement="SequenceFlow_0ttx468">
                <di:waypoint x="193" y="714" />
                <di:waypoint x="338" y="714" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_0gmat5b_di" bpmnElement="SequenceFlow_0gmat5b">
                <di:waypoint x="380" y="325" />
                <di:waypoint x="440" y="325" />
                <di:waypoint x="440" y="220" />
                <di:waypoint x="500" y="220" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="ServiceTask_0jn1kw0_di" bpmnElement="Task_0mbpm8l">
                <dc:Bounds x="500" y="180" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_0mrs5zp_di" bpmnElement="SequenceFlow_0mrs5zp">
                <di:waypoint x="600" y="220" />
                <di:waypoint x="702" y="220" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="EndEvent_1xjldkk_di" bpmnElement="EndEvent_08z2oxy">
                <dc:Bounds x="702" y="202" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="691" y="245" width="61" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</bpmn:definitions>