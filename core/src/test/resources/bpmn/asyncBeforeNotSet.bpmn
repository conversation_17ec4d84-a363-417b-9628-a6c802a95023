<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" id="_7FrToMrfEeOyYYI9xhG4Cw" targetNamespace="http://camunda.org/schema/1.0/bpmn" exporter="Camunda Modeler" exporterVersion="5.3.0" xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd">
    <bpmn2:process id="testProcess" isExecutable="true" camunda:historyTimeToLive="5">
        <bpmn2:startEvent id="StartEvent_1">
            <bpmn2:extensionElements>
                <camunda:properties>
                    <camunda:property name="startableEvents" value="[&#34;created&#34;]" />
                </camunda:properties>
            </bpmn2:extensionElements>
            <bpmn2:outgoing>Flow_0mfcnvw</bpmn2:outgoing>
        </bpmn2:startEvent>
        <bpmn2:endEvent id="EndEvent_1">
            <bpmn2:incoming>Flow_0iknvuv</bpmn2:incoming>
        </bpmn2:endEvent>
        <bpmn2:sequenceFlow id="SequenceFlow_2" name="" sourceRef="ExternalTask_1" targetRef="Gateway_0x9rc0t" />
        <bpmn2:serviceTask id="ExternalTask_1" name="Task 1" camunda:type="external" camunda:topic="test-topic">
            <bpmn2:incoming>Flow_0sdwvgl</bpmn2:incoming>
            <bpmn2:outgoing>SequenceFlow_2</bpmn2:outgoing>
        </bpmn2:serviceTask>
        <bpmn2:sequenceFlow id="Flow_0mfcnvw" sourceRef="StartEvent_1" targetRef="Gateway_1b7gpij" />
        <bpmn2:sequenceFlow id="Flow_0sdwvgl" sourceRef="Gateway_1b7gpij" targetRef="ExternalTask_1" />
        <bpmn2:parallelGateway id="Gateway_1b7gpij">
            <bpmn2:incoming>Flow_0mfcnvw</bpmn2:incoming>
            <bpmn2:outgoing>Flow_0sdwvgl</bpmn2:outgoing>
        </bpmn2:parallelGateway>
        <bpmn2:sequenceFlow id="Flow_1054fao" sourceRef="Gateway_0x9rc0t" targetRef="ExternalTask_3" />
        <bpmn2:parallelGateway id="Gateway_0x9rc0t">
            <bpmn2:incoming>SequenceFlow_2</bpmn2:incoming>
            <bpmn2:outgoing>Flow_1054fao</bpmn2:outgoing>
        </bpmn2:parallelGateway>
        <bpmn2:serviceTask id="ExternalTask_3" name="Task 3" camunda:type="external" camunda:topic="test-topic">
            <bpmn2:incoming>Flow_1054fao</bpmn2:incoming>
            <bpmn2:outgoing>Flow_0t2ujun</bpmn2:outgoing>
        </bpmn2:serviceTask>
        <bpmn2:sequenceFlow id="Flow_0t2ujun" sourceRef="ExternalTask_3" targetRef="Event_1" />
        <bpmn2:sequenceFlow id="Flow_0iknvuv" sourceRef="Event_1" targetRef="EndEvent_1" />
        <bpmn2:intermediateCatchEvent id="Event_1" name="Messgae Event">
            <bpmn2:incoming>Flow_0t2ujun</bpmn2:incoming>
            <bpmn2:outgoing>Flow_0iknvuv</bpmn2:outgoing>
            <bpmn2:messageEventDefinition id="MessageEventDefinition_0z7ejt0" messageRef="Message_1t06d8u" />
        </bpmn2:intermediateCatchEvent>
    </bpmn2:process>
    <bpmn2:message id="Message_1t06d8u" name="correlate_message" />
    <bpmndi:BPMNDiagram id="BPMNDiagram_1">
        <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="testProcess">
            <bpmndi:BPMNShape id="_BPMNShape_StartEvent_3" bpmnElement="StartEvent_1">
                <dc:Bounds x="152" y="104" width="36" height="36" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="_BPMNShape_EndEvent_2" bpmnElement="EndEvent_1">
                <dc:Bounds x="812" y="104" width="36" height="36" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1y3yc8p_di" bpmnElement="ExternalTask_1">
                <dc:Bounds x="340" y="82" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_0uwy6e1_di" bpmnElement="Gateway_1b7gpij">
                <dc:Bounds x="245" y="97" width="50" height="50" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_0c2ju05_di" bpmnElement="Gateway_0x9rc0t">
                <dc:Bounds x="485" y="97" width="50" height="50" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0104krj_di" bpmnElement="ExternalTask_3">
                <dc:Bounds x="570" y="82" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_1xpl3o3_di" bpmnElement="Event_1">
                <dc:Bounds x="712" y="104" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="693" y="147" width="76" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="BPMNEdge_SequenceFlow_2" bpmnElement="SequenceFlow_2" sourceElement="Activity_1y3yc8p_di" targetElement="Gateway_0c2ju05_di">
                <di:waypoint x="440" y="122" />
                <di:waypoint x="485" y="122" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0mfcnvw_di" bpmnElement="Flow_0mfcnvw">
                <di:waypoint x="188" y="122" />
                <di:waypoint x="245" y="122" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0sdwvgl_di" bpmnElement="Flow_0sdwvgl">
                <di:waypoint x="295" y="122" />
                <di:waypoint x="340" y="122" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1054fao_di" bpmnElement="Flow_1054fao">
                <di:waypoint x="535" y="122" />
                <di:waypoint x="570" y="122" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0t2ujun_di" bpmnElement="Flow_0t2ujun">
                <di:waypoint x="670" y="122" />
                <di:waypoint x="712" y="122" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0iknvuv_di" bpmnElement="Flow_0iknvuv">
                <di:waypoint x="748" y="122" />
                <di:waypoint x="812" y="122" />
            </bpmndi:BPMNEdge>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</bpmn2:definitions>
