<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_0ghypro" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="3.3.1">
  <bpmn:process id="customScheduledActions" isExecutable="true">
    <bpmn:sequenceFlow id="Flow_07dv1jk" sourceRef="customStartEvent" targetRef="decisionElement" />
    <bpmn:businessRuleTask id="decisionElement" name="DMN Rule Processor" camunda:resultVariable="decision" camunda:decisionRef="decisionElement" camunda:mapDecisionResult="singleResult">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{ "taskHandler": "was"}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_07dv1jk</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_19rqyz0</bpmn:outgoing>
    </bpmn:businessRuleTask>
    <bpmn:sequenceFlow id="SequenceFlow_19rqyz0" sourceRef="decisionElement" targetRef="recurrenceStartDate" />
    <bpmn:sequenceFlow id="SequenceFlow_0htwpqa" sourceRef="recurrenceStartDate" targetRef="recurrenceSchedule" />
    <bpmn:intermediateCatchEvent id="recurrenceStartDate" name="Start Date">
      <bpmn:incoming>SequenceFlow_19rqyz0</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0htwpqa</bpmn:outgoing>
      <bpmn:timerEventDefinition>
        <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${startDate}</bpmn:timeDate>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:startEvent id="customStartEvent" name="start process">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="currentStepDetails" value="{ &#34;required&#34;: true }" />
          <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
          <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;was&#34;}" />
          <camunda:property name="startableEvents" value="[&#34;created&#34;]" />
          <camunda:property name="processVariablesDetails" value="[{ 	&#34;variableName&#34;: &#34;entityChangeType&#34;, 	&#34;variableType&#34;: &#34;String&#34; }, { 	&#34;variableName&#34;: &#34;Id&#34;, 	&#34;variableType&#34;: &#34;String&#34; }, { 	&#34;variableName&#34;: &#34;intuit_userid&#34;, 	&#34;variableType&#34;: &#34;String&#34; }, { 	&#34;variableName&#34;: &#34;intuit_realmid&#34;, 	&#34;variableType&#34;: &#34;String&#34; }, { 	&#34;variableName&#34;: &#34;startDate&#34;, 	&#34;variableType&#34;: &#34;String&#34; }, { 	&#34;variableName&#34;: &#34;cronExpression&#34;, 	&#34;variableType&#34;: &#34;String&#34; }]" />
          <camunda:property name="stepDetails" value="{   &#34;customStartEvent&#34;: [     &#34;customStartEvent&#34;,     &#34;decisionElement&#34;,     &#34;recurrenceStartDate&#34;,     &#34;recurrenceSchedule&#34;,     &#34;scheduledAction&#34;,     &#34;scheduledActions_subProcess&#34;,     &#34;subProcess_startEvent&#34;   ] }" />
          <camunda:property name="recurrenceDetails" value="{   &#34;recurrenceStartDate&#34;:&#34;recurrenceStartDate&#34;,   &#34;recurrenceSchedule&#34;:&#34;recurrenceSchedule&#34; }" />
          <camunda:property name="recurrenceRule" value="{}" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_07dv1jk</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:intermediateCatchEvent id="recurrenceSchedule" name="Recur Event">
      <bpmn:incoming>SequenceFlow_0htwpqa</bpmn:incoming>
      <bpmn:incoming>Flow_0ar3k7h</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_17vcf21</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_1hyy4xv">
        <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">${cronExpression}</bpmn:timeCycle>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:subProcess id="SubProcess_0ynplnm" name="Close Process" triggeredByEvent="true">
      <bpmn:sequenceFlow id="SequenceFlow_18kou84" sourceRef="StartEvent_0c9w8if" targetRef="EndEvent_0ayhj3y" />
      <bpmn:endEvent id="EndEvent_0ayhj3y" name="End Process Event">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
              "taskHandler": "was",
              "handlerId": "",
              "actionName": "updateProcessStatus"
              }</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>SequenceFlow_18kou84</bpmn:incoming>
        <bpmn:messageEventDefinition messageRef="Message_0n66iem" camunda:type="external" camunda:topic="scheduledActions" />
      </bpmn:endEvent>
      <bpmn:startEvent id="StartEvent_0c9w8if" name="End Process">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;: &#34;was&#34; }" />
            <camunda:property name="targetApi" value="trigger" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:outgoing>SequenceFlow_18kou84</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_1nlqu1b" messageRef="Message_0tschbo" />
      </bpmn:startEvent>
    </bpmn:subProcess>
    <bpmn:subProcess id="scheduledActions_subProcess" name="scheduledActions subprocess">
      <bpmn:incoming>SequenceFlow_17vcf21</bpmn:incoming>
      <bpmn:outgoing>Flow_0ar3k7h</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0z5zmbj</bpmn:outgoing>
      <bpmn:sequenceFlow id="SequenceFlow_18ykyxq" sourceRef="scheduledAction" targetRef="subProcess_endEvent" />
      <bpmn:sequenceFlow id="SequenceFlow_0gtcv0e" sourceRef="subProcess_startEvent" targetRef="scheduledAction" />
      <bpmn:sendTask id="scheduledAction" name="Schedule Action" camunda:type="external" camunda:topic="scheduledActions">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="parameterDetails" />
            <camunda:inputParameter name="handlerDetails">{}</camunda:inputParameter>
            <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>SequenceFlow_0gtcv0e</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_18ykyxq</bpmn:outgoing>
      </bpmn:sendTask>
      <bpmn:endEvent id="subProcess_endEvent" name="end">
        <bpmn:incoming>SequenceFlow_18ykyxq</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:startEvent id="subProcess_startEvent" name="start">
        <bpmn:outgoing>SequenceFlow_0gtcv0e</bpmn:outgoing>
      </bpmn:startEvent>
    </bpmn:subProcess>
    <bpmn:sequenceFlow id="Flow_0ar3k7h" sourceRef="scheduledActions_subProcess" targetRef="recurrenceSchedule" />
    <bpmn:sequenceFlow id="SequenceFlow_17vcf21" sourceRef="recurrenceSchedule" targetRef="scheduledActions_subProcess" />
    <bpmn:endEvent id="EndEvent_14edhmm" name="End Process">
      <bpmn:incoming>SequenceFlow_0z5zmbj</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_0z5zmbj" sourceRef="scheduledActions_subProcess" targetRef="EndEvent_14edhmm" />
  </bpmn:process>
  <bpmn:message id="Message_10uji9x" name="approved_rejected" />
  <bpmn:message id="Message_1lsccjr" name="customWait" />
  <bpmn:message id="Message_1vqffdi" name="end_process" />
  <bpmn:escalation id="Escalation_1f2mfy4" name="close_task" escalationCode="closetask" />
  <bpmn:message id="Message_0n66iem" name="process_ended_message" />
  <bpmn:escalation id="Escalation_0aq0q52" name="end_process" escalationCode="endprocess" />
  <bpmn:message id="Message_0tschbo" name="deleted_voided_disable" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="customScheduledActions">
      <bpmndi:BPMNEdge id="SequenceFlow_0htwpqa_di" bpmnElement="SequenceFlow_0htwpqa">
        <di:waypoint x="498" y="180" />
        <di:waypoint x="572" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_19rqyz0_di" bpmnElement="SequenceFlow_19rqyz0">
        <di:waypoint x="400" y="180" />
        <di:waypoint x="462" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07dv1jk_di" bpmnElement="Flow_07dv1jk">
        <di:waypoint x="208" y="180" />
        <di:waypoint x="300" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_0io4hr8_di" bpmnElement="decisionElement">
        <dc:Bounds x="300" y="140" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_18kou84_di" bpmnElement="SequenceFlow_18kou84">
        <di:waypoint x="256" y="390" />
        <di:waypoint x="402" y="390" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_19xjaph_di" bpmnElement="EndEvent_0ayhj3y">
        <dc:Bounds x="402" y="372" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="389" y="415" width="63" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0gtcv0e_di" bpmnElement="SequenceFlow_0gtcv0e">
        <di:waypoint x="748" y="180" />
        <di:waypoint x="790" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_18ykyxq_di" bpmnElement="SequenceFlow_18ykyxq">
        <di:waypoint x="890" y="180" />
        <di:waypoint x="942" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="StartEvent_0k8yemu_di" bpmnElement="subProcess_startEvent">
        <dc:Bounds x="712" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="720" y="205" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_05a3wl3_di" bpmnElement="subProcess_endEvent">
        <dc:Bounds x="942" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="950" y="205" width="19" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="SendTask_1mez1bd_di" bpmnElement="scheduledAction">
        <dc:Bounds x="790" y="140" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_1yp99e8_di" bpmnElement="recurrenceStartDate">
        <dc:Bounds x="462" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="455" y="132" width="50" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_08rwoar_di" bpmnElement="customStartEvent">
        <dc:Bounds x="172" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="158" y="138" width="63" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_10itik3_di" bpmnElement="recurrenceSchedule">
        <dc:Bounds x="572" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="559" y="138" width="61" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="SubProcess_02sjcl2_di" bpmnElement="SubProcess_0ynplnm" isExpanded="true">
        <dc:Bounds x="200" y="330" width="258" height="130" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="SubProcess_1q4vnui_di" bpmnElement="scheduledActions_subProcess" isExpanded="true">
        <dc:Bounds x="690" y="80" width="310" height="180" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0ar3k7h_di" bpmnElement="Flow_0ar3k7h">
        <di:waypoint x="845" y="260" />
        <di:waypoint x="845" y="300" />
        <di:waypoint x="590" y="300" />
        <di:waypoint x="590" y="198" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_17vcf21_di" bpmnElement="SequenceFlow_17vcf21">
        <di:waypoint x="608" y="180" />
        <di:waypoint x="690" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="StartEvent_0shfj58_di" bpmnElement="StartEvent_0c9w8if">
        <dc:Bounds x="220" y="372" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="207" y="415" width="63" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_14edhmm_di" bpmnElement="EndEvent_14edhmm">
        <dc:Bounds x="1082" y="152" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1069" y="195" width="63" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0z5zmbj_di" bpmnElement="SequenceFlow_0z5zmbj">
        <di:waypoint x="1000" y="170" />
        <di:waypoint x="1082" y="170" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>