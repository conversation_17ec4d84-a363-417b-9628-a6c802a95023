<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_1ueeqzk" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.11.1">
  <bpmn:process id="customNotification" name="Notification Template" isExecutable="true" camunda:historyTimeToLive="5">
    <bpmn:extensionElements>
      <camunda:properties>
        <camunda:property name="workflowName" value="customNotification" />
      </camunda:properties>
    </bpmn:extensionElements>
    <bpmn:sequenceFlow id="Flow_0uuma6l" sourceRef="customStart_1234_realmId" targetRef="decisionElement" />
    <bpmn:businessRuleTask id="decisionElement" name="DMN Rule Processor" camunda:type="external" camunda:topic="custom-notifications">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{ "taskHandler": "was","handlerId":"was_dmn_evaluator","actionName": "evaluateDMN"}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0uuma6l</bpmn:incoming>
      <bpmn:outgoing>Flow_0e5omq2</bpmn:outgoing>
      <bpmn:outgoing>Sequence_rule_evaluation</bpmn:outgoing>
    </bpmn:businessRuleTask>
    <bpmn:sendTask id="sendNotification" name="Send Notification" camunda:type="external" camunda:topic="custom-notifications">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
          <camunda:inputParameter name="handlerDetails">{}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1usuyro</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1xjrp7d</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:serviceTask id="createTask" name="Create task" camunda:type="external" camunda:topic="custom-notifications">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
          <camunda:inputParameter name="handlerDetails">{}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1n7dipq</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_137bn44</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0e5omq2" name="Conditions unmatched" sourceRef="decisionElement" targetRef="unsatisfiedCondProcessEndEvent">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="unsatisfiedCondProcessEndEvent" name="DMN condition not satisfied: End process">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
            "taskHandler": "was",
            "handlerId": "",
            "actionName": "updateProcessStatus"
            }</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0e5omq2</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0swqckt" messageRef="Message_0p07vw4" camunda:type="external" camunda:topic="custom-notifications" />
    </bpmn:endEvent>
    <bpmn:endEvent id="processEndEvent" name="End process Event">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
            "taskHandler": "was",
            "handlerId": "",
            "actionName": "updateProcessStatus"
            }</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1necc4x</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0vwg5fd" messageRef="Message_0p07vw4" camunda:type="external" camunda:topic="custom-notifications" />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1necc4x" sourceRef="Gateway_1rgsx47" targetRef="processEndEvent" />
    <bpmn:sequenceFlow id="SequenceFlow_137bn44" sourceRef="createTask" targetRef="Gateway_1rgsx47" />
    <bpmn:sequenceFlow id="SequenceFlow_1xjrp7d" sourceRef="sendNotification" targetRef="Gateway_1rgsx47" />
    <bpmn:sequenceFlow id="Sequence_rule_evaluation" name="Rule Evaluation Passed" sourceRef="decisionElement" targetRef="inclusiveGateway">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:inclusiveGateway id="Gateway_1rgsx47">
      <bpmn:incoming>SequenceFlow_137bn44</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_1xjrp7d</bpmn:incoming>
      <bpmn:outgoing>Flow_1necc4x</bpmn:outgoing>
    </bpmn:inclusiveGateway>
    <bpmn:startEvent id="customStart_1234_realmId" name="Start Event">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="stepDetails" value="{   &#34;customStart&#34;: [     &#34;customStart&#34;,     &#34;decisionElement&#34;,     &#34;inclusiveGateway&#34;,     &#34;createTask&#34;,     &#34;sendNotification&#34; ] }" />
          <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
          <camunda:property name="currentStepDetails" value="{ &#34;required&#34;: true }" />
          <camunda:property name="processVariablesDetails" value="[{&#34;variableName&#34;:&#34;entityChangeType&#34;,&#34;variableType&#34;:&#34;String&#34;},{&#34;variableName&#34;:&#34;PaymentId&#34;,&#34;variableType&#34;:&#34;String&#34;},{&#34;variableName&#34;:&#34;intuit_userid&#34;,&#34;variableType&#34;:&#34;String&#34;},{&#34;variableName&#34;:&#34;intuit_realmid&#34;,&#34;variableType&#34;:&#34;String&#34;},{&#34;variableName&#34;:&#34;Company Name&#34;,&#34;variableType&#34;:&#34;String&#34;},{&#34;variableName&#34;: &#34;entityType&#34;, &#34;variableType&#34;: &#34;String&#34;}]" />
          <camunda:property name="handlerDetails" value="{}" />
          <camunda:property name="parameterDetails" value="{}" />
          <camunda:property name="startableEvents" value="[&#34;entityCreate&#34;, &#34;customStart&#34;, &#34;Create&#34;]" />
          <camunda:property name="targetApi" value="evaluate-and-trigger" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_0uuma6l</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:inclusiveGateway id="inclusiveGateway" name="Gateway">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Sequence_rule_evaluation</bpmn:incoming>
      <bpmn:outgoing>Flow_1n7dipq</bpmn:outgoing>
      <bpmn:outgoing>Flow_1usuyro</bpmn:outgoing>
    </bpmn:inclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1n7dipq" name="Create Task" sourceRef="inclusiveGateway" targetRef="createTask">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${(execution.hasVariable("createTask") == true) &amp;&amp; (execution.getVariable("createTask") == true)}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1usuyro" name="Send Notification" sourceRef="inclusiveGateway" targetRef="sendNotification">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${(execution.hasVariable("sendNotification") == true) &amp;&amp; (execution.getVariable("sendNotification") == true)}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmn:escalation id="Escalation_0tyjh9j" name="close_task" escalationCode="closetask" />
  <bpmn:message id="Message_0p07vw4" name="process_ended_message" />
  <bpmn:message id="Message_064b9px" name="customWait" />
  <bpmn:message id="Message_1o1rayd" name="deleted_voided_disable" />
  <bpmn:message id="Message_172hcy9" name="cleanup" />
  <bpmn:message id="Message_1qdj486" name="custom_deleted" />
  <bpmn:message id="Message_19qy4ca" name="entityCreate" />
  <bpmn:message id="Message_1v6gnzb" name="process_ended_message" />
  <bpmn:message id="Message_0yqzqcr" name="deleted_voided_disable" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="customNotification">
      <bpmndi:BPMNEdge id="Flow_1usuyro_di" bpmnElement="Flow_1usuyro">
        <di:waypoint x="550" y="305" />
        <di:waypoint x="550" y="390" />
        <di:waypoint x="700" y="390" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="592" y="403" width="84" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1n7dipq_di" bpmnElement="Flow_1n7dipq">
        <di:waypoint x="550" y="255" />
        <di:waypoint x="550" y="140" />
        <di:waypoint x="700" y="140" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="603" y="113" width="59" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0hln80k_di" bpmnElement="Sequence_rule_evaluation">
        <di:waypoint x="400" y="280" />
        <di:waypoint x="525" y="280" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="444" y="305" width="78" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1xjrp7d_di" bpmnElement="SequenceFlow_1xjrp7d">
        <di:waypoint x="800" y="390" />
        <di:waypoint x="980" y="390" />
        <di:waypoint x="980" y="305" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_137bn44_di" bpmnElement="SequenceFlow_137bn44">
        <di:waypoint x="800" y="140" />
        <di:waypoint x="980" y="140" />
        <di:waypoint x="980" y="255" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1necc4x_di" bpmnElement="Flow_1necc4x">
        <di:waypoint x="1005" y="280" />
        <di:waypoint x="1172" y="280" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1033" y="402" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0e5omq2_di" bpmnElement="Flow_0e5omq2">
        <di:waypoint x="350" y="240" />
        <di:waypoint x="350" y="140" />
        <di:waypoint x="402" y="140" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="282" y="176" width="55" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0uuma6l_di" bpmnElement="Flow_0uuma6l">
        <di:waypoint x="208" y="280" />
        <di:waypoint x="300" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_109vnno_di" bpmnElement="decisionElement">
        <dc:Bounds x="300" y="240" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0a7dlcb_di" bpmnElement="sendNotification">
        <dc:Bounds x="700" y="350" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1sjt7qd_di" bpmnElement="createTask">
        <dc:Bounds x="700" y="100" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1f1cf2c_di" bpmnElement="unsatisfiedCondProcessEndEvent">
        <dc:Bounds x="402" y="122" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="377" y="79" width="85" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1risnpd_di" bpmnElement="processEndEvent">
        <dc:Bounds x="1172" y="262" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1160" y="305" width="62" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_076yzmw_di" bpmnElement="Gateway_1rgsx47">
        <dc:Bounds x="955" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0qkls0c_di" bpmnElement="customStart_1234_realmId">
        <dc:Bounds x="172" y="262" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="165" y="305" width="55" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1r692tu_di" bpmnElement="inclusiveGateway">
        <dc:Bounds x="525" y="255" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="589" y="270" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
