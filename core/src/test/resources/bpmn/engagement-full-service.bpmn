<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bioc="http://bpmn.io/schema/bpmn/biocolor/1.0" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" exporter="Camunda Modeler" exporterVersion="4.2.0" expressionLanguage="http://www.w3.org/1999/XPath" id="Definitions_1fho3ib" targetNamespace="http://bpmn.io/schema/bpmn" typeLanguage="http://www.w3.org/2001/XMLSchema">
  <bpmn:message id="Message_1fy3zel" name="assign_engagement_true"/>
  <bpmn:collaboration id="TTLiveFullServiceWFF" isClosed="false">
    <bpmn:participant id="Participant_0no1f6f" name="Full Service" processRef="engagementTTLiveFullService"/>
  </bpmn:collaboration>
  <bpmn:message id="Message_10mibh2" name="assign_engagement_true"/>
  <bpmn:message id="Message_1ji3lo4" name="MILESTONE_STATUS_UPDATE"/>
  <bpmn:escalation escalationCode="engagement_close" id="Escalation_188xam6" name="Close Engagement"/>
  <bpmn:message id="Message_1qijmpo" name="iep_milestone_manual_override"/>
  <bpmn:message id="Message_0ya9e3t" name="filing_milestone_completed"/>
  <bpmn:message id="Message_0mwl4zo" name="engagement_status_update"/>
  <bpmn:escalation escalationCode="engagement_revoke" id="Escalation_0x6jr3b" name="Revoke Engagement"/>
  <bpmn:error errorCode="com.intuit.spp.vep.integrations.core.error.ProjectException" id="Error_0jlaxyu" name="Any Exception"/>
  <bpmn:message id="Message_0nrohfn" name="assign_engagement"/>
  <bpmn:message id="Message_0otezgn" name="e_signature_update"/>
  <bpmn:signal id="Signal_1axttmk" name="SIGNAL_MILESTONE_COMPLETE"/>
  <bpmn:process camunda:versionTag="v1.0.0" id="engagementTTLiveFullService" isClosed="false" isExecutable="true" name="engagementTTLiveFullService" processType="None">
    <bpmn:subProcess completionQuantity="1" id="Activity_1v06ow3" isForCompensation="false" name="Ready to file" startQuantity="1" triggeredByEvent="false">
      <bpmn:incoming>Flow_1wdgdoe</bpmn:incoming>
      <bpmn:outgoing>Flow_0628d1x</bpmn:outgoing>
      <bpmn:startEvent id="Event_12jbq7e" isInterrupting="true" parallelMultiple="false">
        <bpmn:outgoing>Flow_1h7zcpz</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:endEvent id="Event_0h4s8xy">
        <bpmn:incoming>Flow_1942eiy</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="Flow_1h7zcpz" sourceRef="Event_12jbq7e" targetRef="Activity_0kvaqag"/>
      <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_0kvaqag" implementation="##WebService" isForCompensation="false" name="Complet E-Signature&#10;(StepFn)" startQuantity="1">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-aws-step",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="templateName">esignature_completed_workflow_arn</camunda:inputParameter>
            <camunda:inputParameter name="Id">${engagementId}</camunda:inputParameter>
            <camunda:inputParameter name="intuit_userid">${customerAuthId}</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1h7zcpz</bpmn:incoming>
        <bpmn:incoming>Flow_0rlltad</bpmn:incoming>
        <bpmn:outgoing>Flow_1942eiy</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_1942eiy" sourceRef="Activity_0kvaqag" targetRef="Event_0h4s8xy"/>
      <bpmn:boundaryEvent attachedToRef="Activity_0kvaqag" cancelActivity="true" id="Event_0oh4v1k" parallelMultiple="false">
        <bpmn:outgoing>Flow_0rlltad</bpmn:outgoing>
        <bpmn:timerEventDefinition id="TimerEventDefinition_12vvl43">
          <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">R1/PT2M</bpmn:timeCycle>
        </bpmn:timerEventDefinition>
      </bpmn:boundaryEvent>
      <bpmn:sequenceFlow id="Flow_0rlltad" sourceRef="Event_0oh4v1k" targetRef="Activity_0kvaqag"/>
      <bpmn:textAnnotation id="TextAnnotation_1c4whne" textFormat="text/plain">
        <bpmn:text>Complete E-Sig Stepfunc</bpmn:text>
      </bpmn:textAnnotation>
    </bpmn:subProcess>
    <bpmn:subProcess completionQuantity="1" id="Activity_1nc02ym" isForCompensation="false" name="E-file authorization" startQuantity="1" triggeredByEvent="false">
      <bpmn:incoming>Flow_1wspseh</bpmn:incoming>
      <bpmn:outgoing>Flow_00bd1hf</bpmn:outgoing>
      <bpmn:startEvent id="Event_1v2ekk8" isInterrupting="true" parallelMultiple="false">
        <bpmn:outgoing>Flow_0ro554d</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:endEvent id="Event_1kpi9as">
        <bpmn:incoming>Flow_1w3v8u5</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="Flow_0ro554d" sourceRef="Event_1v2ekk8" targetRef="Activity_17kx36h"/>
      <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_17kx36h" implementation="##WebService" isForCompensation="false" name="Create E-Signature&#10;(StepFn)" startQuantity="1">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-aws-step",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="templateName">esignature_workflow_arn</camunda:inputParameter>
            <camunda:inputParameter name="Id">${engagementId}</camunda:inputParameter>
            <camunda:inputParameter name="intuit_userid">${customerAuthId}</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0ro554d</bpmn:incoming>
        <bpmn:incoming>Flow_0e1ixh8</bpmn:incoming>
        <bpmn:outgoing>Flow_1w3v8u5</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_1w3v8u5" sourceRef="Activity_17kx36h" targetRef="Event_1kpi9as"/>
      <bpmn:boundaryEvent attachedToRef="Activity_17kx36h" cancelActivity="true" id="Event_1qacktq" parallelMultiple="false">
        <bpmn:outgoing>Flow_0e1ixh8</bpmn:outgoing>
        <bpmn:timerEventDefinition id="TimerEventDefinition_09cx65i">
          <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">R1/PT2M</bpmn:timeCycle>
        </bpmn:timerEventDefinition>
      </bpmn:boundaryEvent>
      <bpmn:sequenceFlow id="Flow_0e1ixh8" sourceRef="Event_1qacktq" targetRef="Activity_17kx36h"/>
      <bpmn:textAnnotation id="TextAnnotation_0lgjt1s" textFormat="text/plain">
        <bpmn:text>Kicks off Create E-Sig step function</bpmn:text>
      </bpmn:textAnnotation>
      <bpmn:association associationDirection="None" id="Association_1gqngbd" sourceRef="Activity_17kx36h" targetRef="TextAnnotation_0lgjt1s"/>
    </bpmn:subProcess>
    <bpmn:subProcess completionQuantity="1" id="Activity_0high9c" isForCompensation="false" name="Gathering Info" startQuantity="1" triggeredByEvent="false">
      <bpmn:incoming>Flow_1pakrh8</bpmn:incoming>
      <bpmn:outgoing>Flow_0efb7xo</bpmn:outgoing>
      <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_076pj5o" implementation="##WebService" isForCompensation="false" name="Client Intake" startQuantity="1">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-project-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="projectConfig">
              <camunda:map>
                <camunda:entry key="A. Full Service Client Intake">{     "alias": "TY19 Full Service #1 . B. Gathering Info . A. Client Intake",     "templateId": [       {         "env": "qal",         "value": "559572"       },       {         "env": "qa",         "value": "559572"       },       {         "env": "e2e",         "value": "559572"       },       {         "env": "prod",         "value": "541199"       }     ],     "projectName": [       {         "env": "qal",         "value": "A. Full Service Client Intake"       },       {         "env": "qa",         "value": "A. Full Service Client Intake"       },       {         "env": "e2e",         "value": "A. Full Service Client Intake"       },       {         "env": "prod",         "value": "A. Client Intake"       }     ]   }</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
            <camunda:inputParameter name="assignee">${currentAssignee}</camunda:inputParameter>
            <camunda:inputParameter name="onBehalfOf">${customerAuthId}</camunda:inputParameter>
            <camunda:inputParameter name="firmId">${firmId}</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0trqvjv</bpmn:incoming>
        <bpmn:outgoing>Flow_0h4zv98</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:startEvent id="Event_02mcv8o" isInterrupting="true" parallelMultiple="false">
        <bpmn:outgoing>Flow_0trqvjv</bpmn:outgoing>
        <bpmn:outgoing>Flow_10f7gy9</bpmn:outgoing>
        <bpmn:outgoing>Flow_08zzwt5</bpmn:outgoing>
        <bpmn:outgoing>Flow_1xniz6e</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:endEvent id="Event_10um5py">
        <bpmn:incoming>Flow_175xkxz</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="Flow_0h4zv98" sourceRef="Activity_076pj5o" targetRef="Gateway_1ljj7mk"/>
      <bpmn:sequenceFlow id="Flow_0trqvjv" sourceRef="Event_02mcv8o" targetRef="Activity_076pj5o"/>
      <bpmn:sequenceFlow id="Flow_175xkxz" sourceRef="Event_19siwwv" targetRef="Event_10um5py"/>
      <bpmn:intermediateCatchEvent id="Event_19siwwv" name="Complete Milestone" parallelMultiple="false">
        <bpmn:incoming>Flow_1q7dj2q</bpmn:incoming>
        <bpmn:outgoing>Flow_175xkxz</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_1bfdu7t" messageRef="Message_13clq56"/>
      </bpmn:intermediateCatchEvent>
      <bpmn:sequenceFlow id="Flow_1q7dj2q" sourceRef="Gateway_1ljj7mk" targetRef="Event_19siwwv"/>
      <bpmn:parallelGateway camunda:asyncBefore="true" gatewayDirection="Unspecified" id="Gateway_1ljj7mk">
        <bpmn:incoming>Flow_0h4zv98</bpmn:incoming>
        <bpmn:incoming>Flow_03hjwxh</bpmn:incoming>
        <bpmn:incoming>Flow_0u7k9e0</bpmn:incoming>
        <bpmn:incoming>Flow_1scbrk0</bpmn:incoming>
        <bpmn:outgoing>Flow_1q7dj2q</bpmn:outgoing>
      </bpmn:parallelGateway>
      <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_10exh7b" implementation="##WebService" isForCompensation="false" name="Scope" startQuantity="1">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-project-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="projectConfig">
              <camunda:map>
                <camunda:entry key="Scope">{     "alias": "TY19 Full Service #1 . B. Gathering Info . C. Scope",     "templateId": [       {         "env": "qal",         "value": "559574"       },       {         "env": "qa",         "value": "559574"       },       {         "env": "e2e",         "value": "559574"       },       {         "env": "prod",         "value": "541253"       }     ],     "projectName": [       {         "env": "qal",         "value": "C. Full Service Scope"       },       {         "env": "qa",         "value": "C. Full Service Scope"       },       {         "env": "e2e",         "value": "C. Full Service Scope"       },       {         "env": "prod",         "value": "C. Scope"       }     ]   }</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
            <camunda:inputParameter name="assignee">${currentAssignee}</camunda:inputParameter>
            <camunda:inputParameter name="onBehalfOf">${customerAuthId}</camunda:inputParameter>
            <camunda:inputParameter name="firmId">${firmId}</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_08zzwt5</bpmn:incoming>
        <bpmn:outgoing>Flow_03hjwxh</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_10f7gy9" sourceRef="Event_02mcv8o" targetRef="Activity_1jn3wii"/>
      <bpmn:sequenceFlow id="Flow_08zzwt5" sourceRef="Event_02mcv8o" targetRef="Activity_10exh7b"/>
      <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_056zkjf" implementation="##WebService" isForCompensation="false" name="Data Collection (24hrs)" startQuantity="1">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-project-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="projectConfig">
              <camunda:map>
                <camunda:entry key="Data Collection">{     "alias": "TY19 Full Service #1 . B. Gathering Info . D. Data Collection (24 hrs)",     "templateId": [       {         "env": "qal",         "value": "559575"       },       {         "env": "qa",         "value": "559575"       },       {         "env": "e2e",         "value": "559575"       },       {         "env": "prod",         "value": "541201"       }     ],     "projectName": [       {         "env": "qal",         "value": "Full Service Data Collection"       },       {         "env": "qa",         "value": "Full Service Data Collection"       },       {         "env": "e2e",         "value": "Full Service Data Collection"       },       {         "env": "prod",         "value": "D. Data Collection (24 hrs)"       }     ]   },</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
            <camunda:inputParameter name="assignee">${currentAssignee}</camunda:inputParameter>
            <camunda:inputParameter name="onBehalfOf">${customerAuthId}</camunda:inputParameter>
            <camunda:inputParameter name="firmId">${firmId}</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1xniz6e</bpmn:incoming>
        <bpmn:outgoing>Flow_0u7k9e0</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_1xniz6e" sourceRef="Event_02mcv8o" targetRef="Activity_056zkjf"/>
      <bpmn:sequenceFlow id="Flow_03hjwxh" sourceRef="Activity_10exh7b" targetRef="Gateway_1ljj7mk"/>
      <bpmn:sequenceFlow id="Flow_0u7k9e0" sourceRef="Activity_056zkjf" targetRef="Gateway_1ljj7mk"/>
      <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_1jn3wii" implementation="##WebService" isForCompensation="false" name="Client Verification" startQuantity="1">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-project-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="projectConfig">
              <camunda:map>
                <camunda:entry key="Client Verification">{     "alias": "TY19 Full Service #1 . B. Gathering Info . B. Client Verification",     "templateId": [       {         "env": "qal",         "value": "559573"       },       {         "env": "qa",         "value": "559573"       },       {         "env": "e2e",         "value": "559573"       },       {         "env": "prod",         "value": "541046"       }     ],     "projectName": [       {         "env": "qal",         "value": "B. Full Service Client Verification"       },       {         "env": "qa",         "value": "B. Full Service Client Verification"       },       {         "env": "e2e",         "value": "B. Full Service Client Verification"       },       {         "env": "prod",         "value": "B. Client Verification"       }     ]   }</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
            <camunda:inputParameter name="assignee">${currentAssignee}</camunda:inputParameter>
            <camunda:inputParameter name="onBehalfOf">${customerAuthId}</camunda:inputParameter>
            <camunda:inputParameter name="firmId">${firmId}</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_10f7gy9</bpmn:incoming>
        <bpmn:outgoing>Flow_1scbrk0</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_1scbrk0" sourceRef="Activity_1jn3wii" targetRef="Gateway_1ljj7mk"/>
    </bpmn:subProcess>
    <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_0f379q4" implementation="##WebService" isForCompensation="false" name="Get Engagement State" startQuantity="1">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="path">/v1/engagement/${engagementId}/deep</camunda:entry>
              <camunda:entry key="method">GET</camunda:entry>
              <camunda:entry key="resultOperation">[{"outputVariableKey":"currentMilestoneStatus","jsonPath":"$.milestoneStatus","singleValue":true},{"outputVariableKey":"customerAuthId","jsonPath":"$.customerAuthId","singleValue":true},{"outputVariableKey":"firmId","jsonPath":"$.firmId","singleValue":true}]</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1dkn0d6</bpmn:incoming>
      <bpmn:incoming>Flow_1adh9uk</bpmn:incoming>
      <bpmn:outgoing>Flow_1qk86w8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="Event_1n2b0mx" name="Milestone:- Post File">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="newMilestone">Post File</camunda:outputParameter>
          <camunda:outputParameter name="milestoneOperation">STARTED</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1lhpbi5</bpmn:incoming>
      <bpmn:incoming>Flow_1qxkx9n</bpmn:incoming>
      <bpmn:outgoing>Flow_1mcfhf9</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_0ucifop" implementation="##WebService" isForCompensation="false" name="Update Milestone and Status" startQuantity="1">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
              <camunda:entry key="method">PATCH</camunda:entry>
              <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{milestoneStatus}"}</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0fydm4j</bpmn:incoming>
      <bpmn:incoming>Flow_1yuzcbv</bpmn:incoming>
      <bpmn:outgoing>Flow_1wglfr4</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_089m3sh" implementation="##WebService" isForCompensation="false" name="Update Milestone and Status" startQuantity="1">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
              <camunda:entry key="method">PATCH</camunda:entry>
              <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{milestoneStatus}"}</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0fg2jyi</bpmn:incoming>
      <bpmn:incoming>Flow_1sbozku</bpmn:incoming>
      <bpmn:outgoing>Flow_0lu2rpw</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="Event_1ow40jj" name="Milestone:- E-file authorization Complete">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="milestone">E-file authorization</camunda:outputParameter>
          <camunda:outputParameter name="milestoneStatus">COMPLETED</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_00bd1hf</bpmn:incoming>
      <bpmn:incoming>Flow_0nkwkm5</bpmn:incoming>
      <bpmn:outgoing>Flow_0fg2jyi</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:intermediateThrowEvent id="Event_05n1189" name="Milestone:- E-file authorization Created">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="milestone">E-file authorization</camunda:outputParameter>
          <camunda:outputParameter name="milestoneStatus">CREATED</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0yvv6lc</bpmn:incoming>
      <bpmn:incoming>Flow_0f3ym69</bpmn:incoming>
      <bpmn:outgoing>Flow_01opz0x</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:intermediateThrowEvent id="Event_138axi8" name="Milestone:- Client Review Complete">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="milestone">Client Review</camunda:outputParameter>
          <camunda:outputParameter name="milestoneStatus">COMPLETED</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1we0fx9</bpmn:incoming>
      <bpmn:incoming>Flow_1ln5drr</bpmn:incoming>
      <bpmn:outgoing>Flow_0dwkltv</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:intermediateThrowEvent id="Event_0kxekbm" name="Milestone:- Client Review">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="newMilestone">Client Review</camunda:outputParameter>
          <camunda:outputParameter name="milestoneOperation">STARTED</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1pbj1r0</bpmn:incoming>
      <bpmn:incoming>Flow_15ugd6a</bpmn:incoming>
      <bpmn:outgoing>Flow_1asjruj</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:intermediateThrowEvent id="Event_0fzvruf" name="Milestone:- Evaluation">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="newMilestone">Evaluation</camunda:outputParameter>
          <camunda:outputParameter name="milestoneOperation">STARTED</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_02jo7ta</bpmn:incoming>
      <bpmn:incoming>Flow_14ij8ih</bpmn:incoming>
      <bpmn:outgoing>Flow_1rfw316</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:intermediateThrowEvent id="Event_0vx3ty5" name="Milestone:- Preparing Return">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="newMilestone">Preparing Return</camunda:outputParameter>
          <camunda:outputParameter name="milestoneOperation">STARTED</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0efb7xo</bpmn:incoming>
      <bpmn:incoming>Flow_0apc94y</bpmn:incoming>
      <bpmn:outgoing>Flow_1juqmfm</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:startEvent id="startEvent" isInterrupting="true" name="startEvent" parallelMultiple="false">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{ &quot;recordType&quot;: &quot;engagement&quot;}"/>
          <camunda:property name="stepDetails" value="{   &quot;startEvent&quot;: [     &quot;startEvent&quot;   ] }"/>
          <camunda:property name="startableEvents" value="[&quot;created&quot;]"/>
          <camunda:property name="processVariablesDetails" value="[   {     &quot;variableName&quot;: &quot;engagementId&quot;,     &quot;variableType&quot;: &quot;String&quot;   } ]"/>
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_1qalahq</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:boundaryEvent attachedToRef="Activity_0high9c" cancelActivity="true" id="Event_0geqh0m" name="Override Milestone" parallelMultiple="false">
      <bpmn:outgoing>Flow_0apc94y</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1o2jn6y" messageRef="Message_1qijmpo"/>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_0628d1x" sourceRef="Activity_1v06ow3" targetRef="Event_1bjektk"/>
    <bpmn:sequenceFlow id="Flow_00bd1hf" sourceRef="Activity_1nc02ym" targetRef="Event_1ow40jj"/>
    <bpmn:sequenceFlow id="Flow_1qalahq" sourceRef="startEvent" targetRef="Event_13fu65j"/>
    <bpmn:sequenceFlow id="Flow_1qk86w8" sourceRef="Activity_0f379q4" targetRef="Activity_0z1ajok"/>
    <bpmn:sequenceFlow id="Flow_1mcfhf9" sourceRef="Event_1n2b0mx" targetRef="Event_1kki2gd"/>
    <bpmn:sequenceFlow id="Flow_0fg2jyi" sourceRef="Event_1ow40jj" targetRef="Activity_089m3sh"/>
    <bpmn:sequenceFlow id="Flow_01opz0x" sourceRef="Event_05n1189" targetRef="Activity_0wd8moj"/>
    <bpmn:sequenceFlow id="Flow_0dwkltv" sourceRef="Event_138axi8" targetRef="Activity_0diy46m"/>
    <bpmn:sequenceFlow id="Flow_1asjruj" sourceRef="Event_0kxekbm" targetRef="Event_0peq26s"/>
    <bpmn:sequenceFlow id="Flow_1rfw316" sourceRef="Event_0fzvruf" targetRef="Event_1xcriqk"/>
    <bpmn:sequenceFlow id="Flow_1juqmfm" sourceRef="Event_0vx3ty5" targetRef="Event_1sxj8x5"/>
    <bpmn:subProcess completionQuantity="1" id="Activity_1pwgfvg" isForCompensation="false" name="Preparing Return" startQuantity="1" triggeredByEvent="false">
      <bpmn:incoming>Flow_10j9yb6</bpmn:incoming>
      <bpmn:outgoing>Flow_02jo7ta</bpmn:outgoing>
      <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_00e0cl5" implementation="##WebService" isForCompensation="false" name="Self Review" startQuantity="1">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-project-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="projectConfig">
              <camunda:map>
                <camunda:entry key="Self Review">{     "alias": "TY19 Full Service #1 . C. Preparing Return . C. Self Review",     "templateId": [       {         "env": "qal",         "value": "560442"       },       {         "env": "qa",         "value": "560442"       },       {         "env": "e2e",         "value": "560442"       },       {         "env": "prod",         "value": "541051"       }     ],     "projectName": [       {         "env": "qal",         "value": "C. Full Service Self Review"       },       {         "env": "qa",         "value": "C. Full Service Self Review"       },       {         "env": "e2e",         "value": "C. Full Service Self Review"       },       {         "env": "prod",         "value": "C. Self Review"       }     ]   }</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
            <camunda:inputParameter name="assignee">${currentAssignee}</camunda:inputParameter>
            <camunda:inputParameter name="onBehalfOf">${customerAuthId}</camunda:inputParameter>
            <camunda:inputParameter name="firmId">${firmId}</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1ugqxo4</bpmn:incoming>
        <bpmn:outgoing>Flow_019ff3w</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:startEvent id="Event_12tc9wi" isInterrupting="true" parallelMultiple="false">
        <bpmn:outgoing>Flow_1t5336c</bpmn:outgoing>
        <bpmn:outgoing>Flow_1vsq7pv</bpmn:outgoing>
        <bpmn:outgoing>Flow_1ugqxo4</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:parallelGateway camunda:asyncBefore="true" gatewayDirection="Unspecified" id="Gateway_186408p">
        <bpmn:incoming>Flow_109msdv</bpmn:incoming>
        <bpmn:incoming>Flow_15ax9x8</bpmn:incoming>
        <bpmn:incoming>Flow_019ff3w</bpmn:incoming>
        <bpmn:outgoing>Flow_0mjz2az</bpmn:outgoing>
      </bpmn:parallelGateway>
      <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_10jp24f" implementation="##WebService" isForCompensation="false" name="Preparation Checklist" startQuantity="1">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-project-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="projectConfig">
              <camunda:map>
                <camunda:entry key="Preparation Checklist">{     "alias": "TY19 Full Service #1 . C. Preparing Return . B. Preparation Checklist",     "templateId": [       {         "env": "qal",         "value": "560441"       },       {         "env": "qa",         "value": "560441"       },       {         "env": "e2e",         "value": "560441"       },       {         "env": "prod",         "value": "541203"       }     ],     "projectName": [       {         "env": "qal",         "value": "B. Full Service Preparation Checklist"       },       {         "env": "qa",         "value": "B. Full Service Preparation Checklist"       },       {         "env": "e2e",         "value": "B. Full Service Preparation Checklist"       },       {         "env": "prod",         "value": "B. Preparation Checklist"       }     ]   }</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
            <camunda:inputParameter name="assignee">${currentAssignee}</camunda:inputParameter>
            <camunda:inputParameter name="onBehalfOf">${customerAuthId}</camunda:inputParameter>
            <camunda:inputParameter name="firmId">${firmId}</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1vsq7pv</bpmn:incoming>
        <bpmn:outgoing>Flow_15ax9x8</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:intermediateCatchEvent id="Event_1u8ipfw" name="Complete Milestone" parallelMultiple="false">
        <bpmn:incoming>Flow_0mjz2az</bpmn:incoming>
        <bpmn:outgoing>Flow_09jedxb</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_0p2d231" messageRef="Message_13clq56"/>
      </bpmn:intermediateCatchEvent>
      <bpmn:endEvent id="Event_0u74wde">
        <bpmn:incoming>Flow_09jedxb</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_1t726z2" implementation="##WebService" isForCompensation="false" name="Return Preparation" startQuantity="1">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-project-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="projectConfig">
              <camunda:map>
                <camunda:entry key="Return Preparation">{     "alias": "TY19 Full Service #1 . C. Preparing Return . A. Return Preparation",     "templateId": [       {         "env": "qal",         "value": "560440"       },       {         "env": "qa",         "value": "560440"       },       {         "env": "e2e",         "value": "560440"       },       {         "env": "prod",         "value": "541202"       }     ],     "projectName": [       {         "env": "qal",         "value": "A. Full Service Return Preparation"       },       {         "env": "qa",         "value": "A. Full Service Return Preparation"       },       {         "env": "e2e",         "value": "A. Full Service Return Preparation"       },       {         "env": "prod",         "value": "A. Return Preparation"       }     ]   }</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
            <camunda:inputParameter name="assignee">${currentAssignee}</camunda:inputParameter>
            <camunda:inputParameter name="onBehalfOf">${customerAuthId}</camunda:inputParameter>
            <camunda:inputParameter name="firmId">${firmId}</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1t5336c</bpmn:incoming>
        <bpmn:outgoing>Flow_109msdv</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_109msdv" sourceRef="Activity_1t726z2" targetRef="Gateway_186408p"/>
      <bpmn:sequenceFlow id="Flow_1t5336c" sourceRef="Event_12tc9wi" targetRef="Activity_1t726z2"/>
      <bpmn:sequenceFlow id="Flow_09jedxb" sourceRef="Event_1u8ipfw" targetRef="Event_0u74wde"/>
      <bpmn:sequenceFlow id="Flow_0mjz2az" sourceRef="Gateway_186408p" targetRef="Event_1u8ipfw"/>
      <bpmn:sequenceFlow id="Flow_1vsq7pv" sourceRef="Event_12tc9wi" targetRef="Activity_10jp24f"/>
      <bpmn:sequenceFlow id="Flow_1ugqxo4" sourceRef="Event_12tc9wi" targetRef="Activity_00e0cl5"/>
      <bpmn:sequenceFlow id="Flow_15ax9x8" sourceRef="Activity_10jp24f" targetRef="Gateway_186408p"/>
      <bpmn:sequenceFlow id="Flow_019ff3w" sourceRef="Activity_00e0cl5" targetRef="Gateway_186408p"/>
    </bpmn:subProcess>
    <bpmn:boundaryEvent attachedToRef="Activity_1pwgfvg" cancelActivity="true" id="Event_0xwuv37" name="Override Milestone" parallelMultiple="false">
      <bpmn:outgoing>Flow_14ij8ih</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1v3kup2" messageRef="Message_1qijmpo"/>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_02jo7ta" sourceRef="Activity_1pwgfvg" targetRef="Event_0fzvruf"/>
    <bpmn:sequenceFlow id="Flow_14ij8ih" sourceRef="Event_0xwuv37" targetRef="Event_0fzvruf"/>
    <bpmn:subProcess completionQuantity="1" id="Activity_0v2er12" isForCompensation="false" name="Evaluation" startQuantity="1" triggeredByEvent="false">
      <bpmn:incoming>Flow_13gtdzk</bpmn:incoming>
      <bpmn:outgoing>Flow_1pbj1r0</bpmn:outgoing>
      <bpmn:startEvent id="Event_15zsent" isInterrupting="true" parallelMultiple="false">
        <bpmn:outgoing>Flow_1p77v13</bpmn:outgoing>
        <bpmn:outgoing>Flow_03ih1za</bpmn:outgoing>
        <bpmn:outgoing>Flow_15v5oz2</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:parallelGateway camunda:asyncBefore="true" gatewayDirection="Unspecified" id="Gateway_0bogblp">
        <bpmn:incoming>Flow_0veyll2</bpmn:incoming>
        <bpmn:incoming>Flow_0kqet7j</bpmn:incoming>
        <bpmn:incoming>Flow_0f5c2me</bpmn:incoming>
        <bpmn:outgoing>Flow_0zc4710</bpmn:outgoing>
      </bpmn:parallelGateway>
      <bpmn:intermediateCatchEvent id="Event_1l7faev" name="Complete Milestone" parallelMultiple="false">
        <bpmn:incoming>Flow_0zc4710</bpmn:incoming>
        <bpmn:outgoing>Flow_0qhtdcz</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_1ou996l" messageRef="Message_13clq56"/>
      </bpmn:intermediateCatchEvent>
      <bpmn:endEvent id="Event_0ldda2q">
        <bpmn:incoming>Flow_0qhtdcz</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_0s85hnt" implementation="##WebService" isForCompensation="false" name="Return Evaluation - SET Checklist" startQuantity="1">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-project-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="projectConfig">
              <camunda:map>
                <camunda:entry key="Return Evaluation - SET Checklist">{     "alias": "TY19 Full Service #1 . D. Evaluation . A. Return Evaluation - SET Checklist",     "templateId": [       {         "env": "qal",         "value": "560443"       },       {         "env": "qa",         "value": "560443"       },       {         "env": "e2e",         "value": "560443"       },       {         "env": "prod",         "value": "541052"       }     ],     "projectName": [       {         "env": "qal",         "value": "A. Full Service SET Checklist"       },       {         "env": "qa",         "value": "A. Full Service SET Checklist"       },       {         "env": "e2e",         "value": "A. Full Service SET Checklist"       },       {         "env": "prod",         "value": "A. Return Evaluation - SET Checklist"       }     ]   }</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
            <camunda:inputParameter name="assignee">${currentAssignee}</camunda:inputParameter>
            <camunda:inputParameter name="onBehalfOf">${customerAuthId}</camunda:inputParameter>
            <camunda:inputParameter name="firmId">${firmId}</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1p77v13</bpmn:incoming>
        <bpmn:outgoing>Flow_0veyll2</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_0tsgac9" implementation="##WebService" isForCompensation="false" name="SET Approval" startQuantity="1">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-project-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="projectConfig">
              <camunda:map>
                <camunda:entry key="Self Review">{     "alias": "TY19 Full Service #1 . D. Evaluation . C. SET Approval",     "templateId": [       {         "env": "qal",         "value": "560445"       },       {         "env": "qa",         "value": "560445"       },       {         "env": "e2e",         "value": "560445"       },       {         "env": "prod",         "value": "541054"       }     ],     "projectName": [       {         "env": "qal",         "value": "C. Full Service SET Approval"       },       {         "env": "qa",         "value": "C. Full Service SET Approval"       },       {         "env": "e2e",         "value": "C. Full Service SET Approval"       },       {         "env": "prod",         "value": "C. SET Approval"       }     ]   }</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
            <camunda:inputParameter name="assignee">${currentAssignee}</camunda:inputParameter>
            <camunda:inputParameter name="onBehalfOf">${customerAuthId}</camunda:inputParameter>
            <camunda:inputParameter name="firmId">${firmId}</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_15v5oz2</bpmn:incoming>
        <bpmn:outgoing>Flow_0f5c2me</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_1ely2dm" implementation="##WebService" isForCompensation="false" name="Signer Revisions" startQuantity="1">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-project-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="projectConfig">
              <camunda:map>
                <camunda:entry key="Signer Revisions">{     "alias": "TY19 Full Service #1 . D. Evaluation . B. Signer Revisions",     "templateId": [       {         "env": "qal",         "value": "560453"       },       {         "env": "qa",         "value": "560453"       },       {         "env": "e2e",         "value": "560453"       },       {         "env": "prod",         "value": "541255"       }     ],     "projectName": [       {         "env": "qal",         "value": "B. Full Service Signer Revisions"       },       {         "env": "qa",         "value": "B. Full Service Signer Revisions"       },       {         "env": "e2e",         "value": "B. Full Service Signer Revisions"       },       {         "env": "prod",         "value": "B. Signer Revisions"       }     ]   }</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
            <camunda:inputParameter name="assignee">${currentAssignee}</camunda:inputParameter>
            <camunda:inputParameter name="onBehalfOf">${customerAuthId}</camunda:inputParameter>
            <camunda:inputParameter name="firmId">${firmId}</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_03ih1za</bpmn:incoming>
        <bpmn:outgoing>Flow_0kqet7j</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_0veyll2" sourceRef="Activity_0s85hnt" targetRef="Gateway_0bogblp"/>
      <bpmn:sequenceFlow id="Flow_1p77v13" sourceRef="Event_15zsent" targetRef="Activity_0s85hnt"/>
      <bpmn:sequenceFlow id="Flow_0qhtdcz" sourceRef="Event_1l7faev" targetRef="Event_0ldda2q"/>
      <bpmn:sequenceFlow id="Flow_0zc4710" sourceRef="Gateway_0bogblp" targetRef="Event_1l7faev"/>
      <bpmn:sequenceFlow id="Flow_03ih1za" sourceRef="Event_15zsent" targetRef="Activity_1ely2dm"/>
      <bpmn:sequenceFlow id="Flow_15v5oz2" sourceRef="Event_15zsent" targetRef="Activity_0tsgac9"/>
      <bpmn:sequenceFlow id="Flow_0kqet7j" sourceRef="Activity_1ely2dm" targetRef="Gateway_0bogblp"/>
      <bpmn:sequenceFlow id="Flow_0f5c2me" sourceRef="Activity_0tsgac9" targetRef="Gateway_0bogblp"/>
    </bpmn:subProcess>
    <bpmn:boundaryEvent attachedToRef="Activity_0v2er12" cancelActivity="true" id="Event_0a20oua" name="Override Milestone" parallelMultiple="false">
      <bpmn:outgoing>Flow_15ugd6a</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0u7n1b7" messageRef="Message_1qijmpo"/>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_15ugd6a" sourceRef="Event_0a20oua" targetRef="Event_0kxekbm"/>
    <bpmn:sequenceFlow id="Flow_1pbj1r0" sourceRef="Activity_0v2er12" targetRef="Event_0kxekbm"/>
    <bpmn:subProcess completionQuantity="1" id="Activity_10fjotv" isForCompensation="false" name="Client Review" startQuantity="1" triggeredByEvent="false">
      <bpmn:incoming>Flow_1mv09fq</bpmn:incoming>
      <bpmn:outgoing>Flow_1ln5drr</bpmn:outgoing>
      <bpmn:startEvent id="Event_1o9x07r" isInterrupting="true" parallelMultiple="false">
        <bpmn:outgoing>Flow_1ayjgaw</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:endEvent id="Event_0siouhc">
        <bpmn:incoming>Flow_00wb4bk</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_1nztq05" implementation="##WebService" isForCompensation="false" name="Client Review" startQuantity="1">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-project-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="projectConfig">
              <camunda:map>
                <camunda:entry key="Client Review">{     "alias": "TY19 Full Service #1 . E. Client Review . Client Review",     "templateId": [       {         "env": "qal",         "value": "560446"       },       {         "env": "qa",         "value": "560446"       },       {         "env": "e2e",         "value": "560446"       },       {         "env": "prod",         "value": "541055"       }     ],     "projectName": [       {         "env": "qal",         "value": "Full Service Client Review"       },       {         "env": "qa",         "value": "Full Service Client Review"       },       {         "env": "e2e",         "value": "Full Service Client Review"       },       {         "env": "prod",         "value": "Client Review"       }     ]   }</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
            <camunda:inputParameter name="assignee">${currentAssignee}</camunda:inputParameter>
            <camunda:inputParameter name="onBehalfOf">${customerAuthId}</camunda:inputParameter>
            <camunda:inputParameter name="firmId">${firmId}</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_18473xx</bpmn:incoming>
        <bpmn:outgoing>Flow_0iqfpgc</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_1ayjgaw" sourceRef="Event_1o9x07r" targetRef="Gateway_172fa4n"/>
      <bpmn:sequenceFlow id="Flow_0iqfpgc" sourceRef="Activity_1nztq05" targetRef="Gateway_01trq3o"/>
      <bpmn:subProcess completionQuantity="1" id="Activity_1pbxo45" isForCompensation="false" name="(TTO) FnF Tech Debt" startQuantity="1" triggeredByEvent="false">
        <bpmn:incoming>Flow_1cr3o7k</bpmn:incoming>
        <bpmn:outgoing>Flow_1reo3ug</bpmn:outgoing>
        <bpmn:startEvent id="Event_1y4g3yx" isInterrupting="true" parallelMultiple="false">
          <bpmn:outgoing>Flow_198xfat</bpmn:outgoing>
        </bpmn:startEvent>
        <bpmn:sequenceFlow id="Flow_198xfat" sourceRef="Event_1y4g3yx" targetRef="Activity_1gouvz1"/>
        <bpmn:endEvent id="Event_1v0g1w0">
          <bpmn:incoming>Flow_163c3xs</bpmn:incoming>
        </bpmn:endEvent>
        <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_1gouvz1" implementation="##WebService" isForCompensation="false" name="Create Work Entity" startQuantity="1">
          <bpmn:extensionElements>
            <camunda:inputOutput>
              <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
              <camunda:inputParameter name="engagementRequest">
                <camunda:map>
                  <camunda:entry key="path">/v1/engagement/${engagementId}/milestone/start</camunda:entry>
                  <camunda:entry key="method">PATCH</camunda:entry>
                  <camunda:entry key="requestBody">{"milestone" : "Client Review"}</camunda:entry>
                </camunda:map>
              </camunda:inputParameter>
            </camunda:inputOutput>
          </bpmn:extensionElements>
          <bpmn:incoming>Flow_198xfat</bpmn:incoming>
          <bpmn:outgoing>Flow_1x4xxgb</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_18geoc0" implementation="##WebService" isForCompensation="false" name="Update Work Status In Progress" startQuantity="1">
          <bpmn:extensionElements>
            <camunda:inputOutput>
              <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
              <camunda:inputParameter name="engagementRequest">
                <camunda:map>
                  <camunda:entry key="path">/v1/engagement/#{engagementId}/work/#{clientReviewWorkId}/status/IN_PROGRESS</camunda:entry>
                  <camunda:entry key="method">PATCH</camunda:entry>
                </camunda:map>
              </camunda:inputParameter>
            </camunda:inputOutput>
          </bpmn:extensionElements>
          <bpmn:incoming>Flow_0oac3tg</bpmn:incoming>
          <bpmn:incoming>Flow_0rcx2h1</bpmn:incoming>
          <bpmn:outgoing>Flow_1rl4dey</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" default="Flow_0zl6871" id="Activity_1k8kokq" implementation="##WebService" isForCompensation="false" name="Wait for work entity creation" startQuantity="1">
          <bpmn:extensionElements>
            <camunda:inputOutput>
              <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
              <camunda:inputParameter name="engagementRequest">
                <camunda:map>
                  <camunda:entry key="path">/v1/engagement/#{engagementId}/works</camunda:entry>
                  <camunda:entry key="method">GET</camunda:entry>
                  <camunda:entry key="resultOperation">[   {     "outputVariableKey": "clientReviewWorkId",     "jsonPath": "$[?(@.milestone == \"Client Review\")].workId",     "singleValue": true   } ]</camunda:entry>
                </camunda:map>
              </camunda:inputParameter>
            </camunda:inputOutput>
          </bpmn:extensionElements>
          <bpmn:incoming>Flow_1x4xxgb</bpmn:incoming>
          <bpmn:incoming>Flow_0kmv0nv</bpmn:incoming>
          <bpmn:incoming>Flow_02bdhfk</bpmn:incoming>
          <bpmn:incoming>Flow_0lr2h83</bpmn:incoming>
          <bpmn:incoming>Flow_1badf5b</bpmn:incoming>
          <bpmn:outgoing>Flow_0oac3tg</bpmn:outgoing>
          <bpmn:outgoing>Flow_0zl6871</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0oac3tg" name="Found&#10; WorkId" sourceRef="Activity_1k8kokq" targetRef="Activity_18geoc0">
          <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${ clientReviewWorkId != '[]'}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="Flow_1x4xxgb" sourceRef="Activity_1gouvz1" targetRef="Activity_1k8kokq"/>
        <bpmn:sequenceFlow id="Flow_0kmv0nv" sourceRef="Event_0zvdba9" targetRef="Activity_1k8kokq"/>
        <bpmn:intermediateCatchEvent id="Event_0nlj8n9" name="Work Status Update" parallelMultiple="false">
          <bpmn:incoming>Flow_0la2yfv</bpmn:incoming>
          <bpmn:incoming>Flow_0v3mz39</bpmn:incoming>
          <bpmn:outgoing>Flow_163c3xs</bpmn:outgoing>
          <bpmn:messageEventDefinition id="MessageEventDefinition_1b0btsb" messageRef="Message_1yjnx1m"/>
        </bpmn:intermediateCatchEvent>
        <bpmn:sequenceFlow id="Flow_163c3xs" sourceRef="Event_0nlj8n9" targetRef="Event_1v0g1w0"/>
        <bpmn:intermediateCatchEvent id="Event_0zvdba9" name="Work Created" parallelMultiple="false">
          <bpmn:incoming>Flow_0zl6871</bpmn:incoming>
          <bpmn:outgoing>Flow_0kmv0nv</bpmn:outgoing>
          <bpmn:messageEventDefinition id="MessageEventDefinition_0d824q4" messageRef="Message_0i4jw9o"/>
        </bpmn:intermediateCatchEvent>
        <bpmn:sequenceFlow id="Flow_0zl6871" sourceRef="Activity_1k8kokq" targetRef="Event_0zvdba9"/>
        <bpmn:sequenceFlow id="Flow_0lr2h83" sourceRef="Event_1liiw3f" targetRef="Activity_1k8kokq"/>
        <bpmn:sequenceFlow id="Flow_02bdhfk" sourceRef="Event_1lw15ac" targetRef="Activity_1k8kokq"/>
        <bpmn:boundaryEvent attachedToRef="Activity_1gouvz1" cancelActivity="true" id="Event_1lw15ac" name="Milestone&#10;Start" parallelMultiple="false">
          <bpmn:outgoing>Flow_02bdhfk</bpmn:outgoing>
          <bpmn:messageEventDefinition id="MessageEventDefinition_1vk8w8r" messageRef="Message_1oc9jh3"/>
        </bpmn:boundaryEvent>
        <bpmn:boundaryEvent attachedToRef="Activity_1gouvz1" cancelActivity="true" id="Event_1liiw3f" name="Work&#10;Created" parallelMultiple="false">
          <bpmn:outgoing>Flow_0lr2h83</bpmn:outgoing>
          <bpmn:messageEventDefinition id="MessageEventDefinition_16r3n6b" messageRef="Message_0i4jw9o"/>
        </bpmn:boundaryEvent>
        <bpmn:intermediateCatchEvent id="Event_0gca3r0" name="Work Status Update to In Progress" parallelMultiple="false">
          <bpmn:incoming>Flow_1rl4dey</bpmn:incoming>
          <bpmn:outgoing>Flow_0la2yfv</bpmn:outgoing>
          <bpmn:messageEventDefinition id="MessageEventDefinition_0by7dvc" messageRef="Message_1yjnx1m"/>
        </bpmn:intermediateCatchEvent>
        <bpmn:sequenceFlow id="Flow_1rl4dey" sourceRef="Activity_18geoc0" targetRef="Event_0gca3r0"/>
        <bpmn:sequenceFlow id="Flow_0la2yfv" sourceRef="Event_0gca3r0" targetRef="Event_0nlj8n9"/>
        <bpmn:boundaryEvent attachedToRef="Activity_18geoc0" cancelActivity="true" id="Event_1thbozm" name="Work Status Update to In Progress" parallelMultiple="false">
          <bpmn:outgoing>Flow_0v3mz39</bpmn:outgoing>
          <bpmn:messageEventDefinition id="MessageEventDefinition_0kxv1c9" messageRef="Message_1yjnx1m"/>
        </bpmn:boundaryEvent>
        <bpmn:sequenceFlow id="Flow_0v3mz39" sourceRef="Event_1thbozm" targetRef="Event_0nlj8n9"/>
        <bpmn:boundaryEvent attachedToRef="Activity_1k8kokq" cancelActivity="true" id="Event_1xdtu5j" parallelMultiple="false">
          <bpmn:outgoing>Flow_1badf5b</bpmn:outgoing>
          <bpmn:timerEventDefinition id="TimerEventDefinition_096j7nj">
            <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">R1/PT2M</bpmn:timeCycle>
          </bpmn:timerEventDefinition>
        </bpmn:boundaryEvent>
        <bpmn:sequenceFlow id="Flow_1badf5b" sourceRef="Event_1xdtu5j" targetRef="Activity_1k8kokq"/>
        <bpmn:boundaryEvent attachedToRef="Activity_18geoc0" cancelActivity="true" id="Event_1qvv6jf" parallelMultiple="false">
          <bpmn:outgoing>Flow_0rcx2h1</bpmn:outgoing>
          <bpmn:timerEventDefinition id="TimerEventDefinition_0vhlwmg">
            <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">R1/PT2M</bpmn:timeCycle>
          </bpmn:timerEventDefinition>
        </bpmn:boundaryEvent>
        <bpmn:sequenceFlow id="Flow_0rcx2h1" sourceRef="Event_1qvv6jf" targetRef="Activity_18geoc0"/>
      </bpmn:subProcess>
      <bpmn:sequenceFlow id="Flow_18473xx" sourceRef="Gateway_172fa4n" targetRef="Activity_1nztq05"/>
      <bpmn:sequenceFlow id="Flow_1cr3o7k" sourceRef="Gateway_172fa4n" targetRef="Activity_1pbxo45"/>
      <bpmn:parallelGateway gatewayDirection="Unspecified" id="Gateway_172fa4n">
        <bpmn:incoming>Flow_1ayjgaw</bpmn:incoming>
        <bpmn:outgoing>Flow_18473xx</bpmn:outgoing>
        <bpmn:outgoing>Flow_1cr3o7k</bpmn:outgoing>
      </bpmn:parallelGateway>
      <bpmn:sequenceFlow id="Flow_00wb4bk" sourceRef="Gateway_01trq3o" targetRef="Event_0siouhc"/>
      <bpmn:sequenceFlow id="Flow_1reo3ug" sourceRef="Activity_1pbxo45" targetRef="Gateway_01trq3o"/>
      <bpmn:parallelGateway gatewayDirection="Unspecified" id="Gateway_01trq3o">
        <bpmn:incoming>Flow_1reo3ug</bpmn:incoming>
        <bpmn:incoming>Flow_0iqfpgc</bpmn:incoming>
        <bpmn:outgoing>Flow_00wb4bk</bpmn:outgoing>
      </bpmn:parallelGateway>
    </bpmn:subProcess>
    <bpmn:boundaryEvent attachedToRef="Activity_10fjotv" cancelActivity="true" id="Event_0q338g3" name="Override Milestone" parallelMultiple="false">
      <bpmn:outgoing>Flow_1we0fx9</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0hvuyur" messageRef="Message_1qijmpo"/>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_1we0fx9" sourceRef="Event_0q338g3" targetRef="Event_138axi8"/>
    <bpmn:sequenceFlow id="Flow_1ln5drr" sourceRef="Activity_10fjotv" targetRef="Event_138axi8"/>
    <bpmn:subProcess completionQuantity="1" id="Activity_0quuajf" isForCompensation="false" name="Post File" startQuantity="1" triggeredByEvent="false">
      <bpmn:incoming>Flow_1nztl3b</bpmn:incoming>
      <bpmn:outgoing>Flow_07g2ggd</bpmn:outgoing>
      <bpmn:startEvent id="Event_0dqc6ob" isInterrupting="true" parallelMultiple="false">
        <bpmn:outgoing>Flow_0qig890</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:endEvent id="Event_0bcib01">
        <bpmn:incoming>Flow_0m493eg</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_1b1mvhw" implementation="##WebService" isForCompensation="false" name="Wrap Up" startQuantity="1">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-project-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="projectConfig">
              <camunda:map>
                <camunda:entry key="Wrap Up">{     "alias": "TY19 Full Service #1 . I: Post File . Wrap Up",     "templateId": [       {         "env": "qal",         "value": "560451"       },       {         "env": "qa",         "value": "560451"       },       {         "env": "e2e",         "value": "560451"       },       {         "env": "prod",         "value": "541064"       }     ],     "projectName": [       {         "env": "qal",         "value": "Full Service Post File Wrap Up"       },       {         "env": "qa",         "value": "Full Service Post File Wrap Up"       },       {         "env": "e2e",         "value": "Full Service Post File Wrap Up"       },       {         "env": "prod",         "value": "Wrap Up"       }     ]   }</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
            <camunda:inputParameter name="assignee">${currentAssignee}</camunda:inputParameter>
            <camunda:inputParameter name="onBehalfOf">${customerAuthId}</camunda:inputParameter>
            <camunda:inputParameter name="firmId">${firmId}</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0qig890</bpmn:incoming>
        <bpmn:outgoing>Flow_0vk3n6v</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:intermediateCatchEvent id="Event_1ks67vx" name="Complete Milestone" parallelMultiple="false">
        <bpmn:incoming>Flow_0vk3n6v</bpmn:incoming>
        <bpmn:outgoing>Flow_0m493eg</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_0c9ga8s" messageRef="Message_13clq56"/>
      </bpmn:intermediateCatchEvent>
      <bpmn:sequenceFlow id="Flow_0m493eg" sourceRef="Event_1ks67vx" targetRef="Event_0bcib01"/>
      <bpmn:sequenceFlow id="Flow_0qig890" sourceRef="Event_0dqc6ob" targetRef="Activity_1b1mvhw"/>
      <bpmn:sequenceFlow id="Flow_0vk3n6v" sourceRef="Activity_1b1mvhw" targetRef="Event_1ks67vx"/>
    </bpmn:subProcess>
    <bpmn:boundaryEvent attachedToRef="Activity_0quuajf" cancelActivity="true" id="Event_0zbpubv" name="Override Milestone" parallelMultiple="false">
      <bpmn:outgoing>Flow_1anibps</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_19yyihq" messageRef="Message_1qijmpo"/>
    </bpmn:boundaryEvent>
    <bpmn:intermediateThrowEvent id="Event_0ymsc27" name="Milestone:- Filing Blocked">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="milestone">Filing</camunda:outputParameter>
          <camunda:outputParameter name="milestoneStatus">BLOCKED</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1kirbzb</bpmn:incoming>
      <bpmn:outgoing>Flow_1fredxf</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_1fredxf" sourceRef="Event_0ymsc27" targetRef="Activity_1m9ps2u"/>
    <bpmn:subProcess completionQuantity="1" id="Activity_0vfq6i3" isForCompensation="false" startQuantity="1" triggeredByEvent="true">
      <bpmn:startEvent id="Event_171lk3o" isInterrupting="true" name="Close Engagement" parallelMultiple="false">
        <bpmn:outgoing>Flow_0wd8vzx</bpmn:outgoing>
        <bpmn:escalationEventDefinition escalationRef="Escalation_188xam6" id="EscalationEventDefinition_01ieyod"/>
      </bpmn:startEvent>
      <bpmn:intermediateCatchEvent id="Event_14t8to2" name="Engagement Status Update" parallelMultiple="false">
        <bpmn:incoming>Flow_0bgrwrt</bpmn:incoming>
        <bpmn:outgoing>Flow_1197qb7</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_0tfp9tf" messageRef="Message_0mwl4zo"/>
      </bpmn:intermediateCatchEvent>
      <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" default="Flow_0bgrwrt" id="Activity_04068p7" implementation="##WebService" isForCompensation="false" name="Get Engagement Status" startQuantity="1">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="engagementRequest">
              <camunda:map>
                <camunda:entry key="path">/v1/engagement/${engagementId}/deep</camunda:entry>
                <camunda:entry key="method">GET</camunda:entry>
                <camunda:entry key="resultOperation">[{"outputVariableKey":"engagementStatus","jsonPath":"$.engagementStatus","singleValue":true}]</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1197qb7</bpmn:incoming>
        <bpmn:incoming>Flow_0wd8vzx</bpmn:incoming>
        <bpmn:incoming>Flow_0nnkyql</bpmn:incoming>
        <bpmn:outgoing>Flow_1a6yp5b</bpmn:outgoing>
        <bpmn:outgoing>Flow_0bgrwrt</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:endEvent id="Event_0enrmul">
        <bpmn:incoming>Flow_1a6yp5b</bpmn:incoming>
        <bpmn:terminateEventDefinition id="TerminateEventDefinition_12f93lc"/>
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="Flow_1a6yp5b" name="Completed" sourceRef="Activity_04068p7" targetRef="Event_0enrmul">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${engagementStatus == 'COMPLETED'}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="Flow_1197qb7" sourceRef="Event_14t8to2" targetRef="Activity_04068p7"/>
      <bpmn:sequenceFlow id="Flow_0bgrwrt" name="Not Completed" sourceRef="Activity_04068p7" targetRef="Event_14t8to2"/>
      <bpmn:sequenceFlow id="Flow_0wd8vzx" sourceRef="Event_171lk3o" targetRef="Activity_04068p7"/>
      <bpmn:boundaryEvent attachedToRef="Activity_04068p7" cancelActivity="true" id="Event_06xh53h" parallelMultiple="false">
        <bpmn:outgoing>Flow_0nnkyql</bpmn:outgoing>
        <bpmn:timerEventDefinition id="TimerEventDefinition_0x694j0">
          <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">R1/PT2M</bpmn:timeCycle>
        </bpmn:timerEventDefinition>
      </bpmn:boundaryEvent>
      <bpmn:sequenceFlow id="Flow_0nnkyql" sourceRef="Event_06xh53h" targetRef="Activity_04068p7"/>
    </bpmn:subProcess>
    <bpmn:sequenceFlow id="Flow_07g2ggd" sourceRef="Activity_0quuajf" targetRef="Event_1th2y64"/>
    <bpmn:sequenceFlow id="Flow_1anibps" sourceRef="Event_0zbpubv" targetRef="Event_1th2y64"/>
    <bpmn:intermediateThrowEvent id="Event_1th2y64" name="Close Engagement">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="closeReason">CLOSED</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1anibps</bpmn:incoming>
      <bpmn:incoming>Flow_07g2ggd</bpmn:incoming>
      <bpmn:escalationEventDefinition escalationRef="Escalation_188xam6" id="EscalationEventDefinition_1f4ygbc"/>
    </bpmn:intermediateThrowEvent>
    <bpmn:intermediateThrowEvent id="Event_1chta8d" name="Milestone:- Gathering info">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="newMilestone">Gathering Info</camunda:outputParameter>
          <camunda:outputParameter name="milestoneOperation">STARTED</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1nfmmeh</bpmn:incoming>
      <bpmn:incoming>Flow_0ero9u2</bpmn:incoming>
      <bpmn:outgoing>Flow_0trjzav</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_0trjzav" sourceRef="Event_1chta8d" targetRef="Event_1udg5hy"/>
    <bpmn:boundaryEvent attachedToRef="Activity_05smsqc" cancelActivity="true" id="Event_0444oex" name="Filing&#10;Rejected" parallelMultiple="false">
      <bpmn:outgoing>Flow_1kirbzb</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0ntxd6b" messageRef="Message_0p42hze"/>
    </bpmn:boundaryEvent>
    <bpmn:boundaryEvent attachedToRef="Activity_05smsqc" cancelActivity="true" id="Event_03aeaop" name="Override Milestone" parallelMultiple="false">
      <bpmn:outgoing>Flow_1lhpbi5</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_16vd4pb" messageRef="Message_1qijmpo"/>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_1kirbzb" sourceRef="Event_0444oex" targetRef="Event_0ymsc27"/>
    <bpmn:sequenceFlow id="Flow_1lhpbi5" sourceRef="Event_03aeaop" targetRef="Event_1n2b0mx"/>
    <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_0z1ajok" implementation="##WebService" isForCompensation="false" name="Setup&#10;(StepFn)" startQuantity="1">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-aws-step",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="templateName">setup_fs_workflow_arn</camunda:inputParameter>
          <camunda:inputParameter name="Id">${engagementId}</camunda:inputParameter>
          <camunda:inputParameter name="intuit_userid">${customerAuthId}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1lyw4rk</bpmn:incoming>
      <bpmn:incoming>Flow_1qk86w8</bpmn:incoming>
      <bpmn:outgoing>Flow_1vvc4oa</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1vvc4oa" sourceRef="Activity_0z1ajok" targetRef="Event_10k9vuf"/>
    <bpmn:subProcess completionQuantity="1" id="Activity_00qswlx" isForCompensation="false" name="Client Assigned" startQuantity="1" triggeredByEvent="false">
      <bpmn:incoming>Flow_0phfcnk</bpmn:incoming>
      <bpmn:outgoing>Flow_0ero9u2</bpmn:outgoing>
      <bpmn:startEvent id="Event_082o2ed" isInterrupting="true" parallelMultiple="false">
        <bpmn:outgoing>Flow_0478cgn</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:intermediateCatchEvent id="Event_07dcf8b" name="Assignment" parallelMultiple="false">
        <bpmn:incoming>Flow_1sfmeqk</bpmn:incoming>
        <bpmn:outgoing>Flow_0h1yuf3</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_0p052lj" messageRef="Message_0nrohfn"/>
      </bpmn:intermediateCatchEvent>
      <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" default="Flow_1sfmeqk" id="Activity_1j14b2p" implementation="##WebService" isForCompensation="false" name="Check Assignments" startQuantity="1">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="engagementRequest">
              <camunda:map>
                <camunda:entry key="path">/v1/engagement/${engagementId}/deep</camunda:entry>
                <camunda:entry key="method">GET</camunda:entry>
                <camunda:entry key="resultOperation">[   {     "outputVariableKey": "currentAssignee",     "jsonPath": "$.assignments[?(@.status == \"ASSIGNED\")].expertId",     "singleValue": true   },   {     "outputVariableKey": "assignmentIds",     "jsonPath": "$.assignments[?(@.status == \"ASSIGNED\")].assignmentId",     "singleValue": true   } ]</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0h1yuf3</bpmn:incoming>
        <bpmn:incoming>Flow_0478cgn</bpmn:incoming>
        <bpmn:incoming>Flow_0dmzoij</bpmn:incoming>
        <bpmn:outgoing>Flow_1k4rc0z</bpmn:outgoing>
        <bpmn:outgoing>Flow_1sfmeqk</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:endEvent id="Event_1cft000">
        <bpmn:incoming>Flow_1k4rc0z</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="Flow_1k4rc0z" name="true" sourceRef="Activity_1j14b2p" targetRef="Event_1cft000">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${currentAssignee != '[]'}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="Flow_1sfmeqk" name="false" sourceRef="Activity_1j14b2p" targetRef="Event_07dcf8b"/>
      <bpmn:sequenceFlow id="Flow_0h1yuf3" sourceRef="Event_07dcf8b" targetRef="Activity_1j14b2p"/>
      <bpmn:sequenceFlow id="Flow_0478cgn" sourceRef="Event_082o2ed" targetRef="Activity_1j14b2p"/>
      <bpmn:boundaryEvent attachedToRef="Activity_1j14b2p" cancelActivity="true" id="Event_15mu9qq" parallelMultiple="false">
        <bpmn:outgoing>Flow_0dmzoij</bpmn:outgoing>
        <bpmn:timerEventDefinition id="TimerEventDefinition_18j4nhu">
          <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">R1/PT2M</bpmn:timeCycle>
        </bpmn:timerEventDefinition>
      </bpmn:boundaryEvent>
      <bpmn:sequenceFlow id="Flow_0dmzoij" sourceRef="Event_15mu9qq" targetRef="Activity_1j14b2p"/>
    </bpmn:subProcess>
    <bpmn:sequenceFlow id="Flow_0ero9u2" sourceRef="Activity_00qswlx" targetRef="Event_1chta8d"/>
    <bpmn:boundaryEvent attachedToRef="Activity_00qswlx" cancelActivity="true" id="Event_0gmpmbc" name="Override Milestone" parallelMultiple="false">
      <bpmn:outgoing>Flow_1nfmmeh</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_08y3d6w" messageRef="Message_1qijmpo"/>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_1nfmmeh" sourceRef="Event_0gmpmbc" targetRef="Event_1chta8d"/>
    <bpmn:intermediateThrowEvent id="Event_10k9vuf" name="Milestone: Client Assigned">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="newMilestone">Client Assigned</camunda:outputParameter>
          <camunda:outputParameter name="milestoneOperation">STARTED</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1vvc4oa</bpmn:incoming>
      <bpmn:outgoing>Flow_0zehl2o</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_0zehl2o" sourceRef="Event_10k9vuf" targetRef="Event_0fa526x"/>
    <bpmn:subProcess completionQuantity="1" id="Activity_00hoaeh" isForCompensation="false" startQuantity="1" triggeredByEvent="true">
      <bpmn:sequenceFlow id="Flow_1mcr59j" sourceRef="Event_0k2v9ds" targetRef="Activity_0bwc3fs"/>
      <bpmn:sequenceFlow id="Flow_095hpil" name="Not Completed" sourceRef="Activity_0bwc3fs" targetRef="Event_0ik9ljb"/>
      <bpmn:sequenceFlow id="Flow_0pw02qr" name="Completed" sourceRef="Activity_0bwc3fs" targetRef="Event_1s6hk39">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${engagementStatus == 'COMPLETED'}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:intermediateThrowEvent id="Event_1s6hk39" name="Close Engagement">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:outputParameter name="closeReason">CLOSED</camunda:outputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0pw02qr</bpmn:incoming>
        <bpmn:escalationEventDefinition escalationRef="Escalation_188xam6" id="EscalationEventDefinition_0ivubad"/>
      </bpmn:intermediateThrowEvent>
      <bpmn:startEvent id="Event_0k2v9ds" isInterrupting="false" name="Engagement Status Update" parallelMultiple="false">
        <bpmn:outgoing>Flow_1mcr59j</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_1kspqgk" messageRef="Message_0mwl4zo"/>
      </bpmn:startEvent>
      <bpmn:endEvent id="Event_0ik9ljb">
        <bpmn:incoming>Flow_095hpil</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" default="Flow_095hpil" id="Activity_0bwc3fs" implementation="##WebService" isForCompensation="false" name="Get Engagement Status" startQuantity="1">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="engagementRequest">
              <camunda:map>
                <camunda:entry key="path">/v1/engagement/${engagementId}/deep</camunda:entry>
                <camunda:entry key="method">GET</camunda:entry>
                <camunda:entry key="resultOperation">[{"outputVariableKey":"engagementStatus","jsonPath":"$.engagementStatus","singleValue":true}]</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1mcr59j</bpmn:incoming>
        <bpmn:incoming>Flow_0o49i2t</bpmn:incoming>
        <bpmn:outgoing>Flow_095hpil</bpmn:outgoing>
        <bpmn:outgoing>Flow_0pw02qr</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:boundaryEvent attachedToRef="Activity_0bwc3fs" cancelActivity="true" id="Event_1u79s5e" parallelMultiple="false">
        <bpmn:outgoing>Flow_0o49i2t</bpmn:outgoing>
        <bpmn:timerEventDefinition id="TimerEventDefinition_19glxho">
          <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">R1/PT2M</bpmn:timeCycle>
        </bpmn:timerEventDefinition>
      </bpmn:boundaryEvent>
      <bpmn:sequenceFlow id="Flow_0o49i2t" sourceRef="Event_1u79s5e" targetRef="Activity_0bwc3fs"/>
    </bpmn:subProcess>
    <bpmn:sequenceFlow id="Flow_0nkwkm5" sourceRef="Event_18mpuz1" targetRef="Event_1ow40jj"/>
    <bpmn:boundaryEvent attachedToRef="Activity_1v06ow3" cancelActivity="true" id="Event_0bd402b" name="Override Milestone" parallelMultiple="false">
      <bpmn:outgoing>Flow_1842p3e</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1kc8vok" messageRef="Message_1qijmpo"/>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_1842p3e" sourceRef="Event_0bd402b" targetRef="Event_1bjektk"/>
    <bpmn:receiveTask completionQuantity="1" id="Activity_070bx12" implementation="##WebService" instantiate="false" isForCompensation="false" messageRef="Message_0otezgn" name="JMS: E-Signature Complete" startQuantity="1">
      <bpmn:incoming>Flow_0lu2rpw</bpmn:incoming>
      <bpmn:outgoing>Flow_0b49hu5</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:sequenceFlow id="Flow_0lu2rpw" sourceRef="Activity_089m3sh" targetRef="Activity_070bx12"/>
    <bpmn:sequenceFlow id="Flow_0b49hu5" sourceRef="Activity_070bx12" targetRef="Event_09rqaio"/>
    <bpmn:boundaryEvent attachedToRef="Activity_070bx12" cancelActivity="true" id="Event_08c0sei" name="Start Milestone" parallelMultiple="false">
      <bpmn:outgoing>Flow_0hdtrao</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0zwhqu5" messageRef="Message_1oc9jh3"/>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_0hdtrao" sourceRef="Event_08c0sei" targetRef="Event_0m8k29l"/>
    <bpmn:subProcess completionQuantity="1" id="Activity_00h5g6w" isForCompensation="false" startQuantity="1" triggeredByEvent="true">
      <bpmn:startEvent id="Event_0kqav6m" isInterrupting="false" parallelMultiple="false">
        <bpmn:outgoing>Flow_1fi6wuj</bpmn:outgoing>
        <bpmn:conditionalEventDefinition camunda:variableEvent="create, update" camunda:variableName="newMilestone" id="ConditionalEventDefinition_1e9m2c0">
          <bpmn:condition xsi:type="bpmn:tFormalExpression">${ execution.getVariable('newMilestone') != null }</bpmn:condition>
        </bpmn:conditionalEventDefinition>
      </bpmn:startEvent>
      <bpmn:sequenceFlow id="Flow_1fi6wuj" sourceRef="Event_0kqav6m" targetRef="Gateway_07rifb2"/>
      <bpmn:intermediateThrowEvent id="Event_1b2or91" name="CREATED">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:outputParameter name="milestone">${newMilestone}</camunda:outputParameter>
            <camunda:outputParameter name="newMilestoneStatus">CREATED</camunda:outputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_19svcm0</bpmn:incoming>
        <bpmn:incoming>Flow_104r4xk</bpmn:incoming>
        <bpmn:outgoing>Flow_0xly1ja</bpmn:outgoing>
      </bpmn:intermediateThrowEvent>
      <bpmn:sequenceFlow id="Flow_0xly1ja" sourceRef="Event_1b2or91" targetRef="Activity_1ky3sgg"/>
      <bpmn:intermediateThrowEvent id="Event_0fgobso" name="IN_PROGRESS">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:outputParameter name="newMilestoneStatus">IN_PROGRESS</camunda:outputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_008v4uh</bpmn:incoming>
        <bpmn:outgoing>Flow_1dwgzy9</bpmn:outgoing>
      </bpmn:intermediateThrowEvent>
      <bpmn:sequenceFlow id="Flow_1dwgzy9" sourceRef="Event_0fgobso" targetRef="Activity_1woybm1"/>
      <bpmn:intermediateThrowEvent id="Event_0oz60qb" name="COMPLETED">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:outputParameter name="newMilestoneStatus">COMPLETED</camunda:outputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0ridwgu</bpmn:incoming>
        <bpmn:outgoing>Flow_1vgl6oz</bpmn:outgoing>
      </bpmn:intermediateThrowEvent>
      <bpmn:exclusiveGateway default="Flow_0k56x86" gatewayDirection="Unspecified" id="Gateway_07rifb2">
        <bpmn:incoming>Flow_1fi6wuj</bpmn:incoming>
        <bpmn:outgoing>Flow_19svcm0</bpmn:outgoing>
        <bpmn:outgoing>Flow_0ridwgu</bpmn:outgoing>
        <bpmn:outgoing>Flow_0k56x86</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:sequenceFlow id="Flow_19svcm0" sourceRef="Gateway_07rifb2" targetRef="Event_1b2or91">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${ execution.getVariable('newMilestoneStatus') == null}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="Flow_0ridwgu" name="IN_PROGRESS" sourceRef="Gateway_07rifb2" targetRef="Event_0oz60qb">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${ execution.getVariable('newMilestoneStatus') != null &amp;&amp; newMilestoneStatus == 'IN_PROGRESS'}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="Flow_1vgl6oz" sourceRef="Event_0oz60qb" targetRef="Activity_1ivtvsk"/>
      <bpmn:intermediateThrowEvent id="Event_193ogdu" name="">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:outputParameter name="milestoneOperation">COMPLETE</camunda:outputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0m0lpqv</bpmn:incoming>
      </bpmn:intermediateThrowEvent>
      <bpmn:sequenceFlow id="Flow_0k56x86" sourceRef="Gateway_07rifb2" targetRef="Event_1snx2oz"/>
      <bpmn:endEvent id="Event_1snx2oz" name="Unknown Milestone Update">
        <bpmn:incoming>Flow_0k56x86</bpmn:incoming>
        <bpmn:errorEventDefinition errorRef="Error_04rk94q" id="ErrorEventDefinition_0ywwzup"/>
      </bpmn:endEvent>
      <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_1ivtvsk" implementation="##WebService" isForCompensation="false" name="Update Milestone Status" startQuantity="1">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="engagementRequest">
              <camunda:map>
                <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
                <camunda:entry key="method">PATCH</camunda:entry>
                <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{newMilestoneStatus}"}</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1vgl6oz</bpmn:incoming>
        <bpmn:incoming>Flow_023h0ma</bpmn:incoming>
        <bpmn:outgoing>Flow_104r4xk</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_104r4xk" sourceRef="Activity_1ivtvsk" targetRef="Event_1b2or91"/>
      <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_1ky3sgg" implementation="##WebService" isForCompensation="false" name="Update Milestone Status" startQuantity="1">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="engagementRequest">
              <camunda:map>
                <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
                <camunda:entry key="method">PATCH</camunda:entry>
                <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{newMilestoneStatus}"}</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0xly1ja</bpmn:incoming>
        <bpmn:incoming>Flow_0xrzt9o</bpmn:incoming>
        <bpmn:outgoing>Flow_008v4uh</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_1woybm1" implementation="##WebService" isForCompensation="false" name="Update Milestone Status" startQuantity="1">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="engagementRequest">
              <camunda:map>
                <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
                <camunda:entry key="method">PATCH</camunda:entry>
                <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{newMilestoneStatus}"}</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1dwgzy9</bpmn:incoming>
        <bpmn:incoming>Flow_190i9b2</bpmn:incoming>
        <bpmn:outgoing>Flow_0m0lpqv</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_008v4uh" sourceRef="Activity_1ky3sgg" targetRef="Event_0fgobso"/>
      <bpmn:sequenceFlow id="Flow_0m0lpqv" sourceRef="Activity_1woybm1" targetRef="Event_193ogdu"/>
      <bpmn:boundaryEvent attachedToRef="Activity_1ivtvsk" cancelActivity="true" id="Event_13qx7k8" parallelMultiple="false">
        <bpmn:outgoing>Flow_023h0ma</bpmn:outgoing>
        <bpmn:timerEventDefinition id="TimerEventDefinition_0dqixgn">
          <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">R1/PT2M</bpmn:timeCycle>
        </bpmn:timerEventDefinition>
      </bpmn:boundaryEvent>
      <bpmn:sequenceFlow id="Flow_023h0ma" sourceRef="Event_13qx7k8" targetRef="Activity_1ivtvsk"/>
      <bpmn:boundaryEvent attachedToRef="Activity_1ky3sgg" cancelActivity="true" id="Event_1age5s2" parallelMultiple="false">
        <bpmn:outgoing>Flow_0xrzt9o</bpmn:outgoing>
        <bpmn:timerEventDefinition id="TimerEventDefinition_0sg3ggi">
          <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">R1/PT2M</bpmn:timeCycle>
        </bpmn:timerEventDefinition>
      </bpmn:boundaryEvent>
      <bpmn:sequenceFlow id="Flow_0xrzt9o" sourceRef="Event_1age5s2" targetRef="Activity_1ky3sgg"/>
      <bpmn:boundaryEvent attachedToRef="Activity_1woybm1" cancelActivity="true" id="Event_16mw62p" parallelMultiple="false">
        <bpmn:outgoing>Flow_190i9b2</bpmn:outgoing>
        <bpmn:timerEventDefinition id="TimerEventDefinition_1qtadka">
          <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">R1/PT2M</bpmn:timeCycle>
        </bpmn:timerEventDefinition>
      </bpmn:boundaryEvent>
      <bpmn:sequenceFlow id="Flow_190i9b2" sourceRef="Event_16mw62p" targetRef="Activity_1woybm1"/>
    </bpmn:subProcess>
    <bpmn:intermediateCatchEvent id="Event_1udg5hy" name="Milestone Updated" parallelMultiple="false">
      <bpmn:incoming>Flow_0trjzav</bpmn:incoming>
      <bpmn:outgoing>Flow_13vgnpo</bpmn:outgoing>
      <bpmn:conditionalEventDefinition camunda:variableEvent="create, update" camunda:variableName="milestoneOperation" id="ConditionalEventDefinition_0qpa24l">
        <bpmn:condition xsi:type="bpmn:tFormalExpression">${ milestoneOperation == 'COMPLETE'}</bpmn:condition>
      </bpmn:conditionalEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:sequenceFlow id="Flow_13vgnpo" sourceRef="Event_1udg5hy" targetRef="Activity_157fl83"/>
    <bpmn:sequenceFlow id="Flow_0apc94y" sourceRef="Event_0geqh0m" targetRef="Event_0vx3ty5"/>
    <bpmn:sequenceFlow id="Flow_0efb7xo" sourceRef="Activity_0high9c" targetRef="Event_0vx3ty5"/>
    <bpmn:intermediateCatchEvent id="Event_0fa526x" name="Milestone Updated" parallelMultiple="false">
      <bpmn:incoming>Flow_0zehl2o</bpmn:incoming>
      <bpmn:outgoing>Flow_0phfcnk</bpmn:outgoing>
      <bpmn:conditionalEventDefinition camunda:variableEvent="create, update" camunda:variableName="milestoneOperation" id="ConditionalEventDefinition_015ge68">
        <bpmn:condition xsi:type="bpmn:tFormalExpression">${ milestoneOperation == 'COMPLETE'}</bpmn:condition>
      </bpmn:conditionalEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:sequenceFlow id="Flow_0phfcnk" sourceRef="Event_0fa526x" targetRef="Activity_00qswlx"/>
    <bpmn:intermediateCatchEvent id="Event_1sxj8x5" name="Milestone Updated" parallelMultiple="false">
      <bpmn:incoming>Flow_1juqmfm</bpmn:incoming>
      <bpmn:outgoing>Flow_10j9yb6</bpmn:outgoing>
      <bpmn:conditionalEventDefinition camunda:variableEvent="create, update" camunda:variableName="milestoneOperation" id="ConditionalEventDefinition_0gcz8fl">
        <bpmn:condition xsi:type="bpmn:tFormalExpression">${ milestoneOperation == 'COMPLETE'}</bpmn:condition>
      </bpmn:conditionalEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:sequenceFlow id="Flow_10j9yb6" sourceRef="Event_1sxj8x5" targetRef="Activity_1pwgfvg"/>
    <bpmn:intermediateCatchEvent id="Event_1xcriqk" name="Milestone Updated" parallelMultiple="false">
      <bpmn:incoming>Flow_1rfw316</bpmn:incoming>
      <bpmn:outgoing>Flow_13gtdzk</bpmn:outgoing>
      <bpmn:conditionalEventDefinition camunda:variableEvent="create, update" camunda:variableName="milestoneOperation" id="ConditionalEventDefinition_07a8jbw">
        <bpmn:condition xsi:type="bpmn:tFormalExpression">${ milestoneOperation == 'COMPLETE'}</bpmn:condition>
      </bpmn:conditionalEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:sequenceFlow id="Flow_13gtdzk" sourceRef="Event_1xcriqk" targetRef="Activity_0v2er12"/>
    <bpmn:intermediateCatchEvent id="Event_0peq26s" name="Wait for Milestone Update" parallelMultiple="false">
      <bpmn:incoming>Flow_1asjruj</bpmn:incoming>
      <bpmn:outgoing>Flow_1mv09fq</bpmn:outgoing>
      <bpmn:conditionalEventDefinition camunda:variableEvent="create, update" camunda:variableName="milestoneOperation" id="ConditionalEventDefinition_1w57zw8">
        <bpmn:condition xsi:type="bpmn:tFormalExpression">${ milestoneOperation == 'COMPLETE'}</bpmn:condition>
      </bpmn:conditionalEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:sequenceFlow id="Flow_1mv09fq" sourceRef="Event_0peq26s" targetRef="Activity_10fjotv"/>
    <bpmn:sequenceFlow id="Flow_0yvv6lc" sourceRef="Activity_1whocbs" targetRef="Event_05n1189"/>
    <bpmn:receiveTask completionQuantity="1" id="Activity_1whocbs" implementation="##WebService" instantiate="false" isForCompensation="false" messageRef="Message_1oc9jh3" name="Start E-File Authorization" startQuantity="1">
      <bpmn:incoming>Flow_0qhde4m</bpmn:incoming>
      <bpmn:outgoing>Flow_0yvv6lc</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_0wd8moj" implementation="##WebService" isForCompensation="false" name="Update Milestone and Status" startQuantity="1">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
              <camunda:entry key="method">PATCH</camunda:entry>
              <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{milestoneStatus}"}</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_01opz0x</bpmn:incoming>
      <bpmn:incoming>Flow_0fhl5gk</bpmn:incoming>
      <bpmn:outgoing>Flow_1wspseh</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1wspseh" sourceRef="Activity_0wd8moj" targetRef="Activity_1nc02ym"/>
    <bpmn:intermediateCatchEvent id="Event_1kki2gd" name="Milestone Updated" parallelMultiple="false">
      <bpmn:incoming>Flow_1mcfhf9</bpmn:incoming>
      <bpmn:outgoing>Flow_1nztl3b</bpmn:outgoing>
      <bpmn:conditionalEventDefinition camunda:variableEvent="create, update" camunda:variableName="milestoneOperation" id="ConditionalEventDefinition_1d52a90">
        <bpmn:condition xsi:type="bpmn:tFormalExpression">${ milestoneOperation == 'COMPLETE'}</bpmn:condition>
      </bpmn:conditionalEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:sequenceFlow id="Flow_1nztl3b" sourceRef="Event_1kki2gd" targetRef="Activity_0quuajf"/>
    <bpmn:boundaryEvent attachedToRef="Activity_05smsqc" cancelActivity="true" id="Event_1q10yql" name="E-File Submit" parallelMultiple="false">
      <bpmn:outgoing>Flow_1t4394s</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1q2ez0h" messageRef="Message_1cysixt"/>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_1t4394s" sourceRef="Event_1q10yql" targetRef="Event_1bg8vmj"/>
    <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_1m9ps2u" implementation="##WebService" isForCompensation="false" name="Update Milestone and Status" startQuantity="1">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
              <camunda:entry key="method">PATCH</camunda:entry>
              <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{milestoneStatus}"}</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_078kids</bpmn:incoming>
      <bpmn:incoming>Flow_1fredxf</bpmn:incoming>
      <bpmn:incoming>Flow_196tk97</bpmn:incoming>
      <bpmn:outgoing>Flow_1390vsl</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1390vsl" sourceRef="Activity_1m9ps2u" targetRef="Activity_05smsqc"/>
    <bpmn:intermediateThrowEvent id="Event_1bg8vmj" name="Milestone:- Filing In Progress">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="milestone">Filing</camunda:outputParameter>
          <camunda:outputParameter name="milestoneStatus">IN_PROGRESS</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1t4394s</bpmn:incoming>
      <bpmn:outgoing>Flow_078kids</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_078kids" sourceRef="Event_1bg8vmj" targetRef="Activity_1m9ps2u"/>
    <bpmn:subProcess completionQuantity="1" id="Activity_05smsqc" isForCompensation="false" startQuantity="1" triggeredByEvent="false">
      <bpmn:incoming>Flow_1390vsl</bpmn:incoming>
      <bpmn:incoming>Flow_0ephib4</bpmn:incoming>
      <bpmn:outgoing>Flow_1qxkx9n</bpmn:outgoing>
      <bpmn:startEvent id="Event_1cr2lw6" isInterrupting="true" parallelMultiple="false">
        <bpmn:outgoing>Flow_1c4s7ou</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:receiveTask completionQuantity="1" id="Activity_0vf6cow" implementation="##WebService" instantiate="false" isForCompensation="false" messageRef="Message_0ya9e3t" name="Filing Success" startQuantity="1">
        <bpmn:incoming>Flow_1c4s7ou</bpmn:incoming>
        <bpmn:outgoing>Flow_1ubpfaj</bpmn:outgoing>
      </bpmn:receiveTask>
      <bpmn:sequenceFlow id="Flow_1c4s7ou" sourceRef="Event_1cr2lw6" targetRef="Activity_0vf6cow"/>
      <bpmn:endEvent id="Event_1ram7f8">
        <bpmn:incoming>Flow_1ubpfaj</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="Flow_1ubpfaj" sourceRef="Activity_0vf6cow" targetRef="Event_1ram7f8"/>
    </bpmn:subProcess>
    <bpmn:sequenceFlow id="Flow_1qxkx9n" name="Success" sourceRef="Activity_05smsqc" targetRef="Event_1n2b0mx"/>
    <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_0diy46m" implementation="##WebService" isForCompensation="false" name="Update Milestone and Status" startQuantity="1">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
              <camunda:entry key="method">PATCH</camunda:entry>
              <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{milestoneStatus}"}</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0dwkltv</bpmn:incoming>
      <bpmn:outgoing>Flow_0qhde4m</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0qhde4m" sourceRef="Activity_0diy46m" targetRef="Activity_1whocbs"/>
    <bpmn:intermediateThrowEvent id="Event_09rqaio" name="Milestone:- Ready to File In Progress">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="milestone">Ready to file</camunda:outputParameter>
          <camunda:outputParameter name="milestoneStatus">CREATED</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0b49hu5</bpmn:incoming>
      <bpmn:incoming>Flow_1xzmfim</bpmn:incoming>
      <bpmn:outgoing>Flow_1wx391z</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_1wx391z" sourceRef="Event_09rqaio" targetRef="Activity_0z2r6sj"/>
    <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_0z2r6sj" implementation="##WebService" isForCompensation="false" name="Update Milestone and Status" startQuantity="1">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
              <camunda:entry key="method">PATCH</camunda:entry>
              <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{milestoneStatus}"}</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1wx391z</bpmn:incoming>
      <bpmn:incoming>Flow_0x52wba</bpmn:incoming>
      <bpmn:outgoing>Flow_1wdgdoe</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1wdgdoe" sourceRef="Activity_0z2r6sj" targetRef="Activity_1v06ow3"/>
    <bpmn:intermediateThrowEvent id="Event_1bjektk" name="Milestone:- Ready to File Completed">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="milestone">Ready to file</camunda:outputParameter>
          <camunda:outputParameter name="milestoneStatus">COMPLETED</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0628d1x</bpmn:incoming>
      <bpmn:incoming>Flow_1842p3e</bpmn:incoming>
      <bpmn:outgoing>Flow_0fydm4j</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_0fydm4j" sourceRef="Event_1bjektk" targetRef="Activity_0ucifop"/>
    <bpmn:sequenceFlow id="Flow_1wglfr4" sourceRef="Activity_0ucifop" targetRef="Event_0b2ezuq"/>
    <bpmn:boundaryEvent attachedToRef="Activity_1nc02ym" cancelActivity="true" id="Event_18mpuz1" name="Override Milestone" parallelMultiple="false">
      <bpmn:outgoing>Flow_0nkwkm5</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1mznxgd" messageRef="Message_1qijmpo"/>
    </bpmn:boundaryEvent>
    <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_157fl83" implementation="##WebService" isForCompensation="false" name="Engagement Status to In Progress" startQuantity="1">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="path">/v1/engagement/#{engagementId}/status/IN_PROGRESS</camunda:entry>
              <camunda:entry key="method">PATCH</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_13vgnpo</bpmn:incoming>
      <bpmn:incoming>Flow_17wxx1r</bpmn:incoming>
      <bpmn:outgoing>Flow_1pakrh8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1pakrh8" sourceRef="Activity_157fl83" targetRef="Activity_0high9c"/>
    <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_0679y6c" implementation="##WebService" isForCompensation="false" name="Update Milestone and Status" startQuantity="1">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
              <camunda:entry key="method">PATCH</camunda:entry>
              <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{milestoneStatus}"}</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_16e8roa</bpmn:incoming>
      <bpmn:incoming>Flow_1yyzjwd</bpmn:incoming>
      <bpmn:outgoing>Flow_1xzmfim</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1xzmfim" sourceRef="Activity_0679y6c" targetRef="Event_09rqaio"/>
    <bpmn:intermediateThrowEvent id="Event_0m8k29l" name="Milestone:- Ready to File Created">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="milestone">Ready to file</camunda:outputParameter>
          <camunda:outputParameter name="milestoneStatus">CREATED</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0hdtrao</bpmn:incoming>
      <bpmn:outgoing>Flow_16e8roa</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_16e8roa" sourceRef="Event_0m8k29l" targetRef="Activity_0679y6c"/>
    <bpmn:intermediateThrowEvent id="Event_0b2ezuq" name="Milestone:- Filing In Progress">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="milestone">Filing</camunda:outputParameter>
          <camunda:outputParameter name="milestoneStatus">CREATED</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1wglfr4</bpmn:incoming>
      <bpmn:outgoing>Flow_0hunamg</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_0hunamg" sourceRef="Event_0b2ezuq" targetRef="Activity_1hnd6bi"/>
    <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_1hnd6bi" implementation="##WebService" isForCompensation="false" name="Update Milestone and Status" startQuantity="1">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
              <camunda:entry key="method">PATCH</camunda:entry>
              <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "#{milestoneStatus}"}</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0hunamg</bpmn:incoming>
      <bpmn:incoming>Flow_1cusb0m</bpmn:incoming>
      <bpmn:outgoing>Flow_0ephib4</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0ephib4" sourceRef="Activity_1hnd6bi" targetRef="Activity_05smsqc"/>
    <bpmn:sequenceFlow id="Flow_0f3ym69" sourceRef="Event_13gs364" targetRef="Event_05n1189"/>
    <bpmn:boundaryEvent attachedToRef="Activity_0f379q4" cancelActivity="true" id="Event_10sd9gr" parallelMultiple="false">
      <bpmn:outgoing>Flow_1dkn0d6</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_1e2zhpg">
        <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">R1/PT2M</bpmn:timeCycle>
      </bpmn:timerEventDefinition>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_1dkn0d6" sourceRef="Event_10sd9gr" targetRef="Activity_0f379q4"/>
    <bpmn:sequenceFlow id="Flow_1lyw4rk" sourceRef="Event_1ds0rkt" targetRef="Activity_0z1ajok"/>
    <bpmn:boundaryEvent attachedToRef="Activity_157fl83" cancelActivity="true" id="Event_1wobyh0" parallelMultiple="false">
      <bpmn:outgoing>Flow_17wxx1r</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_1jm8kwf">
        <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">R1/PT2M</bpmn:timeCycle>
      </bpmn:timerEventDefinition>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_17wxx1r" sourceRef="Event_1wobyh0" targetRef="Activity_157fl83"/>
    <bpmn:boundaryEvent attachedToRef="Activity_089m3sh" cancelActivity="true" id="Event_0vhckqr" parallelMultiple="false">
      <bpmn:outgoing>Flow_1sbozku</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_0da75sb">
        <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">R1/PT2M</bpmn:timeCycle>
      </bpmn:timerEventDefinition>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_1sbozku" sourceRef="Event_0vhckqr" targetRef="Activity_089m3sh"/>
    <bpmn:boundaryEvent attachedToRef="Activity_0wd8moj" cancelActivity="true" id="Event_0bdw6ep" parallelMultiple="false">
      <bpmn:outgoing>Flow_0fhl5gk</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_17b32ht">
        <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">R1/PT2M</bpmn:timeCycle>
      </bpmn:timerEventDefinition>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_0fhl5gk" sourceRef="Event_0bdw6ep" targetRef="Activity_0wd8moj"/>
    <bpmn:boundaryEvent attachedToRef="Activity_0679y6c" cancelActivity="true" id="Event_039teyx" parallelMultiple="false">
      <bpmn:outgoing>Flow_1yyzjwd</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_08s8a59">
        <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">R1/PT2M</bpmn:timeCycle>
      </bpmn:timerEventDefinition>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_1yyzjwd" sourceRef="Event_039teyx" targetRef="Activity_0679y6c"/>
    <bpmn:boundaryEvent attachedToRef="Activity_0z2r6sj" cancelActivity="true" id="Event_14opq3c" parallelMultiple="false">
      <bpmn:outgoing>Flow_0x52wba</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_1rvoe0n">
        <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">R1/PT2M</bpmn:timeCycle>
      </bpmn:timerEventDefinition>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_0x52wba" sourceRef="Event_14opq3c" targetRef="Activity_0z2r6sj"/>
    <bpmn:boundaryEvent attachedToRef="Activity_0ucifop" cancelActivity="true" id="Event_0ay42ls" parallelMultiple="false">
      <bpmn:outgoing>Flow_1yuzcbv</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_1bisun5">
        <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">R1/PT2M</bpmn:timeCycle>
      </bpmn:timerEventDefinition>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_1yuzcbv" sourceRef="Event_0ay42ls" targetRef="Activity_0ucifop"/>
    <bpmn:boundaryEvent attachedToRef="Activity_1hnd6bi" cancelActivity="true" id="Event_01z91y6" parallelMultiple="false">
      <bpmn:outgoing>Flow_1cusb0m</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_0i2fp6v">
        <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">R1/PT2M</bpmn:timeCycle>
      </bpmn:timerEventDefinition>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_1cusb0m" sourceRef="Event_01z91y6" targetRef="Activity_1hnd6bi"/>
    <bpmn:boundaryEvent attachedToRef="Activity_1m9ps2u" cancelActivity="true" id="Event_0doxj6u" parallelMultiple="false">
      <bpmn:outgoing>Flow_196tk97</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_1pzlzbl">
        <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">R1/PT2M</bpmn:timeCycle>
      </bpmn:timerEventDefinition>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_196tk97" sourceRef="Event_0doxj6u" targetRef="Activity_1m9ps2u"/>
    <bpmn:boundaryEvent attachedToRef="Activity_1whocbs" cancelActivity="true" id="Event_13gs364" name="Override Milestone" parallelMultiple="false">
      <bpmn:outgoing>Flow_0f3ym69</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_04dnjk5" messageRef="Message_1qijmpo"/>
    </bpmn:boundaryEvent>
    <bpmn:subProcess completionQuantity="1" id="Activity_1ub4uec" isForCompensation="false" startQuantity="1" triggeredByEvent="true">
      <bpmn:serviceTask camunda:topic="engagement" camunda:type="external" completionQuantity="1" id="Activity_0lp5px6" implementation="##WebService" isForCompensation="false" name="Update Milestone Status" startQuantity="1">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
"taskHandler":"was",
"handlerId":"intuit-workflows/vep-int-engagement-service",
"actionName":"publishEvent"
}</camunda:inputParameter>
            <camunda:inputParameter name="engagementRequest">
              <camunda:map>
                <camunda:entry key="path">/v1/engagement/#{engagementId}/milestone</camunda:entry>
                <camunda:entry key="method">PATCH</camunda:entry>
                <camunda:entry key="requestBody">{"milestone" : "#{milestone}", "milestoneStatus": "BLOCKED"}</camunda:entry>
              </camunda:map>
            </camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1rlz9jr</bpmn:incoming>
      </bpmn:serviceTask>
      <bpmn:exclusiveGateway default="Flow_1aitcb3" gatewayDirection="Unspecified" id="Gateway_0jzqvuk" name="hasIncidents ?">
        <bpmn:incoming>Flow_11bbdmr</bpmn:incoming>
        <bpmn:outgoing>Flow_1aitcb3</bpmn:outgoing>
        <bpmn:outgoing>Flow_1rlz9jr</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:scriptTask completionQuantity="1" id="Activity_0x45xis" isForCompensation="false" name="log incident count" scriptFormat="javascript" startQuantity="1">
        <bpmn:incoming>Flow_1xsgq4b</bpmn:incoming>
        <bpmn:outgoing>Flow_11bbdmr</bpmn:outgoing>
        <bpmn:script>java.lang.System.out.println("processInstanceId="+ execution.getProcessInstanceId() + ", Incident count = "+execution.getProcessEngineServices().getRuntimeService().createIncidentQuery().processInstanceId(execution.getProcessInstanceId()).list().size())</bpmn:script>
      </bpmn:scriptTask>
      <bpmn:startEvent id="Event_0yaq6h7" isInterrupting="false" parallelMultiple="false">
        <bpmn:outgoing>Flow_1xsgq4b</bpmn:outgoing>
        <bpmn:timerEventDefinition id="TimerEventDefinition_0dh4ela">
          <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">0 0/5 * 1/1 * ? *</bpmn:timeCycle>
        </bpmn:timerEventDefinition>
      </bpmn:startEvent>
      <bpmn:intermediateThrowEvent id="Event_10z2xmm">
        <bpmn:incoming>Flow_1aitcb3</bpmn:incoming>
      </bpmn:intermediateThrowEvent>
      <bpmn:sequenceFlow id="Flow_1aitcb3" sourceRef="Gateway_0jzqvuk" targetRef="Event_10z2xmm"/>
      <bpmn:sequenceFlow id="Flow_1xsgq4b" sourceRef="Event_0yaq6h7" targetRef="Activity_0x45xis"/>
      <bpmn:sequenceFlow id="Flow_11bbdmr" sourceRef="Activity_0x45xis" targetRef="Gateway_0jzqvuk"/>
      <bpmn:sequenceFlow id="Flow_1rlz9jr" name="Yes" sourceRef="Gateway_0jzqvuk" targetRef="Activity_0lp5px6">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">#{execution.getProcessEngineServices().getRuntimeService().createIncidentQuery().processInstanceId(execution.getProcessInstanceId()).list().size() &gt; 0}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:textAnnotation id="TextAnnotation_0jajszm" textFormat="text/plain">
        <bpmn:text>run every 5 mins</bpmn:text>
      </bpmn:textAnnotation>
      <bpmn:association associationDirection="None" id="Association_1xtlmwt" sourceRef="Event_0yaq6h7" targetRef="TextAnnotation_0jajszm"/>
    </bpmn:subProcess>
    <bpmn:boundaryEvent attachedToRef="Activity_0z1ajok" cancelActivity="true" id="Event_1ds0rkt" parallelMultiple="false">
      <bpmn:outgoing>Flow_1lyw4rk</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_19p61qb">
        <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">R1/PT2M</bpmn:timeCycle>
      </bpmn:timerEventDefinition>
    </bpmn:boundaryEvent>
    <bpmn:intermediateThrowEvent id="Event_13fu65j" name="Milestone: Client Unassigned">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="newMilestone">Client Unassigned</camunda:outputParameter>
          <camunda:outputParameter name="milestoneOperation">STARTED</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1qalahq</bpmn:incoming>
      <bpmn:outgoing>Flow_1g7vrvd</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_1g7vrvd" sourceRef="Event_13fu65j" targetRef="Event_1kb51uc"/>
    <bpmn:intermediateCatchEvent id="Event_1kb51uc" name="Milestone Updated" parallelMultiple="false">
      <bpmn:incoming>Flow_1g7vrvd</bpmn:incoming>
      <bpmn:outgoing>Flow_1adh9uk</bpmn:outgoing>
      <bpmn:conditionalEventDefinition camunda:variableEvent="create, update" camunda:variableName="milestoneOperation" id="ConditionalEventDefinition_0ajwcu7">
        <bpmn:condition xsi:type="bpmn:tFormalExpression">${ milestoneOperation == 'COMPLETE'}</bpmn:condition>
      </bpmn:conditionalEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:sequenceFlow id="Flow_1adh9uk" sourceRef="Event_1kb51uc" targetRef="Activity_0f379q4"/>
    <bpmn:group categoryValueRef="CategoryValue_1gadsg8" id="Group_0x4ipdw"/>
    <bpmn:group categoryValueRef="CategoryValue_1gg8zar" id="Group_0tw2d7h"/>
    <bpmn:textAnnotation id="TextAnnotation_15wsbx5" textFormat="text/plain">
      <bpmn:text>Todo:

1. MilestoneStatus to Blocked on error
2. Validate Filing Milestone Transitions 
3. Update Stepfunc template names - Niraj
4. Validate milestones supported for Override - Niraj
5. Add assignment stepfunc and not trigger via engagement service - Niraj
6. boundary timer event for resiliency
7. Dashboard for camunda stuff/transitions/failures (non external task failures/successes)
8. onboarding team to WAS / Camunda
9. migration path
10. automatic milestones need to go through CREATED -&gt; IN_PROGRESS -&gt; COMPLETED</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:group categoryValueRef="CategoryValue_01tlnch" id="Group_0yag5d3"/>
    <bpmn:textAnnotation id="TextAnnotation_0bgxkp1" textFormat="text/plain">
      <bpmn:text>Process is terminated when close/revoke stepfn will mark enagementStatus as COMPLETED</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association associationDirection="None" id="Association_1m2dkze" sourceRef="Activity_00hoaeh" targetRef="TextAnnotation_0bgxkp1"/>
    <bpmn:textAnnotation id="TextAnnotation_1xv03cp" textFormat="text/plain">
      <bpmn:text>Todo: handle not completed statuses ??</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association associationDirection="None" id="Association_1lutrnw" sourceRef="Activity_0vfq6i3" targetRef="TextAnnotation_1xv03cp"/>
    <bpmn:textAnnotation id="TextAnnotation_01yk1iv" textFormat="text/plain">
      <bpmn:text>TTO DocuSign Widget: When Customer Submits their Email in TTO</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association associationDirection="None" id="Association_0bfv32b" sourceRef="Activity_1whocbs" targetRef="TextAnnotation_01yk1iv"/>
    <bpmn:textAnnotation id="TextAnnotation_08vsdrt" textFormat="text/plain">
      <bpmn:text>IEP: Expert Files WIth IRS
Filing Scheduler publihses the message when filed</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:textAnnotation id="TextAnnotation_10pxain" textFormat="text/plain">
      <bpmn:text>Customer E-Signs Docusign Packet</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association associationDirection="None" id="Association_1wndb39" sourceRef="Activity_070bx12" targetRef="TextAnnotation_10pxain"/>
    <bpmn:textAnnotation id="TextAnnotation_1bhys7w" textFormat="text/plain">
      <bpmn:text>what if the pro is unassigned during gathering info</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association associationDirection="None" id="Association_13nvzee" sourceRef="Activity_1j14b2p" targetRef="TextAnnotation_1bhys7w"/>
    <bpmn:textAnnotation id="TextAnnotation_1rlylyy" textFormat="text/plain">
      <bpmn:text>How does a pro know when to refresh if milestone transition takes more than 5 s ??</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association associationDirection="None" id="Association_01f28lj" sourceRef="Activity_0high9c" targetRef="TextAnnotation_1rlylyy"/>
    <bpmn:textAnnotation id="TextAnnotation_08ku9bs" textFormat="text/plain">
      <bpmn:text>talk to connor about auto SET approval ???</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association associationDirection="None" id="Association_01jbzav" sourceRef="Activity_0v2er12" targetRef="TextAnnotation_08ku9bs"/>
    <bpmn:textAnnotation id="TextAnnotation_0gj5j9m" textFormat="text/plain">
      <bpmn:text>is this in progress ??</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association associationDirection="None" id="Association_10pgw1x" sourceRef="Event_05n1189" targetRef="TextAnnotation_0gj5j9m"/>
  </bpmn:process>
  <bpmn:error id="Error_14cp3lo" name="Error_0drnm7u"/>
  <bpmn:message id="Message_13clq56" name="iep_milestone_manual_complete"/>
  <bpmn:category id="Category_11w3on5">
    <bpmn:categoryValue id="CategoryValue_1gadsg8" value="Expert"/>
  </bpmn:category>
  <bpmn:category id="Category_1qg0v4w">
    <bpmn:categoryValue id="CategoryValue_1gg8zar" value="System"/>
  </bpmn:category>
  <bpmn:message id="Message_0p42hze" name="filing_milestone_blocked"/>
  <bpmn:category id="Category_024eicj">
    <bpmn:categoryValue id="CategoryValue_01tlnch" value="Customer"/>
  </bpmn:category>
  <bpmn:message id="Message_0mhhauc" name="INTERNAL_MILESTONE_UPDATE_COMPLETE"/>
  <bpmn:message id="Message_0me3ygd" name="INTERNAL_UPDATE_MILESTONE"/>
  <bpmn:escalation escalationCode="INTERNAL" id="Escalation_0j1uwrx" name="Escalation_1a86a6l"/>
  <bpmn:message id="Message_1oc9jh3" name="iep_milestone_manual_start"/>
  <bpmn:error camunda:errorMessage="Milestone state is incorrect" errorCode="BAD_MILESTONE_STATE" id="Error_1ekqarn" name="Error_0l9adeb"/>
  <bpmn:message id="Message_1cysixt" name="filing_milestone_in_progress"/>
  <bpmn:error camunda:errorMessage="Bad request to update unsupported milestone status" errorCode="UNKNOWN_MILESTONE_UPDATE" id="Error_04rk94q" name="UNKNOWN_MILESTONE_UPDATE"/>
  <bpmn:message id="Message_0i4jw9o" name="work_created"/>
  <bpmn:message id="Message_1yjnx1m" name="work_status_update"/>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane bpmnElement="engagementTTLiveFullService" id="BPMNPlane_1">
      <bpmndi:BPMNShape bpmnElement="TextAnnotation_1xv03cp" id="TextAnnotation_1xv03cp_di">
        <dc:Bounds height="53" width="100" x="1160" y="1044"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="Flow_0f3ym69" id="Flow_0f3ym69_di">
        <di:waypoint x="5620" y="1552"/>
        <di:waypoint x="5620" y="830"/>
        <di:waypoint x="5632" y="830"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0ephib4" id="Flow_0ephib4_di">
        <di:waypoint x="7830" y="830"/>
        <di:waypoint x="7890" y="830"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0hunamg" id="Flow_0hunamg_di">
        <di:waypoint x="7678" y="830"/>
        <di:waypoint x="7730" y="830"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_16e8roa" id="Flow_16e8roa_di">
        <di:waypoint x="6690" y="902"/>
        <di:waypoint x="6690" y="880"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1xzmfim" id="Flow_1xzmfim_di">
        <di:waypoint x="6740" y="830"/>
        <di:waypoint x="6772" y="830"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1pakrh8" id="Flow_1pakrh8_di">
        <di:waypoint x="2080" y="862"/>
        <di:waypoint x="2080" y="1276"/>
        <di:waypoint x="2120" y="1276"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1wglfr4" id="Flow_1wglfr4_di">
        <di:waypoint x="7600" y="830"/>
        <di:waypoint x="7642" y="830"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0fydm4j" id="Flow_0fydm4j_di">
        <di:waypoint x="7468" y="830"/>
        <di:waypoint x="7500" y="830"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1wdgdoe" id="Flow_1wdgdoe_di">
        <di:waypoint x="6970" y="830"/>
        <di:waypoint x="7065" y="830"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1wx391z" id="Flow_1wx391z_di">
        <di:waypoint x="6808" y="830"/>
        <di:waypoint x="6870" y="830"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0qhde4m" id="Flow_0qhde4m_di">
        <di:waypoint x="5490" y="870"/>
        <di:waypoint x="5490" y="1610"/>
        <di:waypoint x="5520" y="1610"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1qxkx9n" id="Flow_1qxkx9n_di">
        <di:waypoint x="8225" y="880"/>
        <di:waypoint x="8382" y="880"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="14" width="42" x="8283" y="862"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_078kids" id="Flow_078kids_di">
        <di:waypoint x="7948" y="710"/>
        <di:waypoint x="8000" y="710"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1390vsl" id="Flow_1390vsl_di">
        <di:waypoint x="8050" y="750"/>
        <di:waypoint x="8050" y="780"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1t4394s" id="Flow_1t4394s_di">
        <di:waypoint x="7930" y="762"/>
        <di:waypoint x="7930" y="728"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1nztl3b" id="Flow_1nztl3b_di">
        <di:waypoint x="8480" y="898"/>
        <di:waypoint x="8480" y="1050"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1wspseh" id="Flow_1wspseh_di">
        <di:waypoint x="5830" y="830"/>
        <di:waypoint x="5950" y="830"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0yvv6lc" id="Flow_0yvv6lc_di">
        <di:waypoint x="5620" y="1610"/>
        <di:waypoint x="5650" y="1610"/>
        <di:waypoint x="5650" y="848"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1mv09fq" id="Flow_1mv09fq_di">
        <di:waypoint x="4428" y="830"/>
        <di:waypoint x="4480" y="830"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_13gtdzk" id="Flow_13gtdzk_di">
        <di:waypoint x="3658" y="830"/>
        <di:waypoint x="3710" y="830"/>
        <di:waypoint x="3710" y="1256"/>
        <di:waypoint x="3860" y="1256"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_10j9yb6" id="Flow_10j9yb6_di">
        <di:waypoint x="2798" y="822"/>
        <di:waypoint x="2850" y="822"/>
        <di:waypoint x="2850" y="1256"/>
        <di:waypoint x="3010" y="1256"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0phfcnk" id="Flow_0phfcnk_di">
        <di:waypoint x="1188" y="820"/>
        <di:waypoint x="1270" y="820"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0efb7xo" id="Flow_0efb7xo_di">
        <di:waypoint x="2538" y="1276"/>
        <di:waypoint x="2670" y="1276"/>
        <di:waypoint x="2670" y="840"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0apc94y" id="Flow_0apc94y_di">
        <di:waypoint x="2480" y="1053"/>
        <di:waypoint x="2480" y="822"/>
        <di:waypoint x="2650" y="822"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_13vgnpo" id="Flow_13vgnpo_di">
        <di:waypoint x="1988" y="820"/>
        <di:waypoint x="2030" y="820"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0hdtrao" id="Flow_0hdtrao_di">
        <di:waypoint x="6690" y="1553"/>
        <di:waypoint x="6690" y="938"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0b49hu5" id="Flow_0b49hu5_di">
        <di:waypoint x="6710" y="1610"/>
        <di:waypoint x="6790" y="1610"/>
        <di:waypoint x="6790" y="850"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0lu2rpw" id="Flow_0lu2rpw_di">
        <di:waypoint x="6460" y="830"/>
        <di:waypoint x="6580" y="830"/>
        <di:waypoint x="6580" y="1610"/>
        <di:waypoint x="6610" y="1610"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1842p3e" id="Flow_1842p3e_di">
        <di:waypoint x="7373" y="730"/>
        <di:waypoint x="7450" y="730"/>
        <di:waypoint x="7450" y="812"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0nkwkm5" id="Flow_0nkwkm5_di">
        <di:waypoint x="6268" y="750"/>
        <di:waypoint x="6320" y="750"/>
        <di:waypoint x="6320" y="812"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0zehl2o" id="Flow_0zehl2o_di">
        <di:waypoint x="1076" y="820"/>
        <di:waypoint x="1152" y="820"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1nfmmeh" id="Flow_1nfmmeh_di">
        <di:waypoint x="1729" y="776"/>
        <di:waypoint x="1870" y="776"/>
        <di:waypoint x="1870" y="802"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0ero9u2" id="Flow_0ero9u2_di">
        <di:waypoint x="1711" y="820"/>
        <di:waypoint x="1852" y="820"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1vvc4oa" id="Flow_1vvc4oa_di">
        <di:waypoint x="940" y="820"/>
        <di:waypoint x="1040" y="820"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1lhpbi5" id="Flow_1lhpbi5_di">
        <di:waypoint x="8243" y="920"/>
        <di:waypoint x="8400" y="920"/>
        <di:waypoint x="8400" y="898"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1kirbzb" id="Flow_1kirbzb_di">
        <di:waypoint x="8190" y="762"/>
        <di:waypoint x="8190" y="728"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0trjzav" id="Flow_0trjzav_di">
        <di:waypoint x="1888" y="820"/>
        <di:waypoint x="1952" y="820"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1anibps" id="Flow_1anibps_di">
        <di:waypoint x="8668" y="1090"/>
        <di:waypoint x="8770" y="1090"/>
        <di:waypoint x="8770" y="1191"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_07g2ggd" id="Flow_07g2ggd_di">
        <di:waypoint x="8650" y="1209"/>
        <di:waypoint x="8752" y="1209"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1fredxf" id="Flow_1fredxf_di">
        <di:waypoint x="8172" y="710"/>
        <di:waypoint x="8100" y="710"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1ln5drr" id="Flow_1ln5drr_di">
        <di:waypoint x="5320" y="830"/>
        <di:waypoint x="5372" y="830"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1we0fx9" id="Flow_1we0fx9_di">
        <di:waypoint x="5338" y="1080"/>
        <di:waypoint x="5390" y="1080"/>
        <di:waypoint x="5390" y="848"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1pbj1r0" id="Flow_1pbj1r0_di">
        <di:waypoint x="4278" y="1256"/>
        <di:waypoint x="4320" y="1256"/>
        <di:waypoint x="4320" y="848"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_15ugd6a" id="Flow_15ugd6a_di">
        <di:waypoint x="4220" y="1033"/>
        <di:waypoint x="4220" y="830"/>
        <di:waypoint x="4302" y="830"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_14ij8ih" id="Flow_14ij8ih_di">
        <di:waypoint x="3370" y="1033"/>
        <di:waypoint x="3370" y="830"/>
        <di:waypoint x="3492" y="830"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_02jo7ta" id="Flow_02jo7ta_di">
        <di:waypoint x="3428" y="1256"/>
        <di:waypoint x="3510" y="1256"/>
        <di:waypoint x="3510" y="850"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1juqmfm" id="Flow_1juqmfm_di">
        <di:waypoint x="2688" y="822"/>
        <di:waypoint x="2762" y="822"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1rfw316" id="Flow_1rfw316_di">
        <di:waypoint x="3528" y="830"/>
        <di:waypoint x="3622" y="830"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1asjruj" id="Flow_1asjruj_di">
        <di:waypoint x="4338" y="830"/>
        <di:waypoint x="4392" y="830"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0dwkltv" id="Flow_0dwkltv_di">
        <di:waypoint x="5408" y="830"/>
        <di:waypoint x="5440" y="830"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_01opz0x" id="Flow_01opz0x_di">
        <di:waypoint x="5668" y="830"/>
        <di:waypoint x="5730" y="830"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0fg2jyi" id="Flow_0fg2jyi_di">
        <di:waypoint x="6338" y="830"/>
        <di:waypoint x="6360" y="830"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1mcfhf9" id="Flow_1mcfhf9_di">
        <di:waypoint x="8418" y="880"/>
        <di:waypoint x="8462" y="880"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1qk86w8" id="Flow_1qk86w8_di">
        <di:waypoint x="586" y="820"/>
        <di:waypoint x="840" y="820"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1qalahq" id="Flow_1qalahq_di">
        <di:waypoint x="225" y="820"/>
        <di:waypoint x="287" y="820"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_00bd1hf" id="Flow_00bd1hf_di">
        <di:waypoint x="6250" y="830"/>
        <di:waypoint x="6302" y="830"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0628d1x" id="Flow_0628d1x_di">
        <di:waypoint x="7355" y="830"/>
        <di:waypoint x="7432" y="830"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1lyw4rk" id="Flow_1lyw4rk_di">
        <di:waypoint x="940" y="762"/>
        <di:waypoint x="940" y="742"/>
        <di:waypoint x="890" y="742"/>
        <di:waypoint x="890" y="780"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_17wxx1r" id="Flow_17wxx1r_di">
        <di:waypoint x="2130" y="764"/>
        <di:waypoint x="2130" y="744"/>
        <di:waypoint x="2090" y="744"/>
        <di:waypoint x="2090" y="782"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1sbozku" id="Flow_1sbozku_di">
        <di:waypoint x="6460" y="772"/>
        <di:waypoint x="6460" y="752"/>
        <di:waypoint x="6410" y="752"/>
        <di:waypoint x="6410" y="790"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0fhl5gk" id="Flow_0fhl5gk_di">
        <di:waypoint x="5830" y="772"/>
        <di:waypoint x="5830" y="752"/>
        <di:waypoint x="5780" y="752"/>
        <di:waypoint x="5780" y="790"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1yyzjwd" id="Flow_1yyzjwd_di">
        <di:waypoint x="6720" y="782"/>
        <di:waypoint x="6720" y="762"/>
        <di:waypoint x="6680" y="762"/>
        <di:waypoint x="6680" y="800"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0x52wba" id="Flow_0x52wba_di">
        <di:waypoint x="6970" y="772"/>
        <di:waypoint x="6970" y="752"/>
        <di:waypoint x="6920" y="752"/>
        <di:waypoint x="6920" y="790"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1yuzcbv" id="Flow_1yuzcbv_di">
        <di:waypoint x="7580" y="772"/>
        <di:waypoint x="7580" y="752"/>
        <di:waypoint x="7540" y="752"/>
        <di:waypoint x="7540" y="790"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1cusb0m" id="Flow_1cusb0m_di">
        <di:waypoint x="7810" y="888"/>
        <di:waypoint x="7810" y="908"/>
        <di:waypoint x="7770" y="908"/>
        <di:waypoint x="7770" y="870"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_196tk97" id="Flow_196tk97_di">
        <di:waypoint x="8080" y="652"/>
        <di:waypoint x="8080" y="640"/>
        <di:waypoint x="8040" y="640"/>
        <di:waypoint x="8040" y="670"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1g7vrvd" id="Flow_1g7vrvd_di">
        <di:waypoint x="323" y="820"/>
        <di:waypoint x="374" y="820"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1adh9uk" id="Flow_1adh9uk_di">
        <di:waypoint x="410" y="820"/>
        <di:waypoint x="486" y="820"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1dkn0d6" id="Flow_1dkn0d6_di">
        <di:waypoint x="586" y="762"/>
        <di:waypoint x="586" y="742"/>
        <di:waypoint x="536" y="742"/>
        <di:waypoint x="536" y="780"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Event_13fu65j" id="Event_13fu65j_di">
        <dc:Bounds height="36" width="36" x="287" y="802"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="82" x="264" y="848"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_0f379q4" id="Activity_0f379q4_di">
        <dc:Bounds height="80" width="100" x="486" y="780"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Event_1kb51uc" id="Event_1kb51uc_di">
        <dc:Bounds height="36" width="36" x="374" y="802"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="48" x="368" y="845"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_1v06ow3" id="Activity_1v06ow3_di" isExpanded="true">
        <dc:Bounds height="300" width="290" x="7065" y="670"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="Flow_1942eiy" id="Flow_1942eiy_di">
        <di:waypoint x="7255" y="818"/>
        <di:waypoint x="7287" y="818"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1h7zcpz" id="Flow_1h7zcpz_di">
        <di:waypoint x="7123" y="818"/>
        <di:waypoint x="7155" y="818"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0rlltad" id="Flow_0rlltad_di">
        <di:waypoint x="7255" y="876"/>
        <di:waypoint x="7255" y="896"/>
        <di:waypoint x="7205" y="896"/>
        <di:waypoint x="7205" y="858"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape bpmnElement="Event_12jbq7e" id="Event_12jbq7e_di">
        <dc:Bounds height="36" width="36" x="7087" y="800"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_0h4s8xy" id="Event_0h4s8xy_di">
        <dc:Bounds height="36" width="36" x="7287" y="800"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="white" bioc:stroke="black" bpmnElement="Activity_0kvaqag" id="Activity_0kvaqag_di">
        <dc:Bounds height="80" width="100" x="7155" y="778"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="TextAnnotation_1c4whne" id="TextAnnotation_1c4whne_di">
        <dc:Bounds height="40" width="100" x="7115" y="720"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_0oh4v1k" id="Event_0oh4v1k_di">
        <dc:Bounds height="36" width="36" x="7237" y="840"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_1nc02ym" id="Activity_1nc02ym_di" isExpanded="true">
        <dc:Bounds height="302" width="300" x="5950" y="671"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="Flow_1w3v8u5" id="Flow_1w3v8u5_di">
        <di:waypoint x="6147" y="821"/>
        <di:waypoint x="6192" y="821"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0ro554d" id="Flow_0ro554d_di">
        <di:waypoint x="6008" y="821"/>
        <di:waypoint x="6047" y="821"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0e1ixh8" id="Flow_0e1ixh8_di">
        <di:waypoint x="6120" y="763"/>
        <di:waypoint x="6120" y="743"/>
        <di:waypoint x="6070" y="743"/>
        <di:waypoint x="6070" y="781"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape bpmnElement="Event_1v2ekk8" id="Event_1v2ekk8_di">
        <dc:Bounds height="36" width="36" x="5972" y="803"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_1kpi9as" id="Event_1kpi9as_di">
        <dc:Bounds height="36" width="36" x="6192" y="803"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="white" bioc:stroke="black" bpmnElement="Activity_17kx36h" id="Activity_17kx36h_di">
        <dc:Bounds height="80" width="100" x="6047" y="781"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="TextAnnotation_0lgjt1s" id="TextAnnotation_0lgjt1s_di">
        <dc:Bounds height="40" width="210" x="5980" y="881"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="Association_1gqngbd" id="Association_1gqngbd_di">
        <di:waypoint x="6065" y="861"/>
        <di:waypoint x="6049" y="881"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape bpmnElement="Event_1qacktq" id="Event_1qacktq_di">
        <dc:Bounds height="36" width="36" x="6102" y="763"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_0high9c" id="Activity_0high9c_di" isExpanded="true">
        <dc:Bounds height="409" width="418" x="2120" y="1071"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="Flow_1scbrk0" id="Flow_1scbrk0_di">
        <di:waypoint x="2320" y="1242"/>
        <di:waypoint x="2380" y="1242"/>
        <di:waypoint x="2380" y="1255"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0u7k9e0" id="Flow_0u7k9e0_di">
        <di:waypoint x="2320" y="1420"/>
        <di:waypoint x="2380" y="1420"/>
        <di:waypoint x="2380" y="1305"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_03hjwxh" id="Flow_03hjwxh_di">
        <di:waypoint x="2320" y="1332"/>
        <di:waypoint x="2380" y="1332"/>
        <di:waypoint x="2380" y="1305"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1xniz6e" id="Flow_1xniz6e_di">
        <di:waypoint x="2160" y="1298"/>
        <di:waypoint x="2160" y="1420"/>
        <di:waypoint x="2220" y="1420"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_08zzwt5" id="Flow_08zzwt5_di">
        <di:waypoint x="2160" y="1298"/>
        <di:waypoint x="2160" y="1332"/>
        <di:waypoint x="2220" y="1332"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_10f7gy9" id="Flow_10f7gy9_di">
        <di:waypoint x="2160" y="1262"/>
        <di:waypoint x="2160" y="1242"/>
        <di:waypoint x="2220" y="1242"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1q7dj2q" id="Flow_1q7dj2q_di">
        <di:waypoint x="2405" y="1280"/>
        <di:waypoint x="2426" y="1280"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_175xkxz" id="Flow_175xkxz_di">
        <di:waypoint x="2462" y="1280"/>
        <di:waypoint x="2482" y="1280"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0trqvjv" id="Flow_0trqvjv_di">
        <di:waypoint x="2160" y="1262"/>
        <di:waypoint x="2160" y="1153"/>
        <di:waypoint x="2220" y="1153"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0h4zv98" id="Flow_0h4zv98_di">
        <di:waypoint x="2320" y="1153"/>
        <di:waypoint x="2380" y="1153"/>
        <di:waypoint x="2380" y="1255"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape bpmnElement="Activity_076pj5o" id="Activity_076pj5o_di">
        <dc:Bounds height="80" width="100" x="2220" y="1113"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_02mcv8o" id="Event_02mcv8o_di">
        <dc:Bounds height="36" width="36" x="2142" y="1262"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_10um5py" id="Event_10um5py_di">
        <dc:Bounds height="36" width="36" x="2482" y="1262"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_19siwwv" id="Event_14gtfkt_di">
        <dc:Bounds height="36" width="36" x="2426" y="1262"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="48" x="2420" y="1305"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Gateway_1ljj7mk" id="Gateway_1hdyj5d_di">
        <dc:Bounds height="50" width="50" x="2355" y="1255"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_10exh7b" id="Activity_10exh7b_di">
        <dc:Bounds height="80" width="100" x="2220" y="1292"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_056zkjf" id="Activity_056zkjf_di">
        <dc:Bounds height="80" width="100" x="2220" y="1380"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_1jn3wii" id="Activity_1jn3wii_di">
        <dc:Bounds height="80" width="100" x="2220" y="1202"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Event_1n2b0mx" id="Event_1n2b0mx_di">
        <dc:Bounds height="36" width="36" x="8382" y="862"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="54" x="8373" y="825"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Activity_0ucifop" id="Activity_0ucifop_di">
        <dc:Bounds height="80" width="100" x="7500" y="790"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Activity_089m3sh" id="Activity_089m3sh_di">
        <dc:Bounds height="80" width="100" x="6360" y="790"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Event_1ow40jj" id="Event_1ow40jj_di">
        <dc:Bounds height="36" width="36" x="6302" y="812"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="53" width="63" x="6288" y="857.5"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Event_05n1189" id="Event_05n1189_di">
        <dc:Bounds height="36" width="36" x="5632" y="812"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="53" width="63" x="5621" y="750"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Event_138axi8" id="Event_138axi8_di">
        <dc:Bounds height="36" width="36" x="5372" y="812"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="40" width="68" x="5356" y="756"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Event_0kxekbm" id="Event_0kxekbm_di">
        <dc:Bounds height="36" width="36" x="4302" y="812"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="68" x="4286" y="783"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Event_0fzvruf" id="Event_0fzvruf_di">
        <dc:Bounds height="36" width="36" x="3492" y="812"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="54" x="3483" y="783"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Event_0vx3ty5" id="Event_0vx3ty5_di">
        <dc:Bounds height="36" width="36" x="2652" y="804"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="84" x="2628" y="772"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="startEvent" id="Event_1hv3gkl_di">
        <dc:Bounds height="36" width="36" x="189" y="802"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="14" width="50" x="183" y="845"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_1pwgfvg" id="Activity_1pwgfvg_di" isExpanded="true">
        <dc:Bounds height="409" width="418" x="3010" y="1051"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="Flow_019ff3w" id="Flow_019ff3w_di">
        <di:waypoint x="3210" y="1380"/>
        <di:waypoint x="3270" y="1380"/>
        <di:waypoint x="3270" y="1285"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_15ax9x8" id="Flow_15ax9x8_di">
        <di:waypoint x="3210" y="1260"/>
        <di:waypoint x="3245" y="1260"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1ugqxo4" id="Flow_1ugqxo4_di">
        <di:waypoint x="3050" y="1278"/>
        <di:waypoint x="3050" y="1380"/>
        <di:waypoint x="3110" y="1380"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1vsq7pv" id="Flow_1vsq7pv_di">
        <di:waypoint x="3068" y="1260"/>
        <di:waypoint x="3110" y="1260"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0mjz2az" id="Flow_0mjz2az_di">
        <di:waypoint x="3295" y="1260"/>
        <di:waypoint x="3316" y="1260"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_09jedxb" id="Flow_09jedxb_di">
        <di:waypoint x="3352" y="1260"/>
        <di:waypoint x="3372" y="1260"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1t5336c" id="Flow_1t5336c_di">
        <di:waypoint x="3050" y="1242"/>
        <di:waypoint x="3050" y="1133"/>
        <di:waypoint x="3110" y="1133"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_109msdv" id="Flow_109msdv_di">
        <di:waypoint x="3210" y="1133"/>
        <di:waypoint x="3270" y="1133"/>
        <di:waypoint x="3270" y="1235"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape bpmnElement="Activity_00e0cl5" id="Activity_00e0cl5_di">
        <dc:Bounds height="80" width="100" x="3110" y="1340"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_12tc9wi" id="Event_12tc9wi_di">
        <dc:Bounds height="36" width="36" x="3032" y="1242"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Gateway_186408p" id="Gateway_186408p_di">
        <dc:Bounds height="50" width="50" x="3245" y="1235"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_10jp24f" id="Activity_10jp24f_di">
        <dc:Bounds height="80" width="100" x="3110" y="1220"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_1u8ipfw" id="Event_1u8ipfw_di">
        <dc:Bounds height="36" width="36" x="3316" y="1242"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="48" x="3310" y="1285"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_0u74wde" id="Event_0u74wde_di">
        <dc:Bounds height="36" width="36" x="3372" y="1242"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_1t726z2" id="Activity_1t726z2_di">
        <dc:Bounds height="80" width="100" x="3110" y="1093"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_0v2er12" id="Activity_0v2er12_di" isExpanded="true">
        <dc:Bounds height="409" width="418" x="3860" y="1051"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="Flow_0f5c2me" id="Flow_0f5c2me_di">
        <di:waypoint x="4060" y="1380"/>
        <di:waypoint x="4120" y="1380"/>
        <di:waypoint x="4120" y="1285"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0kqet7j" id="Flow_0kqet7j_di">
        <di:waypoint x="4060" y="1260"/>
        <di:waypoint x="4095" y="1260"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_15v5oz2" id="Flow_15v5oz2_di">
        <di:waypoint x="3900" y="1278"/>
        <di:waypoint x="3900" y="1380"/>
        <di:waypoint x="3960" y="1380"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_03ih1za" id="Flow_03ih1za_di">
        <di:waypoint x="3918" y="1260"/>
        <di:waypoint x="3960" y="1260"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0zc4710" id="Flow_0zc4710_di">
        <di:waypoint x="4145" y="1260"/>
        <di:waypoint x="4165" y="1260"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0qhtdcz" id="Flow_0qhtdcz_di">
        <di:waypoint x="4201" y="1260"/>
        <di:waypoint x="4222" y="1260"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1p77v13" id="Flow_1p77v13_di">
        <di:waypoint x="3900" y="1242"/>
        <di:waypoint x="3900" y="1133"/>
        <di:waypoint x="3960" y="1133"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0veyll2" id="Flow_0veyll2_di">
        <di:waypoint x="4060" y="1133"/>
        <di:waypoint x="4120" y="1133"/>
        <di:waypoint x="4120" y="1235"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape bpmnElement="Event_15zsent" id="Event_15zsent_di">
        <dc:Bounds height="36" width="36" x="3882" y="1242"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Gateway_0bogblp" id="Gateway_0bogblp_di">
        <dc:Bounds height="50" width="50" x="4095" y="1235"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_1l7faev" id="Event_1l7faev_di">
        <dc:Bounds height="36" width="36" x="4165" y="1242"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="48" x="4159" y="1285"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_0ldda2q" id="Event_0ldda2q_di">
        <dc:Bounds height="36" width="36" x="4222" y="1242"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_0s85hnt" id="Activity_0s85hnt_di">
        <dc:Bounds height="80" width="100" x="3960" y="1093"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_0tsgac9" id="Activity_0tsgac9_di">
        <dc:Bounds height="80" width="100" x="3960" y="1340"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_1ely2dm" id="Activity_1ely2dm_di">
        <dc:Bounds height="80" width="100" x="3960" y="1220"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_10fjotv" id="Activity_10fjotv_di" isExpanded="true">
        <dc:Bounds height="799" width="840" x="4480" y="650"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="Flow_1reo3ug" id="Flow_1reo3ug_di">
        <di:waypoint x="5110" y="968"/>
        <di:waypoint x="5110" y="1345"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_00wb4bk" id="Flow_00wb4bk_di">
        <di:waypoint x="5135" y="1370"/>
        <di:waypoint x="5204" y="1370"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1cr3o7k" id="Flow_1cr3o7k_di">
        <di:waypoint x="4690" y="1345"/>
        <di:waypoint x="4690" y="968"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_18473xx" id="Flow_18473xx_di">
        <di:waypoint x="4715" y="1370"/>
        <di:waypoint x="4840" y="1370"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0iqfpgc" id="Flow_0iqfpgc_di">
        <di:waypoint x="4940" y="1370"/>
        <di:waypoint x="5085" y="1370"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1ayjgaw" id="Flow_1ayjgaw_di">
        <di:waypoint x="4576" y="1370"/>
        <di:waypoint x="4665" y="1370"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape bpmnElement="Event_1o9x07r" id="Event_1o9x07r_di">
        <dc:Bounds height="36" width="36" x="4540" y="1352"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_0siouhc" id="Event_0siouhc_di">
        <dc:Bounds height="36" width="36" x="5204" y="1352"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_1nztq05" id="Activity_1nztq05_di">
        <dc:Bounds height="80" width="100" x="4840" y="1330"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(255, 224, 178)" bioc:stroke="rgb(251, 140, 0)" bpmnElement="Activity_1pbxo45" id="Activity_1pbxo45_di" isExpanded="true">
        <dc:Bounds height="288" width="700" x="4540" y="680"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="Flow_0v3mz39" id="Flow_0v3mz39_di">
        <di:waypoint x="4940" y="762"/>
        <di:waypoint x="4940" y="742"/>
        <di:waypoint x="5180" y="742"/>
        <di:waypoint x="5180" y="802"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0la2yfv" id="Flow_0la2yfv_di">
        <di:waypoint x="5070" y="820"/>
        <di:waypoint x="5162" y="820"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1rl4dey" id="Flow_1rl4dey_di">
        <di:waypoint x="4954" y="820"/>
        <di:waypoint x="5034" y="820"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_02bdhfk" id="Flow_02bdhfk_di">
        <di:waypoint x="4677" y="790"/>
        <di:waypoint x="4702" y="800"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0lr2h83" id="Flow_0lr2h83_di">
        <di:waypoint x="4677" y="850"/>
        <di:waypoint x="4702" y="840"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0zl6871" id="Flow_0zl6871_di">
        <di:waypoint x="4792" y="780"/>
        <di:waypoint x="4792" y="740"/>
        <di:waypoint x="4770" y="740"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_163c3xs" id="Flow_163c3xs_di">
        <di:waypoint x="5180" y="838"/>
        <di:waypoint x="5180" y="892"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0kmv0nv" id="Flow_0kmv0nv_di">
        <di:waypoint x="4734" y="740"/>
        <di:waypoint x="4712" y="740"/>
        <di:waypoint x="4712" y="780"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1x4xxgb" id="Flow_1x4xxgb_di">
        <di:waypoint x="4662" y="820"/>
        <di:waypoint x="4702" y="820"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0oac3tg" id="Flow_0oac3tg_di">
        <di:waypoint x="4802" y="820"/>
        <di:waypoint x="4854" y="820"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="38" x="4809" y="783"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_198xfat" id="Flow_198xfat_di">
        <di:waypoint x="4612" y="902"/>
        <di:waypoint x="4612" y="860"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1badf5b" id="Flow_1badf5b_di">
        <di:waypoint x="4780" y="878"/>
        <di:waypoint x="4780" y="898"/>
        <di:waypoint x="4740" y="898"/>
        <di:waypoint x="4740" y="860"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0rcx2h1" id="Flow_0rcx2h1_di">
        <di:waypoint x="4940" y="878"/>
        <di:waypoint x="4940" y="898"/>
        <di:waypoint x="4904" y="898"/>
        <di:waypoint x="4904" y="860"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape bpmnElement="Event_1y4g3yx" id="Event_1y4g3yx_di">
        <dc:Bounds height="36" width="36" x="4594" y="902"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_1v0g1w0" id="Event_1v0g1w0_di">
        <dc:Bounds height="36" width="36" x="5162" y="892"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_1gouvz1" id="Activity_1gouvz1_di">
        <dc:Bounds height="80" width="100" x="4562" y="780"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_18geoc0" id="Activity_18geoc0_di">
        <dc:Bounds height="80" width="100" x="4854" y="780"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_1k8kokq" id="Activity_1k8kokq_di">
        <dc:Bounds height="80" width="100" x="4702" y="780"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_0nlj8n9" id="Event_1kf10vg_di">
        <dc:Bounds height="36" width="36" x="5162" y="802"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="60" x="5110" y="838"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_0zvdba9" id="Event_0mflbb1_di">
        <dc:Bounds height="36" width="36" x="4734" y="722"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="14" width="68" x="4678" y="706"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_0gca3r0" id="Event_0gca3r0_di">
        <dc:Bounds height="36" width="36" x="5034" y="802"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="40" width="60" x="5022" y="756"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_1thbozm" id="Event_1i9soug_di">
        <dc:Bounds height="36" width="36" x="4922" y="762"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="40" width="60" x="4870" y="726"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_1liiw3f" id="Event_0s9d2mm_di">
        <dc:Bounds height="36" width="36" x="4644" y="842"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="40" x="4640" y="883"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_1lw15ac" id="Event_1wtsg15_di">
        <dc:Bounds height="36" width="36" x="4644" y="762"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="48" x="4638" y="732"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_1xdtu5j" id="Event_1xdtu5j_di">
        <dc:Bounds height="36" width="36" x="4762" y="842"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_1qvv6jf" id="Event_1qvv6jf_di">
        <dc:Bounds height="36" width="36" x="4922" y="842"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Gateway_172fa4n" id="Gateway_1fxg9ss_di">
        <dc:Bounds height="50" width="50" x="4665" y="1345"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Gateway_01trq3o" id="Gateway_0btukeu_di">
        <dc:Bounds height="50" width="50" x="5085" y="1345"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_0quuajf" id="Activity_0quuajf_di" isExpanded="true">
        <dc:Bounds height="300" width="340" x="8310" y="1050"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="Flow_0vk3n6v" id="Flow_0vk3n6v_di">
        <di:waypoint x="8497" y="1213"/>
        <di:waypoint x="8529" y="1213"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0qig890" id="Flow_0qig890_di">
        <di:waypoint x="8368" y="1213"/>
        <di:waypoint x="8397" y="1213"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0m493eg" id="Flow_0m493eg_di">
        <di:waypoint x="8565" y="1213"/>
        <di:waypoint x="8592" y="1213"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape bpmnElement="Event_0dqc6ob" id="Event_0dqc6ob_di">
        <dc:Bounds height="36" width="36" x="8332" y="1195"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_0bcib01" id="Event_0bcib01_di">
        <dc:Bounds height="36" width="36" x="8592" y="1195"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_1b1mvhw" id="Activity_1b1mvhw_di">
        <dc:Bounds height="80" width="100" x="8397" y="1173"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_1ks67vx" id="Event_1ks67vx_di">
        <dc:Bounds height="36" width="36" x="8529" y="1195"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="48" x="8523" y="1238"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(255, 224, 178)" bioc:stroke="rgb(251, 140, 0)" bpmnElement="Event_0ymsc27" id="Event_0ymsc27_di">
        <dc:Bounds height="36" width="36" x="8172" y="692"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="69" x="8155.5" y="655"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_1th2y64" id="Event_1th2y64_di">
        <dc:Bounds height="36" width="36" x="8752" y="1191"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="63" x="8739" y="1234"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Event_1chta8d" id="Event_1chta8d_di">
        <dc:Bounds height="36" width="36" x="1852" y="802"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="70" x="1835" y="848"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="white" bioc:stroke="black" bpmnElement="Activity_0z1ajok" id="Activity_0z1ajok_di">
        <dc:Bounds height="80" width="100" x="840" y="780"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_00qswlx" id="Activity_00qswlx_di" isExpanded="true">
        <dc:Bounds height="218" width="441" x="1270" y="711"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="Flow_0478cgn" id="Flow_0478cgn_di">
        <di:waypoint x="1328" y="824"/>
        <di:waypoint x="1360" y="824"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0h1yuf3" id="Flow_0h1yuf3_di">
        <di:waypoint x="1431" y="757"/>
        <di:waypoint x="1450" y="757"/>
        <di:waypoint x="1450" y="784"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1sfmeqk" id="Flow_1sfmeqk_di">
        <di:waypoint x="1370" y="784"/>
        <di:waypoint x="1370" y="757"/>
        <di:waypoint x="1393" y="757"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="14" width="24" x="1338" y="770"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1k4rc0z" id="Flow_1k4rc0z_di">
        <di:waypoint x="1460" y="824"/>
        <di:waypoint x="1652" y="824"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="14" width="19" x="1500" y="805"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0dmzoij" id="Flow_0dmzoij_di">
        <di:waypoint x="1440" y="882"/>
        <di:waypoint x="1440" y="902"/>
        <di:waypoint x="1380" y="902"/>
        <di:waypoint x="1380" y="864"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape bpmnElement="Event_082o2ed" id="Event_082o2ed_di">
        <dc:Bounds height="36" width="36" x="1292" y="806"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_07dcf8b" id="Event_07dcf8b_di">
        <dc:Bounds height="36" width="36" x="1393" y="739"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="14" width="58" x="1335" y="737"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_1j14b2p" id="Activity_1j14b2p_di">
        <dc:Bounds height="80" width="100" x="1360" y="784"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_1cft000" id="Event_1cft000_di">
        <dc:Bounds height="36" width="36" x="1652" y="806"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_15mu9qq" id="Event_15mu9qq_di">
        <dc:Bounds height="36" width="36" x="1422" y="846"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Event_10k9vuf" id="Event_10k9vuf_di">
        <dc:Bounds height="36" width="36" x="1040" y="802"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="82" x="1017" y="848"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_070bx12" id="Activity_06upocz_di">
        <dc:Bounds height="80" width="100" x="6610" y="1570"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_00h5g6w" id="Activity_14s3p9d_di" isExpanded="true">
        <dc:Bounds height="403" width="530" x="180" y="1033"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="Flow_0m0lpqv" id="Flow_0m0lpqv_di">
        <di:waypoint x="650" y="1190"/>
        <di:waypoint x="650" y="1268"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_008v4uh" id="Flow_008v4uh_di">
        <di:waypoint x="530" y="1190"/>
        <di:waypoint x="530" y="1268"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_104r4xk" id="Flow_104r4xk_di">
        <di:waypoint x="382" y="1190"/>
        <di:waypoint x="382" y="1268"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0k56x86" id="Flow_0k56x86_di">
        <di:waypoint x="310" y="1311"/>
        <di:waypoint x="310" y="1366"/>
        <di:waypoint x="372" y="1366"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1vgl6oz" id="Flow_1vgl6oz_di">
        <di:waypoint x="310" y="1188"/>
        <di:waypoint x="310" y="1150"/>
        <di:waypoint x="332" y="1150"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0ridwgu" id="Flow_0ridwgu_di">
        <di:waypoint x="310" y="1261"/>
        <di:waypoint x="310" y="1224"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="14" width="80" x="220" y="1235"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_19svcm0" id="Flow_19svcm0_di">
        <di:waypoint x="335" y="1286"/>
        <di:waypoint x="364" y="1286"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1dwgzy9" id="Flow_1dwgzy9_di">
        <di:waypoint x="548" y="1286"/>
        <di:waypoint x="590" y="1286"/>
        <di:waypoint x="590" y="1190"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0xly1ja" id="Flow_0xly1ja_di">
        <di:waypoint x="400" y="1286"/>
        <di:waypoint x="480" y="1286"/>
        <di:waypoint x="480" y="1190"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1fi6wuj" id="Flow_1fi6wuj_di">
        <di:waypoint x="243" y="1286"/>
        <di:waypoint x="285" y="1286"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_023h0ma" id="Flow_023h0ma_di">
        <di:waypoint x="410" y="1092"/>
        <di:waypoint x="410" y="1072"/>
        <di:waypoint x="350" y="1072"/>
        <di:waypoint x="350" y="1110"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0xrzt9o" id="Flow_0xrzt9o_di">
        <di:waypoint x="530" y="1092"/>
        <di:waypoint x="530" y="1072"/>
        <di:waypoint x="490" y="1072"/>
        <di:waypoint x="490" y="1110"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_190i9b2" id="Flow_190i9b2_di">
        <di:waypoint x="670" y="1092"/>
        <di:waypoint x="670" y="1072"/>
        <di:waypoint x="620" y="1072"/>
        <di:waypoint x="620" y="1110"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape bpmnElement="Event_0kqav6m" id="Event_0kqav6m_di">
        <dc:Bounds height="36" width="36" x="207" y="1268"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_0oz60qb" id="Event_0oz60qb_di">
        <dc:Bounds height="36" width="36" x="292" y="1188"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="14" width="69" x="235" y="1143"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Gateway_07rifb2" id="Gateway_07rifb2_di" isMarkerVisible="true">
        <dc:Bounds height="50" width="50" x="285" y="1261"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_1snx2oz" id="Event_0yv0x0v_di">
        <dc:Bounds height="36" width="36" x="372" y="1348"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="86" x="347" y="1391"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Activity_1ivtvsk" id="Activity_1ivtvsk_di">
        <dc:Bounds height="80" width="100" x="332" y="1110"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Activity_1ky3sgg" id="Activity_1ky3sgg_di">
        <dc:Bounds height="80" width="100" x="452" y="1110"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Activity_1woybm1" id="Activity_1woybm1_di">
        <dc:Bounds height="80" width="100" x="570" y="1110"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_1b2or91" id="Event_1b2or91_di">
        <dc:Bounds height="36" width="36" x="364" y="1268"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="14" width="52" x="356" y="1311"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_0fgobso" id="Event_0fgobso_di">
        <dc:Bounds height="36" width="36" x="512" y="1268"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="14" width="80" x="490" y="1311"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_193ogdu" id="Event_0g6h88b_di">
        <dc:Bounds height="36" width="36" x="632" y="1268"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_13qx7k8" id="Event_13qx7k8_di">
        <dc:Bounds height="36" width="36" x="392" y="1092"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_1age5s2" id="Event_1age5s2_di">
        <dc:Bounds height="36" width="36" x="512" y="1092"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_16mw62p" id="Event_16mw62p_di">
        <dc:Bounds height="36" width="36" x="652" y="1092"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Event_1udg5hy" id="Event_1udg5hy_di">
        <dc:Bounds height="36" width="36" x="1952" y="802"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="48" x="1946" y="845"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Event_0fa526x" id="Event_0fa526x_di">
        <dc:Bounds height="36" width="36" x="1152" y="802"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="48" x="1146" y="845"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Event_1sxj8x5" id="Event_1sxj8x5_di">
        <dc:Bounds height="36" width="36" x="2762" y="804"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="48" x="2756" y="772"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Event_1xcriqk" id="Event_1xcriqk_di">
        <dc:Bounds height="36" width="36" x="3622" y="812"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="48" x="3616" y="783"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Event_0peq26s" id="Event_0peq26s_di">
        <dc:Bounds height="36" width="36" x="4392" y="812"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="86" x="4367" y="783"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_1whocbs" id="Activity_108821l_di">
        <dc:Bounds height="80" width="100" x="5520" y="1570"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Activity_0wd8moj" id="Activity_0wd8moj_di">
        <dc:Bounds height="80" width="100" x="5730" y="790"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Event_1kki2gd" id="Event_1kki2gd_di">
        <dc:Bounds height="36" width="36" x="8462" y="862"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="48" x="8456" y="825"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Activity_1m9ps2u" id="Activity_1m9ps2u_di">
        <dc:Bounds height="80" width="100" x="8000" y="670"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Event_1bg8vmj" id="Event_1bg8vmj_di">
        <dc:Bounds height="36" width="36" x="7912" y="692"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="86" x="7887" y="656.5"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_05smsqc" id="Activity_05smsqc_di" isExpanded="true">
        <dc:Bounds height="180" width="335" x="7890" y="780"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="Flow_1ubpfaj" id="Flow_1ubpfaj_di">
        <di:waypoint x="8102" y="880"/>
        <di:waypoint x="8162" y="880"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1c4s7ou" id="Flow_1c4s7ou_di">
        <di:waypoint x="7951" y="880"/>
        <di:waypoint x="8002" y="880"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape bpmnElement="Event_1cr2lw6" id="Event_1cr2lw6_di">
        <dc:Bounds height="36" width="36" x="7915" y="862"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_0vf6cow" id="Activity_0vf6cow_di">
        <dc:Bounds height="80" width="100" x="8002" y="840"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_1ram7f8" id="Event_1ram7f8_di">
        <dc:Bounds height="36" width="36" x="8162" y="862"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Activity_0diy46m" id="Activity_0diy46m_di">
        <dc:Bounds height="80" width="100" x="5440" y="790"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Event_09rqaio" id="Event_09rqaio_di">
        <dc:Bounds height="36" width="36" x="6772" y="812"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="40" width="78" x="6751" y="766"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Activity_0z2r6sj" id="Activity_0z2r6sj_di">
        <dc:Bounds height="80" width="100" x="6870" y="790"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Event_1bjektk" id="Event_1bjektk_di">
        <dc:Bounds height="36" width="36" x="7432" y="812"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="40" width="65" x="7417" y="858"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_157fl83" id="Activity_157fl83_di">
        <dc:Bounds height="80" width="100" x="2030" y="782"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Activity_0679y6c" id="Activity_0679y6c_di">
        <dc:Bounds height="80" width="100" x="6640" y="800"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Event_0m8k29l" id="Event_0m8k29l_di">
        <dc:Bounds height="36" width="36" x="6672" y="902"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="40" width="65" x="6603" y="910"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Event_0b2ezuq" id="Event_0b2ezuq_di">
        <dc:Bounds height="36" width="36" x="7642" y="812"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="86" x="7617" y="777"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Activity_1hnd6bi" id="Activity_1hnd6bi_di">
        <dc:Bounds height="80" width="100" x="7730" y="790"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_0vfq6i3" id="Activity_0vfq6i3_di" isExpanded="true">
        <dc:Bounds height="193" width="381" x="739" y="1034"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="Flow_0wd8vzx" id="Flow_0wd8vzx_di">
        <di:waypoint x="808" y="1145"/>
        <di:waypoint x="859" y="1145"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0bgrwrt" id="Flow_0bgrwrt_di">
        <di:waypoint x="869" y="1105"/>
        <di:waypoint x="869" y="1072"/>
        <di:waypoint x="891" y="1072"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="14" width="74" x="782" y="1065"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1197qb7" id="Flow_1197qb7_di">
        <di:waypoint x="927" y="1072"/>
        <di:waypoint x="949" y="1072"/>
        <di:waypoint x="949" y="1105"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1a6yp5b" id="Flow_1a6yp5b_di">
        <di:waypoint x="959" y="1145"/>
        <di:waypoint x="1031" y="1145"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="14" width="54" x="969" y="1120"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0nnkyql" id="Flow_0nnkyql_di">
        <di:waypoint x="959" y="1203"/>
        <di:waypoint x="959" y="1210"/>
        <di:waypoint x="909" y="1210"/>
        <di:waypoint x="909" y="1185"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape bpmnElement="Event_171lk3o" id="Event_171lk3o_di">
        <dc:Bounds height="36" width="36" x="772" y="1127"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="63" x="760" y="1170"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_14t8to2" id="Event_0psvjwa_di">
        <dc:Bounds height="36" width="36" x="891" y="1054"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="70" x="934" y="1045"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_04068p7" id="Activity_04068p7_di">
        <dc:Bounds height="80" width="100" x="859" y="1105"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_0enrmul" id="Event_0enrmul_di">
        <dc:Bounds height="36" width="36" x="1031" y="1127"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_06xh53h" id="Event_06xh53h_di">
        <dc:Bounds height="36" width="36" x="941" y="1167"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="Association_1lutrnw" id="Association_1lutrnw_di">
        <di:waypoint x="1120" y="1093"/>
        <di:waypoint x="1160" y="1078"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape bpmnElement="Activity_00hoaeh" id="Activity_00hoaeh_di" isExpanded="true">
        <dc:Bounds height="187" width="380" x="736" y="1250"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="Flow_0pw02qr" id="Flow_0pw02qr_di">
        <di:waypoint x="955" y="1327"/>
        <di:waypoint x="1048" y="1327"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="14" width="54" x="970" y="1308"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_095hpil" id="Flow_095hpil_di">
        <di:waypoint x="905" y="1367"/>
        <di:waypoint x="905" y="1397"/>
        <di:waypoint x="978" y="1397"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="14" width="74" x="902" y="1410"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1mcr59j" id="Flow_1mcr59j_di">
        <di:waypoint x="814" y="1327"/>
        <di:waypoint x="855" y="1327"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_0o49i2t" id="Flow_0o49i2t_di">
        <di:waypoint x="930" y="1269"/>
        <di:waypoint x="930" y="1260"/>
        <di:waypoint x="890" y="1260"/>
        <di:waypoint x="890" y="1287"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape bpmnElement="Event_1s6hk39" id="Event_1s6hk39_di">
        <dc:Bounds height="36" width="36" x="1048" y="1309"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="63" x="1035" y="1352"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_0k2v9ds" id="Event_1karyg4_di">
        <dc:Bounds height="36" width="36" x="778" y="1309"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="70" x="762" y="1352"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_0ik9ljb" id="Event_0ik9ljb_di">
        <dc:Bounds height="36" width="36" x="978" y="1379"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Activity_0bwc3fs" id="Activity_0bwc3fs_di">
        <dc:Bounds height="80" width="100" x="855" y="1287"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_1u79s5e" id="Event_1u79s5e_di">
        <dc:Bounds height="36" width="36" x="912" y="1269"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="Association_1m2dkze" id="Association_1m2dkze_di">
        <di:waypoint x="1116" y="1336"/>
        <di:waypoint x="1170" y="1321"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape bpmnElement="Activity_1ub4uec" id="Activity_1ub4uec_di" isExpanded="true">
        <dc:Bounds height="168" width="690" x="180" y="1554"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="TextAnnotation_0jajszm" id="TextAnnotation_0jajszm_di">
        <dc:Bounds height="29.976489028213166" width="119.97942789968651" x="220" y="1574"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="Flow_1aitcb3" id="Flow_1aitcb3_di">
        <di:waypoint x="580" y="1667"/>
        <di:waypoint x="580" y="1684"/>
        <di:waypoint x="642" y="1684"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1xsgq4b" id="Flow_1xsgq4b_di">
        <di:waypoint x="328" y="1642"/>
        <di:waypoint x="400" y="1642"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_11bbdmr" id="Flow_11bbdmr_di">
        <di:waypoint x="500" y="1642"/>
        <di:waypoint x="555" y="1642"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Flow_1rlz9jr" id="Flow_1rlz9jr_di">
        <di:waypoint x="605" y="1642"/>
        <di:waypoint x="710" y="1642"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="14" width="18" x="606" y="1623"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape bioc:fill="rgb(255, 205, 210)" bioc:stroke="rgb(229, 57, 53)" bpmnElement="Gateway_0jzqvuk" id="Gateway_0jzqvuk_di" isMarkerVisible="true">
        <dc:Bounds height="50" width="50" x="555" y="1617"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="14" width="71" x="545" y="1587"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(255, 205, 210)" bioc:stroke="rgb(229, 57, 53)" bpmnElement="Activity_0x45xis" id="Activity_0x45xis_di">
        <dc:Bounds height="80" width="100" x="400" y="1602"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_0yaq6h7" id="Event_0yaq6h7_di">
        <dc:Bounds height="36" width="36" x="292" y="1624"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_10z2xmm" id="Event_10z2xmm_di">
        <dc:Bounds height="36" width="36" x="642" y="1666"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(255, 224, 178)" bioc:stroke="rgb(251, 140, 0)" bpmnElement="Activity_0lp5px6" id="Activity_0lp5px6_di">
        <dc:Bounds height="80" width="100" x="710" y="1602"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="Association_1xtlmwt" id="Association_1xtlmwt_di">
        <di:waypoint x="300" y="1627"/>
        <di:waypoint x="286" y="1604"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape bpmnElement="Event_1ds0rkt" id="Event_1ds0rkt_di">
        <dc:Bounds height="36" width="36" x="922" y="762"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_1wobyh0" id="Event_1wobyh0_di">
        <dc:Bounds height="36" width="36" x="2112" y="764"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_0vhckqr" id="Event_0vhckqr_di">
        <dc:Bounds height="36" width="36" x="6442" y="772"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_0bdw6ep" id="Event_0bdw6ep_di">
        <dc:Bounds height="36" width="36" x="5812" y="772"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_039teyx" id="Event_039teyx_di">
        <dc:Bounds height="36" width="36" x="6702" y="782"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_14opq3c" id="Event_14opq3c_di">
        <dc:Bounds height="36" width="36" x="6952" y="772"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_0ay42ls" id="Event_0ay42ls_di">
        <dc:Bounds height="36" width="36" x="7562" y="772"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_01z91y6" id="Event_01z91y6_di">
        <dc:Bounds height="36" width="36" x="7792" y="852"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_0doxj6u" id="Event_0doxj6u_di">
        <dc:Bounds height="36" width="36" x="8062" y="652"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_10sd9gr" id="Event_0fxnnmf_di">
        <dc:Bounds height="36" width="36" x="568" y="762"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Group_0x4ipdw" id="Group_0x4ipdw_di">
        <dc:Bounds height="490" width="8659" x="160" y="1010"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="14" width="32" x="4394" y="1017"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(255, 224, 178)" bioc:stroke="rgb(251, 140, 0)" bpmnElement="Group_0tw2d7h" id="Group_0tw2d7h_di">
        <dc:Bounds height="360" width="8659" x="160" y="630"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="14" width="37" x="4391" y="637"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="TextAnnotation_15wsbx5" id="TextAnnotation_15wsbx5_di">
        <dc:Bounds height="221" width="290" x="160" y="80"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(187, 222, 251)" bioc:stroke="rgb(30, 136, 229)" bpmnElement="Group_0yag5d3" id="Group_0yag5d3_di">
        <dc:Bounds height="205" width="8659" x="160" y="1535"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="14" width="48" x="4386" y="1542"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="TextAnnotation_0bgxkp1" id="TextAnnotation_0bgxkp1_di">
        <dc:Bounds height="75" width="185" x="1170" y="1310"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="TextAnnotation_01yk1iv" id="TextAnnotation_01yk1iv_di">
        <dc:Bounds height="83.47062053816585" width="99.99656781987919" x="5670" y="1620"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="TextAnnotation_08vsdrt" id="TextAnnotation_08vsdrt_di">
        <dc:Bounds height="58.99753193960511" width="169.98765969802554" x="7700" y="681"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="TextAnnotation_10pxain" id="TextAnnotation_10pxain_di">
        <dc:Bounds height="53.426248548199766" width="99.99274099883856" x="6730" y="1635"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(255, 205, 210)" bioc:stroke="rgb(229, 57, 53)" bpmnElement="TextAnnotation_1bhys7w" id="TextAnnotation_1bhys7w_di">
        <dc:Bounds height="69" width="100" x="1140" y="500"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(255, 205, 210)" bioc:stroke="rgb(229, 57, 53)" bpmnElement="TextAnnotation_1rlylyy" id="TextAnnotation_1rlylyy_di">
        <dc:Bounds height="109.17537746806039" width="99.99274099883856" x="2530" y="850"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(255, 205, 210)" bioc:stroke="rgb(229, 57, 53)" bpmnElement="TextAnnotation_08ku9bs" id="TextAnnotation_08ku9bs_di">
        <dc:Bounds height="53.426248548199766" width="99.99274099883856" x="4330" y="1310"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(255, 205, 210)" bioc:stroke="rgb(229, 57, 53)" bpmnElement="TextAnnotation_0gj5j9m" id="TextAnnotation_0gj5j9m_di">
        <dc:Bounds height="39" width="100" x="5710" y="710"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(225, 190, 231)" bioc:stroke="rgb(142, 36, 170)" bpmnElement="Event_13gs364" id="Event_1vwvrqa_di">
        <dc:Bounds height="36" width="36" x="5602" y="1552"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="48" x="5546" y="1535"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(225, 190, 231)" bioc:stroke="rgb(142, 36, 170)" bpmnElement="Event_18mpuz1" id="Event_18mpuz1_di">
        <dc:Bounds height="36" width="36" x="6232" y="732"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="48" x="6266" y="706"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Event_1q10yql" id="Event_0w1jp3g_di">
        <dc:Bounds height="36" width="36" x="7912" y="762"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="14" width="67" x="7858" y="743"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(225, 190, 231)" bioc:stroke="rgb(142, 36, 170)" bpmnElement="Event_08c0sei" id="Event_1iwclzq_di">
        <dc:Bounds height="36" width="36" x="6676" y="1552"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="14" width="74" x="6599" y="1547"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(225, 190, 231)" bioc:stroke="rgb(142, 36, 170)" bpmnElement="Event_0bd402b" id="Event_0bd402b_di">
        <dc:Bounds height="36" width="36" x="7337" y="712"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="48" x="7376" y="690"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(225, 190, 231)" bioc:stroke="rgb(142, 36, 170)" bpmnElement="Event_0gmpmbc" id="Event_0gmpmbc_di">
        <dc:Bounds height="36" width="36" x="1693" y="758"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="48" x="1730" y="741"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(225, 190, 231)" bioc:stroke="rgb(142, 36, 170)" bpmnElement="Event_03aeaop" id="Event_03aeaop_di">
        <dc:Bounds height="36" width="36" x="8207" y="902"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="48" x="8231" y="936"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(255, 205, 210)" bioc:stroke="rgb(229, 57, 53)" bpmnElement="Event_0444oex" id="Event_0444oex_di">
        <dc:Bounds height="36" width="36" x="8172" y="762"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="44" x="8203" y="736"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(225, 190, 231)" bioc:stroke="rgb(142, 36, 170)" bpmnElement="Event_0zbpubv" id="Event_0zbpubv_di">
        <dc:Bounds height="36" width="36" x="8632" y="1072"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="48" x="8676" y="1057"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(225, 190, 231)" bioc:stroke="rgb(142, 36, 170)" bpmnElement="Event_0q338g3" id="Event_0q338g3_di">
        <dc:Bounds height="36" width="36" x="5302" y="1062"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="48" x="5336" y="1096"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(225, 190, 231)" bioc:stroke="rgb(142, 36, 170)" bpmnElement="Event_0a20oua" id="Event_0a20oua_di">
        <dc:Bounds height="36" width="36" x="4202" y="1033"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="48" x="4146" y="1015"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(225, 190, 231)" bioc:stroke="rgb(142, 36, 170)" bpmnElement="Event_0xwuv37" id="Event_0xwuv37_di">
        <dc:Bounds height="36" width="36" x="3352" y="1033"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="48" x="3295" y="1019"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bioc:fill="rgb(225, 190, 231)" bioc:stroke="rgb(142, 36, 170)" bpmnElement="Event_0geqh0m" id="Event_0geqh0m_di">
        <dc:Bounds height="36" width="36" x="2462" y="1053"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="27" width="48" x="2396" y="1027"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="Association_0bfv32b" id="Association_0bfv32b_di">
        <di:waypoint x="5620" y="1618"/>
        <di:waypoint x="5670" y="1627"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Association_1wndb39" id="Association_1wndb39_di">
        <di:waypoint x="6710" y="1627"/>
        <di:waypoint x="6735" y="1635"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Association_13nvzee" id="Association_13nvzee_di">
        <di:waypoint x="1382" y="784"/>
        <di:waypoint x="1228" y="569"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Association_01f28lj" id="Association_01f28lj_di">
        <di:waypoint x="2454" y="1071"/>
        <di:waypoint x="2530" y="947"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Association_01jbzav" id="Association_01jbzav_di">
        <di:waypoint x="4278" y="1302"/>
        <di:waypoint x="4330" y="1314"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="Association_10pgw1x" id="Association_10pgw1x_di">
        <di:waypoint x="5663" y="817"/>
        <di:waypoint x="5736" y="749"/>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
