<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:bioc="http://bpmn.io/schema/bpmn/biocolor/1.0" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0gds1uo" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.14.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.15.0">
  <bpmn:process id="offerAndBIStage" isExecutable="true" camunda:historyTimeToLive="5">
    <bpmn:extensionElements>
      <camunda:properties>
        <camunda:property name="description" value="Written Offer And Validations Stage" />
      </camunda:properties>
    </bpmn:extensionElements>
    <bpmn:startEvent id="startWrittenOfferAndBI" name="Start Written Offer and BI Stage" camunda:asyncBefore="true">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{ &#34;recordType&#34;: &#34;candidate&#34;}" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_02fr6vm</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="Gateway_0qqgrlx" name="Trigger Request BI?">
      <bpmn:incoming>Flow_02fr6vm</bpmn:incoming>
      <bpmn:outgoing>Flow_1u88awq</bpmn:outgoing>
      <bpmn:outgoing>Flow_02asalr</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_02fr6vm" sourceRef="startWrittenOfferAndBI" targetRef="Gateway_0qqgrlx" />
    <bpmn:sequenceFlow id="Flow_1u88awq" name="Yes" sourceRef="Gateway_0qqgrlx" targetRef="Gateway_07d3ald">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${triggerRequestBI == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="requestBIServiceTask" name="Request BI - Service Task" camunda:type="external" camunda:topic="expert-hiring">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{               &#34;taskHandler&#34;:&#34;was&#34;,               &#34;handlerId&#34;:&#34;REQUEST_BI&#34;,               &#34;actionName&#34;:&#34;publishEvent&#34;               }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0sgnmet</bpmn:incoming>
      <bpmn:outgoing>Flow_0hqwgqo</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0hqwgqo" sourceRef="requestBIServiceTask" targetRef="requestBIReceiveTask" />
    <bpmn:receiveTask id="requestBIReceiveTask" name="Request BI" messageRef="Message_1aqh0pn">
      <bpmn:incoming>Flow_0hqwgqo</bpmn:incoming>
      <bpmn:outgoing>Flow_03gdlwl</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_1axw0vs" name="Request BI?">
      <bpmn:incoming>Flow_09hmbss</bpmn:incoming>
      <bpmn:outgoing>Flow_0s89z4r</bpmn:outgoing>
      <bpmn:outgoing>Flow_1yjwq2d</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_03gdlwl" sourceRef="requestBIReceiveTask" targetRef="Gateway_1oigs6o" />
    <bpmn:sequenceFlow id="Flow_0s89z4r" name="SKIP_BI" sourceRef="Gateway_1axw0vs" targetRef="Gateway_0u4p40b">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${requestBIOutcome == 'SKIP_BI'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:parallelGateway id="Gateway_0u4p40b">
      <bpmn:incoming>Flow_0s89z4r</bpmn:incoming>
      <bpmn:outgoing>Flow_11ppero</bpmn:outgoing>
      <bpmn:outgoing>Flow_0i6y399</bpmn:outgoing>
      <bpmn:outgoing>Flow_00it9kt</bpmn:outgoing>
      <bpmn:outgoing>Flow_18nnl8w</bpmn:outgoing>
      <bpmn:outgoing>Flow_0trpzid</bpmn:outgoing>
      <bpmn:outgoing>Flow_184oj2a</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:sequenceFlow id="Flow_1yjwq2d" name="Request BI and Written Offer" sourceRef="Gateway_1axw0vs" targetRef="Gateway_1qaojk1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${requestBIOutcome == 'INITIATE_BI'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:parallelGateway id="Gateway_1qaojk1">
      <bpmn:incoming>Flow_1yjwq2d</bpmn:incoming>
      <bpmn:outgoing>Flow_1ewzqcl</bpmn:outgoing>
      <bpmn:outgoing>Flow_0lcrl3r</bpmn:outgoing>
      <bpmn:outgoing>Flow_1lq37xx</bpmn:outgoing>
      <bpmn:outgoing>Flow_14timu3</bpmn:outgoing>
      <bpmn:outgoing>Flow_0gnnfxz</bpmn:outgoing>
      <bpmn:outgoing>Flow_0f7x9vw</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:exclusiveGateway id="Gateway_0s27jxg" name="Request BI and trigger BI?">
      <bpmn:incoming>Flow_1ewzqcl</bpmn:incoming>
      <bpmn:outgoing>Flow_0f77zat</bpmn:outgoing>
      <bpmn:outgoing>Flow_1curk38</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1ewzqcl" name="Request BI" sourceRef="Gateway_1qaojk1" targetRef="Gateway_0s27jxg" />
    <bpmn:exclusiveGateway id="Gateway_0l1dfzh" name="Trigger Written Offer?">
      <bpmn:incoming>Flow_0up1yt0</bpmn:incoming>
      <bpmn:outgoing>Flow_0wklnpq</bpmn:outgoing>
      <bpmn:outgoing>Flow_0vabwly</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0lcrl3r" sourceRef="Gateway_1qaojk1" targetRef="Gateway_0cide47" />
    <bpmn:sequenceFlow id="Flow_0wklnpq" name="Yes" sourceRef="Gateway_0l1dfzh" targetRef="Gateway_0dundix">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${triggerWrittenOffer == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="extendWrittenOfferServiceTask" name="Extend Written offer - Service Task" camunda:type="external" camunda:topic="expert-hiring">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{               &#34;taskHandler&#34;:&#34;was&#34;,               &#34;handlerId&#34;:&#34;EXTEND_WRITTEN_OFFER&#34;,               &#34;actionName&#34;:&#34;publishEvent&#34;               }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1hmyu5h</bpmn:incoming>
      <bpmn:outgoing>Flow_0ipcv79</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0ipcv79" sourceRef="extendWrittenOfferServiceTask" targetRef="extendWrittenOfferReceiveTask" />
    <bpmn:receiveTask id="extendWrittenOfferReceiveTask" name="Extend Written offer" messageRef="Message_1lo99y8">
      <bpmn:incoming>Flow_0ipcv79</bpmn:incoming>
      <bpmn:outgoing>Flow_1iuttea</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:serviceTask id="signWrittenOfferServiceTask" name="Sign Written offer - Service Task" camunda:type="external" camunda:topic="expert-hiring">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{               &#34;taskHandler&#34;:&#34;was&#34;,               &#34;handlerId&#34;:&#34;SIGN_WRITTEN_OFFER&#34;,               &#34;actionName&#34;:&#34;publishEvent&#34;               }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1ow6fjf</bpmn:incoming>
      <bpmn:outgoing>Flow_1wb8zu2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1wb8zu2" sourceRef="signWrittenOfferServiceTask" targetRef="signWrittenOfferReceiveTask" />
    <bpmn:receiveTask id="signWrittenOfferReceiveTask" name="Sign Written offer" messageRef="Message_1hummg2">
      <bpmn:incoming>Flow_1wb8zu2</bpmn:incoming>
      <bpmn:outgoing>Flow_1caykvy</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_1bucivc" name="Written Offer Accepted?">
      <bpmn:incoming>Flow_1fdd57s</bpmn:incoming>
      <bpmn:outgoing>Flow_0f5sz3h</bpmn:outgoing>
      <bpmn:outgoing>Flow_0evb7cy</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1caykvy" sourceRef="signWrittenOfferReceiveTask" targetRef="Gateway_1jzi2kz" />
    <bpmn:sequenceFlow id="Flow_0vabwly" name="No" sourceRef="Gateway_0l1dfzh" targetRef="Gateway_0nadjor">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${triggerWrittenOffer == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0f5sz3h" name="DECLINED" sourceRef="Gateway_1bucivc" targetRef="Gateway_0dundix">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${signWrittenOfferOutcome == 'DECLINED' || signWrittenOfferOutcome == 'FAILED' || signWrittenOfferOutcome == 'REJECTED' }</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0evb7cy" name="ACCEPTED" sourceRef="Gateway_1bucivc" targetRef="Gateway_0cr4i5x">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${signWrittenOfferOutcome == 'ACCEPTED'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:parallelGateway id="Gateway_0cr4i5x" camunda:asyncBefore="true">
      <bpmn:incoming>Flow_0evb7cy</bpmn:incoming>
      <bpmn:incoming>Flow_1bvxnww</bpmn:incoming>
      <bpmn:incoming>Flow_0ztczmr</bpmn:incoming>
      <bpmn:incoming>Flow_1uj4xo9</bpmn:incoming>
      <bpmn:incoming>Flow_01n6ka4</bpmn:incoming>
      <bpmn:outgoing>Flow_1m9dyo9</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:exclusiveGateway id="Gateway_0r7ypf6" name="Trigger State Credentials?">
      <bpmn:incoming>Flow_02z7dzy</bpmn:incoming>
      <bpmn:outgoing>Flow_0id96cf</bpmn:outgoing>
      <bpmn:outgoing>Flow_1a6qotz</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1lq37xx" sourceRef="Gateway_1qaojk1" targetRef="Gateway_1hdtcu8" />
    <bpmn:sequenceFlow id="Flow_0id96cf" name="Yes" sourceRef="Gateway_0r7ypf6" targetRef="verifyStateCredentialsServiceTask">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${triggerStateCredentials == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="verifyStateCredentialsServiceTask" name="Verify state credentials - Service Task" camunda:type="external" camunda:topic="expert-hiring">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{               &#34;taskHandler&#34;:&#34;was&#34;,               &#34;handlerId&#34;:&#34;VERIFY_CREDENTIALS_STATE&#34;,               &#34;actionName&#34;:&#34;publishEvent&#34;               }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0id96cf</bpmn:incoming>
      <bpmn:outgoing>Flow_02m3zg5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_02m3zg5" sourceRef="verifyStateCredentialsServiceTask" targetRef="verifyStateCredentialsReceiveTask" />
    <bpmn:receiveTask id="verifyStateCredentialsReceiveTask" name="Verify state credentials" messageRef="Message_19x0r91">
      <bpmn:incoming>Flow_02m3zg5</bpmn:incoming>
      <bpmn:outgoing>Flow_1m7pqfj</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_1ilri6n">
      <bpmn:incoming>Flow_1a6qotz</bpmn:incoming>
      <bpmn:incoming>Flow_1m7pqfj</bpmn:incoming>
      <bpmn:outgoing>Flow_0ni7imt</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1vs0cv9" name="REJECTED" sourceRef="Gateway_1gnq50a" targetRef="verifyStateCredentialsEndEvent">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${verifyStateCredentialsOutcome == 'REJECTED'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0ni7imt" sourceRef="Gateway_1ilri6n" targetRef="Gateway_1gnq50a" />
    <bpmn:exclusiveGateway id="Gateway_09qe9ki" name="Trigger PTIN?">
      <bpmn:incoming>Flow_0nvklcm</bpmn:incoming>
      <bpmn:outgoing>Flow_15xtasd</bpmn:outgoing>
      <bpmn:outgoing>Flow_0957qcj</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_14timu3" sourceRef="Gateway_1qaojk1" targetRef="Gateway_0hjhcvm" />
    <bpmn:sequenceFlow id="Flow_15xtasd" name="Yes" sourceRef="Gateway_09qe9ki" targetRef="verifyPTINServiceTask">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${triggerPTINCredentials == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="verifyPTINServiceTask" name="Verify PTIN - Service Task" camunda:type="external" camunda:topic="expert-hiring">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{               &#34;taskHandler&#34;:&#34;was&#34;,               &#34;handlerId&#34;:&#34;VERIFY_PTIN&#34;,               &#34;actionName&#34;:&#34;publishEvent&#34;               }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_15xtasd</bpmn:incoming>
      <bpmn:outgoing>Flow_17bjsfo</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_17bjsfo" sourceRef="verifyPTINServiceTask" targetRef="verifyPTINReceiveTask" />
    <bpmn:receiveTask id="verifyPTINReceiveTask" name="Verify PTIN" messageRef="Message_0zs6yya">
      <bpmn:incoming>Flow_17bjsfo</bpmn:incoming>
      <bpmn:outgoing>Flow_1we62a5</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_1ij1cet">
      <bpmn:incoming>Flow_0957qcj</bpmn:incoming>
      <bpmn:incoming>Flow_1we62a5</bpmn:incoming>
      <bpmn:outgoing>Flow_1lcou11</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0xk0sgv" name="REJECTED" sourceRef="Gateway_0deecps" targetRef="verifyPTINEndEvent">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${verifyPTINOutcome == 'REJECTED'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1553l6c" name="ACCEPTED, NOT_APPLICABLE or SKIPPED" sourceRef="Gateway_0deecps" targetRef="Gateway_1hlad3p">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${verifyPTINOutcome == 'ACCEPTED' || verifyPTINOutcome == 'SKIPPED' || verifyPTINOutcome == 'NOT_APPLICABLE'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0g2cfam" name="Trigger Connectivity?">
      <bpmn:incoming>Flow_1pyvbl6</bpmn:incoming>
      <bpmn:outgoing>Flow_1bkdcvi</bpmn:outgoing>
      <bpmn:outgoing>Flow_0gkkj6e</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0gnnfxz" sourceRef="Gateway_1qaojk1" targetRef="Gateway_1n6yebj" />
    <bpmn:sequenceFlow id="Flow_1bkdcvi" name="Yes" sourceRef="Gateway_0g2cfam" targetRef="verifyConnectivityServiceTask">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${triggerConnectivity == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="verifyConnectivityServiceTask" name="Verify Connectivity - Service Task" camunda:type="external" camunda:topic="expert-hiring">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{               &#34;taskHandler&#34;:&#34;was&#34;,               &#34;handlerId&#34;:&#34;VERIFY_INTERNET_SPEED&#34;,               &#34;actionName&#34;:&#34;publishEvent&#34;               }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1bkdcvi</bpmn:incoming>
      <bpmn:outgoing>Flow_05wjcn3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_05wjcn3" sourceRef="verifyConnectivityServiceTask" targetRef="verifyConnectivityReceiveTask" />
    <bpmn:receiveTask id="verifyConnectivityReceiveTask" name="Verify Connectivity" messageRef="Message_06qw280">
      <bpmn:incoming>Flow_05wjcn3</bpmn:incoming>
      <bpmn:outgoing>Flow_1sbgpu2</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_18xelqw">
      <bpmn:incoming>Flow_0gkkj6e</bpmn:incoming>
      <bpmn:incoming>Flow_1sbgpu2</bpmn:incoming>
      <bpmn:outgoing>Flow_006kfjt</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1qe3o07" name="ACCEPTED, NOT_APPLICABLE or SKIPPED" sourceRef="Gateway_0r5vt34" targetRef="Gateway_13iljkn">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${verifyConnectivityOutcome == 'ACCEPTED' || verifyConnectivityOutcome == 'SKIPPED' || verifyConnectivityOutcome == 'NOT_APPLICABLE'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0ocly0g" name="REJECTED" sourceRef="Gateway_0r5vt34" targetRef="verifyConnectivityEndEvent">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${verifyConnectivityOutcome == 'REJECTED'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_11ppero" sourceRef="Gateway_0u4p40b" targetRef="Gateway_0cide47" />
    <bpmn:sequenceFlow id="Flow_0i6y399" sourceRef="Gateway_0u4p40b" targetRef="Gateway_1hdtcu8" />
    <bpmn:sequenceFlow id="Flow_00it9kt" sourceRef="Gateway_0u4p40b" targetRef="Gateway_1n6yebj" />
    <bpmn:sequenceFlow id="Flow_0f77zat" name="Request BI outcome is Initiat_Bi and trigger initiat_Bi is true" sourceRef="Gateway_0s27jxg" targetRef="initiateBIServiceTask">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${requestBIOutcome == 'INITIATE_BI' &amp;&amp; triggerInitiateBI == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_05j7fnf" sourceRef="initiateBIServiceTask" targetRef="initiateBIReceiveTask" />
    <bpmn:serviceTask id="initiateBIServiceTask" name="Initiate BI - Service Task" camunda:type="external" camunda:topic="expert-hiring">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{               &#34;taskHandler&#34;:&#34;was&#34;,               &#34;handlerId&#34;:&#34;INITIATE_BI&#34;,               &#34;actionName&#34;:&#34;publishEvent&#34;               }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0f77zat</bpmn:incoming>
      <bpmn:outgoing>Flow_05j7fnf</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:receiveTask id="initiateBIReceiveTask" name="Initiate BI" messageRef="Message_1jciz1h">
      <bpmn:incoming>Flow_05j7fnf</bpmn:incoming>
      <bpmn:outgoing>Flow_0auwm8e</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_1g5uww0">
      <bpmn:incoming>Flow_0auwm8e</bpmn:incoming>
      <bpmn:incoming>Flow_1curk38</bpmn:incoming>
      <bpmn:outgoing>Flow_0dp2crd</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0auwm8e" sourceRef="initiateBIReceiveTask" targetRef="Gateway_1g5uww0" />
    <bpmn:sequenceFlow id="Flow_1curk38" name="Request BI outcome is Initiat_Bi and trigger initiat_Bi is false" sourceRef="Gateway_0s27jxg" targetRef="Gateway_1g5uww0">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${requestBIOutcome == 'INITIATE_BI' &amp;&amp; triggerInitiateBI == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0y0ui2n" name="CAN consent accepted?">
      <bpmn:incoming>Flow_0dp2crd</bpmn:incoming>
      <bpmn:outgoing>Flow_0so5isn</bpmn:outgoing>
      <bpmn:outgoing>Flow_1knht1b</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0dp2crd" sourceRef="Gateway_1g5uww0" targetRef="Gateway_0y0ui2n" />
    <bpmn:exclusiveGateway id="Gateway_07d3ald">
      <bpmn:incoming>Flow_1u88awq</bpmn:incoming>
      <bpmn:incoming>Flow_0so5isn</bpmn:incoming>
      <bpmn:outgoing>Flow_0sgnmet</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0sgnmet" sourceRef="Gateway_07d3ald" targetRef="requestBIServiceTask" />
    <bpmn:sequenceFlow id="Flow_0so5isn" name="CANDIDATE_CONSENT_DECLINED" sourceRef="Gateway_0y0ui2n" targetRef="Gateway_07d3ald">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${initiateBIOutcome == 'CANDIDATE_CONSENT_DECLINED'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_014u0vx" name="Trigger Conduct BI?">
      <bpmn:incoming>Flow_1knht1b</bpmn:incoming>
      <bpmn:outgoing>Flow_1wnd77x</bpmn:outgoing>
      <bpmn:outgoing>Flow_1jmm9he</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1knht1b" name="CANDIDATE_CONSENT_ACCEPTED" sourceRef="Gateway_0y0ui2n" targetRef="Gateway_014u0vx">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${initiateBIOutcome == 'CANDIDATE_CONSENT_ACCEPTED'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1wnd77x" name="Yes" sourceRef="Gateway_014u0vx" targetRef="conductBIServiceTask">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${triggerConductBI == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="conductBIServiceTask" name="Conduct BI - Service Task" camunda:type="external" camunda:topic="expert-hiring">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{               &#34;taskHandler&#34;:&#34;was&#34;,               &#34;handlerId&#34;:&#34;CONDUCT_BI&#34;,               &#34;actionName&#34;:&#34;publishEvent&#34;               }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1wnd77x</bpmn:incoming>
      <bpmn:outgoing>Flow_0a8gcks</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0a8gcks" sourceRef="conductBIServiceTask" targetRef="conductBIReceiveTask" />
    <bpmn:receiveTask id="conductBIReceiveTask" name="Conduct BI" messageRef="Message_1u3ludb">
      <bpmn:incoming>Flow_0a8gcks</bpmn:incoming>
      <bpmn:outgoing>Flow_0twujbz</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_0ab8gb0">
      <bpmn:incoming>Flow_0twujbz</bpmn:incoming>
      <bpmn:incoming>Flow_1jmm9he</bpmn:incoming>
      <bpmn:outgoing>Flow_1hup34j</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0twujbz" sourceRef="conductBIReceiveTask" targetRef="Gateway_0ab8gb0" />
    <bpmn:sequenceFlow id="Flow_1jmm9he" name="No" sourceRef="Gateway_014u0vx" targetRef="Gateway_0ab8gb0">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${triggerConductBI == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1rmibk7" name="Trigger Approve BI?">
      <bpmn:incoming>Flow_1hup34j</bpmn:incoming>
      <bpmn:outgoing>Flow_1ywlc8q</bpmn:outgoing>
      <bpmn:outgoing>Flow_1qr7ovd</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1hup34j" sourceRef="Gateway_0ab8gb0" targetRef="Gateway_1rmibk7" />
    <bpmn:sequenceFlow id="Flow_1ywlc8q" name="Yes" sourceRef="Gateway_1rmibk7" targetRef="approveBIServiceTask">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${triggerApproveBI == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="approveBIServiceTask" name="Approve BI - Service Task" camunda:type="external" camunda:topic="expert-hiring">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{               &#34;taskHandler&#34;:&#34;was&#34;,               &#34;handlerId&#34;:&#34;APPROVE_BI&#34;,               &#34;actionName&#34;:&#34;publishEvent&#34;               }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1ywlc8q</bpmn:incoming>
      <bpmn:outgoing>Flow_17zsh4o</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_17zsh4o" sourceRef="approveBIServiceTask" targetRef="approveBIReceiveTask" />
    <bpmn:receiveTask id="approveBIReceiveTask" name="Approve BI" messageRef="Message_1omns55">
      <bpmn:incoming>Flow_17zsh4o</bpmn:incoming>
      <bpmn:outgoing>Flow_1sql4c5</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_15scvxw">
      <bpmn:incoming>Flow_1sql4c5</bpmn:incoming>
      <bpmn:incoming>Flow_1qr7ovd</bpmn:incoming>
      <bpmn:outgoing>Flow_04bp2i7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1sql4c5" sourceRef="approveBIReceiveTask" targetRef="Gateway_15scvxw" />
    <bpmn:sequenceFlow id="Flow_1qr7ovd" name="No" sourceRef="Gateway_1rmibk7" targetRef="Gateway_15scvxw">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${triggerApproveBI == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0fq86r7" name="continue?">
      <bpmn:incoming>Flow_04bp2i7</bpmn:incoming>
      <bpmn:outgoing>Flow_11lww72</bpmn:outgoing>
      <bpmn:outgoing>Flow_1xxmwwm</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_04bp2i7" sourceRef="Gateway_15scvxw" targetRef="Gateway_0fq86r7" />
    <bpmn:sequenceFlow id="Flow_11lww72" name="No" sourceRef="Gateway_0fq86r7" targetRef="approveBIEndEvent">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approveBIOutcome == 'REJECTED' }</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1xxmwwm" name="yes" sourceRef="Gateway_0fq86r7" targetRef="Gateway_1p1dvad">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approveBIOutcome == 'ACCEPTED' }</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0n1scs8" name="Trigger Primary Credentials?">
      <bpmn:incoming>Flow_0xbxn59</bpmn:incoming>
      <bpmn:outgoing>Flow_11ybs0r</bpmn:outgoing>
      <bpmn:outgoing>Flow_1pa6z6y</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_11ybs0r" name="Yes" sourceRef="Gateway_0n1scs8" targetRef="verifyPrimaryCredentialsServiceTask">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${triggerPrimaryCredentials == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="verifyPrimaryCredentialsServiceTask" name="Verify Primary Credentials - Service Task" camunda:type="external" camunda:topic="expert-hiring">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{               &#34;taskHandler&#34;:&#34;was&#34;,               &#34;handlerId&#34;:&#34;VERIFY_CREDENTIALS_PRIMARY&#34;,               &#34;actionName&#34;:&#34;publishEvent&#34;               }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_11ybs0r</bpmn:incoming>
      <bpmn:outgoing>Flow_1n71ia2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1n71ia2" sourceRef="verifyPrimaryCredentialsServiceTask" targetRef="verifyPrimaryCredentialsReceiveTask" />
    <bpmn:receiveTask id="verifyPrimaryCredentialsReceiveTask" name="Verify Primary Credentials" messageRef="Message_17232vz">
      <bpmn:incoming>Flow_1n71ia2</bpmn:incoming>
      <bpmn:outgoing>Flow_0zl4e33</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_0kmqr2m">
      <bpmn:incoming>Flow_1pa6z6y</bpmn:incoming>
      <bpmn:incoming>Flow_0zl4e33</bpmn:incoming>
      <bpmn:outgoing>Flow_0r9g5xp</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1pa6z6y" name="No" sourceRef="Gateway_0n1scs8" targetRef="Gateway_0kmqr2m">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${triggerPrimaryCredentials == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0a497u6" name="Trigger certifications?">
      <bpmn:incoming>Flow_176rzi4</bpmn:incoming>
      <bpmn:outgoing>Flow_0ypkuw2</bpmn:outgoing>
      <bpmn:outgoing>Flow_11751fw</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_18nnl8w" sourceRef="Gateway_0u4p40b" targetRef="Gateway_18s3kof" />
    <bpmn:sequenceFlow id="Flow_0ypkuw2" name="Yes" sourceRef="Gateway_0a497u6" targetRef="verifyCertificationsServiceTask">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${triggerCertifications == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="verifyCertificationsServiceTask" name="Verify Certifications - Service Task" camunda:type="external" camunda:topic="expert-hiring">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{               &#34;taskHandler&#34;:&#34;was&#34;,               &#34;handlerId&#34;:&#34;VERIFY_CERTIFICATIONS&#34;,               &#34;actionName&#34;:&#34;publishEvent&#34;               }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0ypkuw2</bpmn:incoming>
      <bpmn:outgoing>Flow_12vylkb</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_12vylkb" sourceRef="verifyCertificationsServiceTask" targetRef="verifyCertificationsReceiveTask" />
    <bpmn:receiveTask id="verifyCertificationsReceiveTask" name="Verify Certifications" messageRef="Message_1sex4wm">
      <bpmn:incoming>Flow_12vylkb</bpmn:incoming>
      <bpmn:outgoing>Flow_1isfmpn</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_0vfdppk" name="certifications Accepted Join">
      <bpmn:incoming>Flow_11751fw</bpmn:incoming>
      <bpmn:incoming>Flow_1isfmpn</bpmn:incoming>
      <bpmn:outgoing>Flow_0wi9wyo</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_11751fw" name="No" sourceRef="Gateway_0a497u6" targetRef="Gateway_0vfdppk">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${triggerCertifications == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0o32tob" name="certifications Accepted?">
      <bpmn:incoming>Flow_0wi9wyo</bpmn:incoming>
      <bpmn:outgoing>Flow_0wmt03k</bpmn:outgoing>
      <bpmn:outgoing>Flow_01izyjs</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0wi9wyo" sourceRef="Gateway_0vfdppk" targetRef="Gateway_0o32tob" />
    <bpmn:sequenceFlow id="Flow_0wmt03k" name="REJECTED" sourceRef="Gateway_0o32tob" targetRef="verifyCertificationsEndEvent">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${verifyCertificationsOutcome == 'REJECTED'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1a6qotz" name="No" sourceRef="Gateway_0r7ypf6" targetRef="Gateway_1ilri6n">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${triggerStateCredentials == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0957qcj" name="No" sourceRef="Gateway_09qe9ki" targetRef="Gateway_1ij1cet">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${triggerPTINCredentials == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1gnq50a" name="State Credential Outcome">
      <bpmn:incoming>Flow_0ni7imt</bpmn:incoming>
      <bpmn:outgoing>Flow_0idu84j</bpmn:outgoing>
      <bpmn:outgoing>Flow_1vs0cv9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0idu84j" name="ACCEPTED, NOT_APPLICABLE or SKIPPED" sourceRef="Gateway_1gnq50a" targetRef="Gateway_0v0vnm0">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${verifyStateCredentialsOutcome == 'ACCEPTED' || verifyStateCredentialsOutcome == 'SKIPPED' || verifyStateCredentialsOutcome == 'NOT_APPLICABLE'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0deecps" name="PTIN Outcome">
      <bpmn:incoming>Flow_1lcou11</bpmn:incoming>
      <bpmn:outgoing>Flow_1553l6c</bpmn:outgoing>
      <bpmn:outgoing>Flow_0xk0sgv</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1lcou11" sourceRef="Gateway_1ij1cet" targetRef="Gateway_0deecps" />
    <bpmn:exclusiveGateway id="Gateway_0r5vt34" name="Verify Conn. Outcome">
      <bpmn:incoming>Flow_006kfjt</bpmn:incoming>
      <bpmn:outgoing>Flow_1qe3o07</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ocly0g</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_006kfjt" sourceRef="Gateway_18xelqw" targetRef="Gateway_0r5vt34" />
    <bpmn:sequenceFlow id="Flow_01izyjs" name="ACCEPTED, NOT_APPLICABLE or SKIPPED" sourceRef="Gateway_0o32tob" targetRef="Gateway_1tgcm85">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${verifyCertificationsOutcome == 'ACCEPTED' || verifyCertificationsOutcome == 'SKIPPED' || verifyCertificationsOutcome == 'NOT_APPLICABLE'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0uzfyhm" name="Primary Credentials Accepted?">
      <bpmn:incoming>Flow_0r9g5xp</bpmn:incoming>
      <bpmn:outgoing>Flow_1p18vc0</bpmn:outgoing>
      <bpmn:outgoing>Flow_15n0vt1</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0r9g5xp" sourceRef="Gateway_0kmqr2m" targetRef="Gateway_0uzfyhm" />
    <bpmn:sequenceFlow id="Flow_1p18vc0" name="REJECTED" sourceRef="Gateway_0uzfyhm" targetRef="primaryCredentialsEndEvent">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${verifyPrimaryCredentialsOutcome == 'REJECTED'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_15n0vt1" name="ACCEPTED , NOT_APPLICABLE or SKIPPED" sourceRef="Gateway_0uzfyhm" targetRef="Gateway_0jfpmbb">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${verifyPrimaryCredentialsOutcome == 'ACCEPTED' || verifyPrimaryCredentialsOutcome == 'NOT_APPLICABLE'  || verifyPrimaryCredentialsOutcome == 'SKIPPED'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0gkkj6e" name="No" sourceRef="Gateway_0g2cfam" targetRef="Gateway_18xelqw">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${triggerConnectivity == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1m9dyo9" sourceRef="Gateway_0cr4i5x" targetRef="Gateway_10n829e" />
    <bpmn:exclusiveGateway id="Gateway_1oigs6o">
      <bpmn:incoming>Flow_03gdlwl</bpmn:incoming>
      <bpmn:incoming>Flow_02asalr</bpmn:incoming>
      <bpmn:outgoing>Flow_09hmbss</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_09hmbss" sourceRef="Gateway_1oigs6o" targetRef="Gateway_1axw0vs" />
    <bpmn:sequenceFlow id="Flow_02asalr" name="No" sourceRef="Gateway_0qqgrlx" targetRef="Gateway_1oigs6o">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${triggerRequestBI == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0f7x9vw" sourceRef="Gateway_1qaojk1" targetRef="Gateway_18s3kof" />
    <bpmn:sequenceFlow id="Flow_0trpzid" sourceRef="Gateway_0u4p40b" targetRef="Gateway_0hjhcvm" />
    <bpmn:sequenceFlow id="Flow_184oj2a" sourceRef="Gateway_0u4p40b" targetRef="Gateway_1p1dvad" />
    <bpmn:exclusiveGateway id="Gateway_1p1dvad">
      <bpmn:incoming>Flow_184oj2a</bpmn:incoming>
      <bpmn:incoming>Flow_1xxmwwm</bpmn:incoming>
      <bpmn:outgoing>Flow_0xbxn59</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0v0vnm0">
      <bpmn:incoming>Flow_0idu84j</bpmn:incoming>
      <bpmn:outgoing>Flow_1bvxnww</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1bvxnww" sourceRef="Gateway_0v0vnm0" targetRef="Gateway_0cr4i5x" />
    <bpmn:exclusiveGateway id="Gateway_1hlad3p">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1553l6c</bpmn:incoming>
      <bpmn:outgoing>Flow_0ztczmr</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ztczmr" sourceRef="Gateway_1hlad3p" targetRef="Gateway_0cr4i5x" />
    <bpmn:exclusiveGateway id="Gateway_13iljkn">
      <bpmn:incoming>Flow_1qe3o07</bpmn:incoming>
      <bpmn:outgoing>Flow_1uj4xo9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1uj4xo9" sourceRef="Gateway_13iljkn" targetRef="Gateway_0cr4i5x" />
    <bpmn:exclusiveGateway id="Gateway_1tgcm85">
      <bpmn:incoming>Flow_01izyjs</bpmn:incoming>
      <bpmn:outgoing>Flow_01n6ka4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_01n6ka4" sourceRef="Gateway_1tgcm85" targetRef="Gateway_0cr4i5x" />
    <bpmn:endEvent id="verifyPTINEndEvent" name="end of workflow">
      <bpmn:incoming>Flow_0xk0sgv</bpmn:incoming>
      <bpmn:escalationEventDefinition id="EscalationEventDefinition_0r991qq" escalationRef="Escalation_0niaons" />
    </bpmn:endEvent>
    <bpmn:endEvent id="verifyConnectivityEndEvent" name="end of workflow">
      <bpmn:incoming>Flow_0ocly0g</bpmn:incoming>
      <bpmn:escalationEventDefinition id="EscalationEventDefinition_0eisl4d" escalationRef="Escalation_0niaons" />
    </bpmn:endEvent>
    <bpmn:endEvent id="verifyCertificationsEndEvent" name="end of workflow">
      <bpmn:incoming>Flow_0wmt03k</bpmn:incoming>
      <bpmn:escalationEventDefinition id="EscalationEventDefinition_0lgyhi9" escalationRef="Escalation_0niaons" />
    </bpmn:endEvent>
    <bpmn:endEvent id="primaryCredentialsEndEvent" name="end of workflow">
      <bpmn:incoming>Flow_1p18vc0</bpmn:incoming>
      <bpmn:escalationEventDefinition id="EscalationEventDefinition_00n1klt" escalationRef="Escalation_0niaons" />
    </bpmn:endEvent>
    <bpmn:endEvent id="approveBIEndEvent" name="end of worflow">
      <bpmn:incoming>Flow_11lww72</bpmn:incoming>
      <bpmn:escalationEventDefinition id="EscalationEventDefinition_13vgwqz" escalationRef="Escalation_0niaons" />
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_1hdtcu8">
      <bpmn:incoming>Flow_0i6y399</bpmn:incoming>
      <bpmn:incoming>Flow_1lq37xx</bpmn:incoming>
      <bpmn:outgoing>Flow_02z7dzy</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0hjhcvm">
      <bpmn:incoming>Flow_0trpzid</bpmn:incoming>
      <bpmn:incoming>Flow_14timu3</bpmn:incoming>
      <bpmn:outgoing>Flow_0nvklcm</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1n6yebj">
      <bpmn:incoming>Flow_00it9kt</bpmn:incoming>
      <bpmn:incoming>Flow_0gnnfxz</bpmn:incoming>
      <bpmn:outgoing>Flow_1pyvbl6</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_18s3kof">
      <bpmn:incoming>Flow_18nnl8w</bpmn:incoming>
      <bpmn:incoming>Flow_0f7x9vw</bpmn:incoming>
      <bpmn:outgoing>Flow_176rzi4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0cide47">
      <bpmn:incoming>Flow_11ppero</bpmn:incoming>
      <bpmn:incoming>Flow_0lcrl3r</bpmn:incoming>
      <bpmn:outgoing>Flow_0up1yt0</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0up1yt0" sourceRef="Gateway_0cide47" targetRef="Gateway_0l1dfzh" />
    <bpmn:exclusiveGateway id="Gateway_1jzi2kz">
      <bpmn:incoming>Flow_1caykvy</bpmn:incoming>
      <bpmn:incoming>Flow_0vo5yy2</bpmn:incoming>
      <bpmn:outgoing>Flow_1fdd57s</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1fdd57s" sourceRef="Gateway_1jzi2kz" targetRef="Gateway_1bucivc" />
    <bpmn:exclusiveGateway id="Gateway_0jfpmbb">
      <bpmn:incoming>Flow_15n0vt1</bpmn:incoming>
      <bpmn:outgoing>Flow_07wkqy2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0dundix">
      <bpmn:incoming>Flow_0wklnpq</bpmn:incoming>
      <bpmn:incoming>Flow_0f5sz3h</bpmn:incoming>
      <bpmn:outgoing>Flow_1hmyu5h</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1hmyu5h" sourceRef="Gateway_0dundix" targetRef="extendWrittenOfferServiceTask" />
    <bpmn:subProcess id="Activity_0i75s6j" name="Terminates Candidate Workflow upon receiving TERMINATE_CANDIDATE_WORKFLOW message" triggeredByEvent="true">
      <bpmn:startEvent id="Event_1n5qq1l" name="Terminate Workflow">
        <bpmn:outgoing>Flow_0hfhesi</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_0dzlrz2" messageRef="Message_0eqv78g" />
      </bpmn:startEvent>
      <bpmn:endEvent id="Event_0i8ldxu" name="End of Candidate Workflow">
        <bpmn:incoming>Flow_0hfhesi</bpmn:incoming>
        <bpmn:escalationEventDefinition id="EscalationEventDefinition_1os6yfg" escalationRef="Escalation_0niaons" />
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="Flow_0hfhesi" sourceRef="Event_1n5qq1l" targetRef="Event_0i8ldxu" />
    </bpmn:subProcess>
    <bpmn:endEvent id="verifyStateCredentialsEndEvent" name="end of workflow">
      <bpmn:incoming>Flow_1vs0cv9</bpmn:incoming>
      <bpmn:escalationEventDefinition id="EscalationEventDefinition_0psp45l" escalationRef="Escalation_0niaons" />
    </bpmn:endEvent>
    <bpmn:subProcess id="Activity_078qpmn" name="Trigger redos when receiving RETRIGGER message" triggeredByEvent="true">
      <bpmn:startEvent id="Event_0dm3pqv" name="Retrigger ">
        <bpmn:extensionElements>
          <camunda:executionListener expression="${execution.setVariable(&#34;retrigger&#34;, &#34;true&#34;)}" event="end" />
        </bpmn:extensionElements>
        <bpmn:outgoing>Flow_1t9dokn</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_1ce7kho" messageRef="Message_0mht2s3" />
      </bpmn:startEvent>
      <bpmn:sequenceFlow id="Flow_1t9dokn" sourceRef="Event_0dm3pqv" targetRef="Event_0pvz8f8" />
      <bpmn:endEvent id="Event_0pvz8f8" name="End of Candidate Workflow">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
            "taskHandler": "was",
            "handlerId": "",
            "actionName": "updateProcessStatus"
            }</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1t9dokn</bpmn:incoming>
        <bpmn:messageEventDefinition id="MessageEventDefinition_1xaza0t" messageRef="Message_058w0xb" camunda:type="external" camunda:topic="expert-hiring" />
      </bpmn:endEvent>
    </bpmn:subProcess>
    <bpmn:parallelGateway id="Gateway_10n829e" camunda:asyncBefore="true">
      <bpmn:incoming>Flow_1m9dyo9</bpmn:incoming>
      <bpmn:incoming>Flow_07wkqy2</bpmn:incoming>
      <bpmn:outgoing>Flow_0ez23b5</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:sequenceFlow id="Flow_07wkqy2" sourceRef="Gateway_0jfpmbb" targetRef="Gateway_10n829e" />
    <bpmn:endEvent id="Event_0zs3qbu" name="End of Written Offer and BI Stage">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
            "taskHandler": "was",
            "handlerId": "",
            "actionName": "updateProcessStatus"
            }</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:executionListener expression="${execution.setVariable(&#34;retrigger&#34;, &#34;false&#34;)}" event="end" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0ez23b5</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1kpz46u" messageRef="Message_18latf9" camunda:type="external" camunda:topic="expert-hiring" />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0ez23b5" sourceRef="Gateway_10n829e" targetRef="Event_0zs3qbu" />
    <bpmn:sequenceFlow id="Flow_176rzi4" sourceRef="Gateway_18s3kof" targetRef="Gateway_0a497u6" />
    <bpmn:sequenceFlow id="Flow_1pyvbl6" sourceRef="Gateway_1n6yebj" targetRef="Gateway_0g2cfam" />
    <bpmn:sequenceFlow id="Flow_0nvklcm" sourceRef="Gateway_0hjhcvm" targetRef="Gateway_09qe9ki" />
    <bpmn:sequenceFlow id="Flow_02z7dzy" sourceRef="Gateway_1hdtcu8" targetRef="Gateway_0r7ypf6" />
    <bpmn:sequenceFlow id="Flow_0xbxn59" sourceRef="Gateway_1p1dvad" targetRef="Gateway_0n1scs8" />
    <bpmn:exclusiveGateway id="Gateway_0nadjor" name="Trigger sign Offer?">
      <bpmn:incoming>Flow_0vabwly</bpmn:incoming>
      <bpmn:outgoing>Flow_0vo5yy2</bpmn:outgoing>
      <bpmn:outgoing>Flow_0szkaoz</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0vo5yy2" name="No" sourceRef="Gateway_0nadjor" targetRef="Gateway_1jzi2kz">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('signWrittenOfferOutcome') != null }</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1aamatq">
      <bpmn:incoming>Flow_0szkaoz</bpmn:incoming>
      <bpmn:incoming>Flow_1iuttea</bpmn:incoming>
      <bpmn:outgoing>Flow_1ow6fjf</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0szkaoz" name="Yes" sourceRef="Gateway_0nadjor" targetRef="Gateway_1aamatq">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('signWrittenOfferOutcome') == null }</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1iuttea" sourceRef="extendWrittenOfferReceiveTask" targetRef="Gateway_1aamatq" />
    <bpmn:sequenceFlow id="Flow_1ow6fjf" sourceRef="Gateway_1aamatq" targetRef="signWrittenOfferServiceTask" />
    <bpmn:boundaryEvent id="intiateBiTimer" name="intiate bi timer" cancelActivity="false" attachedToRef="initiateBIReceiveTask">
      <bpmn:outgoing>Flow_078nos9</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_11oat5w">
        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">${completeBIrequestTimerDuration}</bpmn:timeDuration>
      </bpmn:timerEventDefinition>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_078nos9" sourceRef="intiateBiTimer" targetRef="initiateBIReceiveTaskReminder" />
    <bpmn:serviceTask id="initiateBIReceiveTaskReminder" name="intiate bi notification- service task" camunda:type="external" camunda:topic="expert-hiring">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{               &#34;taskHandler&#34;:&#34;was&#34;,               &#34;handlerId&#34;:&#34;INITIATE_BI&#34;,               &#34;actionName&#34;:&#34;publishEvent&#34; , &#34;type&#34;:&#34;INIT_BI_NOTIFICATION&#34;              }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_078nos9</bpmn:incoming>
      <bpmn:outgoing>Flow_145a5yp</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_1tgxg1r" name="intiateBiNotifSent">
      <bpmn:incoming>Flow_145a5yp</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_145a5yp" sourceRef="initiateBIReceiveTaskReminder" targetRef="Event_1tgxg1r" />
    <bpmn:serviceTask id="extendWrittenOfferNotification" name="extend Written Offer Notification - service task" camunda:type="external" camunda:topic="expert-hiring">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{               &#34;taskHandler&#34;:&#34;was&#34;,               &#34;handlerId&#34;:&#34;EXTEND_WRITTEN_OFFER&#34;,               &#34;actionName&#34;:&#34;publishEvent&#34; , &#34;type&#34;:&#34;EXTEND_WRT_OFFER_NOTIFICATION&#34;              }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_14ptsvi</bpmn:incoming>
      <bpmn:outgoing>Flow_0i01pi8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="signWrittenOfferNotification" name="sign Written Offer Notification" camunda:type="external" camunda:topic="expert-hiring">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{               &#34;taskHandler&#34;:&#34;was&#34;,               &#34;handlerId&#34;:&#34;SIGN_WRITTEN_OFFER&#34;,               &#34;actionName&#34;:&#34;publishEvent&#34; , &#34;type&#34;:&#34;SIGN_WRT_OFFER_NOTIFICATION&#34;              }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_10de4em</bpmn:incoming>
      <bpmn:outgoing>Flow_1nbmg7d</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_1pbbg8i" name="singWrittenOfferNotifSent">
      <bpmn:incoming>Flow_1nbmg7d</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1nbmg7d" sourceRef="signWrittenOfferNotification" targetRef="Event_1pbbg8i" />
    <bpmn:endEvent id="Event_0xvy1ke" name="extendWrittenOfferNotifSent">
      <bpmn:incoming>Flow_0i01pi8</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0i01pi8" sourceRef="extendWrittenOfferNotification" targetRef="Event_0xvy1ke" />
    <bpmn:boundaryEvent id="extendWrittenOfferTimer" name="extend written offer timer" cancelActivity="false" attachedToRef="extendWrittenOfferReceiveTask">
      <bpmn:outgoing>Flow_14ptsvi</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_1w0cmxg">
        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">${sendWrittenOfferDuration}</bpmn:timeDuration>
      </bpmn:timerEventDefinition>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_14ptsvi" sourceRef="extendWrittenOfferTimer" targetRef="extendWrittenOfferNotification" />
    <bpmn:boundaryEvent id="signWrittenOfferTimer" name="Sign Written offer timer" cancelActivity="false" attachedToRef="signWrittenOfferReceiveTask">
      <bpmn:outgoing>Flow_10de4em</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_0vcdo2i">
        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">${signWrittenOfferDuration}</bpmn:timeDuration>
      </bpmn:timerEventDefinition>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_10de4em" sourceRef="signWrittenOfferTimer" targetRef="signWrittenOfferNotification" />
    <bpmn:boundaryEvent id="verify_connectivity_timer" name="VERIFY CONNECTIVITY TIMER" cancelActivity="false" attachedToRef="verifyConnectivityReceiveTask">
      <bpmn:outgoing>Flow_1rinq9y</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_0wha5hm">
        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">${verifyConnectivityDuration}</bpmn:timeDuration>
      </bpmn:timerEventDefinition>
    </bpmn:boundaryEvent>
    <bpmn:serviceTask id="verifyConnectivityNotification" name="verify Connectivity Notification" camunda:type="external" camunda:topic="expert-hiring">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{               &#34;taskHandler&#34;:&#34;was&#34;,               &#34;handlerId&#34;:&#34;VERIFY_INTERNET_SPEED&#34;,               &#34;actionName&#34;:&#34;publishEvent&#34; , &#34;type&#34;:&#34;VERIFY_CONNECTIVITY_NOTIFICATION&#34;              }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1rinq9y</bpmn:incoming>
      <bpmn:outgoing>Flow_0oeunqp</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_0o3iy44" name="verifyConnectivityNotifSent">
      <bpmn:incoming>Flow_0oeunqp</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0oeunqp" sourceRef="verifyConnectivityNotification" targetRef="Event_0o3iy44" />
    <bpmn:sequenceFlow id="Flow_1rinq9y" sourceRef="verify_connectivity_timer" targetRef="verifyConnectivityNotification" />
    <bpmn:boundaryEvent id="verifyPTINTimer" name="VERIFY PTIN TIMER" cancelActivity="false" attachedToRef="verifyPTINReceiveTask">
      <bpmn:outgoing>Flow_0yzvbrw</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_0sp6ojn">
        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">${verifyPtinDuration}</bpmn:timeDuration>
      </bpmn:timerEventDefinition>
    </bpmn:boundaryEvent>
    <bpmn:serviceTask id="verifyPtinNotification" name="verify PTIN Notification" camunda:type="external" camunda:topic="expert-hiring">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{               &#34;taskHandler&#34;:&#34;was&#34;,               &#34;handlerId&#34;:&#34;VERIFY_PTIN&#34;,               &#34;actionName&#34;:&#34;publishEvent&#34; , &#34;type&#34;:&#34;VERIFY_PTIN_NOTIFICATION&#34;              }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0yzvbrw</bpmn:incoming>
      <bpmn:outgoing>Flow_1oab8wd</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0yzvbrw" sourceRef="verifyPTINTimer" targetRef="verifyPtinNotification" />
    <bpmn:endEvent id="Event_0dp48hx" name="verifyPTINNotifSent">
      <bpmn:incoming>Flow_1oab8wd</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1oab8wd" sourceRef="verifyPtinNotification" targetRef="Event_0dp48hx" />
    <bpmn:sequenceFlow id="Flow_1sbgpu2" sourceRef="verifyConnectivityReceiveTask" targetRef="Gateway_18xelqw" />
    <bpmn:sequenceFlow id="Flow_1m7pqfj" sourceRef="verifyStateCredentialsReceiveTask" targetRef="Gateway_1ilri6n" />
    <bpmn:sequenceFlow id="Flow_1isfmpn" sourceRef="verifyCertificationsReceiveTask" targetRef="Gateway_0vfdppk" />
    <bpmn:sequenceFlow id="Flow_0zl4e33" sourceRef="verifyPrimaryCredentialsReceiveTask" targetRef="Gateway_0kmqr2m" />
    <bpmn:sequenceFlow id="Flow_1we62a5" sourceRef="verifyPTINReceiveTask" targetRef="Gateway_1ij1cet" />
  </bpmn:process>
  <bpmn:message id="Message_1lo99y8" name="EXTEND_WRITTEN_OFFER" />
  <bpmn:message id="Message_1aqh0pn" name="REQUEST_BI" />
  <bpmn:message id="Message_1hummg2" name="SIGN_WRITTEN_OFFER" />
  <bpmn:message id="Message_19x0r91" name="VERIFY_CREDENTIALS_STATE" />
  <bpmn:message id="Message_0zs6yya" name="VERIFY_PTIN" />
  <bpmn:message id="Message_06qw280" name="VERIFY_INTERNET_SPEED" />
  <bpmn:message id="Message_1jciz1h" name="INITIATE_BI" />
  <bpmn:message id="Message_1u3ludb" name="CONDUCT_BI" />
  <bpmn:message id="Message_1omns55" name="APPROVE_BI" />
  <bpmn:message id="Message_17232vz" name="VERIFY_CREDENTIALS_PRIMARY" />
  <bpmn:message id="Message_1sex4wm" name="VERIFY_CERTIFICATIONS" />
  <bpmn:escalation id="Escalation_044dzzx" />
  <bpmn:message id="Message_0mht2s3" name="RETRIGGER" />
  <bpmn:escalation id="Escalation_1c2gtni" />
  <bpmn:escalation id="Escalation_0d4qzqd" name="TEST_EVENT" escalationCode="TEST_EVENT" />
  <bpmn:message id="Message_058w0xb" name="process_ended_message" />
  <bpmn:escalation id="Escalation_1cc5bw8" />
  <bpmn:escalation id="Escalation_15lq5pp" />
  <bpmn:escalation id="Escalation_0niaons" name="TERMINATE_PARENT" escalationCode="TERMINATE_PARENT" />
  <bpmn:message id="Message_0eqv78g" name="TERMINATE_CANDIDATE_WORKFLOW" />
  <bpmn:message id="Message_19u3o15" name="process_ended_message" />
  <bpmn:message id="Message_18latf9" name="process_ended_message" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="offerAndBIStage">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="startWrittenOfferAndBI">
        <dc:Bounds x="179" y="559" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="167" y="602" width="61" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0qqgrlx_di" bpmnElement="Gateway_0qqgrlx" isMarkerVisible="true">
        <dc:Bounds x="255" y="552" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="240" y="515" width="79" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1npgqli_di" bpmnElement="requestBIServiceTask">
        <dc:Bounds x="430" y="537" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_154gmyk_di" bpmnElement="requestBIReceiveTask">
        <dc:Bounds x="580" y="537" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1axw0vs_di" bpmnElement="Gateway_1axw0vs" isMarkerVisible="true">
        <dc:Bounds x="815" y="552" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="811" y="522" width="61" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0pi612e_di" bpmnElement="Gateway_0u4p40b">
        <dc:Bounds x="815" y="705" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1b3bo5c_di" bpmnElement="Gateway_1qaojk1">
        <dc:Bounds x="1055" y="552" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0s27jxg_di" bpmnElement="Gateway_0s27jxg" isMarkerVisible="true">
        <dc:Bounds x="1055" y="375" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="964" y="390" width="76" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0l1dfzh_di" bpmnElement="Gateway_0l1dfzh" isMarkerVisible="true">
        <dc:Bounds x="1225" y="705" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1209" y="676" width="73" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_166f4k7_di" bpmnElement="extendWrittenOfferServiceTask">
        <dc:Bounds x="1420" y="660" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_017g8w6_di" bpmnElement="extendWrittenOfferReceiveTask">
        <dc:Bounds x="1560" y="660" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1g3pock_di" bpmnElement="signWrittenOfferServiceTask">
        <dc:Bounds x="1810" y="660" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0htenax_di" bpmnElement="signWrittenOfferReceiveTask">
        <dc:Bounds x="1960" y="660" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1bucivc_di" bpmnElement="Gateway_1bucivc" isMarkerVisible="true">
        <dc:Bounds x="2165" y="775" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2068" y="806" width="63" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0ytzfe9_di" bpmnElement="Gateway_0cr4i5x">
        <dc:Bounds x="2445" y="955" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0r7ypf6_di" bpmnElement="Gateway_0r7ypf6" isMarkerVisible="true">
        <dc:Bounds x="1355" y="879" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1298" y="916" width="64" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_133pgeg_di" bpmnElement="verifyStateCredentialsServiceTask">
        <dc:Bounds x="1630" y="864" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0g886qi_di" bpmnElement="verifyStateCredentialsReceiveTask">
        <dc:Bounds x="1780" y="864" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1ilri6n_di" bpmnElement="Gateway_1ilri6n" isMarkerVisible="true">
        <dc:Bounds x="1985" y="879" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_09qe9ki_di" bpmnElement="Gateway_09qe9ki" isMarkerVisible="true">
        <dc:Bounds x="1365" y="1065" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1305" y="1102" width="69" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1gjzaff_di" bpmnElement="verifyPTINServiceTask">
        <dc:Bounds x="1580" y="1050" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1sdfij5_di" bpmnElement="verifyPTINReceiveTask">
        <dc:Bounds x="1790" y="1050" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1ij1cet_di" bpmnElement="Gateway_1ij1cet" isMarkerVisible="true">
        <dc:Bounds x="1998" y="1065" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1919.5" y="963" width="81" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0g2cfam_di" bpmnElement="Gateway_0g2cfam" isMarkerVisible="true">
        <dc:Bounds x="1355" y="1355" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1287" y="1396" width="67" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0frfrhf_di" bpmnElement="verifyConnectivityServiceTask">
        <dc:Bounds x="1550" y="1340" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1hl5w36_di" bpmnElement="verifyConnectivityReceiveTask">
        <dc:Bounds x="1710" y="1340" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_18xelqw_di" bpmnElement="Gateway_18xelqw" isMarkerVisible="true">
        <dc:Bounds x="1989" y="1355" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2049" y="1076" width="60" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1cszqc2_di" bpmnElement="initiateBIServiceTask">
        <dc:Bounds x="1160" y="250" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_11vqm2f_di" bpmnElement="initiateBIReceiveTask">
        <dc:Bounds x="1320" y="250" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1g5uww0_di" bpmnElement="Gateway_1g5uww0" isMarkerVisible="true">
        <dc:Bounds x="1485" y="225" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0y0ui2n_di" bpmnElement="Gateway_0y0ui2n" isMarkerVisible="true">
        <dc:Bounds x="1605" y="225" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1665" y="236" width="65" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_07d3ald_di" bpmnElement="Gateway_07d3ald" isMarkerVisible="true">
        <dc:Bounds x="345" y="552" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_014u0vx_di" bpmnElement="Gateway_014u0vx" isMarkerVisible="true">
        <dc:Bounds x="1655" y="375" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1640" y="347" width="79" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0o187de_di" bpmnElement="conductBIServiceTask">
        <dc:Bounds x="1760" y="360" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0zd200h_di" bpmnElement="conductBIReceiveTask">
        <dc:Bounds x="1920" y="360" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0ab8gb0_di" bpmnElement="Gateway_0ab8gb0" isMarkerVisible="true">
        <dc:Bounds x="1945" y="475" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1rmibk7_di" bpmnElement="Gateway_1rmibk7" isMarkerVisible="true">
        <dc:Bounds x="2075" y="525" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2061" y="476" width="78" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1izp0h5_di" bpmnElement="approveBIServiceTask">
        <dc:Bounds x="2210" y="510" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1bputy1_di" bpmnElement="approveBIReceiveTask">
        <dc:Bounds x="2400" y="510" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_15scvxw_di" bpmnElement="Gateway_15scvxw" isMarkerVisible="true">
        <dc:Bounds x="2555" y="655" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0fq86r7_di" bpmnElement="Gateway_0fq86r7" isMarkerVisible="true">
        <dc:Bounds x="2665" y="655" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2666" y="625" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0n1scs8_di" bpmnElement="Gateway_0n1scs8" isMarkerVisible="true">
        <dc:Bounds x="1279" y="1725" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1217" y="1766" width="76" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_177zk51_di" bpmnElement="verifyPrimaryCredentialsServiceTask">
        <dc:Bounds x="1610" y="1710" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0zhghtw_di" bpmnElement="verifyPrimaryCredentialsReceiveTask">
        <dc:Bounds x="1790" y="1710" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0kmqr2m_di" bpmnElement="Gateway_0kmqr2m" isMarkerVisible="true">
        <dc:Bounds x="2019" y="1725" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1986" y="1360" width="56" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0a497u6_di" bpmnElement="Gateway_0a497u6" isMarkerVisible="true">
        <dc:Bounds x="1355" y="1535" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1296" y="1580" width="68" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0fs25b1_di" bpmnElement="verifyCertificationsServiceTask">
        <dc:Bounds x="1630" y="1520" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_19kw0h0_di" bpmnElement="verifyCertificationsReceiveTask">
        <dc:Bounds x="1790" y="1520" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0vfdppk_di" bpmnElement="Gateway_0vfdppk" isMarkerVisible="true">
        <dc:Bounds x="1995" y="1535" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1985" y="1490" width="70" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0o32tob_di" bpmnElement="Gateway_0o32tob" isMarkerVisible="true">
        <dc:Bounds x="2085" y="1535" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2080" y="1592" width="62" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1gnq50a_di" bpmnElement="Gateway_1gnq50a" isMarkerVisible="true">
        <dc:Bounds x="2075" y="879" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2063" y="936" width="79" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0deecps_di" bpmnElement="Gateway_0deecps" isMarkerVisible="true">
        <dc:Bounds x="2095" y="1065" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2084" y="1122" width="73" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0r5vt34_di" bpmnElement="Gateway_0r5vt34" isMarkerVisible="true">
        <dc:Bounds x="2085" y="1355" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2084" y="1412" width="60" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0uzfyhm_di" bpmnElement="Gateway_0uzfyhm" isMarkerVisible="true">
        <dc:Bounds x="2115" y="1725" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2112" y="1782" width="56" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1oigs6o_di" bpmnElement="Gateway_1oigs6o" isMarkerVisible="true">
        <dc:Bounds x="725" y="552" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1p1dvad_di" bpmnElement="Gateway_1p1dvad" isMarkerVisible="true">
        <dc:Bounds x="925" y="1545" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="963" y="1483" width="53" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0v0vnm0_di" bpmnElement="Gateway_0v0vnm0" isMarkerVisible="true">
        <dc:Bounds x="2295" y="879" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1hlad3p_di" bpmnElement="Gateway_1hlad3p" isMarkerVisible="true">
        <dc:Bounds x="2295" y="1035" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_13iljkn_di" bpmnElement="Gateway_13iljkn" isMarkerVisible="true">
        <dc:Bounds x="2285" y="1325" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1tgcm85_di" bpmnElement="Gateway_1tgcm85" isMarkerVisible="true">
        <dc:Bounds x="2285" y="1505" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ur6f9q_di" bpmnElement="verifyPTINEndEvent">
        <dc:Bounds x="2192" y="987" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2241" y="1028" width="77" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0mqjb6p_di" bpmnElement="verifyConnectivityEndEvent">
        <dc:Bounds x="2192" y="1292" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2221" y="1371" width="77" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1h8jb7s_di" bpmnElement="verifyCertificationsEndEvent">
        <dc:Bounds x="2202" y="1492" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2241" y="1503" width="77" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1l1r8ez_di" bpmnElement="primaryCredentialsEndEvent">
        <dc:Bounds x="2226" y="1682" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2275" y="1693" width="77" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ajjzmi_di" bpmnElement="approveBIEndEvent">
        <dc:Bounds x="2672" y="782" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2656" y="825" width="71" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1hdtcu8_di" bpmnElement="Gateway_1hdtcu8" isMarkerVisible="true">
        <dc:Bounds x="1105" y="835" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0hjhcvm_di" bpmnElement="Gateway_0hjhcvm" isMarkerVisible="true">
        <dc:Bounds x="1075" y="995" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1n6yebj_di" bpmnElement="Gateway_1n6yebj" isMarkerVisible="true">
        <dc:Bounds x="1035" y="1135" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_18s3kof_di" bpmnElement="Gateway_18s3kof" isMarkerVisible="true">
        <dc:Bounds x="985" y="1315" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0cide47_di" bpmnElement="Gateway_0cide47" isMarkerVisible="true">
        <dc:Bounds x="1135" y="705" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1jzi2kz_di" bpmnElement="Gateway_1jzi2kz" isMarkerVisible="true">
        <dc:Bounds x="1985" y="775" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0jfpmbb_di" bpmnElement="Gateway_0jfpmbb" isMarkerVisible="true">
        <dc:Bounds x="2675" y="1295" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0dundix_di" bpmnElement="Gateway_0dundix" isMarkerVisible="true">
        <dc:Bounds x="1315" y="705" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0i75s6j_di" bpmnElement="Activity_0i75s6j" isExpanded="true" bioc:stroke="black" bioc:fill="white">
        <dc:Bounds x="840" y="1976" width="240" height="160" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1n5qq1l_di" bpmnElement="Event_1n5qq1l">
        <dc:Bounds x="862" y="2032" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="859" y="2075" width="49" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0i8ldxu_di" bpmnElement="Event_0i8ldxu">
        <dc:Bounds x="1012" y="2032" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="982" y="2082" width="86" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0hfhesi_di" bpmnElement="Flow_0hfhesi">
        <di:waypoint x="898" y="2050" />
        <di:waypoint x="1012" y="2050" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_1grrqiy_di" bpmnElement="verifyStateCredentialsEndEvent">
        <dc:Bounds x="2192" y="842" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2235" y="853" width="77" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_078qpmn_di" bpmnElement="Activity_078qpmn" isExpanded="true" bioc:stroke="black" bioc:fill="white">
        <dc:Bounds x="1230" y="1976" width="240" height="160" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0dm3pqv_di" bpmnElement="Event_0dm3pqv">
        <dc:Bounds x="1252" y="2032" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1252" y="2075" width="46" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1k0382d_di" bpmnElement="Event_0pvz8f8">
        <dc:Bounds x="1402" y="2032" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1372" y="2082" width="86" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1t9dokn_di" bpmnElement="Flow_1t9dokn">
        <di:waypoint x="1288" y="2050" />
        <di:waypoint x="1402" y="2050" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Gateway_1onexb1_di" bpmnElement="Gateway_10n829e">
        <dc:Bounds x="2935" y="1235" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0zs3qbu_di" bpmnElement="Event_0zs3qbu" bioc:stroke="black" bioc:fill="white">
        <dc:Bounds x="2942" y="1422" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2928" y="1478" width="70" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1lr2va0" bpmnElement="Gateway_0nadjor" isMarkerVisible="true">
        <dc:Bounds x="1715" y="775" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1711" y="825" width="58" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0bvlr0j" bpmnElement="Gateway_1aamatq" isMarkerVisible="true">
        <dc:Bounds x="1715" y="675" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1658" y="595" width="63" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0pi9jdu_di" bpmnElement="initiateBIReceiveTaskReminder">
        <dc:Bounds x="1460" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1tgxg1r_di" bpmnElement="Event_1tgxg1r">
        <dc:Bounds x="1582" y="102" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1560" y="145" width="85" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0upvdkl" bpmnElement="extendWrittenOfferNotification">
        <dc:Bounds x="1560" y="537" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_03uil9z" bpmnElement="signWrittenOfferNotification">
        <dc:Bounds x="1840" y="550" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1pbbg8i_di" bpmnElement="Event_1pbbg8i">
        <dc:Bounds x="1992" y="572" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1967" y="615" width="88" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0xvy1ke_di" bpmnElement="Event_0xvy1ke">
        <dc:Bounds x="1712" y="559" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1688" y="602" width="89" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_19rs57d_di" bpmnElement="verifyConnectivityNotification">
        <dc:Bounds x="1610" y="1250" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0o3iy44_di" bpmnElement="Event_0o3iy44">
        <dc:Bounds x="1452" y="1272" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1431" y="1315" width="81" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0vzvb0c" bpmnElement="verifyPtinNotification">
        <dc:Bounds x="1690" y="980" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0dp48hx_di" bpmnElement="Event_0dp48hx">
        <dc:Bounds x="1572" y="987" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1548" y="1030" width="88" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ogy4b3" bpmnElement="verifyPTINTimer">
        <dc:Bounds x="1872" y="1032" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1806" y="1023" width="68" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1kreta3_di" bpmnElement="verify_connectivity_timer">
        <dc:Bounds x="1782" y="1322" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1709" y="1313" width="82" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0w9ygab" bpmnElement="signWrittenOfferTimer">
        <dc:Bounds x="2042" y="642" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2092" y="650" width="85" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0shzoht" bpmnElement="extendWrittenOfferTimer">
        <dc:Bounds x="1622" y="642" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1661" y="646" width="69" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_04ut2wz_di" bpmnElement="intiateBiTimer">
        <dc:Bounds x="1402" y="232" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1345" y="213" width="69" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_02fr6vm_di" bpmnElement="Flow_02fr6vm">
        <di:waypoint x="215" y="577" />
        <di:waypoint x="255" y="577" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1u88awq_di" bpmnElement="Flow_1u88awq">
        <di:waypoint x="305" y="577" />
        <di:waypoint x="345" y="577" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="316" y="559" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0hqwgqo_di" bpmnElement="Flow_0hqwgqo">
        <di:waypoint x="530" y="577" />
        <di:waypoint x="580" y="577" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03gdlwl_di" bpmnElement="Flow_03gdlwl">
        <di:waypoint x="680" y="577" />
        <di:waypoint x="725" y="577" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0s89z4r_di" bpmnElement="Flow_0s89z4r">
        <di:waypoint x="840" y="602" />
        <di:waypoint x="840" y="705" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="849" y="652" width="42" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1yjwq2d_di" bpmnElement="Flow_1yjwq2d">
        <di:waypoint x="865" y="577" />
        <di:waypoint x="1055" y="577" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="922" y="586" width="76" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ewzqcl_di" bpmnElement="Flow_1ewzqcl">
        <di:waypoint x="1080" y="552" />
        <di:waypoint x="1080" y="425" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1042" y="513" width="55" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lcrl3r_di" bpmnElement="Flow_0lcrl3r">
        <di:waypoint x="1105" y="577" />
        <di:waypoint x="1160" y="577" />
        <di:waypoint x="1160" y="705" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wklnpq_di" bpmnElement="Flow_0wklnpq">
        <di:waypoint x="1275" y="730" />
        <di:waypoint x="1315" y="730" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1283" y="707" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ipcv79_di" bpmnElement="Flow_0ipcv79">
        <di:waypoint x="1520" y="700" />
        <di:waypoint x="1560" y="700" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wb8zu2_di" bpmnElement="Flow_1wb8zu2">
        <di:waypoint x="1910" y="700" />
        <di:waypoint x="1960" y="700" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1caykvy_di" bpmnElement="Flow_1caykvy">
        <di:waypoint x="2010" y="740" />
        <di:waypoint x="2010" y="775" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vabwly_di" bpmnElement="Flow_0vabwly">
        <di:waypoint x="1250" y="755" />
        <di:waypoint x="1250" y="800" />
        <di:waypoint x="1715" y="800" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1259" y="779" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0f5sz3h_di" bpmnElement="Flow_0f5sz3h">
        <di:waypoint x="2190" y="775" />
        <di:waypoint x="2190" y="640" />
        <di:waypoint x="1340" y="640" />
        <di:waypoint x="1340" y="705" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2202" y="720" width="56" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0evb7cy_di" bpmnElement="Flow_0evb7cy">
        <di:waypoint x="2215" y="800" />
        <di:waypoint x="2470" y="800" />
        <di:waypoint x="2470" y="955" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2377" y="783" width="60" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1lq37xx_di" bpmnElement="Flow_1lq37xx">
        <di:waypoint x="1100" y="582" />
        <di:waypoint x="1100" y="740" />
        <di:waypoint x="1130" y="760" />
        <di:waypoint x="1130" y="835" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0id96cf_di" bpmnElement="Flow_0id96cf">
        <di:waypoint x="1405" y="904" />
        <di:waypoint x="1630" y="904" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1509" y="886" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02m3zg5_di" bpmnElement="Flow_02m3zg5">
        <di:waypoint x="1730" y="904" />
        <di:waypoint x="1780" y="904" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vs0cv9_di" bpmnElement="Flow_1vs0cv9">
        <di:waypoint x="2100" y="879" />
        <di:waypoint x="2100" y="860" />
        <di:waypoint x="2192" y="860" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2102" y="842" width="59" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ni7imt_di" bpmnElement="Flow_0ni7imt">
        <di:waypoint x="2035" y="904" />
        <di:waypoint x="2075" y="904" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2110" y="800" width="73" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14timu3_di" bpmnElement="Flow_14timu3">
        <di:waypoint x="1080" y="602" />
        <di:waypoint x="1080" y="750" />
        <di:waypoint x="1100" y="770" />
        <di:waypoint x="1100" y="995" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15xtasd_di" bpmnElement="Flow_15xtasd">
        <di:waypoint x="1415" y="1090" />
        <di:waypoint x="1580" y="1090" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1489" y="1072" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_17bjsfo_di" bpmnElement="Flow_17bjsfo">
        <di:waypoint x="1680" y="1090" />
        <di:waypoint x="1790" y="1090" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xk0sgv_di" bpmnElement="Flow_0xk0sgv">
        <di:waypoint x="2120" y="1065" />
        <di:waypoint x="2120" y="1015" />
        <di:waypoint x="2195" y="1015" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2138" y="1027" width="59" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1553l6c_di" bpmnElement="Flow_1553l6c">
        <di:waypoint x="2145" y="1090" />
        <di:waypoint x="2220" y="1090" />
        <di:waypoint x="2220" y="1060" />
        <di:waypoint x="2295" y="1060" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2143" y="1127" width="84" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0gnnfxz_di" bpmnElement="Flow_0gnnfxz">
        <di:waypoint x="1060" y="582" />
        <di:waypoint x="1060" y="1135" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bkdcvi_di" bpmnElement="Flow_1bkdcvi">
        <di:waypoint x="1405" y="1380" />
        <di:waypoint x="1550" y="1380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1469" y="1362" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05wjcn3_di" bpmnElement="Flow_05wjcn3">
        <di:waypoint x="1650" y="1380" />
        <di:waypoint x="1710" y="1380" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qe3o07_di" bpmnElement="Flow_1qe3o07">
        <di:waypoint x="2135" y="1380" />
        <di:waypoint x="2210" y="1380" />
        <di:waypoint x="2210" y="1350" />
        <di:waypoint x="2285" y="1350" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2147" y="1416" width="84" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ocly0g_di" bpmnElement="Flow_0ocly0g">
        <di:waypoint x="2110" y="1355" />
        <di:waypoint x="2110" y="1310" />
        <di:waypoint x="2192" y="1310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2111" y="1382" width="59" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11ppero_di" bpmnElement="Flow_11ppero">
        <di:waypoint x="865" y="730" />
        <di:waypoint x="1135" y="730" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0i6y399_di" bpmnElement="Flow_0i6y399">
        <di:waypoint x="860" y="735" />
        <di:waypoint x="860" y="860" />
        <di:waypoint x="1105" y="860" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00it9kt_di" bpmnElement="Flow_00it9kt">
        <di:waypoint x="820" y="735" />
        <di:waypoint x="820" y="1160" />
        <di:waypoint x="1035" y="1160" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0f77zat_di" bpmnElement="Flow_0f77zat">
        <di:waypoint x="1080" y="375" />
        <di:waypoint x="1080" y="290" />
        <di:waypoint x="1160" y="290" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="986" y="277" width="87" height="66" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05j7fnf_di" bpmnElement="Flow_05j7fnf">
        <di:waypoint x="1260" y="290" />
        <di:waypoint x="1320" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0auwm8e_di" bpmnElement="Flow_0auwm8e">
        <di:waypoint x="1420" y="290" />
        <di:waypoint x="1453" y="290" />
        <di:waypoint x="1453" y="250" />
        <di:waypoint x="1485" y="250" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1curk38_di" bpmnElement="Flow_1curk38">
        <di:waypoint x="1105" y="400" />
        <di:waypoint x="1510" y="400" />
        <di:waypoint x="1510" y="275" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1264" y="402" width="87" height="66" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dp2crd_di" bpmnElement="Flow_0dp2crd">
        <di:waypoint x="1535" y="250" />
        <di:waypoint x="1605" y="250" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0sgnmet_di" bpmnElement="Flow_0sgnmet">
        <di:waypoint x="395" y="577" />
        <di:waypoint x="430" y="577" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0so5isn_di" bpmnElement="Flow_0so5isn">
        <di:waypoint x="1630" y="225" />
        <di:waypoint x="1630" y="190" />
        <di:waypoint x="370" y="190" />
        <di:waypoint x="370" y="552" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="987" y="150" width="86" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1knht1b_di" bpmnElement="Flow_1knht1b">
        <di:waypoint x="1630" y="275" />
        <di:waypoint x="1630" y="400" />
        <di:waypoint x="1655" y="400" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1545" y="301" width="83" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wnd77x_di" bpmnElement="Flow_1wnd77x">
        <di:waypoint x="1705" y="400" />
        <di:waypoint x="1760" y="400" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1724" y="382" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0a8gcks_di" bpmnElement="Flow_0a8gcks">
        <di:waypoint x="1860" y="400" />
        <di:waypoint x="1920" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0twujbz_di" bpmnElement="Flow_0twujbz">
        <di:waypoint x="1970" y="440" />
        <di:waypoint x="1970" y="475" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jmm9he_di" bpmnElement="Flow_1jmm9he">
        <di:waypoint x="1680" y="425" />
        <di:waypoint x="1680" y="500" />
        <di:waypoint x="1945" y="500" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1688" y="424" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hup34j_di" bpmnElement="Flow_1hup34j">
        <di:waypoint x="1995" y="500" />
        <di:waypoint x="2035" y="500" />
        <di:waypoint x="2035" y="550" />
        <di:waypoint x="2075" y="550" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ywlc8q_di" bpmnElement="Flow_1ywlc8q">
        <di:waypoint x="2125" y="550" />
        <di:waypoint x="2210" y="550" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2159" y="492" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_17zsh4o_di" bpmnElement="Flow_17zsh4o">
        <di:waypoint x="2310" y="550" />
        <di:waypoint x="2400" y="550" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1sql4c5_di" bpmnElement="Flow_1sql4c5">
        <di:waypoint x="2500" y="550" />
        <di:waypoint x="2580" y="550" />
        <di:waypoint x="2580" y="655" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qr7ovd_di" bpmnElement="Flow_1qr7ovd">
        <di:waypoint x="2100" y="575" />
        <di:waypoint x="2100" y="620" />
        <di:waypoint x="2580" y="620" />
        <di:waypoint x="2580" y="655" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2164" y="607" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04bp2i7_di" bpmnElement="Flow_04bp2i7">
        <di:waypoint x="2605" y="680" />
        <di:waypoint x="2665" y="680" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11lww72_di" bpmnElement="Flow_11lww72">
        <di:waypoint x="2690" y="705" />
        <di:waypoint x="2690" y="782" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2702" y="738" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xxmwwm_di" bpmnElement="Flow_1xxmwwm">
        <di:waypoint x="2715" y="680" />
        <di:waypoint x="3190" y="680" />
        <di:waypoint x="3190" y="1880" />
        <di:waypoint x="770" y="1880" />
        <di:waypoint x="770" y="1570" />
        <di:waypoint x="925" y="1570" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1951" y="1923" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11ybs0r_di" bpmnElement="Flow_11ybs0r">
        <di:waypoint x="1329" y="1750" />
        <di:waypoint x="1610" y="1750" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1461" y="1732" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1n71ia2_di" bpmnElement="Flow_1n71ia2">
        <di:waypoint x="1710" y="1750" />
        <di:waypoint x="1790" y="1750" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pa6z6y_di" bpmnElement="Flow_1pa6z6y">
        <di:waypoint x="1304" y="1775" />
        <di:waypoint x="1304" y="1810" />
        <di:waypoint x="2044" y="1810" />
        <di:waypoint x="2044" y="1775" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1552" y="1792" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18nnl8w_di" bpmnElement="Flow_18nnl8w">
        <di:waypoint x="816" y="731" />
        <di:waypoint x="760" y="750" />
        <di:waypoint x="760" y="1340" />
        <di:waypoint x="985" y="1340" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ypkuw2_di" bpmnElement="Flow_0ypkuw2">
        <di:waypoint x="1405" y="1560" />
        <di:waypoint x="1630" y="1560" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1509" y="1542" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12vylkb_di" bpmnElement="Flow_12vylkb">
        <di:waypoint x="1730" y="1560" />
        <di:waypoint x="1790" y="1560" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11751fw_di" bpmnElement="Flow_11751fw">
        <di:waypoint x="1380" y="1585" />
        <di:waypoint x="1380" y="1630" />
        <di:waypoint x="2020" y="1630" />
        <di:waypoint x="2020" y="1585" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1705" y="1613" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wi9wyo_di" bpmnElement="Flow_0wi9wyo">
        <di:waypoint x="2045" y="1560" />
        <di:waypoint x="2085" y="1560" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wmt03k_di" bpmnElement="Flow_0wmt03k">
        <di:waypoint x="2110" y="1535" />
        <di:waypoint x="2110" y="1510" />
        <di:waypoint x="2202" y="1510" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2128" y="1492" width="59" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1a6qotz_di" bpmnElement="Flow_1a6qotz">
        <di:waypoint x="1380" y="929" />
        <di:waypoint x="1380" y="970" />
        <di:waypoint x="2010" y="970" />
        <di:waypoint x="2010" y="929" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1688" y="952" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0957qcj_di" bpmnElement="Flow_0957qcj">
        <di:waypoint x="1390" y="1115" />
        <di:waypoint x="1390" y="1150" />
        <di:waypoint x="2023" y="1150" />
        <di:waypoint x="2023" y="1115" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1699" y="1132" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0idu84j_di" bpmnElement="Flow_0idu84j">
        <di:waypoint x="2125" y="904" />
        <di:waypoint x="2295" y="904" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2196" y="912" width="84" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1lcou11_di" bpmnElement="Flow_1lcou11">
        <di:waypoint x="2048" y="1090" />
        <di:waypoint x="2095" y="1090" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_006kfjt_di" bpmnElement="Flow_006kfjt">
        <di:waypoint x="2039" y="1380" />
        <di:waypoint x="2085" y="1380" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01izyjs_di" bpmnElement="Flow_01izyjs">
        <di:waypoint x="2135" y="1560" />
        <di:waypoint x="2210" y="1560" />
        <di:waypoint x="2210" y="1530" />
        <di:waypoint x="2285" y="1530" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2154" y="1603" width="84" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0r9g5xp_di" bpmnElement="Flow_0r9g5xp">
        <di:waypoint x="2069" y="1750" />
        <di:waypoint x="2115" y="1750" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1p18vc0_di" bpmnElement="Flow_1p18vc0">
        <di:waypoint x="2140" y="1725" />
        <di:waypoint x="2140" y="1700" />
        <di:waypoint x="2226" y="1700" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2156" y="1673" width="59" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15n0vt1_di" bpmnElement="Flow_15n0vt1">
        <di:waypoint x="2165" y="1750" />
        <di:waypoint x="2610" y="1750" />
        <di:waypoint x="2610" y="1320" />
        <di:waypoint x="2675" y="1320" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2398" y="1780" width="84" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0gkkj6e_di" bpmnElement="Flow_0gkkj6e">
        <di:waypoint x="1380" y="1405" />
        <di:waypoint x="1380" y="1450" />
        <di:waypoint x="2014" y="1450" />
        <di:waypoint x="2014" y="1405" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1690" y="1432" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1m9dyo9_di" bpmnElement="Flow_1m9dyo9">
        <di:waypoint x="2495" y="980" />
        <di:waypoint x="2960" y="980" />
        <di:waypoint x="2960" y="1235" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09hmbss_di" bpmnElement="Flow_09hmbss">
        <di:waypoint x="775" y="577" />
        <di:waypoint x="815" y="577" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02asalr_di" bpmnElement="Flow_02asalr">
        <di:waypoint x="280" y="602" />
        <di:waypoint x="280" y="680" />
        <di:waypoint x="750" y="680" />
        <di:waypoint x="750" y="602" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="496" y="660" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0f7x9vw_di" bpmnElement="Flow_0f7x9vw">
        <di:waypoint x="1059" y="581" />
        <di:waypoint x="1010" y="710" />
        <di:waypoint x="1010" y="1315" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0trpzid_di" bpmnElement="Flow_0trpzid">
        <di:waypoint x="840" y="755" />
        <di:waypoint x="840" y="1020" />
        <di:waypoint x="1075" y="1020" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_184oj2a_di" bpmnElement="Flow_184oj2a">
        <di:waypoint x="815" y="730" />
        <di:waypoint x="670" y="730" />
        <di:waypoint x="670" y="1460" />
        <di:waypoint x="950" y="1460" />
        <di:waypoint x="950" y="1545" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bvxnww_di" bpmnElement="Flow_1bvxnww">
        <di:waypoint x="2345" y="904" />
        <di:waypoint x="2450" y="904" />
        <di:waypoint x="2450" y="975" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ztczmr_di" bpmnElement="Flow_0ztczmr">
        <di:waypoint x="2345" y="1060" />
        <di:waypoint x="2450" y="1060" />
        <di:waypoint x="2450" y="985" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1uj4xo9_di" bpmnElement="Flow_1uj4xo9">
        <di:waypoint x="2335" y="1350" />
        <di:waypoint x="2460" y="1350" />
        <di:waypoint x="2460" y="995" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01n6ka4_di" bpmnElement="Flow_01n6ka4">
        <di:waypoint x="2335" y="1530" />
        <di:waypoint x="2470" y="1530" />
        <di:waypoint x="2470" y="1005" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0up1yt0_di" bpmnElement="Flow_0up1yt0">
        <di:waypoint x="1185" y="730" />
        <di:waypoint x="1225" y="730" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fdd57s_di" bpmnElement="Flow_1fdd57s">
        <di:waypoint x="2035" y="800" />
        <di:waypoint x="2165" y="800" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hmyu5h_di" bpmnElement="Flow_1hmyu5h">
        <di:waypoint x="1365" y="730" />
        <di:waypoint x="1388" y="730" />
        <di:waypoint x="1388" y="700" />
        <di:waypoint x="1420" y="700" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07wkqy2_di" bpmnElement="Flow_07wkqy2">
        <di:waypoint x="2700" y="1295" />
        <di:waypoint x="2700" y="1260" />
        <di:waypoint x="2935" y="1260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ez23b5_di" bpmnElement="Flow_0ez23b5">
        <di:waypoint x="2960" y="1285" />
        <di:waypoint x="2960" y="1422" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_176rzi4_di" bpmnElement="Flow_176rzi4">
        <di:waypoint x="1010" y="1365" />
        <di:waypoint x="1010" y="1560" />
        <di:waypoint x="1355" y="1560" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pyvbl6_di" bpmnElement="Flow_1pyvbl6">
        <di:waypoint x="1060" y="1185" />
        <di:waypoint x="1060" y="1380" />
        <di:waypoint x="1355" y="1380" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0nvklcm_di" bpmnElement="Flow_0nvklcm">
        <di:waypoint x="1100" y="1045" />
        <di:waypoint x="1100" y="1090" />
        <di:waypoint x="1365" y="1090" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02z7dzy_di" bpmnElement="Flow_02z7dzy">
        <di:waypoint x="1130" y="885" />
        <di:waypoint x="1130" y="904" />
        <di:waypoint x="1355" y="904" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xbxn59_di" bpmnElement="Flow_0xbxn59">
        <di:waypoint x="950" y="1595" />
        <di:waypoint x="950" y="1750" />
        <di:waypoint x="1279" y="1750" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vo5yy2_di" bpmnElement="Flow_0vo5yy2">
        <di:waypoint x="1765" y="800" />
        <di:waypoint x="1985" y="800" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1869" y="782" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0szkaoz_di" bpmnElement="Flow_0szkaoz">
        <di:waypoint x="1740" y="775" />
        <di:waypoint x="1740" y="725" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1747" y="747" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1iuttea_di" bpmnElement="Flow_1iuttea">
        <di:waypoint x="1660" y="700" />
        <di:waypoint x="1715" y="700" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ow6fjf_di" bpmnElement="Flow_1ow6fjf">
        <di:waypoint x="1765" y="700" />
        <di:waypoint x="1810" y="700" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_078nos9_di" bpmnElement="Flow_078nos9">
        <di:waypoint x="1420" y="232" />
        <di:waypoint x="1420" y="120" />
        <di:waypoint x="1460" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_145a5yp_di" bpmnElement="Flow_145a5yp">
        <di:waypoint x="1560" y="120" />
        <di:waypoint x="1582" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1nbmg7d_di" bpmnElement="Flow_1nbmg7d">
        <di:waypoint x="1940" y="590" />
        <di:waypoint x="1992" y="590" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0i01pi8_di" bpmnElement="Flow_0i01pi8">
        <di:waypoint x="1660" y="577" />
        <di:waypoint x="1712" y="577" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14ptsvi_di" bpmnElement="Flow_14ptsvi">
        <di:waypoint x="1640" y="642" />
        <di:waypoint x="1640" y="630" />
        <di:waypoint x="1610" y="630" />
        <di:waypoint x="1610" y="617" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10de4em_di" bpmnElement="Flow_10de4em">
        <di:waypoint x="2060" y="642" />
        <di:waypoint x="2060" y="620" />
        <di:waypoint x="1940" y="620" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0oeunqp_di" bpmnElement="Flow_0oeunqp">
        <di:waypoint x="1610" y="1290" />
        <di:waypoint x="1488" y="1290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rinq9y_di" bpmnElement="Flow_1rinq9y">
        <di:waypoint x="1818" y="1340" />
        <di:waypoint x="1858" y="1340" />
        <di:waypoint x="1858" y="1290" />
        <di:waypoint x="1710" y="1290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yzvbrw_di" bpmnElement="Flow_0yzvbrw">
        <di:waypoint x="1890" y="1032" />
        <di:waypoint x="1890" y="1020" />
        <di:waypoint x="1790" y="1020" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1oab8wd_di" bpmnElement="Flow_1oab8wd">
        <di:waypoint x="1690" y="1020" />
        <di:waypoint x="1669" y="1020" />
        <di:waypoint x="1669" y="1005" />
        <di:waypoint x="1608" y="1005" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1sbgpu2_di" bpmnElement="Flow_1sbgpu2">
        <di:waypoint x="1810" y="1380" />
        <di:waypoint x="1989" y="1380" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1m7pqfj_di" bpmnElement="Flow_1m7pqfj">
        <di:waypoint x="1880" y="904" />
        <di:waypoint x="1985" y="904" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1isfmpn_di" bpmnElement="Flow_1isfmpn">
        <di:waypoint x="1890" y="1560" />
        <di:waypoint x="1995" y="1560" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zl4e33_di" bpmnElement="Flow_0zl4e33">
        <di:waypoint x="1890" y="1750" />
        <di:waypoint x="2019" y="1750" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1we62a5_di" bpmnElement="Flow_1we62a5">
        <di:waypoint x="1890" y="1090" />
        <di:waypoint x="1998" y="1090" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>