<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" exporter="Camunda Modeler" exporterVersion="3.3.1" expressionLanguage="http://www.w3.org/1999/XPath" id="Definitions_0ghypro" targetNamespace="http://bpmn.io/schema/bpmn" typeLanguage="http://www.w3.org/2001/XMLSchema">

  <bpmn:process id="customScheduledActions_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" isClosed="false" isExecutable="true" name="Testing Recurrence bpmn2" processType="None">

    <bpmn:sequenceFlow id="Flow_07dv1jk_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" sourceRef="customStartEvent_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" targetRef="decisionElement_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21"/>

    <bpmn:businessRuleTask camunda:decisionRef="decisionElement_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" camunda:mapDecisionResult="singleResult" camunda:resultVariable="decision" completionQuantity="1" id="decisionElement_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" implementation="##unspecified" isForCompensation="false" name="DMN Rule Processor" startQuantity="1">

      <bpmn:extensionElements>

        <camunda:inputOutput>

          <camunda:inputParameter name="handlerDetails">{ "taskHandler": "was"}</camunda:inputParameter>

          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>

        </camunda:inputOutput>

      </bpmn:extensionElements>

      <bpmn:incoming>Flow_07dv1jk_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:incoming>

      <bpmn:outgoing>SequenceFlow_19rqyz0_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:outgoing>

    </bpmn:businessRuleTask>

    <bpmn:sequenceFlow id="SequenceFlow_19rqyz0_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" sourceRef="decisionElement_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" targetRef="recurrenceStartDate_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21"/>

    <bpmn:sequenceFlow id="SequenceFlow_0htwpqa_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" sourceRef="recurrenceStartDate_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" targetRef="recurrenceSchedule_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21"/>

    <bpmn:intermediateCatchEvent id="recurrenceStartDate_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" name="Start Date" parallelMultiple="false">

      <bpmn:incoming>SequenceFlow_19rqyz0_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:incoming>

      <bpmn:outgoing>SequenceFlow_0htwpqa_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:outgoing>

      <bpmn:timerEventDefinition>

        <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${startDate}</bpmn:timeDate>

      </bpmn:timerEventDefinition>

    </bpmn:intermediateCatchEvent>

    <bpmn:startEvent id="customStartEvent_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" isInterrupting="true" name="start process" parallelMultiple="false">

      <bpmn:extensionElements>

        <camunda:properties>

          <camunda:property name="currentStepDetails" value="{ &quot;required&quot;: true }"/>

          <camunda:property name="taskDetails" value="{ &quot;required&quot;: true }"/>

          <camunda:property name="handlerDetails" value="{  &quot;taskHandler&quot;: &quot;was&quot;}"/>

          <camunda:property name="startableEvents" value="[&quot;created&quot;]"/>

          <camunda:property name="processVariablesDetails" value="[{&quot;variableName&quot;:&quot;CompanyName&quot;,&quot;variableType&quot;:&quot;string&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;entityChangeType&quot;,&quot;variableType&quot;:&quot;String&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;Customer&quot;,&quot;variableType&quot;:&quot;string&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;CustomerEmail&quot;,&quot;variableType&quot;:&quot;string&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;CompanyEmail&quot;,&quot;variableType&quot;:&quot;string&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;Id&quot;,&quot;variableType&quot;:&quot;String&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;CustomerName&quot;,&quot;variableType&quot;:&quot;string&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;StatementDate&quot;,&quot;variableType&quot;:&quot;string&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;intuit_userid&quot;,&quot;variableType&quot;:&quot;String&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;intuit_realmid&quot;,&quot;variableType&quot;:&quot;String&quot;,&quot;overrideIfAbsent&quot;:true}]"/>

          <camunda:property name="stepDetails" value="{&quot;customStartEvent_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21&quot;:[&quot;recurrenceStartDate_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21&quot;,&quot;customStartEvent_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21&quot;,&quot;recurrenceSchedule_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21&quot;,&quot;scheduledActions_subProcess_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21&quot;,&quot;decisionElement_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21&quot;,&quot;sendStatement_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21&quot;,&quot;subProcess_startEvent_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21&quot;]}"/>

          <camunda:property name="recurrenceDetails" value="{&quot;recurrenceStartDate&quot;:&quot;recurrenceStartDate_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21&quot;,&quot;recurrenceSchedule&quot;:&quot;recurrenceSchedule_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21&quot;}"/>

          <camunda:property name="recurrenceRule" value="{&quot;recurType&quot;:&quot;MONTHLY&quot;,&quot;interval&quot;:1,&quot;daysOfMonth&quot;:[1,25],&quot;startDate&quot;:{&quot;centuryOfEra&quot;:20,&quot;yearOfEra&quot;:2021,&quot;yearOfCentury&quot;:21,&quot;weekyear&quot;:2021,&quot;monthOfYear&quot;:8,&quot;weekOfWeekyear&quot;:30,&quot;hourOfDay&quot;:0,&quot;minuteOfHour&quot;:0,&quot;secondOfMinute&quot;:0,&quot;millisOfSecond&quot;:0,&quot;millisOfDay&quot;:0,&quot;secondOfDay&quot;:0,&quot;minuteOfDay&quot;:0,&quot;year&quot;:2021,&quot;dayOfMonth&quot;:1,&quot;dayOfWeek&quot;:7,&quot;era&quot;:1,&quot;dayOfYear&quot;:213,&quot;chronology&quot;:{&quot;zone&quot;:{&quot;uncachedZone&quot;:{&quot;cachable&quot;:true,&quot;fixed&quot;:false,&quot;id&quot;:&quot;Asia/Kolkata&quot;},&quot;fixed&quot;:false,&quot;id&quot;:&quot;Asia/Kolkata&quot;}},&quot;zone&quot;:{&quot;uncachedZone&quot;:{&quot;cachable&quot;:true,&quot;fixed&quot;:false,&quot;id&quot;:&quot;Asia/Kolkata&quot;},&quot;fixed&quot;:false,&quot;id&quot;:&quot;Asia/Kolkata&quot;},&quot;millis&quot;:1627756200000,&quot;afterNow&quot;:false,&quot;beforeNow&quot;:true,&quot;equalNow&quot;:false},&quot;active&quot;:true,&quot;$sdk_validated&quot;:true}"/>

        </camunda:properties>

      </bpmn:extensionElements>

      <bpmn:outgoing>Flow_07dv1jk_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:outgoing>

    </bpmn:startEvent>

    <bpmn:intermediateCatchEvent id="recurrenceSchedule_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" name="Recur Event" parallelMultiple="false">

      <bpmn:incoming>SequenceFlow_0htwpqa_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:incoming>

      <bpmn:incoming>Flow_0ar3k7h_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:incoming>

      <bpmn:outgoing>SequenceFlow_17vcf21_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:outgoing>

      <bpmn:timerEventDefinition id="TimerEventDefinition_1hyy4xv">

        <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">${cronExpression}</bpmn:timeCycle>

      </bpmn:timerEventDefinition>

    </bpmn:intermediateCatchEvent>

    <bpmn:subProcess completionQuantity="1" id="SubProcess_0ynplnm_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" isForCompensation="false" name="Close Process" startQuantity="1" triggeredByEvent="true">

      <bpmn:sequenceFlow id="SequenceFlow_18kou84_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" sourceRef="StartEvent_0c9w8if_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" targetRef="EndEvent_0ayhj3y_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21"/>

      <bpmn:endEvent id="EndEvent_0ayhj3y_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" name="End Process Event">

        <bpmn:extensionElements>

          <camunda:inputOutput>

            <camunda:inputParameter name="handlerDetails">{
              "taskHandler": "was",
              "handlerId": "",
              "actionName": "updateProcessStatus"
              }</camunda:inputParameter>

          </camunda:inputOutput>

        </bpmn:extensionElements>

        <bpmn:incoming>SequenceFlow_18kou84_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:incoming>

        <bpmn:messageEventDefinition camunda:topic="scheduledActions" camunda:type="external" messageRef="Message_0n66iem"/>

      </bpmn:endEvent>

      <bpmn:startEvent id="StartEvent_0c9w8if_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" isInterrupting="true" name="End Process" parallelMultiple="false">

        <bpmn:extensionElements>

          <camunda:properties>

            <camunda:property name="handlerDetails" value="{&quot;taskHandler&quot;: &quot;was&quot; }"/>

            <camunda:property name="targetApi" value="trigger"/>

          </camunda:properties>

        </bpmn:extensionElements>

        <bpmn:outgoing>SequenceFlow_18kou84_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:outgoing>

        <bpmn:messageEventDefinition id="MessageEventDefinition_1nlqu1b" messageRef="Message_0tschbo"/>

      </bpmn:startEvent>

    </bpmn:subProcess>

    <bpmn:subProcess completionQuantity="1" id="scheduledActions_subProcess_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" isForCompensation="false" name="scheduledActions subprocess" startQuantity="1" triggeredByEvent="false">

      <bpmn:incoming>SequenceFlow_17vcf21_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:incoming>

      <bpmn:outgoing>Flow_0ar3k7h_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:outgoing>

      <bpmn:outgoing>SequenceFlow_0z5zmbj_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:outgoing>

      <bpmn:sequenceFlow id="SequenceFlow_18ykyxq_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" sourceRef="sendStatement_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" targetRef="subProcess_endEvent_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21"/>

      <bpmn:sequenceFlow id="SequenceFlow_0gtcv0e_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" sourceRef="subProcess_startEvent_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" targetRef="sendStatement_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21">
        <conditionExpression id="conditionExpression_6044821a-b5c2-42d8-acea-0c738a45de18" xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL">${true}</conditionExpression>
      </bpmn:sequenceFlow>

      <bpmn:sendTask camunda:topic="scheduledActions" camunda:type="external" completionQuantity="1" id="sendStatement_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" implementation="##WebService" isForCompensation="false" name="Schedule Action" startQuantity="1">

        <bpmn:extensionElements>

          <camunda:inputOutput>

            <camunda:inputParameter name="parameterDetails">{"CC":{"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"BCC":{"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"Message":{"fieldValue":["Dear [[CustomerName]],\n\nHere's a friendly reminder to take a look at your statement dated [[StatementDate]].\n\nRegards,\n[[CompanyName]]"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"endDate":{"configurable":true,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string"},"SendTo":{"fieldValue":["[[CustomerEmail]]"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string","handlerFieldName":"To"},"CompanyEmail":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CompanyEmail"},"StatementDate":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"StatementDate"},"Subject":{"fieldValue":["Statement from [[CompanyName]]"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"CompanyName":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CompanyName"},"CustomerEmail":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CustomerEmail"},"statementType":{"configurable":true,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string"},"customerIds":{"configurable":true,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string"},"CustomerName":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CustomerName"},"startDate":{"configurable":true,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string"}}</camunda:inputParameter>

            <camunda:inputParameter name="handlerDetails">{"taskHandler":"appconnect","actionName":"executeWorkflowAction","handlerId":"intuit-workflows/was-send-statement","recordType":null,"responseFields":null,"handlerScope":null}</camunda:inputParameter>

            <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>

          </camunda:inputOutput>

        </bpmn:extensionElements>

        <bpmn:incoming>SequenceFlow_0gtcv0e_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:incoming>

        <bpmn:outgoing>SequenceFlow_18ykyxq_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:outgoing>

      </bpmn:sendTask>

      <bpmn:endEvent id="subProcess_endEvent_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" name="end">

        <bpmn:incoming>SequenceFlow_18ykyxq_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:incoming>

      </bpmn:endEvent>

      <bpmn:startEvent id="subProcess_startEvent_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" isInterrupting="true" name="start" parallelMultiple="false">

        <bpmn:outgoing>SequenceFlow_0gtcv0e_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:outgoing>

      </bpmn:startEvent>

    </bpmn:subProcess>

    <bpmn:sequenceFlow id="Flow_0ar3k7h_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" sourceRef="scheduledActions_subProcess_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" targetRef="recurrenceSchedule_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21"/>

    <bpmn:sequenceFlow id="SequenceFlow_17vcf21_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" sourceRef="recurrenceSchedule_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" targetRef="scheduledActions_subProcess_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21"/>

    <bpmn:endEvent id="EndEvent_14edhmm_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" name="End Process">

      <bpmn:incoming>SequenceFlow_0z5zmbj_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:incoming>

    </bpmn:endEvent>

    <bpmn:sequenceFlow id="SequenceFlow_0z5zmbj_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" sourceRef="scheduledActions_subProcess_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" targetRef="EndEvent_14edhmm_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21"/>

  </bpmn:process>

  <bpmn:message id="Message_10uji9x" name="approved_rejected"/>

  <bpmn:message id="Message_1lsccjr" name="customWait"/>

  <bpmn:message id="Message_1vqffdi" name="end_process"/>

  <bpmn:escalation escalationCode="closetask" id="Escalation_1f2mfy4" name="close_task"/>

  <bpmn:message id="Message_0n66iem" name="process_ended_message"/>

  <bpmn:escalation escalationCode="endprocess" id="Escalation_0aq0q52" name="end_process"/>

  <bpmn:message id="Message_0tschbo" name="deleted_voided_disable"/>

  <bpmndi:BPMNDiagram id="BPMNDiagram_1">

    <bpmndi:BPMNPlane bpmnElement="customScheduledActions_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" id="BPMNPlane_1">

      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_0htwpqa_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" id="SequenceFlow_0htwpqa_di">

        <di:waypoint x="498" y="180"/>

        <di:waypoint x="572" y="180"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_19rqyz0_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" id="SequenceFlow_19rqyz0_di">

        <di:waypoint x="400" y="180"/>

        <di:waypoint x="462" y="180"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_07dv1jk_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" id="Flow_07dv1jk_di">

        <di:waypoint x="208" y="180"/>

        <di:waypoint x="300" y="180"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNShape bpmnElement="decisionElement_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" id="Activity_0io4hr8_di">

        <dc:Bounds height="80" width="100" x="300" y="140"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_18kou84_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" id="SequenceFlow_18kou84_di">

        <di:waypoint x="256" y="390"/>

        <di:waypoint x="402" y="390"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNShape bpmnElement="EndEvent_0ayhj3y_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" id="EndEvent_19xjaph_di">

        <dc:Bounds height="36" width="36" x="402" y="372"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="27" width="63" x="389" y="415"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_0gtcv0e_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" id="SequenceFlow_0gtcv0e_di">

        <di:waypoint x="748" y="180"/>

        <di:waypoint x="790" y="180"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_18ykyxq_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" id="SequenceFlow_18ykyxq_di">

        <di:waypoint x="890" y="180"/>

        <di:waypoint x="942" y="180"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNShape bpmnElement="subProcess_startEvent_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" id="StartEvent_0k8yemu_di">

        <dc:Bounds height="36" width="36" x="712" y="162"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="22" x="720" y="205"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="subProcess_endEvent_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" id="EndEvent_05a3wl3_di">

        <dc:Bounds height="36" width="36" x="942" y="162"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="19" x="950" y="205"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="sendStatement_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" id="SendTask_1mez1bd_di">

        <dc:Bounds height="80" width="100" x="790" y="140"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="recurrenceStartDate_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" id="IntermediateCatchEvent_1yp99e8_di">

        <dc:Bounds height="36" width="36" x="462" y="162"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="50" x="455" y="132"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="customStartEvent_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" id="StartEvent_08rwoar_di">

        <dc:Bounds height="36" width="36" x="172" y="162"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="63" x="158" y="138"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="recurrenceSchedule_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" id="Event_10itik3_di">

        <dc:Bounds height="36" width="36" x="572" y="162"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="61" x="559" y="138"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="SubProcess_0ynplnm_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" id="SubProcess_02sjcl2_di" isExpanded="true">

        <dc:Bounds height="130" width="258" x="200" y="330"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="scheduledActions_subProcess_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" id="SubProcess_1q4vnui_di" isExpanded="true">

        <dc:Bounds height="180" width="310" x="690" y="80"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNEdge bpmnElement="Flow_0ar3k7h_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" id="Flow_0ar3k7h_di">

        <di:waypoint x="845" y="260"/>

        <di:waypoint x="845" y="300"/>

        <di:waypoint x="590" y="300"/>

        <di:waypoint x="590" y="198"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_17vcf21_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" id="SequenceFlow_17vcf21_di">

        <di:waypoint x="608" y="180"/>

        <di:waypoint x="690" y="180"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNShape bpmnElement="StartEvent_0c9w8if_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" id="StartEvent_0shfj58_di">

        <dc:Bounds height="36" width="36" x="220" y="372"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="63" x="207" y="415"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="EndEvent_14edhmm_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" id="EndEvent_14edhmm_di">

        <dc:Bounds height="36" width="36" x="1082" y="152"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="63" x="1069" y="195"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_0z5zmbj_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" id="SequenceFlow_0z5zmbj_di">

        <di:waypoint x="1000" y="170"/>

        <di:waypoint x="1082" y="170"/>

      </bpmndi:BPMNEdge>

    </bpmndi:BPMNPlane>

  </bpmndi:BPMNDiagram>

</bpmn:definitions>