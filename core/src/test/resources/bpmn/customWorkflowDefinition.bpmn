<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_1ueeqzk" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.9.0">
  <bpmn:process id="customReminder_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" name="Payment Due Sandeep" processType="None" isClosed="false" isExecutable="true" camunda:historyTimeToLive="14">
    <bpmn:extensionElements>
      <camunda:properties>
        <camunda:property name="workflowName" value="customReminder" />
      </camunda:properties>
    </bpmn:extensionElements>
    <bpmn:startEvent id="customStartEvent_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" name="start process">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="stepDetails" value="{&#34;customStartEvent_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324&#34;:[&#34;decisionElement_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324&#34;,&#34;sendCompanyEmail_c_a5121eb9-a104-4d7c-b03d-2ff2fb099324&#34;,&#34;customStartEvent_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324&#34;,&#34;sendExternalEmail_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324&#34;,&#34;createTask_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324&#34;,&#34;inclusiveGateway&#34;,&#34;sendPushNotification_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324&#34;],&#34;customWorkflowWaitEvent_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324&#34;:[&#34;customWorkflowWaitEvent_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324&#34;]}" />
          <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
          <camunda:property name="currentStepDetails" value="{ &#34;required&#34;: true }" />
          <camunda:property name="processVariablesDetails" value="[{&#34;variableName&#34;:&#34;entityChangeType&#34;,&#34;variableType&#34;:&#34;String&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;TxnPaymentStatus&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;TxnDate&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;CompanyEmail&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;TxnAmount&#34;,&#34;variableType&#34;:&#34;double&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;intuit_userid&#34;,&#34;variableType&#34;:&#34;String&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;TxnDueDays&#34;,&#34;variableType&#34;:&#34;integer&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;TxnBalanceAmount&#34;,&#34;variableType&#34;:&#34;double&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;CompanyName&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;TxnDueDate&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;CustomerEmail&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;DocNumber&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;Id&#34;,&#34;variableType&#34;:&#34;String&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;CustomerName&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;intuit_realmid&#34;,&#34;variableType&#34;:a&#34;String&#34;,&#34;overrideIfAbsent&#34;:true}]" />
          <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;: &#34;appconnect&#34;, &#34;handlerId&#34;: &#34;intuit-workflows/custom-reminder-start&#34;, &#34;actionName&#34;: &#34;executeWorkflowAction&#34;}" />
          <camunda:property name="parameterDetails" value="{&#34;FilterCloseTaskConditions&#34;:{&#34;fieldValue&#34;:[&#34;txn_paid&#34;],&#34;configurable&#34;:false,&#34;requiredByHandler&#34;:true,&#34;requiredByUI&#34;:false,&#34;multiSelect&#34;:false},&#34;FilterCondition&#34;:{&#34;fieldValue&#34;:[&#34;{\&#34;rules\&#34;:[{\&#34;parameterName\&#34;:\&#34;TxnAmount\&#34;,\&#34;conditionalExpression\&#34;:\&#34;GTE 0\&#34;},{\&#34;parameterName\&#34;:\&#34;TxnPaymentStatus\&#34;,\&#34;conditionalExpression\&#34;:\&#34;CONTAINS Unpaid\&#34;},{\&#34;parameterName\&#34;:\&#34;TxnDueDays\&#34;,\&#34;conditionalExpression\&#34;:\&#34;BF 1\&#34;}]}&#34;],&#34;configurable&#34;:false,&#34;requiredByHandler&#34;:true,&#34;requiredByUI&#34;:false,&#34;multiSelect&#34;:false},&#34;FilterRecordType&#34;:{&#34;fieldValue&#34;:[&#34;invoice&#34;],&#34;configurable&#34;:false,&#34;requiredByHandler&#34;:false,&#34;requiredByUI&#34;:false,&#34;multiSelect&#34;:false}}" />
          <camunda:property name="startableEvents" value="[&#34;newCustomStart&#34;]" />
          <camunda:property name="targetApi" value="evaluate-and-trigger" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_0uuma6l_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0uuma6l_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" sourceRef="customStartEvent_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" targetRef="decisionElement_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" />
    <bpmn:businessRuleTask id="decisionElement_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" name="DMN Rule Processor" implementation="##unspecified" camunda:resultVariable="decision" camunda:decisionRef="decisionElement_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" camunda:mapDecisionResult="singleResult">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{ "taskHandler": "was"}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0uuma6l_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:incoming>
      <bpmn:outgoing>Flow_0e5omq2_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:outgoing>
      <bpmn:outgoing>Sequence_decision_result_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:outgoing>
    </bpmn:businessRuleTask>
    <bpmn:sequenceFlow id="Flow_1jh72q4_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" sourceRef="Gateway_1rgsx47_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" targetRef="Event_1fgu7ic_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" />
    <bpmn:sendTask id="sendExternalEmail_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" name="Send external email" implementation="##WebService" camunda:type="external" camunda:topic="reminders">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="parameterDetails">{
  "CC": {
    "configurable": true,
    "requiredByHandler": true,
    "requiredByUI": true,
    "multiSelect": false,
    "fieldType": "string"
  },
  "BCC": {
    "configurable": true,
    "requiredByHandler": true,
    "requiredByUI": true,
    "multiSelect": false,
    "fieldType": "string"
  },
  "Message": {
    "fieldValue": [
      "Hi, \nInvoice [[DocNumber]] with Amount  [[TxnAmount]] needs your attention. Please take a look at the attached invoice and contact me if you have any questions. \n\nThanks,\n[[CompanyName]]"
    ],
    "configurable": true,
    "requiredByHandler": true,
    "requiredByUI": true,
    "multiSelect": false,
    "fieldType": "string"
  },
  "TxnDate": {
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE",
    "handlerFieldName": "TxnDate"
  },
  "CompanyEmail": {
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE",
    "handlerFieldName": "CompanyEmail"
  },
  "Send To": {
    "fieldValue": [
      "[[CustomerEmail]]"
    ],
    "configurable": true,
    "requiredByHandler": true,
    "requiredByUI": true,
    "multiSelect": false,
    "fieldType": "string",
    "handlerFieldName": "To"
  },
  "TxnAmount": {
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "double",
    "valueType": "PROCESS_VARIABLE",
    "handlerFieldName": "TxnAmount"
  },
  "Subject": {
    "fieldValue": [
    ],
    "configurable": true,
    "requiredByHandler": true,
    "requiredByUI": true,
    "multiSelect": false,
    "fieldType": "string"
  },
  "TxnBalanceAmount": {
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "double",
    "valueType": "PROCESS_VARIABLE",
    "handlerFieldName": "TxnBalanceAmount"
  },
  "CompanyName": {
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE",
    "handlerFieldName": "CompanyName"
  },
  "IsEmail": {
    "fieldValue": [
      "true"
    ],
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string"
  },
  "TxnDueDate": {
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE",
    "handlerFieldName": "TxnDueDate"
  },
  "CustomerEmail": {
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE",
    "handlerFieldName": "CustomerEmail"
  },
  "DocNumber": {
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE",
    "handlerFieldName": "DocNumber"
  },
  "CustomerName": {
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE",
    "handlerFieldName": "CustomerName"
  }
}</camunda:inputParameter>
          <camunda:inputParameter name="handlerDetails">{"taskHandler":"appconnect","actionName":"executeWorkflowAction","handlerId":"intuit-workflows/was-send-notification","recordType":null,"responseFields":null,"handlerScope":null}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_sendExternalEmail_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1xjrp7d_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:serviceTask id="createTask_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" name="Create task" implementation="##WebService" camunda:type="external" camunda:topic="reminders">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="parameterDetails">{"Assignee":{"fieldValue":["9130350413749026"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"TxnDate":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnDate"},"CompanyEmail":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CompanyEmail"},"TxnAmount":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"double","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnAmount"},"TxnBalanceAmount":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"double","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnBalanceAmount"},"CompanyName":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CompanyName"},"CloseTask":{"fieldValue":["txn_paid"],"possibleFieldValues":["txn_paid","txn_sent","close_manually"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"TxnDueDate":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnDueDate"},"TaskName":{"fieldValue":["Review Invoice [[DocNumber]]"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"CustomerEmail":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CustomerEmail"},"DocNumber":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"DocNumber"},"ProjectType":{"fieldValue":["QB_INVOICE_DUE_REMINDER"],"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string"},"TaskType":{"fieldValue":["QB_INVOICE"],"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string"},"CustomerName":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CustomerName"},"Id":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnId"},"intuit_realmid":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"RealmId"}}</camunda:inputParameter>
          <camunda:inputParameter name="handlerDetails">{"taskHandler":"appconnect","actionName":"executeWorkflowAction","handlerId":"intuit-workflows/taskmanager-create-task","recordType":null,"responseFields":["projectId"],"handlerScope":null}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_createTask_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_137bn44_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:receiveTask id="customWorkflowWaitEvent_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" name="Wait for state change" implementation="##WebService" messageRef="Message_064b9px">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{"taskHandler": "appconnect", "handlerId": "intuit-workflows/custom-reminder-wait", "actionName": "executeWorkflowAction" ,"recordType":"invoice"}</camunda:inputParameter>
          <camunda:inputParameter name="currentStepDetails">{ "required": false }</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{"FilterCloseTaskConditions":{"fieldValue":["txn_paid"],"configurable":false,"requiredByHandler":false,"requiredByUI":false,"multiSelect":false},"FilterCondition":{"fieldValue":["{\"rules\":[{\"parameterName\":\"TxnAmount\",\"conditionalExpression\":\"GTE 0\"},{\"parameterName\":\"TxnPaymentStatus\",\"conditionalExpression\":\"CONTAINS Unpaid\"},{\"parameterName\":\"TxnDueDays\",\"conditionalExpression\":\"BF 1\"}]}"],"configurable":false,"requiredByHandler":false,"requiredByUI":false,"multiSelect":false},"FilterRecordType":{"fieldValue":["invoice"],"configurable":false,"requiredByHandler":false,"requiredByUI":false,"multiSelect":false}}</camunda:inputParameter>
          <camunda:inputParameter name="targetApi">trigger</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1necc4x_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:incoming>
      <bpmn:outgoing>Flow_1xevg2t_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:endEvent id="Event_1fgu7ic_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" name="End the process if create task is false">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
            "taskHandler": "was",
            "handlerId": "",
            "actionName": "updateProcessStatus"
            }</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1jh72q4_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1qq1zcv" />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0e5omq2_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" name="Conditions unmatched" sourceRef="decisionElement_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" targetRef="Event_1dx97ea_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decision.sendReminder == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_1dx97ea_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" name="DMN condition not satisfied: End process">
      <bpmn:incoming>Flow_0e5omq2_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0swqckt" />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1xevg2t_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" sourceRef="customWorkflowWaitEvent_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" targetRef="Event_1hhbrmg_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" />
    <bpmn:endEvent id="Event_1hhbrmg_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" name="State change occured">
      <bpmn:incoming>Flow_1xevg2t_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:incoming>
      <bpmn:escalationEventDefinition id="EscalationEventDefinition_0ay2jrq" escalationRef="Escalation_0tyjh9j" />
    </bpmn:endEvent>
    <bpmn:subProcess id="Activity_108jjaa_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" triggeredByEvent="true">
      <bpmn:serviceTask id="closeTask_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" name="Close Project service task" implementation="##WebService" camunda:type="external" camunda:topic="reminders">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
              "taskHandler": "appconnect",
              "handlerId": "intuit-workflows/taskmanager-update-task",
              "actionName": "executeWorkflowAction"
              }</camunda:inputParameter>
            <camunda:inputParameter name="parameterDetails">{
  "projectId": {
    "fieldValue": [],
    "handlerFieldName": "Project",
    "requiredByHandler": true,
    "requiredByUI": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE"
  },
  "Status": {
    "fieldValue": [
      "Complete"
    ],
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string"
  }
}</camunda:inputParameter>
            <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_16ihqz2_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:incoming>
        <bpmn:outgoing>Flow_0pxn6yi_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:startEvent id="Event_0vepyso_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" name="Close task">
        <bpmn:outgoing>Flow_16ihqz2_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:outgoing>
        <bpmn:escalationEventDefinition id="EscalationEventDefinition_0ghanme" escalationRef="Escalation_0tyjh9j" />
      </bpmn:startEvent>
      <bpmn:endEvent id="Event_0ey8tt4_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" name="End process">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
              "taskHandler": "was",
              "handlerId": "",
              "actionName": "updateProcessStatus"
              }</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0pxn6yi_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:incoming>
        <bpmn:messageEventDefinition id="MessageEventDefinition_07qnodz" messageRef="Message_0p07vw4" camunda:type="external" camunda:topic="reminders" />
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="Flow_0pxn6yi_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" sourceRef="closeTask_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" targetRef="Event_0ey8tt4_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" />
      <bpmn:sequenceFlow id="Flow_16ihqz2_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" sourceRef="Event_0vepyso_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" targetRef="closeTask_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" />
    </bpmn:subProcess>
    <bpmn:boundaryEvent id="Event_1tx361d_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" name="Expiry Event" attachedToRef="customWorkflowWaitEvent_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
      <bpmn:outgoing>Flow_1fep73j_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_1dgqopk">
        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">P15D</bpmn:timeDuration>
      </bpmn:timerEventDefinition>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_1fep73j_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" sourceRef="Event_1tx361d_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" targetRef="Event_0kb35fk_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" />
    <bpmn:endEvent id="Event_0kb35fk_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" name="End process after the process expiration.">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
            "taskHandler": "was",
            "handlerId": "",
            "actionName": "updateProcessStatus"
            }</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1fep73j_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0vwg5fd" camunda:type="external" camunda:topic="reminders" />
    </bpmn:endEvent>
    <bpmn:inclusiveGateway id="Gateway_1rgsx47_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" name="create task?" default="Flow_1jh72q4_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
      <bpmn:incoming>SequenceFlow_137bn44_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_1xjrp7d_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_02eopl1_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:incoming>
      <bpmn:outgoing>Flow_1jh72q4_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:outgoing>
      <bpmn:outgoing>Flow_1necc4x_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:outgoing>
    </bpmn:inclusiveGateway>
    <bpmn:sendTask id="sendPushNotification_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" name="Send push notification" implementation="##WebService" camunda:type="external" camunda:topic="reminders">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="parameterDetails">{
  "TxnBalanceAmount": {
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "double",
    "valueType": "PROCESS_VARIABLE",
    "handlerFieldName": "TxnBalanceAmount"
  },
  "CompanyName": {
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE",
    "handlerFieldName": "CompanyName"
  },
  "TxnDueDate": {
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE",
    "handlerFieldName": "TxnDueDate"
  },
  "Message": {
    "fieldValue": [

    ],
    "configurable": true,
    "requiredByHandler": true,
    "requiredByUI": true,
    "multiSelect": false,
    "fieldType": "string"
  },
  "CustomerEmail": {
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE",
    "handlerFieldName": "CustomerEmail"
  },
  "DocNumber": {
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE",
    "handlerFieldName": "DocNumber"
  },
  "TxnDate": {
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE",
    "handlerFieldName": "TxnDate"
  },
  "CompanyEmail": {
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE",
    "handlerFieldName": "CompanyEmail"
  },
  "SendTo": {
    "configurable": true,
    "requiredByHandler": true,
    "requiredByUI": true,
    "multiSelect": false,
    "fieldType": "string",
    "handlerFieldName": "To",
"fieldValue": [
            "[[CustomerEmail]]"
    ]
  },
  "CustomerName": {
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE",
    "handlerFieldName": "CustomerName"
  },
  "TxnAmount": {
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "double",
    "valueType": "PROCESS_VARIABLE",
    "handlerFieldName": "TxnAmount"
  },
  "Subject": {
    "fieldValue": [
      "A test invoice [[DocNumber]] needs your attention"
    ],
    "configurable": true,
    "requiredByHandler": true,
    "requiredByUI": true,
    "multiSelect": false,
    "fieldType": "string"
  }
}</camunda:inputParameter>
          <camunda:inputParameter name="handlerDetails">{"taskHandler":"appconnect","actionName":"executeWorkflowAction","handlerId":"intuit-workflows/was-send-notification","recordType":null,"responseFields":null,"handlerScope":null}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_sendPushNotification_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_02eopl1_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:sequenceFlow id="Sequence_decision_result_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" name="Rule Evaluation Passed" sourceRef="decisionElement_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" targetRef="inclusiveGateway">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decision.decisionResult == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1necc4x_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" sourceRef="Gateway_1rgsx47_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" targetRef="customWorkflowWaitEvent_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable('projectId') != null}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:inclusiveGateway id="inclusiveGateway" name="Gateway">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Sequence_decision_result_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_createTask_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_sendPushNotification_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_sendExternalEmail_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324</bpmn:outgoing>
    </bpmn:inclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_createTask_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" name="Create Task" sourceRef="inclusiveGateway" targetRef="createTask_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_137bn44_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" sourceRef="createTask_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" targetRef="Gateway_1rgsx47_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" />
    <bpmn:sequenceFlow id="SequenceFlow_1xjrp7d_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" sourceRef="sendExternalEmail_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" targetRef="Gateway_1rgsx47_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" />
    <bpmn:sequenceFlow id="SequenceFlow_02eopl1_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" sourceRef="sendPushNotification_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" targetRef="Gateway_1rgsx47_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" />
    <bpmn:sequenceFlow id="SequenceFlow_sendPushNotification_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" name="Send Push Notification" sourceRef="inclusiveGateway" targetRef="sendPushNotification_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_sendExternalEmail_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" name="Send External Email" sourceRef="inclusiveGateway" targetRef="sendExternalEmail_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmn:escalation id="Escalation_0tyjh9j" name="close_task" escalationCode="closetask" />
  <bpmn:message id="Message_0p07vw4" name="process_ended_message" />
  <bpmn:message id="Message_064b9px" name="customWait" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="customReminder_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
      <bpmndi:BPMNEdge id="SequenceFlow_0n3artf_di" bpmnElement="SequenceFlow_sendExternalEmail_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <di:waypoint x="575" y="405" />
        <di:waypoint x="575" y="280" />
        <di:waypoint x="720" y="280" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="735" y="336" width="70" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1nuvc6b_di" bpmnElement="SequenceFlow_sendPushNotification_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <di:waypoint x="575" y="455" />
        <di:waypoint x="575" y="560" />
        <di:waypoint x="720" y="560" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="732" y="606" width="55" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_02eopl1_di" bpmnElement="SequenceFlow_02eopl1_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <di:waypoint x="820" y="550" />
        <di:waypoint x="980" y="550" />
        <di:waypoint x="980" y="445" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1xjrp7d_di" bpmnElement="SequenceFlow_1xjrp7d_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <di:waypoint x="820" y="280" />
        <di:waypoint x="980" y="280" />
        <di:waypoint x="980" y="395" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_137bn44_di" bpmnElement="SequenceFlow_137bn44_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <di:waypoint x="820" y="160" />
        <di:waypoint x="980" y="160" />
        <di:waypoint x="980" y="395" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0gdfajo_di" bpmnElement="SequenceFlow_createTask_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <di:waypoint x="575" y="405" />
        <di:waypoint x="575" y="160" />
        <di:waypoint x="720" y="160" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="740" y="213" width="59" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1necc4x_di" bpmnElement="Flow_1necc4x_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <di:waypoint x="1005" y="420" />
        <di:waypoint x="1080" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0o8v5j8_di" bpmnElement="Sequence_decision_result_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <di:waypoint x="400" y="430" />
        <di:waypoint x="550" y="430" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="428" y="448" width="78" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fep73j_di" bpmnElement="Flow_1fep73j_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <di:waypoint x="1130" y="478" />
        <di:waypoint x="1130" y="552" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xevg2t_di" bpmnElement="Flow_1xevg2t_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <di:waypoint x="1180" y="420" />
        <di:waypoint x="1262" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0e5omq2_di" bpmnElement="Flow_0e5omq2_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <di:waypoint x="350" y="390" />
        <di:waypoint x="350" y="290" />
        <di:waypoint x="402" y="290" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="282" y="326" width="55" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jh72q4_di" bpmnElement="Flow_1jh72q4_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <di:waypoint x="1005" y="420" />
        <di:waypoint x="1020" y="420" />
        <di:waypoint x="1020" y="280" />
        <di:waypoint x="1142" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0uuma6l_di" bpmnElement="Flow_0uuma6l_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <di:waypoint x="208" y="430" />
        <di:waypoint x="300" y="430" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="customStartEvent_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <dc:Bounds x="172" y="412" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="159" y="455" width="63" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_109vnno_di" bpmnElement="decisionElement_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <dc:Bounds x="300" y="390" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0a7dlcb_di" bpmnElement="sendExternalEmail_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <dc:Bounds x="720" y="240" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1sjt7qd_di" bpmnElement="createTask_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <dc:Bounds x="720" y="120" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1guy81g_di" bpmnElement="customWorkflowWaitEvent_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <dc:Bounds x="1080" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0xbnmwr_di" bpmnElement="Event_1fgu7ic_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <dc:Bounds x="1142" y="262" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1115" y="219" width="89" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1f1cf2c_di" bpmnElement="Event_1dx97ea_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <dc:Bounds x="402" y="272" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="377" y="229" width="85" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_00zaz0i_di" bpmnElement="Event_1hhbrmg_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <dc:Bounds x="1262" y="402" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1248" y="445" width="65" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_108jjaa_di" bpmnElement="Activity_108jjaa_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324" isExpanded="true">
        <dc:Bounds x="160" y="550" width="350" height="200" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_16ihqz2_di" bpmnElement="Flow_16ihqz2_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <di:waypoint x="236" y="650" />
        <di:waypoint x="280" y="650" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pxn6yi_di" bpmnElement="Flow_0pxn6yi_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <di:waypoint x="380" y="650" />
        <di:waypoint x="432" y="650" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_1t3pzu3_di" bpmnElement="closeTask_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <dc:Bounds x="280" y="610" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0vepyso_di" bpmnElement="Event_0vepyso_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <dc:Bounds x="200" y="632" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="192" y="675" width="52" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0ey8tt4_di" bpmnElement="Event_0ey8tt4_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <dc:Bounds x="432" y="632" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="419" y="675" width="62" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1risnpd_di" bpmnElement="Event_0kb35fk_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <dc:Bounds x="1112" y="552" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1087" y="595" width="87" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_168ziyq_di" bpmnElement="Gateway_1rgsx47_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <dc:Bounds x="955" y="395" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="910" y="445" width="60" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0vgc65h_di" bpmnElement="sendPushNotification_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <dc:Bounds x="720" y="510" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0xoew6n_di" bpmnElement="inclusiveGateway">
        <dc:Bounds x="550" y="405" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="528" y="383" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1fte3ra_di" bpmnElement="Event_1tx361d_9130350413748496_a5121eb9-a104-4d7c-b03d-2ff2fb099324">
        <dc:Bounds x="1112" y="442" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1101" y="485" width="62" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>