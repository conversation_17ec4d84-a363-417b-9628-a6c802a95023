<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" exporter="Camunda Modeler" exporterVersion="3.7.0" expressionLanguage="http://www.w3.org/1999/XPath" id="Definitions_1oo65x9" targetNamespace="http://bpmn.io/schema/bpmn" typeLanguage="http://www.w3.org/2001/XMLSchema">

    <bpmn:process camunda:historyTimeToLive="7" id="customApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" isClosed="false" isExecutable="true" name="mybillworkflow" processType="None">

        <bpmn:startEvent id="customStartEvent_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" isInterrupting="true" name="transaction custom event" parallelMultiple="false">

            <bpmn:extensionElements>

                <camunda:properties>

                    <camunda:property name="stepDetails" value="{&quot;customStartEvent_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad&quot;:[&quot;autoApprove_Txn_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad&quot;,&quot;decisionElement_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad&quot;,&quot;inclusiveGateway_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad&quot;,&quot;sendCompanyEmail_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad&quot;,&quot;createTask_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad&quot;,&quot;sendPushNotification_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad&quot;,&quot;customStartEvent_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad&quot;],&quot;customWorkflowWaitEvent_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad&quot;:[&quot;sendApproveNotification_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad&quot;,&quot;sendAutoRejectNotification_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad&quot;,&quot;sendRejectNotification_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad&quot;,&quot;exclusiveGateway_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad&quot;,&quot;customWorkflowWaitEvent_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad&quot;,&quot;autoRejectTxn_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad&quot;]}"/>

                    <camunda:property name="taskDetails" value="{ &quot;required&quot;: true }"/>

                    <camunda:property name="currentStepDetails" value="{ &quot;required&quot;: true }"/>

                    <camunda:property name="processVariablesDetails" value="[{&quot;variableName&quot;:&quot;VendorName&quot;,&quot;variableType&quot;:&quot;string&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;entityChangeType&quot;,&quot;variableType&quot;:&quot;String&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;entityType&quot;,&quot;variableType&quot;:&quot;String&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;TxnDate&quot;,&quot;variableType&quot;:&quot;string&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;CompanyEmail&quot;,&quot;variableType&quot;:&quot;string&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;TxnAmount&quot;,&quot;variableType&quot;:&quot;double&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;intuit_userid&quot;,&quot;variableType&quot;:&quot;string&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;TxnDueDays&quot;,&quot;variableType&quot;:&quot;integer&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;TxnBalanceAmount&quot;,&quot;variableType&quot;:&quot;double&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;CompanyName&quot;,&quot;variableType&quot;:&quot;string&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;VendorEmail&quot;,&quot;variableType&quot;:&quot;string&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;TxnDueDate&quot;,&quot;variableType&quot;:&quot;string&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;DocNumber&quot;,&quot;variableType&quot;:&quot;string&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;Id&quot;,&quot;variableType&quot;:&quot;String&quot;,&quot;overrideIfAbsent&quot;:true},{&quot;variableName&quot;:&quot;intuit_realmid&quot;,&quot;variableType&quot;:&quot;String&quot;,&quot;overrideIfAbsent&quot;:true}]"/>

                    <camunda:property name="handlerDetails" value="{  &quot;taskHandler&quot;: &quot;was&quot;}"/>

                    <camunda:property name="startableEvents" value="[&quot;created&quot;,&quot;updated&quot;]"/>

                </camunda:properties>

            </bpmn:extensionElements>

            <bpmn:outgoing>Flow_0zrvk3w_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:outgoing>

        </bpmn:startEvent>

        <bpmn:businessRuleTask camunda:decisionRef="decisionElement_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" camunda:mapDecisionResult="singleResult" camunda:resultVariable="decision" completionQuantity="1" id="decisionElement_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" implementation="##unspecified" isForCompensation="false" name="Txn Rule Evaluation" startQuantity="1">

            <bpmn:extensionElements>

                <camunda:inputOutput>

                    <camunda:inputParameter name="handlerDetails">{ "taskHandler": "was"}</camunda:inputParameter>

                    <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>

                </camunda:inputOutput>

            </bpmn:extensionElements>

            <bpmn:incoming>Flow_0zrvk3w_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:incoming>

            <bpmn:outgoing>Flow_05n9zzu_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:outgoing>

            <bpmn:outgoing>Flow_1ew03t0_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:outgoing>

        </bpmn:businessRuleTask>

        <bpmn:receiveTask completionQuantity="1" id="customWorkflowWaitEvent_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" implementation="##WebService" instantiate="false" isForCompensation="false" messageRef="Message_0hvjxp3" name="Wait for state change" startQuantity="1">

            <bpmn:extensionElements>

                <camunda:inputOutput>

                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was"
                        }</camunda:inputParameter>

                    <camunda:inputParameter name="currentStepDetails">{ "required": false }</camunda:inputParameter>

                    <camunda:inputParameter name="parameterDetails">{"FilterCloseTaskConditions":{"fieldValue":["~bill.approval.createTask.CloseTask"],"configurable":false,"requiredByHandler":false,"requiredByUI":false,"multiSelect":false},"FilterCondition":{"fieldValue":["{\"rules\":[{\"parameterName\":\"TxnAmount\",\"conditionalExpression\":\"GTE 0\",\"$sdk_validated\":true},{\"parameterName\":\"intuit_userid\",\"conditionalExpression\":\"NOT_CONTAINS 9130356869118536\",\"$sdk_validated\":true}]}"],"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false},"FilterRecordType":{"fieldValue":["bill"],"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false}}</camunda:inputParameter>

                    <camunda:inputParameter name="targetApi">trigger</camunda:inputParameter>

                </camunda:inputOutput>

            </bpmn:extensionElements>

            <bpmn:incoming>Flow_01bjpss_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:incoming>

            <bpmn:outgoing>Flow_03rxzvl_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:outgoing>

        </bpmn:receiveTask>

        <bpmn:subProcess completionQuantity="1" id="Activity_1eq4x6t_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" isForCompensation="false" startQuantity="1" triggeredByEvent="true">

            <bpmn:serviceTask camunda:topic="test_arjun" camunda:type="external" completionQuantity="1" id="Activity_0kcpk0l_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" implementation="##WebService" isForCompensation="false" name="Close Project service task" startQuantity="1">

                <bpmn:extensionElements>

                    <camunda:inputOutput>

                        <camunda:inputParameter name="handlerDetails">{
                            "taskHandler": "appconnect",
                            "handlerId": "intuit-workflows/was-close-task-and-delete-constraint",
                            "actionName": "executeWorkflowAction"
                            }</camunda:inputParameter>

                        <camunda:inputParameter name="parameterDetails">{
                            "projectId": {
                            "fieldValue": [],
                            "handlerFieldName": "Project",
                            "requiredByHandler": true,
                            "requiredByUI": false,
                            "fieldType": "string",
                            "valueType": "PROCESS_VARIABLE"
                            },
                            "assigneeId": {
                            "fieldValue": [],
                            "handlerFieldName": "Assignee",
                            "requiredByHandler": true,
                            "requiredByUI": false,
                            "fieldType": "string",
                            "valueType": "PROCESS_VARIABLE"
                            },
                            "isConstraintAdded": {
                            "fieldValue": [],
                            "handlerFieldName": "isConstraintAdded",
                            "requiredByHandler": true,
                            "requiredByUI": false,
                            "fieldType": "string",
                            "valueType": "PROCESS_VARIABLE"
                            },
                            "Status": {
                            "fieldValue": [
                            "Complete"
                            ],
                            "requiredByHandler": true,
                            "requiredByUI": false,
                            "multiSelect": false,
                            "fieldType": "string"
                            },
                            "Id": {
                            "configurable": false,
                            "requiredByHandler": true,
                            "requiredByUI": false,
                            "multiSelect": false,
                            "fieldType": "string",
                            "valueType": "PROCESS_VARIABLE",
                            "handlerFieldName": "TxnId"
                            },
                            "entityType": {
                            "handlerFieldName": "recordType",
                            "configurable": false,
                            "requiredByHandler": true,
                            "requiredByUI": false,
                            "multiSelect": false,
                            "fieldType": "string",
                            "valueType": "PROCESS_VARIABLE"
                            }
                            }</camunda:inputParameter>

                        <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>

                    </camunda:inputOutput>

                </bpmn:extensionElements>

                <bpmn:incoming>Flow_0twcurc_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:incoming>

                <bpmn:outgoing>Flow_1s3y2z9_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:outgoing>

            </bpmn:serviceTask>

            <bpmn:startEvent id="start_closeProject_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" isInterrupting="true" name="Close task" parallelMultiple="false">

                <bpmn:outgoing>Flow_0twcurc_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:outgoing>

                <bpmn:escalationEventDefinition escalationRef="Escalation_00owd9n" id="EscalationEventDefinition_0pq4h1h"/>

            </bpmn:startEvent>

            <bpmn:endEvent id="Event_1v6ezlv_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" name="End process">

                <bpmn:extensionElements>

                    <camunda:inputOutput>

                        <camunda:inputParameter name="handlerDetails">{
                            "taskHandler": "was",
                            "handlerId": "",
                            "actionName": "updateProcessStatus"
                            }</camunda:inputParameter>

                    </camunda:inputOutput>

                </bpmn:extensionElements>

                <bpmn:incoming>Flow_1s3y2z9_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:incoming>

                <bpmn:messageEventDefinition camunda:topic="test_arjun" camunda:type="external" id="MessageEventDefinition_1644u2c" messageRef="Message_04xqo3b"/>

            </bpmn:endEvent>

            <bpmn:sequenceFlow id="Flow_0twcurc_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" sourceRef="start_closeProject_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" targetRef="Activity_0kcpk0l_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad"/>

            <bpmn:sequenceFlow id="Flow_1s3y2z9_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" sourceRef="Activity_0kcpk0l_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" targetRef="Event_1v6ezlv_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad"/>

        </bpmn:subProcess>

        <bpmn:inclusiveGateway gatewayDirection="Unspecified" id="approvalActions_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" name="approval actions">

            <bpmn:incoming>Flow_0pyokim_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:incoming>

            <bpmn:incoming>Flow_1vb0zfp_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:incoming>

            <bpmn:incoming>Flow_0t3cg7r_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:incoming>

            <bpmn:incoming>Flow_18tv83r_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:incoming>

            <bpmn:outgoing>Flow_01bjpss_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:outgoing>

        </bpmn:inclusiveGateway>

        <bpmn:sendTask camunda:topic="test_arjun" camunda:type="external" completionQuantity="1" id="sendCompanyEmail_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" implementation="##WebService" isForCompensation="false" name="Send company email" startQuantity="1">

            <bpmn:extensionElements>

                <camunda:inputOutput>

                    <camunda:inputParameter name="parameterDetails">{"CC":{"fieldValue":["my cc"],"configurable":false,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"VendorName":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"VendorName"},"BCC":{"configurable":false,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"Message":{"fieldValue":["Hi,\n\nBill [[DocNumber]] is pending approval. Please approve it at the earliest using this task manager link - https://app.qal.qbo.intuit.com/app/taskmanager.\n\nNote that bills that are not approved for more than 30 days will be auto rejected.\n\nThanks,\n[[CompanyName]]"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"entityType":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"RecordType"},"SendTo":{"fieldValue":["[[CompanyEmail]]"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string","handlerFieldName":"To"},"TxnDate":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnDate"},"CompanyEmail":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CompanyEmail"},"TxnAmount":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"double","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnAmount"},"TxnDueDays":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"integer","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnDueDays"},"Subject":{"fieldValue":["Review Bill [[DocNumber]]"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"TxnBalanceAmount":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"double","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnBalanceAmount"},"CompanyName":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CompanyName"},"IsEmail":{"fieldValue":["true"],"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"boolean"},"VendorEmail":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"VendorEmail"},"consolidateNotifications":{"fieldValue":["go blue"],"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"boolean"},"TxnDueDate":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnDueDate"},"DocNumber":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"DocNumber"},"Id":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnId"}}</camunda:inputParameter>

                    <camunda:inputParameter name="handlerDetails">{"taskHandler":"appconnect","actionName":"executeWorkflowAction","handlerId":"intuit-workflows/was-send-notification","recordType":null,"responseFields":null,"handlerScope":null}</camunda:inputParameter>

                    <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>

                </camunda:inputOutput>

            </bpmn:extensionElements>

            <bpmn:incoming>Flow_1iho7gu_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:incoming>

            <bpmn:outgoing>Flow_0pyokim_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:outgoing>

        </bpmn:sendTask>

        <bpmn:inclusiveGateway gatewayDirection="Unspecified" id="inclusiveGateway_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" name="Gateway">

            <bpmn:extensionElements>

                <camunda:properties>

                    <camunda:property name="taskDetails" value="{ &quot;required&quot;: true }"/>

                </camunda:properties>

            </bpmn:extensionElements>

            <bpmn:incoming>Flow_1ew03t0_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:incoming>

            <bpmn:outgoing>Flow_1iho7gu_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:outgoing>

            <bpmn:outgoing>Flow_0t3cg7r_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:outgoing>

            <bpmn:outgoing>Flow_0ul5pyl_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:outgoing>

            <bpmn:outgoing>Flow_0dusrsf_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:outgoing>

        </bpmn:inclusiveGateway>

        <bpmn:boundaryEvent attachedToRef="customWorkflowWaitEvent_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" cancelActivity="true" id="timerElapse_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" name="Expiry Event" parallelMultiple="false">

            <bpmn:outgoing>Flow_1jcy184_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:outgoing>

            <bpmn:timerEventDefinition id="TimerEventDefinition_0t88qgy">

                <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">P30D</bpmn:timeDuration>

            </bpmn:timerEventDefinition>

        </bpmn:boundaryEvent>

        <bpmn:sequenceFlow id="Flow_0zrvk3w_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" sourceRef="customStartEvent_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" targetRef="decisionElement_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad"/>

        <bpmn:sequenceFlow id="Flow_01bjpss_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" sourceRef="approvalActions_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" targetRef="customWorkflowWaitEvent_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad"/>

        <bpmn:sequenceFlow id="Flow_0pyokim_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" sourceRef="sendCompanyEmail_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" targetRef="approvalActions_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad"/>

        <bpmn:sequenceFlow id="Flow_1iho7gu_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" sourceRef="inclusiveGateway_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" targetRef="sendCompanyEmail_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad">

            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${true}</bpmn:conditionExpression>

        </bpmn:sequenceFlow>

        <bpmn:serviceTask camunda:topic="test_arjun" camunda:type="external" completionQuantity="1" id="autoApprove_Txn_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" implementation="##WebService" isForCompensation="false" name="Approve Txn" startQuantity="1">

            <bpmn:extensionElements>

                <camunda:inputOutput>

                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/update-txn-status",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>

                    <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>

                    <camunda:inputParameter name="parameterDetails">{
                        "Id": {
                        "fieldValue": [],
                        "handlerFieldName": "txnId",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType": "PROCESS_VARIABLE"
                        },
                        "txnStatus": {
                        "fieldValue": [
                        "AUTO_APPROVED"
                        ],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "intuit_realmid": {
                        "fieldValue": [],
                        "handlerFieldName": "realmId",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType": "PROCESS_VARIABLE"
                        }
                        }</camunda:inputParameter>

                    <camunda:outputParameter name="outputResponse"/>

                </camunda:inputOutput>

            </bpmn:extensionElements>

            <bpmn:incoming>Flow_05n9zzu_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:incoming>

            <bpmn:outgoing>Flow_1ldl6yv_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:outgoing>

        </bpmn:serviceTask>

        <bpmn:endEvent id="Event_0mlq58t_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" name="Txn Auto Approved">

            <bpmn:extensionElements>

                <camunda:inputOutput>

                    <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>

                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "handlerId": "",
                        "actionName": "updateProcessStatus"
                        }</camunda:inputParameter>

                </camunda:inputOutput>

            </bpmn:extensionElements>

            <bpmn:incoming>Flow_1ldl6yv_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:incoming>

            <bpmn:messageEventDefinition camunda:topic="test_arjun" camunda:type="external" id="MessageEventDefinition_1yd1rfc" messageRef="Message_0j828dx"/>

        </bpmn:endEvent>

        <bpmn:sequenceFlow id="Flow_1ldl6yv_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" sourceRef="autoApprove_Txn_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" targetRef="Event_0mlq58t_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad"/>

        <bpmn:sequenceFlow id="Flow_05n9zzu_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" name="Rules not satisfied" sourceRef="decisionElement_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" targetRef="autoApprove_Txn_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad">

            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decision.decisionResult == false}</bpmn:conditionExpression>

        </bpmn:sequenceFlow>

        <bpmn:sequenceFlow id="Flow_0t3cg7r_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" sourceRef="inclusiveGateway_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" targetRef="approvalActions_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad">

            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.hasVariable("sendPushNotification") == true}</bpmn:conditionExpression>

        </bpmn:sequenceFlow>

        <bpmn:serviceTask camunda:topic="custom-approvals" camunda:type="external" completionQuantity="1" id="createTask_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" implementation="##WebService" isForCompensation="false" name="Create Project Service task" startQuantity="1">

            <bpmn:extensionElements>

                <camunda:inputOutput>

                    <camunda:inputParameter name="handlerDetails">{"taskHandler":"appconnect","actionName":"executeWorkflowAction","handlerId":"intuit-workflows/was-create-task-and-add-constraint","recordType":null,"responseFields":["projectId","closeTaskRule","assigneeId"],"handlerScope":null}</camunda:inputParameter>

                    <camunda:inputParameter name="parameterDetails">{"Assignee":{"fieldValue":["9130356869118576"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"VendorName":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"VendorName"},"TxnDate":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnDate"},"CompanyEmail":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CompanyEmail"},"TxnAmount":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"double","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnAmount"},"TxnDueDays":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"integer","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnDueDays"},"TxnBalanceAmount":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"double","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnBalanceAmount"},"CompanyName":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CompanyName"},"VendorEmail":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"VendorEmail"},"CloseTask":{"fieldValue":["~bill.approval.createTask.CloseTask"],"configurable":true,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string"},"TxnDueDate":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnDueDate"},"TaskName":{"fieldValue":["Approval due for Bill [[DocNumber]]"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"DocNumber":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"DocNumber"},"ProjectType":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string"},"TaskType":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string"},"Id":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnId"},"intuit_realmid":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"RealmId"}}</camunda:inputParameter>

                    <camunda:inputParameter name="taskDetails">{ "required": true}</camunda:inputParameter>

                </camunda:inputOutput>

            </bpmn:extensionElements>

            <bpmn:incoming>Flow_0ul5pyl_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:incoming>

            <bpmn:outgoing>Flow_1vb0zfp_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:outgoing>

        </bpmn:serviceTask>

        <bpmn:serviceTask camunda:topic="test_arjun" camunda:type="external" completionQuantity="1" id="autoRejectTxn_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" implementation="##WebService" isForCompensation="false" name="Auto reject transaction" startQuantity="1">

            <bpmn:extensionElements>

                <camunda:inputOutput>

                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/update-txn-status",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>

                    <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>

                    <camunda:inputParameter name="parameterDetails">{
                        "Id": {
                        "fieldValue": [],
                        "handlerFieldName": "txnId",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType": "PROCESS_VARIABLE"
                        },
                        "txnStatus": {
                        "fieldValue": [
                        "WORKFLOW_AUTO_REJECTED"
                        ],
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "intuit_realmid": {
                        "fieldValue": [],
                        "handlerFieldName": "realmId",
                        "configurable": false,
                        "actionByUI": null,
                        "requiredByHandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string",
                        "valueType": "PROCESS_VARIABLE"
                        }
                        }</camunda:inputParameter>

                    <camunda:outputParameter name="outputResponse"/>

                </camunda:inputOutput>

            </bpmn:extensionElements>

            <bpmn:incoming>Flow_1jcy184_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:incoming>

            <bpmn:outgoing>Flow_0uly9et_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:outgoing>

        </bpmn:serviceTask>

        <bpmn:serviceTask camunda:topic="test_arjun" camunda:type="external" completionQuantity="1" id="sendAutoRejectNotification_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" implementation="##WebService" isForCompensation="false" name="Send notification to creator of transaction" startQuantity="1">

            <bpmn:extensionElements>

                <camunda:inputOutput>

                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/was-send-notification-to-txn-creator",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>

                    <camunda:inputParameter name="parameterDetails">{"CC":{"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"BCC":{"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"helpVariables":["Customer Email"],"fieldType":"string"},"Message":{"fieldValue":["Hi,\n\nBill [[DocNumber]]  couldn’t be approved. [[ApproverName]] didn’t take action within 30 days of it being sent for approval. You need to resend the invoice for approval.\n\nWarm regards,\n[[CompanyName]]"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"NotificationAction":{"fieldValue":["qb001://open/{{RecordType}}/?id=[[{{RecordType}}LocalId]]&amp;companyid=[[realmId]]"],"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","handlerFieldName":"Mobile Notification Action"},"entityType":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"RecordType"},"intuit_userid":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"To"},"Subject":{"fieldValue":["Bill [[DocNumber]] is denied approval"],"configurable":true,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string"},"CompanyName":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CompanyName"},"DocNumber":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"DocNumber"},"isEmail":{"fieldValue":["true"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"boolean"},"Id":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnId"},"isMobile":{"fieldValue":["true"],"configurable":false,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"boolean"},"intuit_realmid":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"realmId"}}</camunda:inputParameter>

                    <camunda:inputParameter name="taskDetails">{ "required": false }</camunda:inputParameter>

                    <camunda:outputParameter name="outputResponse"/>

                </camunda:inputOutput>

            </bpmn:extensionElements>

            <bpmn:incoming>Flow_0uly9et_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:incoming>

            <bpmn:outgoing>Flow_1suemrk_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:outgoing>

        </bpmn:serviceTask>

        <bpmn:endEvent id="Event_15jp7xs_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" name="Txn rejected because of time out">

            <bpmn:extensionElements>

                <camunda:inputOutput>

                    <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>

                </camunda:inputOutput>

            </bpmn:extensionElements>

            <bpmn:incoming>Flow_1suemrk_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:incoming>

            <bpmn:escalationEventDefinition escalationRef="Escalation_0d2bkim" id="EscalationEventDefinition_1ke515p"/>

        </bpmn:endEvent>

        <bpmn:exclusiveGateway default="sequenceRejected_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" gatewayDirection="Unspecified" id="exclusiveGateway_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" name="approved?">

            <bpmn:extensionElements>

                <camunda:properties>

                    <camunda:property name="handlerDetails" value="{ &quot;taskHandler&quot;: &quot;appconnect&quot;,                         &quot;handlerId&quot;: &quot;1234&quot;,                         &quot;handlerName&quot;: &quot;sendEmail&quot;                         }"/>

                    <camunda:property name="parameterDetails" value="{ &quot;{{txn}}.status&quot;: { &quot;fieldValue&quot; : [&quot;approved&quot;],  &quot;configurable&quot; : false,  &quot;actionByUI&quot; : null,  &quot;requiredByhandler&quot; : false,  &quot;requiredByUI&quot;: false,  &quot;multiSelect&quot;: false,  &quot;fieldType&quot;: &quot;string&quot;}}"/>

                    <camunda:property name="taskDetails" value="{&quot;required&quot;: true}"/>

                </camunda:properties>

            </bpmn:extensionElements>

            <bpmn:incoming>Flow_03rxzvl_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:incoming>

            <bpmn:outgoing>sequenceApproved_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:outgoing>

            <bpmn:outgoing>sequenceRejected_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:outgoing>

        </bpmn:exclusiveGateway>

        <bpmn:serviceTask camunda:topic="test_arjun" camunda:type="external" completionQuantity="1" id="sendApproveNotification_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" implementation="##WebService" isForCompensation="false" name="Send notification to creator of transaction" startQuantity="1">

            <bpmn:extensionElements>

                <camunda:inputOutput>

                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/was-send-notification-to-txn-creator",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>

                    <camunda:inputParameter name="parameterDetails">{"CC":{"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"BCC":{"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"helpVariables":["Customer Email"],"fieldType":"string"},"Message":{"fieldValue":["Hi,\n\nBill [[DocNumber]] was approved by [[ApproverName]].\n\nWarm regards,\n[[CompanyName]]"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"NotificationAction":{"fieldValue":["qb001://open/{{RecordType}}/?id=[[{{RecordType}}LocalId]]&amp;companyid=[[realmId]]"],"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","handlerFieldName":"Mobile Notification Action"},"entityType":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"RecordType"},"intuit_userid":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"To"},"Subject":{"fieldValue":["Bill [[DocNumber]] is approved"],"configurable":true,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string"},"CompanyName":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CompanyName"},"DocNumber":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"DocNumber"},"isEmail":{"fieldValue":["true"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"boolean"},"Id":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnId"},"isMobile":{"fieldValue":["true"],"configurable":false,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"boolean"},"intuit_realmid":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"realmId"}}</camunda:inputParameter>

                    <camunda:inputParameter name="taskDetails">{ "required": false }</camunda:inputParameter>

                    <camunda:outputParameter name="outputResponse"/>

                </camunda:inputOutput>

            </bpmn:extensionElements>

            <bpmn:incoming>sequenceApproved_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:incoming>

            <bpmn:outgoing>Flow_03h5end_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:outgoing>

        </bpmn:serviceTask>

        <bpmn:serviceTask camunda:topic="test_arjun" camunda:type="external" completionQuantity="1" id="sendRejectNotification_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" implementation="##WebService" isForCompensation="false" name="Send notification to creator of transaction" startQuantity="1">

            <bpmn:extensionElements>

                <camunda:inputOutput>

                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/was-send-notification-to-txn-creator",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>

                    <camunda:inputParameter name="parameterDetails">{"CC":{"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"BCC":{"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"helpVariables":["Customer Email"],"fieldType":"string"},"Message":{"fieldValue":["Hi,\n\nBill [[DocNumber]] was denied approval by [[ApproverName]].\n\nWarm regards,\n[[CompanyName]]"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"NotificationAction":{"fieldValue":["qb001://open/{{RecordType}}/?id=[[{{RecordType}}LocalId]]&amp;companyid=[[realmId]]"],"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","handlerFieldName":"Mobile Notification Action"},"entityType":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"RecordType"},"intuit_userid":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"To"},"Subject":{"fieldValue":["Bill [[DocNumber]] is denied approval"],"configurable":true,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string"},"CompanyName":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CompanyName"},"DocNumber":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"DocNumber"},"isEmail":{"fieldValue":["true"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"boolean"},"Id":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"TxnId"},"isMobile":{"fieldValue":["true"],"configurable":false,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"boolean"},"intuit_realmid":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"realmId"}}</camunda:inputParameter>

                    <camunda:inputParameter name="taskDetails">{ "required": false }</camunda:inputParameter>

                    <camunda:outputParameter name="outputResponse"/>

                </camunda:inputOutput>

            </bpmn:extensionElements>

            <bpmn:incoming>sequenceRejected_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:incoming>

            <bpmn:outgoing>Flow_0dr7p30_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:outgoing>

        </bpmn:serviceTask>

        <bpmn:endEvent id="Event_1a2394d_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" name="Transaction rejected">

            <bpmn:extensionElements>

                <camunda:inputOutput>

                    <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>

                </camunda:inputOutput>

            </bpmn:extensionElements>

            <bpmn:incoming>Flow_0dr7p30_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:incoming>

            <bpmn:escalationEventDefinition escalationRef="Escalation_0ye464b" id="EscalationEventDefinition_0u4kh9z"/>

        </bpmn:endEvent>

        <bpmn:endEvent id="Event_0nkpdiy_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" name="Transaction approved">

            <bpmn:extensionElements>

                <camunda:inputOutput>

                    <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>

                </camunda:inputOutput>

            </bpmn:extensionElements>

            <bpmn:incoming>Flow_03h5end_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:incoming>

            <bpmn:escalationEventDefinition escalationRef="Escalation_0rubrjz" id="EscalationEventDefinition_00ppu84"/>

        </bpmn:endEvent>

        <bpmn:sequenceFlow id="Flow_03rxzvl_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" sourceRef="customWorkflowWaitEvent_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" targetRef="exclusiveGateway_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad"/>

        <bpmn:sequenceFlow id="sequenceApproved_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" name="Yes" sourceRef="exclusiveGateway_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" targetRef="sendApproveNotification_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad">

            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${entityChangeType == 'approved'}</bpmn:conditionExpression>

        </bpmn:sequenceFlow>

        <bpmn:sequenceFlow id="sequenceRejected_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" name="No" sourceRef="exclusiveGateway_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" targetRef="sendRejectNotification_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad"/>

        <bpmn:sequenceFlow id="Flow_03h5end_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" sourceRef="sendApproveNotification_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" targetRef="Event_0nkpdiy_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad"/>

        <bpmn:sequenceFlow id="Flow_0dr7p30_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" sourceRef="sendRejectNotification_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" targetRef="Event_1a2394d_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad"/>

        <bpmn:sequenceFlow id="Flow_1jcy184_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" sourceRef="timerElapse_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" targetRef="autoRejectTxn_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad"/>

        <bpmn:sequenceFlow id="Flow_0uly9et_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" sourceRef="autoRejectTxn_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" targetRef="sendAutoRejectNotification_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad"/>

        <bpmn:sequenceFlow id="Flow_1suemrk_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" sourceRef="sendAutoRejectNotification_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" targetRef="Event_15jp7xs_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad"/>

        <bpmn:subProcess completionQuantity="1" id="Activity_1u4eup2_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" isForCompensation="false" name="deleted voided disable handler subprocess" startQuantity="1" triggeredByEvent="true">

            <bpmn:startEvent id="Event_0iambok_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" isInterrupting="true" name="deleted voided disable txnapproval" parallelMultiple="false">

                <bpmn:outgoing>Flow_19we8wq_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:outgoing>

                <bpmn:messageEventDefinition id="MessageEventDefinition_00zjz83" messageRef="Message_04yyvx2"/>

            </bpmn:startEvent>

            <bpmn:endEvent id="Event_1pngpkg_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" name="Transaction deleted or voided or disable">

                <bpmn:extensionElements>

                    <camunda:inputOutput>

                        <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>

                    </camunda:inputOutput>

                </bpmn:extensionElements>

                <bpmn:incoming>Flow_19we8wq_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:incoming>

                <bpmn:escalationEventDefinition escalationRef="Escalation_01zakh4" id="EscalationEventDefinition_0hjq3ny"/>

            </bpmn:endEvent>

            <bpmn:sequenceFlow id="Flow_19we8wq_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" sourceRef="Event_0iambok_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" targetRef="Event_1pngpkg_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad"/>

        </bpmn:subProcess>

        <bpmn:sequenceFlow id="Flow_1vb0zfp_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" sourceRef="createTask_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" targetRef="approvalActions_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad"/>

        <bpmn:sequenceFlow id="Flow_1ew03t0_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" name="Rules  satisfied" sourceRef="decisionElement_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" targetRef="inclusiveGateway_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad">

            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decision.decisionResult == true}</bpmn:conditionExpression>

        </bpmn:sequenceFlow>

        <bpmn:sequenceFlow id="Flow_0ul5pyl_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" sourceRef="inclusiveGateway_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" targetRef="createTask_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad">

            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${true}</bpmn:conditionExpression>

        </bpmn:sequenceFlow>

        <bpmn:sendTask camunda:topic="test_arjun" camunda:type="external" completionQuantity="1" id="sendPushNotification_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" implementation="##WebService" isForCompensation="false" name="Send push notification" startQuantity="1">

            <bpmn:extensionElements>

                <camunda:inputOutput>

                    <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>

                    <camunda:inputParameter name="handlerDetails">{}</camunda:inputParameter>

                    <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>

                </camunda:inputOutput>

            </bpmn:extensionElements>

            <bpmn:incoming>Flow_0dusrsf_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:incoming>

            <bpmn:outgoing>Flow_18tv83r_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad</bpmn:outgoing>

        </bpmn:sendTask>

        <bpmn:sequenceFlow id="Flow_0dusrsf_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" sourceRef="inclusiveGateway_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" targetRef="sendPushNotification_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad">

            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${false}</bpmn:conditionExpression>

        </bpmn:sequenceFlow>

        <bpmn:sequenceFlow id="Flow_18tv83r_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" sourceRef="sendPushNotification_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" targetRef="approvalActions_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad"/>

    </bpmn:process>

    <bpmn:message id="Message_0oflesh" name="customWait"/>

    <bpmn:escalation escalationCode="closetask" id="Escalation_025wo89" name="close_task"/>

    <bpmn:escalation escalationCode="closetask" id="Escalation_00owd9n" name="close_task"/>

    <bpmn:message id="Message_04xqo3b" name="process_ended_message"/>

    <bpmn:message id="Message_0j828dx" name="process_ended_message"/>

    <bpmn:escalation escalationCode="closetask" id="Escalation_0d2bkim" name="close_task"/>

    <bpmn:escalation escalationCode="closetask" id="Escalation_0ye464b" name="close_task"/>

    <bpmn:escalation escalationCode="closetask" id="Escalation_0rubrjz" name="close_task"/>

    <bpmn:message id="Message_04yyvx2" name="deleted_voided_disable"/>

    <bpmn:escalation escalationCode="closetask" id="Escalation_01zakh4" name="close_task"/>

    <bpmn:message id="Message_0hvjxp3" name="approved_rejected"/>

    <bpmndi:BPMNDiagram id="BPMNDiagram_1">

        <bpmndi:BPMNPlane bpmnElement="customApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="BPMNPlane_1">

            <bpmndi:BPMNEdge bpmnElement="Flow_1ew03t0_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Flow_1ew03t0_di">

                <di:waypoint x="420" y="360"/>

                <di:waypoint x="590" y="360"/>

                <bpmndi:BPMNLabel>

                    <dc:Bounds height="14" width="75" x="468" y="366"/>

                </bpmndi:BPMNLabel>

            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_1vb0zfp_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Flow_1vb0zfp_di">

                <di:waypoint x="860" y="230"/>

                <di:waypoint x="1000" y="230"/>

                <di:waypoint x="1000" y="325"/>

            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_1suemrk_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Flow_1suemrk_di">

                <di:waypoint x="1240" y="640"/>

                <di:waypoint x="1302" y="640"/>

            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_0uly9et_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Flow_0uly9et_di">

                <di:waypoint x="1190" y="550"/>

                <di:waypoint x="1190" y="600"/>

            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_1jcy184_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Flow_1jcy184_di">

                <di:waypoint x="1190" y="408"/>

                <di:waypoint x="1190" y="470"/>

            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_0dr7p30_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Flow_0dr7p30_di">

                <di:waypoint x="1370" y="80"/>

                <di:waypoint x="1442" y="80"/>

            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_03h5end_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Flow_03h5end_di">

                <di:waypoint x="1370" y="210"/>

                <di:waypoint x="1442" y="210"/>

            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="sequenceRejected_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Flow_1xfif4s_di">

                <di:waypoint x="1190" y="185"/>

                <di:waypoint x="1190" y="80"/>

                <di:waypoint x="1270" y="80"/>

                <bpmndi:BPMNLabel>

                    <dc:Bounds height="14" width="15" x="1198" y="130"/>

                </bpmndi:BPMNLabel>

            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="sequenceApproved_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Flow_1o3b2au_di">

                <di:waypoint x="1215" y="210"/>

                <di:waypoint x="1270" y="210"/>

                <bpmndi:BPMNLabel>

                    <dc:Bounds height="14" width="18" x="1234" y="192"/>

                </bpmndi:BPMNLabel>

            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_03rxzvl_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Flow_03rxzvl_di">

                <di:waypoint x="1190" y="310"/>

                <di:waypoint x="1190" y="235"/>

            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_0t3cg7r_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Flow_0t3cg7r_di">

                <di:waypoint x="640" y="360"/>

                <di:waypoint x="808" y="360"/>

                <di:waypoint x="808" y="350"/>

                <di:waypoint x="975" y="350"/>

            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_05n9zzu_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Flow_05n9zzu_di">

                <di:waypoint x="370" y="320"/>

                <di:waypoint x="370" y="270"/>

                <bpmndi:BPMNLabel>

                    <dc:Bounds height="14" width="90" x="275" y="297"/>

                </bpmndi:BPMNLabel>

            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_1ldl6yv_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Flow_1ldl6yv_di">

                <di:waypoint x="370" y="190"/>

                <di:waypoint x="370" y="128"/>

            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_1iho7gu_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Flow_1iho7gu_di">

                <di:waypoint x="640" y="360"/>

                <di:waypoint x="760" y="360"/>

                <bpmndi:BPMNLabel>

                    <dc:Bounds height="27" width="76" x="771.9999999999999" y="276"/>

                </bpmndi:BPMNLabel>

            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_0pyokim_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Flow_0pyokim_di">

                <di:waypoint x="860" y="350"/>

                <di:waypoint x="975" y="350"/>

            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_01bjpss_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Flow_01bjpss_di">

                <di:waypoint x="1025" y="350"/>

                <di:waypoint x="1140" y="350"/>

            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_0zrvk3w_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Flow_0zrvk3w_di">

                <di:waypoint x="228" y="360"/>

                <di:waypoint x="320" y="360"/>

            </bpmndi:BPMNEdge>

            <bpmndi:BPMNShape bpmnElement="customStartEvent_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Event_0u1c60a_di">

                <dc:Bounds height="36" width="36" x="192" y="342"/>

                <bpmndi:BPMNLabel>

                    <dc:Bounds height="27" width="66" x="180" y="385"/>

                </bpmndi:BPMNLabel>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="decisionElement_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Activity_0e0x7c8_di">

                <dc:Bounds height="80" width="100" x="320" y="320"/>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="customWorkflowWaitEvent_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Activity_0w63ezw_di">

                <dc:Bounds height="80" width="100" x="1140" y="310"/>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="Activity_1eq4x6t_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Activity_1eq4x6t_di" isExpanded="true">

                <dc:Bounds height="200" width="350" x="290" y="530"/>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNEdge bpmnElement="Flow_1s3y2z9_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Flow_1s3y2z9_di">

                <di:waypoint x="510" y="630"/>

                <di:waypoint x="562" y="630"/>

            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_0twcurc_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Flow_0twcurc_di">

                <di:waypoint x="366" y="630"/>

                <di:waypoint x="410" y="630"/>

            </bpmndi:BPMNEdge>

            <bpmndi:BPMNShape bpmnElement="Activity_0kcpk0l_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Activity_0kcpk0l_di">

                <dc:Bounds height="80" width="100" x="410" y="590"/>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="start_closeProject_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Event_0h9quic_di">

                <dc:Bounds height="36" width="36" x="330" y="612"/>

                <bpmndi:BPMNLabel>

                    <dc:Bounds height="14" width="52" x="322" y="655"/>

                </bpmndi:BPMNLabel>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="Event_1v6ezlv_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Event_1v6ezlv_di">

                <dc:Bounds height="36" width="36" x="562" y="612"/>

                <bpmndi:BPMNLabel>

                    <dc:Bounds height="14" width="62" x="549" y="655"/>

                </bpmndi:BPMNLabel>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="approvalActions_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Gateway_0gv6vdr_di">

                <dc:Bounds height="50" width="50" x="975" y="325"/>

                <bpmndi:BPMNLabel>

                    <dc:Bounds height="14" width="81" x="1009" y="363"/>

                </bpmndi:BPMNLabel>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="sendCompanyEmail_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Activity_0ugfapy_di">

                <dc:Bounds height="80" width="100" x="760" y="310"/>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="inclusiveGateway_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Gateway_0dlkaet_di">

                <dc:Bounds height="50" width="50" x="590" y="335"/>

                <bpmndi:BPMNLabel>

                    <dc:Bounds height="14" width="44" x="568" y="313"/>

                </bpmndi:BPMNLabel>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="autoApprove_Txn_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Activity_10hyjzr_di">

                <dc:Bounds height="80" width="100" x="320" y="190"/>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="Event_0mlq58t_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Event_0mlq58t_di">

                <dc:Bounds height="36" width="36" x="352" y="92"/>

                <bpmndi:BPMNLabel>

                    <dc:Bounds height="27" width="48" x="346" y="54.5"/>

                </bpmndi:BPMNLabel>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="createTask_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Activity_1u7acdq_di">

                <dc:Bounds height="80" width="100" x="760" y="190"/>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="autoRejectTxn_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Activity_00imphd_di">

                <dc:Bounds height="80" width="100" x="1140" y="470"/>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="sendAutoRejectNotification_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Activity_0e18sgi_di">

                <dc:Bounds height="80" width="100" x="1140" y="600"/>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="Event_15jp7xs_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Event_15jp7xs_di">

                <dc:Bounds height="36" width="36" x="1302" y="622"/>

                <bpmndi:BPMNLabel>

                    <dc:Bounds height="40" width="78" x="1281" y="665"/>

                </bpmndi:BPMNLabel>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="exclusiveGateway_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Gateway_1u44vq2_di" isMarkerVisible="true">

                <dc:Bounds height="50" width="50" x="1165" y="185"/>

                <bpmndi:BPMNLabel>

                    <dc:Bounds height="14" width="52" x="1124" y="222"/>

                </bpmndi:BPMNLabel>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="sendApproveNotification_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Activity_16w49ba_di">

                <dc:Bounds height="80" width="100" x="1270" y="170"/>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="sendRejectNotification_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Activity_1veg1jh_di">

                <dc:Bounds height="80" width="100" x="1270" y="40"/>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="Event_1a2394d_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Event_1a2394d_di">

                <dc:Bounds height="36" width="36" x="1442" y="62"/>

                <bpmndi:BPMNLabel>

                    <dc:Bounds height="27" width="58" x="1491" y="66"/>

                </bpmndi:BPMNLabel>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="Event_0nkpdiy_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Event_0nkpdiy_di">

                <dc:Bounds height="36" width="36" x="1442" y="192"/>

                <bpmndi:BPMNLabel>

                    <dc:Bounds height="27" width="58" x="1491" y="196"/>

                </bpmndi:BPMNLabel>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="Activity_1u4eup2_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Activity_1u4eup2_di" isExpanded="true">

                <dc:Bounds height="200" width="350" x="290" y="790"/>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNEdge bpmnElement="Flow_19we8wq_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Flow_19we8wq_di">

                <di:waypoint x="388" y="880"/>

                <di:waypoint x="512" y="880"/>

            </bpmndi:BPMNEdge>

            <bpmndi:BPMNShape bpmnElement="Event_0iambok_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Event_0iambok_di">

                <dc:Bounds height="36" width="36" x="352" y="862"/>

                <bpmndi:BPMNLabel>

                    <dc:Bounds height="40" width="72" x="338" y="905"/>

                </bpmndi:BPMNLabel>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="Event_1pngpkg_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Event_1pngpkg_di">

                <dc:Bounds height="36" width="36" x="512" y="862"/>

                <bpmndi:BPMNLabel>

                    <dc:Bounds height="40" width="85" x="488" y="905"/>

                </bpmndi:BPMNLabel>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNShape bpmnElement="timerElapse_txnApproval_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Event_09vb67w_di">

                <dc:Bounds height="36" width="36" x="1172" y="372"/>

                <bpmndi:BPMNLabel>

                    <dc:Bounds height="14" width="62" x="1159" y="415"/>

                </bpmndi:BPMNLabel>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNEdge bpmnElement="Flow_0ul5pyl_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Flow_0ul5pyl_di">

                <di:waypoint x="615" y="335"/>

                <di:waypoint x="615" y="230"/>

                <di:waypoint x="760" y="230"/>

            </bpmndi:BPMNEdge>

            <bpmndi:BPMNShape bpmnElement="sendPushNotification_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Activity_1l1zd9h_di">

                <dc:Bounds height="80" width="100" x="760" y="430"/>

            </bpmndi:BPMNShape>

            <bpmndi:BPMNEdge bpmnElement="Flow_0dusrsf_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Flow_0dusrsf_di">

                <di:waypoint x="615" y="385"/>

                <di:waypoint x="615" y="470"/>

                <di:waypoint x="760" y="470"/>

            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_18tv83r_9130356869118556_c69180fe-6962-4a13-ba0a-bc2f084341ad" id="Flow_18tv83r_di">

                <di:waypoint x="860" y="470"/>

                <di:waypoint x="1000" y="470"/>

                <di:waypoint x="1000" y="375"/>

            </bpmndi:BPMNEdge>

        </bpmndi:BPMNPlane>

    </bpmndi:BPMNDiagram>

</bpmn:definitions>
