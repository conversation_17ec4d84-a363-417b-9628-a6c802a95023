<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1dw6q1n" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.8.1" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.15.0">
  <bpmn:process id="invoiceWorkTaskA" name="invoiceWorkTask" isExecutable="true" camunda:versionTag="1" camunda:historyTimeToLive="5">
    <bpmn:endEvent id="Event_1h6r97t" name="End Process">
      <bpmn:incoming>Flow_10ftxom</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:startEvent id="Event_0pzem0z" name="Process Start Event" camunda:asyncBefore="true">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;was&#34;, &#34;recordType&#34;: &#34;invoice&#34;}" />
          <camunda:property name="startableEvents" value="[&#34;created&#34;, &#34;updated&#34;]" />
          <camunda:property name="processVariablesDetails" value="[{&#34;variableName&#34;:&#34;TotalAmt&#34;, &#34;variableType&#34;: &#34;Double&#34;}, {&#34;variableName&#34;:&#34;CustomerRef_value&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;DepartmentRef_value&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;entityChangeType&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;Id&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;intuit_userid&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;intuit_realmid&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;offeringId&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;recordId&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;priority&#34;, &#34;variableType&#34;: &#34;Integer&#34;}, {&#34;variableName&#34;:&#34;comments&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;dueDate&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;assigneeRoles&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;assignee&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;customerId&#34;, &#34;variableType&#34;: &#34;String&#34;}]" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_1gz3uqg</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_1tzbszm" sourceRef="Activity_08y2cf0" targetRef="Activity_10suogy" />
    <bpmn:sequenceFlow id="Flow_1gz3uqg" sourceRef="Event_0pzem0z" targetRef="Activity_08y2cf0" />
    <bpmn:sequenceFlow id="Flow_07pyz11" sourceRef="Activity_0pfcurq" targetRef="Activity_01wwdz7" />
    <bpmn:serviceTask id="Activity_0pfcurq" name="WorkItem 3" camunda:type="external" camunda:topic="manishs-local">
      <bpmn:extensionElements>
        <camunda:executionListener class="com.intuit.v4.appintgwkflw.wkflautomate.camundaserviceapp.listener.PublishWorkflowEventsExecutionListener" event="start" />
        <camunda:properties>
          <camunda:property name="type" value="SYSTEM_TASK" />
          <camunda:property name="Description" value="test work task 2" />
        </camunda:properties>
        <camunda:inputOutput>
          <camunda:inputParameter name="clientId">${userId}</camunda:inputParameter>
          <camunda:inputParameter name="transactionid">${transactionId}</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:executionListener class="com.intuit.v4.appintgwkflw.wkflautomate.camundaserviceapp.listener.PublishWorkflowEventsExecutionListener" event="end" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1xwkh12</bpmn:incoming>
      <bpmn:outgoing>Flow_07pyz11</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_08y2cf0" name="WorkItem 1" camunda:type="external" camunda:topic="manishs-local">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="type" value="HUMAN_TASK" />
          <camunda:property name="activityName" value="Work task 1" />
          <camunda:property name="description" value="work item" />
          <camunda:property name="priority" value="2" />
          <camunda:property name="key1" value="value1" />
        </camunda:properties>
        <camunda:inputOutput>
          <camunda:inputParameter name="app">${offeringId}</camunda:inputParameter>
          <camunda:inputParameter name="txnId">${recordId}</camunda:inputParameter>
          <camunda:inputParameter name="priority">${priority}</camunda:inputParameter>
          <camunda:inputParameter name="comments">${comments}</camunda:inputParameter>
          <camunda:inputParameter name="dueDate">${dueDate}</camunda:inputParameter>
          <camunda:inputParameter name="assigneeRoles">${assigneeRoles}</camunda:inputParameter>
          <camunda:inputParameter name="assignee">${assignee}</camunda:inputParameter>
          <camunda:inputParameter name="customerId">${intuit_userid}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1gz3uqg</bpmn:incoming>
      <bpmn:outgoing>Flow_1tzbszm</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_10suogy" name="WorkItem 2" camunda:type="external" camunda:topic="manishs-local">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="type" value="HUMAN_TASK" />
          <camunda:property name="activityName" value="Work task 2" />
          <camunda:property name="description" value="work item" />
          <camunda:property name="priority" value="2" />
          <camunda:property name="key1" value="value1" />
        </camunda:properties>
        <camunda:inputOutput>
          <camunda:inputParameter name="app">${offeringId}</camunda:inputParameter>
          <camunda:inputParameter name="txnId">${recordId}</camunda:inputParameter>
          <camunda:inputParameter name="priority">${priority}</camunda:inputParameter>
          <camunda:inputParameter name="comments">${comments}</camunda:inputParameter>
          <camunda:inputParameter name="dueDate">${dueDate}</camunda:inputParameter>
          <camunda:inputParameter name="assigneeRoles">${assigneeRoles}</camunda:inputParameter>
          <camunda:inputParameter name="assignee">${assignee}</camunda:inputParameter>
          <camunda:inputParameter name="customerId">${intuit_userid}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1tzbszm</bpmn:incoming>
      <bpmn:outgoing>Flow_1xwkh12</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1xwkh12" sourceRef="Activity_10suogy" targetRef="Activity_0pfcurq" />
    <bpmn:serviceTask id="Activity_01wwdz7" name="WorkItem 4" camunda:type="external" camunda:topic="manishs-local">
      <bpmn:extensionElements>
        <camunda:executionListener class="com.intuit.v4.appintgwkflw.wkflautomate.camundaserviceapp.listener.PublishWorkflowEventsExecutionListener" event="start" />
        <camunda:inputOutput>
          <camunda:inputParameter name="clientId">${userId}</camunda:inputParameter>
          <camunda:inputParameter name="transactionid">${transactionId}</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:executionListener class="com.intuit.v4.appintgwkflw.wkflautomate.camundaserviceapp.listener.PublishWorkflowEventsExecutionListener" event="end" />
        <camunda:properties>
          <camunda:property name="handlerDetails" value="abc" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_07pyz11</bpmn:incoming>
      <bpmn:outgoing>Flow_0twyc2r</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0twyc2r" sourceRef="Activity_01wwdz7" targetRef="Activity_0kcj1xx" />
    <bpmn:serviceTask id="Activity_0kcj1xx" name="WorkItem 5" camunda:type="external" camunda:topic="manishs-local">
      <bpmn:extensionElements>
        <camunda:executionListener class="com.intuit.v4.appintgwkflw.wkflautomate.camundaserviceapp.listener.PublishWorkflowEventsExecutionListener" event="start" />
        <camunda:inputOutput>
          <camunda:inputParameter name="clientId">${userId}</camunda:inputParameter>
          <camunda:inputParameter name="transactionid">${transactionId}</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:executionListener class="com.intuit.v4.appintgwkflw.wkflautomate.camundaserviceapp.listener.PublishWorkflowEventsExecutionListener" event="end" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0twyc2r</bpmn:incoming>
      <bpmn:outgoing>Flow_10ftxom</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_10ftxom" sourceRef="Activity_0kcj1xx" targetRef="Event_1h6r97t" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="invoiceWorkTaskA">
      <bpmndi:BPMNEdge id="Flow_0twyc2r_di" bpmnElement="Flow_0twyc2r">
        <di:waypoint x="980" y="120" />
        <di:waypoint x="1030" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xwkh12_di" bpmnElement="Flow_1xwkh12">
        <di:waypoint x="630" y="120" />
        <di:waypoint x="710" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07pyz11_di" bpmnElement="Flow_07pyz11">
        <di:waypoint x="810" y="120" />
        <di:waypoint x="880" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gz3uqg_di" bpmnElement="Flow_1gz3uqg">
        <di:waypoint x="208" y="120" />
        <di:waypoint x="360" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1tzbszm_di" bpmnElement="Flow_1tzbszm">
        <di:waypoint x="460" y="120" />
        <di:waypoint x="530" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10ftxom_di" bpmnElement="Flow_10ftxom">
        <di:waypoint x="1130" y="120" />
        <di:waypoint x="1222" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_0pzem0z_di" bpmnElement="Event_0pzem0z">
        <dc:Bounds x="172" y="102" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="158" y="145" width="67" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_07hadiz_di" bpmnElement="Activity_0pfcurq">
        <dc:Bounds x="710" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1enczqy_di" bpmnElement="Activity_08y2cf0">
        <dc:Bounds x="360" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_10suogy_di" bpmnElement="Activity_10suogy">
        <dc:Bounds x="530" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_01wwdz7_di" bpmnElement="Activity_01wwdz7">
        <dc:Bounds x="880" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1h6r97t_di" bpmnElement="Event_1h6r97t">
        <dc:Bounds x="1222" y="102" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1210" y="145" width="63" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0kcj1xx_di" bpmnElement="Activity_0kcj1xx">
        <dc:Bounds x="1030" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
