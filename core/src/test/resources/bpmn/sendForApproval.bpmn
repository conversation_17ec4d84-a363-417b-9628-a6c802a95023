<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bioc="http://bpmn.io/schema/bpmn/biocolor/1.0" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" exporter="Camunda Modeler" exporterVersion="5.0.0" expressionLanguage="http://www.w3.org/1999/XPath" id="Definitions_10pfte7" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.15.0" targetNamespace="http://bpmn.io/schema/bpmn" typeLanguage="http://www.w3.org/2001/XMLSchema">

  <bpmn:process camunda:historyTimeToLive="7" id="sendForApproval" isClosed="false" isExecutable="true" name="sendForApproval" processType="None">

    <bpmn:startEvent id="startEvent" isInterrupting="true" name="startEvent" parallelMultiple="false">

      <bpmn:extensionElements>

        <camunda:properties>

          <camunda:property name="stepDetails" value="{   &quot;startEvent&quot;: [     &quot;startEvent&quot;   ] }"/>

          <camunda:property name="startableEvents" value="[&quot;created&quot;]"/>

          <camunda:property name="processVariablesDetails" value="[   {     &quot;variableName&quot;: &quot;Id&quot;,     &quot;variableType&quot;: &quot;String&quot;   } ]"/>

        </camunda:properties>

      </bpmn:extensionElements>

      <bpmn:outgoing>Flow_0qz7tvz</bpmn:outgoing>

    </bpmn:startEvent>

    <bpmn:subProcess completionQuantity="1" id="Activity_1ppmouy" isForCompensation="false" name="Request Approval" startQuantity="1" triggeredByEvent="false">

      <bpmn:incoming>Flow_0qz7tvz</bpmn:incoming>

      <bpmn:outgoing>Flow_1fblv17</bpmn:outgoing>

      <bpmn:endEvent id="Event_0dp7tjl">

        <bpmn:incoming>Flow_19visso</bpmn:incoming>

      </bpmn:endEvent>

      <bpmn:receiveTask completionQuantity="1" id="Activity_1lt79rp" implementation="##WebService" instantiate="false" isForCompensation="false" messageRef="Message_0f3j91c" name="Wait for state change" startQuantity="1">

        <bpmn:extensionElements>

          <camunda:inputOutput>

            <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was"
                        }</camunda:inputParameter>

            <camunda:inputParameter name="currentStepDetails">{ "required": false }</camunda:inputParameter>

            <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>

            <camunda:inputParameter name="targetApi">trigger</camunda:inputParameter>

          </camunda:inputOutput>

        </bpmn:extensionElements>

        <bpmn:incoming>Flow_0992u54</bpmn:incoming>

        <bpmn:outgoing>Flow_1l3fgfo</bpmn:outgoing>

      </bpmn:receiveTask>

      <bpmn:inclusiveGateway gatewayDirection="Unspecified" id="Gateway_0atk61r" name="approval actions">

        <bpmn:incoming>Flow_1gljm1r</bpmn:incoming>

        <bpmn:incoming>Flow_0gp2o0k</bpmn:incoming>

        <bpmn:incoming>Flow_05bmusq</bpmn:incoming>

        <bpmn:incoming>Flow_1a2cjta</bpmn:incoming>

        <bpmn:outgoing>Flow_0992u54</bpmn:outgoing>

      </bpmn:inclusiveGateway>

      <bpmn:inclusiveGateway gatewayDirection="Unspecified" id="Gateway_03a6pbp" name="Gateway">

        <bpmn:extensionElements>

          <camunda:properties>

            <camunda:property name="taskDetails" value="{ &quot;required&quot;: true }"/>

          </camunda:properties>

        </bpmn:extensionElements>

        <bpmn:incoming>Flow_0ol0n8g</bpmn:incoming>

        <bpmn:outgoing>Flow_1gljm1r</bpmn:outgoing>

        <bpmn:outgoing>Flow_0e1kzl7</bpmn:outgoing>

        <bpmn:outgoing>Flow_1ywa6zf</bpmn:outgoing>

        <bpmn:outgoing>Flow_1rwj3i1</bpmn:outgoing>

      </bpmn:inclusiveGateway>

      <bpmn:serviceTask camunda:topic="topic-yash" camunda:type="external" completionQuantity="1" id="Activity_18mm6ar" implementation="##WebService" isForCompensation="false" name="Auto reject transaction" startQuantity="1">

        <bpmn:extensionElements>

          <camunda:inputOutput>

            <camunda:inputParameter name="handlerDetails">{
    "taskHandler": "appconnect",
    "handlerId": "intuit-workflows/api/update-txn-status.json",
    "actionName": "executeDuzzitAction"
}</camunda:inputParameter>

            <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>

            <camunda:inputParameter name="parameterDetails">{
    "Id": {
        "fieldValue": [],
        "handlerFieldName": "txnId",
        "configurable": false,
        "actionByUI": null,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "txnStatus": {
        "fieldValue": [
            "WORKFLOW_AUTO_REJECTED"
        ],
        "configurable": false,
        "actionByUI": null,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string"
    },
    "intuit_realmid": {
        "fieldValue": [],
        "handlerFieldName": "realmId",
        "configurable": false,
        "actionByUI": null,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    }
}</camunda:inputParameter>

            <camunda:outputParameter name="outputResponse"/>

          </camunda:inputOutput>

        </bpmn:extensionElements>

        <bpmn:incoming>Flow_1u4tc4f</bpmn:incoming>

        <bpmn:outgoing>Flow_12v9vwb</bpmn:outgoing>

      </bpmn:serviceTask>

      <bpmn:exclusiveGateway default="Flow_124glp0" gatewayDirection="Unspecified" id="Gateway_1muwbx4" name="approved?">

        <bpmn:extensionElements>

          <camunda:properties>

            <camunda:property name="handlerDetails" value="{ &quot;taskHandler&quot;: &quot;appconnect&quot;,                         &quot;handlerId&quot;: &quot;1234&quot;,                         &quot;handlerName&quot;: &quot;sendEmail&quot;                         }"/>

            <camunda:property name="parameterDetails" value="{ &quot;{{txn}}.status&quot;: { &quot;fieldValue&quot; : [&quot;approved&quot;],  &quot;configurable&quot; : false,  &quot;actionByUI&quot; : null,  &quot;requiredByhandler&quot; : false,  &quot;requiredByUI&quot;: false,  &quot;multiSelect&quot;: false,  &quot;fieldType&quot;: &quot;string&quot;}}"/>

            <camunda:property name="taskDetails" value="{&quot;required&quot;: true}"/>

          </camunda:properties>

        </bpmn:extensionElements>

        <bpmn:incoming>Flow_1l3fgfo</bpmn:incoming>

        <bpmn:outgoing>Flow_124glp0</bpmn:outgoing>

        <bpmn:outgoing>Flow_003u0j7</bpmn:outgoing>

      </bpmn:exclusiveGateway>

      <bpmn:serviceTask camunda:topic="topic-yash" camunda:type="external" completionQuantity="1" id="Activity_06ul613" implementation="##WebService" isForCompensation="false" name="Close Project service task" startQuantity="1">

        <bpmn:extensionElements>

          <camunda:inputOutput>

            <camunda:inputParameter name="handlerDetails">{
              "taskHandler": "appconnect",
              "handlerId": "intuit-workflows/api/was-close-task-and-delete-constraint.json",
              "actionName": "executeDuzzitAction"
              }</camunda:inputParameter>

            <camunda:inputParameter name="parameterDetails">{
    "projectId": {
        "fieldValue": [],
        "handlerFieldName": "Project",
        "requiredByHandler": true,
        "requiredByUI": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "assigneeId": {
        "fieldValue": [],
        "handlerFieldName": "Assignee",
        "requiredByHandler": true,
        "requiredByUI": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "isConstraintAdded": {
        "fieldValue": [],
        "handlerFieldName": "isConstraintAdded",
        "requiredByHandler": true,
        "requiredByUI": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "Status": {
        "fieldValue": [
            "Complete"
        ],
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string"
    },
    "Id": {
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE",
        "handlerFieldName": "TxnId"
    },
    "entityType": {
        "handlerFieldName": "recordType",
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    }
}</camunda:inputParameter>

            <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>

          </camunda:inputOutput>

        </bpmn:extensionElements>

        <bpmn:incoming>Flow_003u0j7</bpmn:incoming>

        <bpmn:outgoing>Flow_10bjc0k</bpmn:outgoing>

      </bpmn:serviceTask>

      <bpmn:exclusiveGateway gatewayDirection="Unspecified" id="Gateway_1q29ueh">

        <bpmn:incoming>Flow_0smmcdm</bpmn:incoming>

        <bpmn:incoming>Flow_15gwvur</bpmn:incoming>

        <bpmn:outgoing>Flow_0k2lvew</bpmn:outgoing>

      </bpmn:exclusiveGateway>

      <bpmn:startEvent id="Event_0e2vuf5" isInterrupting="true" name="Request Approval" parallelMultiple="false">

        <bpmn:outgoing>Flow_0ol0n8g</bpmn:outgoing>

      </bpmn:startEvent>

      <bpmn:boundaryEvent attachedToRef="Activity_1lt79rp" cancelActivity="true" id="Event_04hv3j2" name="Expiry Event" parallelMultiple="false">

        <bpmn:outgoing>Flow_1u4tc4f</bpmn:outgoing>

        <bpmn:timerEventDefinition id="TimerEventDefinition_166r2ka">

          <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">PT5M</bpmn:timeDuration>

        </bpmn:timerEventDefinition>

      </bpmn:boundaryEvent>

      <bpmn:sequenceFlow id="Flow_0k2lvew" sourceRef="Gateway_1q29ueh" targetRef="Event_1xq3va2"/>

      <bpmn:sequenceFlow id="Flow_124glp0" sourceRef="Gateway_1muwbx4" targetRef="Activity_0ij0ka9"/>

      <bpmn:sequenceFlow id="Flow_12v9vwb" sourceRef="Activity_18mm6ar" targetRef="Activity_1hvnj93"/>

      <bpmn:sequenceFlow id="Flow_003u0j7" name="Yes" sourceRef="Gateway_1muwbx4" targetRef="Activity_06ul613">

        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${entityChangeType == 'approved'}</bpmn:conditionExpression>

      </bpmn:sequenceFlow>

      <bpmn:sequenceFlow id="Flow_0992u54" sourceRef="Gateway_0atk61r" targetRef="Activity_1lt79rp"/>

      <bpmn:sequenceFlow id="Flow_1l3fgfo" sourceRef="Activity_1lt79rp" targetRef="Gateway_1muwbx4"/>

      <bpmn:sequenceFlow id="Flow_1gljm1r" sourceRef="Gateway_03a6pbp" targetRef="Gateway_0atk61r">

        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${false}</bpmn:conditionExpression>

      </bpmn:sequenceFlow>

      <bpmn:sequenceFlow id="Flow_0e1kzl7" sourceRef="Gateway_03a6pbp" targetRef="sendCompanyEmail">

        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.hasVariable("sendCompanyEmail") == true}</bpmn:conditionExpression>

      </bpmn:sequenceFlow>

      <bpmn:sequenceFlow id="Flow_1ywa6zf" sourceRef="Gateway_03a6pbp" targetRef="createTask">

        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${true}</bpmn:conditionExpression>

      </bpmn:sequenceFlow>

      <bpmn:sequenceFlow id="Flow_1rwj3i1" sourceRef="Gateway_03a6pbp" targetRef="sendPushNotification">

        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.hasVariable("sendPushNotification") == true}</bpmn:conditionExpression>

      </bpmn:sequenceFlow>

      <bpmn:sequenceFlow id="Flow_1u4tc4f" sourceRef="Event_04hv3j2" targetRef="Activity_18mm6ar"/>

      <bpmn:sequenceFlow id="Flow_0ol0n8g" sourceRef="Event_0e2vuf5" targetRef="Gateway_03a6pbp"/>

      <bpmn:sequenceFlow id="Flow_10bjc0k" sourceRef="Activity_06ul613" targetRef="Activity_0l9blk5"/>

      <bpmn:serviceTask camunda:topic="topic-yash" camunda:type="external" completionQuantity="1" id="Activity_0ij0ka9" implementation="##WebService" isForCompensation="false" name="Send notification to creator of transaction" startQuantity="1">

        <bpmn:extensionElements>

          <camunda:inputOutput>

            <camunda:inputParameter name="handlerDetails">{
    "taskHandler": "appconnect",
    "handlerId": "intuit-workflows/api/was-send-notification-to-txn-creator.json",
    "actionName": "executeDuzzitAction"
}</camunda:inputParameter>

            <camunda:inputParameter name="parameterDetails">{
    "intuit_userid": {
        "handlerFieldName": "To",
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "Subject": {
        "fieldValue": [
            "~custom.approval.sendRejectNotification.txnApproval.Subject"
        ],
        "configurable": true,
        "actionByUI": null,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string"
    },
    "DocNumber": {
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE",
        "handlerFieldName": "DocNumber"
    },
    "entityType": {
        "handlerFieldName": "RecordType",
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "Message": {
        "fieldValue": [
            "~custom.approval.sendRejectNotification.txnApproval.Message"
        ],
        "configurable": true,
        "requiredByHandler": true,
        "requiredByUI": true,
        "multiSelect": false,
        "fieldType": "string"
    },
    "Id": {
        "handlerFieldName": "TxnId",
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "intuit_realmid": {
        "handlerFieldName": "realmId",
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "isEmail": {
        "fieldValue": [
            "true"
        ],
        "configurable": true,
        "requiredByHandler": true,
        "requiredByUI": true,
        "multiSelect": false,
        "fieldType": "boolean"
    },
    "isMobile": {
        "fieldValue": [
            "true"
        ],
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": true,
        "multiSelect": false,
        "fieldType": "boolean"
    },
    "CC": {
        "configurable": true,
        "requiredByHandler": true,
        "requiredByUI": true,
        "multiSelect": false,
        "fieldType": "string"
    },
    "BCC": {
        "configurable": true,
        "requiredByHandler": true,
        "requiredByUI": true,
        "helpVariables": [
            "Customer Email"
        ],
        "multiSelect": false,
        "fieldType": "string"
    },
    "NotificationAction": {
        "fieldValue": [
            "qb001://open/{{RecordType}}/?id=[[{{RecordType}}LocalId]]&amp;companyid=[[realmId]]"
        ],
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "handlerFieldName": "Mobile Notification Action"
    },
    "CompanyName": {
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE",
        "handlerFieldName": "CompanyName"
    }
}</camunda:inputParameter>

            <camunda:inputParameter name="taskDetails">{ "required": false }</camunda:inputParameter>

            <camunda:outputParameter name="outputResponse"/>

          </camunda:inputOutput>

        </bpmn:extensionElements>

        <bpmn:incoming>Flow_124glp0</bpmn:incoming>

        <bpmn:outgoing>Flow_0smmcdm</bpmn:outgoing>

      </bpmn:serviceTask>

      <bpmn:sequenceFlow id="Flow_0smmcdm" sourceRef="Activity_0ij0ka9" targetRef="Gateway_1q29ueh"/>

      <bpmn:serviceTask camunda:topic="topic-yash" camunda:type="external" completionQuantity="1" id="Activity_0l9blk5" implementation="##WebService" isForCompensation="false" name="Send notification to creator of transaction" startQuantity="1">

        <bpmn:extensionElements>

          <camunda:inputOutput>

            <camunda:inputParameter name="handlerDetails">{
    "taskHandler": "appconnect",
    "handlerId": "intuit-workflows/api/was-send-notification-to-txn-creator.json",
    "actionName": "executeDuzzitAction"
}</camunda:inputParameter>

            <camunda:inputParameter name="parameterDetails">{
    "intuit_userid": {
        "handlerFieldName": "To",
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "Subject": {
        "fieldValue": [
            "~custom.approval.sendApproveNotification.txnApproval.Subject"
        ],
        "configurable": true,
        "actionByUI": null,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string"
    },
    "DocNumber": {
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE",
        "handlerFieldName": "DocNumber"
    },
    "entityType": {
        "handlerFieldName": "RecordType",
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "Message": {
        "fieldValue": [
            "~custom.approval.sendApproveNotification.txnApproval.Message"
        ],
        "configurable": true,
        "requiredByHandler": true,
        "requiredByUI": true,
        "multiSelect": false,
        "fieldType": "string"
    },
    "Id": {
        "handlerFieldName": "TxnId",
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "intuit_realmid": {
        "handlerFieldName": "realmId",
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "isEmail": {
        "fieldValue": [
            "true"
        ],
        "configurable": true,
        "requiredByHandler": true,
        "requiredByUI": true,
        "multiSelect": false,
        "fieldType": "boolean"
    },
    "isMobile": {
        "fieldValue": [
            "true"
        ],
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": true,
        "multiSelect": false,
        "fieldType": "boolean"
    },
    "CC": {
        "configurable": true,
        "requiredByHandler": true,
        "requiredByUI": true,
        "multiSelect": false,
        "fieldType": "string"
    },
    "BCC": {
        "configurable": true,
        "requiredByHandler": true,
        "requiredByUI": true,
        "helpVariables": [
            "Customer Email"
        ],
        "multiSelect": false,
        "fieldType": "string"
    },
    "NotificationAction": {
        "fieldValue": [
            "qb001://open/{{RecordType}}/?id=[[{{RecordType}}LocalId]]&amp;companyid=[[realmId]]"
        ],
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "handlerFieldName": "Mobile Notification Action"
    },
    "CompanyName": {
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE",
        "handlerFieldName": "CompanyName"
    }
}</camunda:inputParameter>

            <camunda:inputParameter name="taskDetails">{ "required": false }</camunda:inputParameter>

            <camunda:outputParameter name="outputResponse"/>

          </camunda:inputOutput>

        </bpmn:extensionElements>

        <bpmn:incoming>Flow_10bjc0k</bpmn:incoming>

        <bpmn:outgoing>Flow_19visso</bpmn:outgoing>

      </bpmn:serviceTask>

      <bpmn:sequenceFlow id="Flow_19visso" sourceRef="Activity_0l9blk5" targetRef="Event_0dp7tjl"/>

      <bpmn:serviceTask camunda:topic="topic-yash" camunda:type="external" completionQuantity="1" id="Activity_1hvnj93" implementation="##WebService" isForCompensation="false" name="Send notification to creator of transaction" startQuantity="1">

        <bpmn:extensionElements>

          <camunda:inputOutput>

            <camunda:inputParameter name="handlerDetails">{
    "taskHandler": "appconnect",
    "handlerId": "intuit-workflows/api/was-send-notification-to-txn-creator.json",
    "actionName": "executeDuzzitAction"
}</camunda:inputParameter>

            <camunda:inputParameter name="parameterDetails">{
    "intuit_userid": {
        "handlerFieldName": "To",
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "Subject": {
        "fieldValue": [
            "~custom.approval.sendAutoRejectNotification.txnApproval.Subject"
        ],
        "configurable": true,
        "actionByUI": null,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string"
    },
    "DocNumber": {
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE",
        "handlerFieldName": "DocNumber"
    },
    "entityType": {
        "handlerFieldName": "RecordType",
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "Message": {
        "fieldValue": [
            "~custom.approval.sendAutoRejectNotification.txnApproval.Message"
        ],
        "configurable": true,
        "requiredByHandler": true,
        "requiredByUI": true,
        "multiSelect": false,
        "fieldType": "string"
    },
    "Id": {
        "handlerFieldName": "TxnId",
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "intuit_realmid": {
        "handlerFieldName": "realmId",
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "isEmail": {
        "fieldValue": [
            "true"
        ],
        "configurable": true,
        "requiredByHandler": true,
        "requiredByUI": true,
        "multiSelect": false,
        "fieldType": "boolean"
    },
    "isMobile": {
        "fieldValue": [
            "true"
        ],
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": true,
        "multiSelect": false,
        "fieldType": "boolean"
    },
    "CC": {
        "configurable": true,
        "requiredByHandler": true,
        "requiredByUI": true,
        "multiSelect": false,
        "fieldType": "string"
    },
    "BCC": {
        "configurable": true,
        "requiredByHandler": true,
        "requiredByUI": true,
        "helpVariables": [
            "Customer Email"
        ],
        "multiSelect": false,
        "fieldType": "string"
    },
    "NotificationAction": {
        "fieldValue": [
            "qb001://open/{{RecordType}}/?id=[[{{RecordType}}LocalId]]&amp;companyid=[[realmId]]"
        ],
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "handlerFieldName": "Mobile Notification Action"
    },
    "CompanyName": {
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE",
        "handlerFieldName": "CompanyName"
    }
}</camunda:inputParameter>

            <camunda:inputParameter name="taskDetails">{ "required": false }</camunda:inputParameter>

            <camunda:outputParameter name="outputResponse"/>

          </camunda:inputOutput>

        </bpmn:extensionElements>

        <bpmn:incoming>Flow_12v9vwb</bpmn:incoming>

        <bpmn:outgoing>Flow_15gwvur</bpmn:outgoing>

      </bpmn:serviceTask>

      <bpmn:sequenceFlow id="Flow_15gwvur" sourceRef="Activity_1hvnj93" targetRef="Gateway_1q29ueh"/>

      <bpmn:serviceTask camunda:topic="topic-yash" camunda:type="external" completionQuantity="1" id="createTask" implementation="##WebService" isForCompensation="false" name="Create Project Service task" startQuantity="1">

        <bpmn:extensionElements>

          <camunda:inputOutput>

            <camunda:inputParameter name="handlerDetails">{}</camunda:inputParameter>

            <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>

            <camunda:inputParameter name="taskDetails">{ "required": true}</camunda:inputParameter>

          </camunda:inputOutput>

        </bpmn:extensionElements>

        <bpmn:incoming>Flow_1ywa6zf</bpmn:incoming>

        <bpmn:outgoing>Flow_0gp2o0k</bpmn:outgoing>

      </bpmn:serviceTask>

      <bpmn:sequenceFlow id="Flow_0gp2o0k" sourceRef="createTask" targetRef="Gateway_0atk61r"/>

      <bpmn:sendTask camunda:topic="topic-yash" camunda:type="external" completionQuantity="1" id="sendCompanyEmail" implementation="##WebService" isForCompensation="false" name="Send Company Emai" startQuantity="1">

        <bpmn:extensionElements>

          <camunda:inputOutput>

            <camunda:inputParameter name="handlerDetails">{}</camunda:inputParameter>

            <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>

            <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>

            <camunda:outputParameter name="outputRsponse"/>

          </camunda:inputOutput>

        </bpmn:extensionElements>

        <bpmn:incoming>Flow_0e1kzl7</bpmn:incoming>

        <bpmn:outgoing>Flow_05bmusq</bpmn:outgoing>

      </bpmn:sendTask>

      <bpmn:sequenceFlow id="Flow_05bmusq" sourceRef="sendCompanyEmail" targetRef="Gateway_0atk61r"/>

      <bpmn:sendTask camunda:topic="topic-yash" camunda:type="external" completionQuantity="1" id="sendPushNotification" implementation="##WebService" isForCompensation="false" name="Send Push Notification" startQuantity="1">

        <bpmn:extensionElements>

          <camunda:inputOutput>

            <camunda:inputParameter name="handlerDetails">{}</camunda:inputParameter>

            <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>

            <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>

            <camunda:outputParameter name="outputRsponse"/>

          </camunda:inputOutput>

        </bpmn:extensionElements>

        <bpmn:incoming>Flow_1rwj3i1</bpmn:incoming>

        <bpmn:outgoing>Flow_1a2cjta</bpmn:outgoing>

      </bpmn:sendTask>

      <bpmn:sequenceFlow id="Flow_1a2cjta" sourceRef="sendPushNotification" targetRef="Gateway_0atk61r"/>

      <bpmn:intermediateThrowEvent id="Event_1xq3va2" name="Close remaining tasks">

        <bpmn:incoming>Flow_0k2lvew</bpmn:incoming>

        <bpmn:escalationEventDefinition escalationRef="Escalation_0wszo57" id="EscalationEventDefinition_12s9rdh"/>

      </bpmn:intermediateThrowEvent>

    </bpmn:subProcess>

    <bpmn:sequenceFlow id="Flow_0qz7tvz" sourceRef="startEvent" targetRef="Activity_1ppmouy"/>

    <bpmn:sequenceFlow id="Flow_1fblv17" sourceRef="Activity_1ppmouy" targetRef="Event_03phske"/>

    <bpmn:subProcess completionQuantity="1" id="Activity_1u0xws3" isForCompensation="false" name="deleted voided disable handler subprocess" startQuantity="1" triggeredByEvent="true">

      <bpmn:startEvent id="Event_1f63nkr" isInterrupting="true" name="deleted voided disable invoiceapproval" parallelMultiple="false">

        <bpmn:outgoing>Flow_1anlwvt</bpmn:outgoing>

        <bpmn:messageEventDefinition id="MessageEventDefinition_1mrpg46" messageRef="Message_1xepa7t"/>

      </bpmn:startEvent>

      <bpmn:endEvent id="Event_17kcx6z" name="Invoice deleted or voided or disable">

        <bpmn:extensionElements>

          <camunda:inputOutput>

            <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>

          </camunda:inputOutput>

        </bpmn:extensionElements>

        <bpmn:incoming>Flow_1anlwvt</bpmn:incoming>

        <bpmn:escalationEventDefinition escalationRef="Escalation_0wszo57" id="EscalationEventDefinition_02n3xxr"/>

      </bpmn:endEvent>

      <bpmn:sequenceFlow id="Flow_1anlwvt" sourceRef="Event_1f63nkr" targetRef="Event_17kcx6z"/>

    </bpmn:subProcess>

    <bpmn:subProcess completionQuantity="1" id="Activity_1kyn5tu" isForCompensation="false" startQuantity="1" triggeredByEvent="true">

      <bpmn:serviceTask camunda:topic="topic-yash" camunda:type="external" completionQuantity="1" id="Activity_0rmr4cj" implementation="##WebService" isForCompensation="false" name="Close All Project service task" startQuantity="1">

        <bpmn:extensionElements>

          <camunda:inputOutput>

            <camunda:inputParameter name="handlerDetails">{
              "taskHandler": "appconnect",
              "handlerId": "intuit-workflows/api/was-close-task-and-delete-constraint.json",
              "actionName": "executeDuzzitAction"
              }</camunda:inputParameter>

            <camunda:inputParameter name="parameterDetails">{
    "projectId": {
        "fieldValue": [],
        "handlerFieldName": "Project",
        "requiredByHandler": true,
        "requiredByUI": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "assigneeId": {
        "fieldValue": [],
        "handlerFieldName": "Assignee",
        "requiredByHandler": true,
        "requiredByUI": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "isConstraintAdded": {
        "fieldValue": [],
        "handlerFieldName": "isConstraintAdded",
        "requiredByHandler": true,
        "requiredByUI": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "Status": {
        "fieldValue": [
            "Complete"
        ],
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string"
    },
    "Id": {
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE",
        "handlerFieldName": "TxnId"
    },
    "entityType": {
        "handlerFieldName": "recordType",
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    }
}</camunda:inputParameter>

            <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>

          </camunda:inputOutput>

        </bpmn:extensionElements>

        <bpmn:incoming>Flow_1n1blyw</bpmn:incoming>

        <bpmn:outgoing>Flow_0n2pu4u</bpmn:outgoing>

      </bpmn:serviceTask>

      <bpmn:endEvent id="Event_0i0ttf2" name="Invoice approval process ended">

        <bpmn:extensionElements>

          <camunda:inputOutput>

            <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "handlerId": "",
                        "actionName": "updateProcessStatus"
                        }</camunda:inputParameter>

            <camunda:inputParameter name="visibleToUI">false</camunda:inputParameter>

          </camunda:inputOutput>

        </bpmn:extensionElements>

        <bpmn:incoming>Flow_0n2pu4u</bpmn:incoming>

        <bpmn:messageEventDefinition camunda:topic="topic-yash" camunda:type="external" id="MessageEventDefinition_06a8jg8" messageRef="Message_04e5l5s"/>

      </bpmn:endEvent>

      <bpmn:sequenceFlow id="Flow_1n1blyw" sourceRef="Event_1gw7eg0" targetRef="Activity_0rmr4cj"/>

      <bpmn:sequenceFlow id="Flow_0n2pu4u" sourceRef="Activity_0rmr4cj" targetRef="Event_0i0ttf2"/>

      <bpmn:startEvent id="Event_1gw7eg0" isInterrupting="false" name="Close project event" parallelMultiple="false">

        <bpmn:outgoing>Flow_1n1blyw</bpmn:outgoing>

        <bpmn:escalationEventDefinition camunda:escalationCodeVariable="closeTasks" escalationRef="Escalation_0wszo57" id="EscalationEventDefinition_183kv99"/>

      </bpmn:startEvent>

    </bpmn:subProcess>

    <bpmn:endEvent id="Event_03phske" name="Invoice approval process ended">

      <bpmn:extensionElements>

        <camunda:inputOutput>

          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "handlerId": "",
                        "actionName": "updateProcessStatus"
                        }</camunda:inputParameter>

          <camunda:inputParameter name="visibleToUI">false</camunda:inputParameter>

        </camunda:inputOutput>

      </bpmn:extensionElements>

      <bpmn:incoming>Flow_1fblv17</bpmn:incoming>

      <bpmn:messageEventDefinition camunda:topic="topic-yash" camunda:type="external" id="MessageEventDefinition_05g2pgu" messageRef="Message_04e5l5s"/>

    </bpmn:endEvent>

  </bpmn:process>

  <bpmn:message id="Message_04gc93u" name="TEST"/>

  <bpmn:message id="Message_06k2nec" name="REQUEST_APPROVAL"/>

  <bpmn:message id="Message_1oeiv49" name="RECEIVED_APPROVAL"/>

  <bpmn:escalation id="Escalation_0t4671p" name="AUTO_EXPIRE"/>

  <bpmn:escalation escalationCode="REQUEST_APPROVAL" id="Escalation_0sx6ll1" name="REQUEST_APPROVAL"/>

  <bpmn:escalation escalationCode="CLOSE_TASK" id="Escalation_1ihxosq" name="CLOSE_TASK"/>

  <bpmn:message id="Message_15y8oxp" name="approved_${approvalRequestId_authId}"/>

  <bpmn:message id="Message_0f3j91c" name="approved_rejected"/>

  <bpmn:message id="Message_14trj2a" name="REQUEST_APPROVAL"/>

  <bpmn:message id="Message_0ln1uhp" name="closeTasks"/>

  <bpmn:message id="Message_1xepa7t" name="deleted_voided_disable"/>

  <bpmn:escalation escalationCode="closeTasks" id="Escalation_0wszo57" name="close_tasks"/>

  <bpmn:message id="Message_04e5l5s" name="process_ended_message"/>

  <bpmn:message id="Message_00ykg0g" name="closeTasks"/>

  <bpmndi:BPMNDiagram id="BPMNDiagram_1">

    <bpmndi:BPMNPlane bpmnElement="sendForApproval" id="BPMNPlane_1">

      <bpmndi:BPMNEdge bpmnElement="Flow_1fblv17" id="Flow_1fblv17_di">

        <di:waypoint x="1513" y="345"/>

        <di:waypoint x="1612" y="345"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_0qz7tvz" id="Flow_0qz7tvz_di">

        <di:waypoint x="158" y="345"/>

        <di:waypoint x="340" y="345"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNShape bpmnElement="startEvent" id="Event_1etobiw_di">

        <dc:Bounds height="36" width="36" x="122" y="327"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="50" x="116" y="370"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bioc:fill="rgb(255, 224, 178)" bioc:stroke="rgb(251, 140, 0)" bpmnElement="Activity_1ppmouy" id="Activity_1u4pk4j_di" isExpanded="true">

        <dc:Bounds height="520" width="1173" x="340" y="85"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNEdge bpmnElement="Flow_1a2cjta" id="Flow_1a2cjta_di">

        <di:waypoint x="730" y="480"/>

        <di:waypoint x="870" y="480"/>

        <di:waypoint x="870" y="365"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_05bmusq" id="Flow_05bmusq_di">

        <di:waypoint x="730" y="340"/>

        <di:waypoint x="845" y="340"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_0gp2o0k" id="Flow_0gp2o0k_di">

        <di:waypoint x="730" y="180"/>

        <di:waypoint x="870" y="180"/>

        <di:waypoint x="870" y="315"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_15gwvur" id="Flow_15gwvur_di">

        <di:waypoint x="1211" y="460"/>

        <di:waypoint x="1211" y="365"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_19visso" id="Flow_19visso_di">

        <di:waypoint x="1382" y="190"/>

        <di:waypoint x="1382" y="262"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_0smmcdm" id="Flow_0smmcdm_di">

        <di:waypoint x="1211" y="280"/>

        <di:waypoint x="1211" y="315"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_10bjc0k" id="Flow_10bjc0k_di">

        <di:waypoint x="1261" y="145"/>

        <di:waypoint x="1332" y="145"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_0ol0n8g" id="Flow_0ol0n8g_di">

        <di:waypoint x="408" y="350"/>

        <di:waypoint x="460" y="350"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_1u4tc4f" id="Flow_1u4tc4f_di">

        <di:waypoint x="1060" y="398"/>

        <di:waypoint x="1060" y="460"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_1rwj3i1" id="Flow_1rwj3i1_di">

        <di:waypoint x="485" y="375"/>

        <di:waypoint x="485" y="480"/>

        <di:waypoint x="630" y="480"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_1ywa6zf" id="Flow_1ywa6zf_di">

        <di:waypoint x="485" y="325"/>

        <di:waypoint x="485" y="180"/>

        <di:waypoint x="630" y="180"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_0e1kzl7" id="Flow_0e1kzl7_di">

        <di:waypoint x="510" y="350"/>

        <di:waypoint x="630" y="350"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_1gljm1r" id="Flow_1gljm1r_di">

        <di:waypoint x="485" y="375"/>

        <di:waypoint x="485" y="560"/>

        <di:waypoint x="870" y="560"/>

        <di:waypoint x="870" y="365"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_1l3fgfo" id="Flow_1l3fgfo_di">

        <di:waypoint x="1060" y="300"/>

        <di:waypoint x="1060" y="265"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_0992u54" id="Flow_0992u54_di">

        <di:waypoint x="895" y="340"/>

        <di:waypoint x="1010" y="340"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_003u0j7" id="Flow_003u0j7_di">

        <di:waypoint x="1060" y="215"/>

        <di:waypoint x="1060" y="145"/>

        <di:waypoint x="1161" y="145"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="18" x="1066" y="177"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_12v9vwb" id="Flow_12v9vwb_di">

        <di:waypoint x="1110" y="500"/>

        <di:waypoint x="1161" y="500"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_124glp0" id="Flow_124glp0_di">

        <di:waypoint x="1085" y="240"/>

        <di:waypoint x="1161" y="240"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_0k2lvew" id="Flow_0k2lvew_di">

        <di:waypoint x="1236" y="340"/>

        <di:waypoint x="1283" y="340"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNShape bpmnElement="Event_0dp7tjl" id="Event_0dp7tjl_di">

        <dc:Bounds height="36" width="36" x="1364" y="262"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Activity_1lt79rp" id="Activity_1lt79rp_di">

        <dc:Bounds height="80" width="100" x="1010" y="300"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Gateway_0atk61r" id="Gateway_0atk61r_di">

        <dc:Bounds height="50" width="50" x="845" y="315"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="81" x="879" y="353"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Gateway_03a6pbp" id="Gateway_03a6pbp_di">

        <dc:Bounds height="50" width="50" x="460" y="325"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="44" x="438" y="303"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Activity_18mm6ar" id="Activity_18mm6ar_di">

        <dc:Bounds height="80" width="100" x="1010" y="460"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Gateway_1muwbx4" id="Gateway_1muwbx4_di" isMarkerVisible="true">

        <dc:Bounds height="50" width="50" x="1035" y="215"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="52" x="994" y="252"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Activity_06ul613" id="Activity_06ul613_di">

        <dc:Bounds height="80" width="100" x="1161" y="105"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Gateway_1q29ueh" id="Gateway_1q29ueh_di" isMarkerVisible="true">

        <dc:Bounds height="50" width="50" x="1186" y="315"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Event_0e2vuf5" id="Event_1gsekun_di">

        <dc:Bounds height="36" width="36" x="372" y="332"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="88" x="348" y="375"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Activity_0ij0ka9" id="Activity_0ij0ka9_di">

        <dc:Bounds height="80" width="100" x="1161" y="200"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Activity_0l9blk5" id="Activity_0l9blk5_di">

        <dc:Bounds height="80" width="100" x="1332" y="110"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Activity_1hvnj93" id="Activity_1hvnj93_di">

        <dc:Bounds height="80" width="100" x="1161" y="460"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="createTask" id="Activity_1q46sw1_di">

        <dc:Bounds height="80" width="100" x="630" y="140"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="sendCompanyEmail" id="Activity_12vn37y_di">

        <dc:Bounds height="80" width="100" x="630" y="310"/>

        <bpmndi:BPMNLabel/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="sendPushNotification" id="Activity_03elim0_di">

        <dc:Bounds height="80" width="100" x="630" y="440"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Event_1xq3va2" id="Event_170nqgj_di">

        <dc:Bounds height="36" width="36" x="1283" y="322"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="27" width="80" x="1261" y="365"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Event_04hv3j2" id="Event_04hv3j2_di">

        <dc:Bounds height="36" width="36" x="1042" y="362"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="14" width="62" x="1029" y="405"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Activity_1u0xws3" id="Activity_1u0xws3_di" isExpanded="true">

        <dc:Bounds height="130" width="340" x="480" y="650"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNEdge bpmnElement="Flow_1anlwvt" id="Flow_1anlwvt_di">

        <di:waypoint x="558" y="700"/>

        <di:waypoint x="732" y="700"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNShape bpmnElement="Event_1f63nkr" id="Event_1f63nkr_di">

        <dc:Bounds height="36" width="36" x="522" y="682"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="40" width="77" x="505" y="725"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Event_17kcx6z" id="Event_17kcx6z_di">

        <dc:Bounds height="36" width="36" x="732" y="682"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="27" width="87" x="706" y="726"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Activity_1kyn5tu" id="Activity_1kyn5tu_di" isExpanded="true">

        <dc:Bounds height="130" width="350" x="930" y="650"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNEdge bpmnElement="Flow_0n2pu4u" id="Flow_0n2pu4u_di">

        <di:waypoint x="1155" y="712"/>

        <di:waypoint x="1207" y="712"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge bpmnElement="Flow_1n1blyw" id="Flow_1n1blyw_di">

        <di:waypoint x="993" y="712"/>

        <di:waypoint x="1055" y="712"/>

      </bpmndi:BPMNEdge>

      <bpmndi:BPMNShape bpmnElement="Activity_0rmr4cj" id="Activity_0rmr4cj_di">

        <dc:Bounds height="80" width="100" x="1055" y="672"/>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Event_0i0ttf2" id="Event_0i0ttf2_di">

        <dc:Bounds height="36" width="36" x="1207" y="694"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="27" width="81" x="1185" y="737"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Event_1gw7eg0" id="Event_1wqw3mm_di">

        <dc:Bounds height="36" width="36" x="957" y="694"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="27" width="65" x="943" y="737"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

      <bpmndi:BPMNShape bpmnElement="Event_03phske" id="Event_03phske_di">

        <dc:Bounds height="36" width="36" x="1612" y="327"/>

        <bpmndi:BPMNLabel>

          <dc:Bounds height="27" width="81" x="1590" y="370"/>

        </bpmndi:BPMNLabel>

      </bpmndi:BPMNShape>

    </bpmndi:BPMNPlane>

  </bpmndi:BPMNDiagram>

</bpmn:definitions>