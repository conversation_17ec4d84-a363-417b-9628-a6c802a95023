<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1ohwrid" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.4.0">
    <bpmn:process id="engagementTestConsumer15" name="engagementTestConsumer15" isExecutable="true" camunda:historyTimeToLive="25">
        <bpmn:serviceTask id="Activity_0sjsbeq" name="Get Engagement State" camunda:type="external" camunda:topic="engagement">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler":"was",
                        "handlerId":"intuit-workflows/vep-int-engagement-service",
                        "actionName":"publishEvent",
                        "handlerScope":"test"
                        }</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1xodyzm</bpmn:incoming>
            <bpmn:outgoing>Flow_1n6e07e</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:endEvent id="Event_0d8m0y2">
            <bpmn:incoming>Flow_1n6e07e</bpmn:incoming>
            <bpmn:terminateEventDefinition id="TerminateEventDefinition_0h4k6jt" />
        </bpmn:endEvent>
        <bpmn:sequenceFlow id="Flow_1n6e07e" sourceRef="Activity_0sjsbeq" targetRef="Event_0d8m0y2" />
        <bpmn:startEvent id="startEvent" name="Engagement Start">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="handlerDetails" value="{ &#34;recordType&#34;: &#34;engagement&#34;}" />
                    <camunda:property name="stepDetails" value="{   &#34;startEvent&#34;: [     &#34;startEvent&#34;   ] }" />
                    <camunda:property name="startableEvents" value="[&#34;created&#34;]" />
                    <camunda:property name="processVariablesDetails" value="[   {     &#34;variableName&#34;: &#34;engagementId&#34;,     &#34;variableType&#34;: &#34;String&#34;   } ]" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:outgoing>Flow_1xodyzm</bpmn:outgoing>
        </bpmn:startEvent>
        <bpmn:sequenceFlow id="Flow_1xodyzm" sourceRef="startEvent" targetRef="Activity_0sjsbeq" />
    </bpmn:process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_1">
        <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="engagementTestConsumer15">
            <bpmndi:BPMNEdge id="Flow_1xodyzm_di" bpmnElement="Flow_1xodyzm">
                <di:waypoint x="288" y="160" />
                <di:waypoint x="410" y="160" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1n6e07e_di" bpmnElement="Flow_1n6e07e">
                <di:waypoint x="510" y="160" />
                <di:waypoint x="672" y="160" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="Activity_0sjsbeq_di" bpmnElement="Activity_0sjsbeq">
                <dc:Bounds x="410" y="120" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_0d8m0y2_di" bpmnElement="Event_0d8m0y2">
                <dc:Bounds x="672" y="142" width="36" height="36" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_0lu2mbv_di" bpmnElement="startEvent">
                <dc:Bounds x="252" y="142" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="153" y="140" width="89" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</bpmn:definitions>
