<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:tns="http://bpmn.io/schema/bpmn" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:yaoqiang="http://bpmn.sourceforge.net" exporter="Camunda Modeler" exporterVersion="3.4.1" expressionLanguage="http://www.w3.org/1999/XPath" id="Definitions_05fq6hu" name="" targetNamespace="http://bpmn.io/schema/bpmn" typeLanguage="http://www.w3.org/2001/XMLSchema" xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL http://bpmn.sourceforge.net/schemas/BPMN20.xsd">
  <bpmn:message id="Message_0997x1n" name="MApproval message"/>
  <bpmn:process id="InvoiceApproval_123456789012" isClosed="false" isExecutable="true" processType="None">
    <bpmn:extensionElements>
      <yaoqiang:description/>
      <yaoqiang:pageFormat height="841.8897637795276" imageableHeight="831.8897637795276" imageableWidth="588.1102362204724" imageableX="5.0" imageableY="5.0" orientation="0" width="598.1102362204724"/>
      <yaoqiang:page background="#FFFFFF" horizontalCount="1" verticalCount="1"/>
    </bpmn:extensionElements>
    <bpmn:startEvent id="StartEvent_1" isInterrupting="true" name="StartEventInvoiceApproval" parallelMultiple="false">
      <bpmn:outgoing>SequenceFlow_04bferx</bpmn:outgoing>
      <bpmn:outputSet/>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="SequenceFlow_04bferx" sourceRef="StartEvent_1" targetRef="invoiceApproveDecisionTable"/>
    <bpmn:businessRuleTask camunda:decisionRef="InvoiceSendDecisionTest" camunda:mapDecisionResult="singleResult" camunda:resultVariable="sIResult" completionQuantity="1" id="invoiceApproveDecisionTable" implementation="##unspecified" isForCompensation="false" name="decision for sending  invoice" startQuantity="1">
      <bpmn:incoming>SequenceFlow_04bferx</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1wrluje</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1f1c42w</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0fck39f</bpmn:outgoing>
    </bpmn:businessRuleTask>
    <bpmn:sequenceFlow id="SequenceFlow_1wrluje" sourceRef="invoiceApproveDecisionTable" targetRef="appConnect_sendAdminMail">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression"><![CDATA[${ sIResult.sendEmail == true}]]></bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_1f1c42w" sourceRef="invoiceApproveDecisionTable" targetRef="appConnect_sendAdminNotification">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression"><![CDATA[${ sIResult.sendSlackNotification == true}]]></bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0fck39f" sourceRef="invoiceApproveDecisionTable" targetRef="appConnect_updateInvoice">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression"><![CDATA[${ sIResult.updateInvoicePending == true}]]></bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask camunda:class="com.intuit.v4.appintgwkflw.wkflautomate.wkflatmnsvcapp.camunda.CamundaTaskDelegate" camunda:modelerTemplate="com.intuit.v4.appintgwkflw.wkflautomate.wkflatmnsvcapp.camunda.delegate" completionQuantity="1" id="appConnect_sendAdminMail" implementation="##WebService" isForCompensation="false" name="Send email for admin approval" startQuantity="1">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <inputOutput>
            <camunda:inputParameter name="ACTION_ID">
              <inputParameter>sendEmail</inputParameter>
            </camunda:inputParameter>
            <camunda:inputParameter name="ACTION_URL">
              <inputParameter>https://stage.connector.appconnect.intuit.com/504916/api/send-html-email.json</inputParameter>
            </camunda:inputParameter>
            <camunda:inputParameter name="QUERY_Token">
              <inputParameter>k1n4k0di2rcy2jd</inputParameter>
            </camunda:inputParameter>
            <camunda:inputParameter name="QUERY_Message">
              <inputParameter>
                <camunda:script scriptFormat="freemarker">
                  <script>&amp;lt;html&amp;gt;
            &amp;lt;p&amp;gt;Hi,&amp;lt;/p&amp;gt;
            &amp;lt;p&amp;gt;Approve or reject the estimate with below details &amp;lt;/p&amp;gt;
            &amp;lt;p&amp;gt;--------------------------------------------------&amp;lt;/p&amp;gt;
            &amp;lt;p&amp;gt;Id       - ${id?c}&amp;lt;/p&amp;gt;
            &amp;lt;p&amp;gt;Amount   - ${amount}&amp;lt;/p&amp;gt;
            &amp;lt;p&amp;gt;Quantity - ${quantity}&amp;lt;/p&amp;gt;
            &amp;lt;p&amp;gt;Date     - ${estimateDate?date}&amp;lt;/p&amp;gt;
            &amp;lt;p&amp;gt;--------------------------------------------------&amp;lt;/p&amp;gt;

            &amp;lt;table width="100%" cellspacing="0" cellpadding="0"&amp;gt;
              &amp;lt;tr&amp;gt;
                  &amp;lt;td&amp;gt;
                      &amp;lt;table cellspacing="0" cellpadding="0"&amp;gt;
                          &amp;lt;tr&amp;gt;
                              &amp;lt;td style="border-radius: 2px;" bgcolor="#008000" padding: 10px&amp;gt;
                                  &amp;lt;a href=https://qboavworkflow-qal.api.intuit.com/v1/redirect?&amp;amp;isApproved=true&amp;amp;estimateId=${id?c}   target="_blank" style="padding: 8px 12px; border: 1px solid #008000;border-radius: 2px;font-family: Helvetica, Arial, sans-serif;font-size: 14px; color: #ffffff;text-decoration: none;font-weight:bold;display: inline-block;"&amp;gt;
                                      Approve
                                  &amp;lt;/a&amp;gt;
                              &amp;lt;/td&amp;gt;
                              &amp;lt;td padding: 10px&amp;gt; &amp;lt;a style="padding: 8px 12px;  border-radius: 2px;font-family: Helvetica, Arial, sans-serif;font-size: 14px; color: #ffffff;text-decoration: none;font-weight:bold;display: inline-block;"&amp;gt;
                              &amp;lt;/a&amp;gt;&amp;lt;/td&amp;gt;
                              &amp;lt;td style="border-radius: 2px;" bgcolor="#ED2939" padding: 10px&amp;gt;
                                  &amp;lt;a href=https://qboavworkflow-qal.api.intuit.com/v1/redirect?&amp;amp;isApproved=false&amp;amp;estimateId=${id?c}  target="_blank" style="padding: 8px 12px; border: 1px solid #ED2939;border-radius: 2px;font-family: Helvetica, Arial, sans-serif;font-size: 14px; color: #ffffff;text-decoration: none;font-weight:bold;display: inline-block;"&amp;gt;
                                      Reject
                                  &amp;lt;/a&amp;gt;
                              &amp;lt;/td&amp;gt;
                          &amp;lt;/tr&amp;gt;
                      &amp;lt;/table&amp;gt;
                  &amp;lt;/td&amp;gt;
              &amp;lt;/tr&amp;gt;
            &amp;lt;/table&amp;gt;
&amp;lt;/html&amp;gt;</script>
                </camunda:script>
              </inputParameter>
            </camunda:inputParameter>
            <camunda:inputParameter name="schema">
              <inputParameter>ACTION_ID,ACTION_URL,QUERY_Token~QUERY_To,QUERY_Subject~OUTPUT_response</inputParameter>
            </camunda:inputParameter>
            <camunda:inputParameter name="QUERY_To">
              <inputParameter>${appConnect_sendAdminMail.to}</inputParameter>
            </camunda:inputParameter>
            <camunda:inputParameter name="QUERY_Subject">
              <inputParameter>${appConnect_sendAdminMail.subject}</inputParameter>
            </camunda:inputParameter>
            <camunda:outputParameter name="${appConnect_sendAdminMail.response}">
              <outputParameter>OUTPUT_response</outputParameter>
            </camunda:outputParameter>
          </inputOutput>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_1wrluje</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0lajwhe</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_0lajwhe" sourceRef="appConnect_sendAdminMail" targetRef="ExclusiveGateway_0688gfd"/>
    <bpmn:sequenceFlow id="SequenceFlow_19h65w6" sourceRef="appConnect_sendAdminNotification" targetRef="ExclusiveGateway_0688gfd"/>
    <bpmn:sequenceFlow id="SequenceFlow_0ngubg9" sourceRef="appConnect_updateInvoice" targetRef="ExclusiveGateway_0688gfd"/>
   
    <bpmn:exclusiveGateway default="SequenceFlow_0t0knw4" gatewayDirection="Unspecified" id="ExclusiveGateway_1sjx62d">
      <bpmn:incoming>SequenceFlow_0vjwa9r</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1o4bl6o</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0t0knw4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_1o4bl6o" name="Approved" sourceRef="ExclusiveGateway_1sjx62d" targetRef="appConnect_sendEInvoice">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression"><![CDATA[${invoice.memo == approved}]]></bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask camunda:class="com.appintgwkflw.wkflautomate.wkflatmnsvcapp.camunda.delegates.CamundaTaskDelegate" completionQuantity="1" id="appConnect_sendEInvoice" implementation="##WebService" isForCompensation="false" name="Send e-invoice to customer" startQuantity="1">
      <bpmn:incoming>SequenceFlow_0uhj221</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_1o4bl6o</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_08nbxqv</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask camunda:class="com.appintgwkflw.wkflautomate.wkflatmnsvcapp.camunda.delegates.CamundaTaskDelegate" completionQuantity="1" id="appConnect_sendAdminNotification" implementation="##WebService" isForCompensation="false" name="Send in product notification to admin" startQuantity="1">
      <bpmn:incoming>SequenceFlow_1f1c42w</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_19h65w6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask camunda:class="com.intuit.v4.appintgwkflw.wkflautomate.wkflatmnsvcapp.camunda.CamundaTaskDelegate" camunda:modelerTemplate="com.intuit.v4.appintgwkflw.wkflautomate.wkflatmnsvcapp.camunda.delegate.invoice" completionQuantity="1" id="appConnect_updateInvoice" implementation="##WebService" isForCompensation="false" name="Update invoice as approved" startQuantity="1">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <inputOutput>
            <camunda:inputParameter name="schema">
              <inputParameter>ACTION_ID,ACTION_URL,QUERY_Token~QUERY_Invoice_Id,QUERY_Memo_Action,QUERY_Memo,QUERY_Ignore_If~OUTPUT_response</inputParameter>
            </camunda:inputParameter>
            <camunda:inputParameter name="ACTION_ID">
              <inputParameter>updateInvoice</inputParameter>
            </camunda:inputParameter>
            <camunda:inputParameter name="ACTION_URL">
              <inputParameter>https://stage.connector.appconnect.intuit.com/504916/api/send-html-email.json</inputParameter>
            </camunda:inputParameter>
            <camunda:inputParameter name="QUERY_Token">
              <inputParameter>k1n4k0di2rcy2jd</inputParameter>
            </camunda:inputParameter>
            <camunda:inputParameter name="QUERY_Invoice_Id">
              <inputParameter>${appConnect_updateInvoice.id}</inputParameter>
            </camunda:inputParameter>
            <camunda:inputParameter name="QUERY_Memo_Action">
              <inputParameter>${appConnect_updateInvoice.memoAction}</inputParameter>
            </camunda:inputParameter>
            <camunda:inputParameter name="QUERY_Memo">
              <inputParameter>${appConnect_updateInvoice.memo}</inputParameter>
            </camunda:inputParameter>
            <camunda:inputParameter name="QUERY_Ignore_If">
              <inputParameter>${appConnect_updateInvoice.ignore}</inputParameter>
            </camunda:inputParameter>
            <camunda:outputParameter name="OUTPUT_response">
              <outputParameter>${appConnect_updateInvoice.response}</outputParameter>
            </camunda:outputParameter>
          </inputOutput>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_0fck39f</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0ngubg9</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_0kij2v0" sourceRef="ExclusiveGateway_0688gfd" targetRef="receiveAdminApproval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression"><![CDATA[${ invoice.memo == null}]]></bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0uhj221" name="No approval required" sourceRef="ExclusiveGateway_0688gfd" targetRef="appConnect_sendEInvoice"/>
    <bpmn:endEvent id="EndEvent_0xi659j">
      <bpmn:incoming>SequenceFlow_08nbxqv</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_0t0knw4</bpmn:incoming>
      <bpmn:inputSet/>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_08nbxqv" sourceRef="appConnect_sendEInvoice" targetRef="EndEvent_0xi659j"/>
    <bpmn:sequenceFlow id="SequenceFlow_0t0knw4" name="Rejected" sourceRef="ExclusiveGateway_1sjx62d" targetRef="EndEvent_0xi659j"/>
    <bpmn:sequenceFlow id="SequenceFlow_11lcvgp" sourceRef="receiveAdminApproval" targetRef="Task_0d3jxmc"/>
    <bpmn:serviceTask camunda:class=".comappintgwkflw.wkflautomate.wkflatmnsvcapp.camunda.delegates.CamundaTaskDelegate" completionQuantity="1" id="Task_0d3jxmc" implementation="##WebService" isForCompensation="false" name="Update invoice status" startQuantity="1">
      <bpmn:incoming>SequenceFlow_11lcvgp</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0vjwa9r</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_0vjwa9r" sourceRef="Task_0d3jxmc" targetRef="ExclusiveGateway_1sjx62d"/>
    <bpmn:receiveTask completionQuantity="1" id="receiveAdminApproval" implementation="##WebService" instantiate="false" isForCompensation="false" messageRef="Message_0997x1n" name="Wait for admin approval" startQuantity="1">
      <bpmn:incoming>SequenceFlow_0kij2v0</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_11lcvgp</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:subProcess completionQuantity="1" id="SubProcess_1i2uvx8" isForCompensation="false" name="Global Error Handler" startQuantity="1" triggeredByEvent="true">
      <bpmn:startEvent id="StartEvent_1b0givf" isInterrupting="true" name="Start" parallelMultiple="false">
        <bpmn:outgoing>SequenceFlow_131jkg1</bpmn:outgoing>
        <bpmn:outputSet/>
        <bpmn:errorEventDefinition camunda:errorCodeVariable="bpmerror" id="ErrorEventDefinition_03kubvt"/>
      </bpmn:startEvent>
      <bpmn:endEvent id="EndEvent_0ldxu8w" name="end">
        <bpmn:incoming>SequenceFlow_0nyi0sz</bpmn:incoming>
        <bpmn:inputSet/>
      </bpmn:endEvent>
      <bpmn:serviceTask camunda:class="ErrorHandlerDelegate" completionQuantity="1" id="ServiceTask_1llie5x" implementation="##WebService" isForCompensation="false" name="Handle Error" startQuantity="1">
        <bpmn:incoming>SequenceFlow_131jkg1</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_0nyi0sz</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="SequenceFlow_131jkg1" sourceRef="StartEvent_1b0givf" targetRef="ServiceTask_1llie5x"/>
      <bpmn:sequenceFlow id="SequenceFlow_0nyi0sz" sourceRef="ServiceTask_1llie5x" targetRef="EndEvent_0ldxu8w"/>
    </bpmn:subProcess>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="Yaoqiang_Diagram-InvoiceApproval_123456789012" name="Untitled Diagram" resolution="96.0">
    <bpmndi:BPMNPlane bpmnElement="InvoiceApproval_123456789012">
      <bpmndi:BPMNShape bpmnElement="StartEvent_1" id="Yaoqiang-StartEvent_1">
        <dc:Bounds height="32.0" width="32.0" x="156.0" y="213.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="18.96" width="147.0" x="101.0" y="183.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="invoiceApproveDecisionTable" id="Yaoqiang-invoiceApproveDecisionTable">
        <dc:Bounds height="80.0" width="100.0" x="275.0" y="334.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="46.96" width="70.0" x="290.0" y="352.52"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="appConnect_sendAdminMail" id="Yaoqiang-appConnect_sendAdminMail">
        <dc:Bounds height="80.0" width="100.0" x="498.0" y="259.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="32.96" width="89.0" x="503.5" y="284.52"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="ExclusiveGateway_0688gfd" id="Yaoqiang-ExclusiveGateway_0688gfd" isMarkerVisible="true">
        <dc:Bounds height="42.0" width="42.0" x="675.0" y="349.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="18.96" width="6.0" x="693.0" y="393.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="ExclusiveGateway_1sjx62d" id="Yaoqiang-ExclusiveGateway_1sjx62d" isMarkerVisible="true">
        <dc:Bounds height="42.0" width="42.0" x="1116.0" y="237.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="18.96" width="6.0" x="1134.0" y="281.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="appConnect_sendEInvoice" id="Yaoqiang-appConnect_sendEInvoice">
        <dc:Bounds height="80.0" width="100.0" x="841.0" y="458.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="32.96" width="85.0" x="848.5" y="483.52"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="appConnect_sendAdminNotification" id="Yaoqiang-appConnect_sendAdminNotification">
        <dc:Bounds height="80.0" width="100.0" x="498.0" y="347.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="60.96" width="81.0" x="507.5" y="358.52"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="appConnect_updateInvoice" id="Yaoqiang-appConnect_updateInvoice">
        <dc:Bounds height="80.0" width="100.0" x="498.0" y="443.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="32.96" width="85.0" x="505.5" y="468.52"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="EndEvent_0xi659j" id="Yaoqiang-EndEvent_0xi659j">
        <dc:Bounds height="32.0" width="32.0" x="873.0" y="648.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="18.96" width="6.0" x="886.0" y="688.52"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Task_0d3jxmc" id="Yaoqiang-Task_0d3jxmc">
        <dc:Bounds height="80.0" width="100.0" x="919.0" y="230.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="32.96" width="85.0" x="926.5" y="255.52"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="receiveAdminApproval" id="Yaoqiang-receiveAdminApproval">
        <dc:Bounds height="80.0" width="100.0" x="772.0" y="230.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="32.96" width="83.0" x="780.5" y="255.52"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="SubProcess_1i2uvx8" id="Yaoqiang-SubProcess_1i2uvx8" isExpanded="true">
        <dc:Bounds height="130.0" width="400.0" x="720.0" y="80.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="18.96" width="116.0" x="735.0" y="85.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="StartEvent_1b0givf" id="Yaoqiang-StartEvent_1b0givf">
        <dc:Bounds height="32.0" width="32.0" x="742.0" y="142.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="18.96" width="31.0" x="744.5" y="185.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="EndEvent_0ldxu8w" id="Yaoqiang-EndEvent_0ldxu8w">
        <dc:Bounds height="32.0" width="32.0" x="1042.0" y="142.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="18.96" width="26.0" x="1047.5" y="185.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="ServiceTask_1llie5x" id="Yaoqiang-ServiceTask_1llie5x">
        <dc:Bounds height="80.0" width="100.0" x="860.0" y="120.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="18.96" width="74.0" x="873.0" y="152.52"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_0t0knw4" id="Yaoqiang-SequenceFlow_0t0knw4">
        <di:waypoint x="1154.2727272727275" y="262.0"/>
        <di:waypoint x="1198.0" y="262.0"/>
        <di:waypoint x="1198.0" y="666.0"/>
        <di:waypoint x="905.3181172519359" y="664.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="18.96" width="51.0" x="1172.5" y="578.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_1f1c42w" id="Yaoqiang-SequenceFlow_1f1c42w">
        <di:waypoint x="375.31818181818187" y="374.0"/>
        <di:waypoint x="498.31818181818187" y="387.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="18.96" width="230.0" x="321.5" y="371.34"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_1wrluje" id="Yaoqiang-SequenceFlow_1wrluje">
        <di:waypoint x="375.31818181818187" y="354.0"/>
        <di:waypoint x="433.0" y="354.0"/>
        <di:waypoint x="433.0" y="299.0"/>
        <di:waypoint x="498.31818181818187" y="299.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="18.96" width="169.0" x="348.5" y="313.2"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_131jkg1" id="Yaoqiang-SequenceFlow_131jkg1">
        <di:waypoint x="774.3181172519359" y="158.0"/>
        <di:waypoint x="860.3181818181819" y="160.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="18.96" width="6.0" x="814.0" y="149.84"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_0vjwa9r" id="Yaoqiang-SequenceFlow_0vjwa9r">
        <di:waypoint x="1019.3181818181819" y="270.0"/>
        <di:waypoint x="1116.3636363636365" y="258.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="18.96" width="6.0" x="1064.5" y="254.18"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_08nbxqv" id="Yaoqiang-SequenceFlow_08nbxqv">
        <di:waypoint x="889.0" y="537.9545454545455"/>
        <di:waypoint x="889.0" y="647.9577095070658"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="18.96" width="6.0" x="886.0" y="583.48"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_1o4bl6o" id="Yaoqiang-SequenceFlow_1o4bl6o">
        <di:waypoint x="1141.0" y="275.2727272727275"/>
        <di:waypoint x="1141.0" y="498.0"/>
        <di:waypoint x="941.3181818181819" y="498.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="32.96" width="170.0" x="1056.0" y="470.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_0kij2v0" id="Yaoqiang-SequenceFlow_0kij2v0">
        <di:waypoint x="717.2727272727274" y="370.0"/>
        <di:waypoint x="722.0" y="270.0"/>
        <di:waypoint x="772.3181818181819" y="270.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="18.96" width="142.0" x="651.0" y="287.73"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_0ngubg9" id="Yaoqiang-SequenceFlow_0ngubg9">
        <di:waypoint x="598.3181818181819" y="483.0"/>
        <di:waypoint x="700.0" y="483.0"/>
        <di:waypoint x="700.0" y="387.27272727272737"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="18.96" width="6.0" x="694.02" y="473.52"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_19h65w6" id="Yaoqiang-SequenceFlow_19h65w6">
        <di:waypoint x="598.3181818181819" y="387.0"/>
        <di:waypoint x="637.0" y="387.0"/>
        <di:waypoint x="637.0" y="374.0"/>
        <di:waypoint x="692.3636363636364" y="387.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="18.96" width="6.0" x="642.34" y="377.52"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_04bferx" id="Yaoqiang-SequenceFlow_04bferx">
        <di:waypoint x="174.0" y="244.86590892859465"/>
        <di:waypoint x="174.0" y="374.0"/>
        <di:waypoint x="275.31818181818187" y="374.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="18.96" width="6.0" x="171.0" y="350.61"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_0nyi0sz" id="Yaoqiang-SequenceFlow_0nyi0sz">
        <di:waypoint x="960.3181818181819" y="160.0"/>
        <di:waypoint x="1042.3182463844278" y="158.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="18.96" width="6.0" x="998.0" y="149.2"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_0fck39f" id="Yaoqiang-SequenceFlow_0fck39f">
        <di:waypoint x="373.0" y="413.9545454545455"/>
        <di:waypoint x="373.0" y="483.0"/>
        <di:waypoint x="498.31818181818187" y="483.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="18.96" width="233.0" x="284.64" y="473.52"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_0lajwhe" id="Yaoqiang-SequenceFlow_0lajwhe">
        <di:waypoint x="598.3181818181819" y="299.0"/>
        <di:waypoint x="700.0" y="299.0"/>
        <di:waypoint x="700.0" y="352.6363636363636"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="18.96" width="6.0" x="672.98" y="289.52"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_0uhj221" id="Yaoqiang-SequenceFlow_0uhj221">
        <di:waypoint x="713.2727272727274" y="374.0"/>
        <di:waypoint x="776.0" y="374.0"/>
        <di:waypoint x="776.0" y="498.0"/>
        <di:waypoint x="841.3181818181819" y="498.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="18.96" width="120.0" x="716.0" y="427.82"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="SequenceFlow_11lcvgp" id="Yaoqiang-SequenceFlow_11lcvgp">
        <di:waypoint x="872.3181818181819" y="270.0"/>
        <di:waypoint x="919.3181818181819" y="270.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="18.96" width="6.0" x="892.82" y="260.52"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>