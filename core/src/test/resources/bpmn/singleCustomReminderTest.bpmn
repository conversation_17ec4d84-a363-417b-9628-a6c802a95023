<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_1ueeqzk" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.4.0">
    <bpmn:process id="customReminder" name="Reminder Template" isExecutable="true" camunda:historyTimeToLive="14">
        <bpmn:startEvent id="customStartEvent" name="start process">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="stepDetails" value="{   &#34;customStartEvent&#34;: [     &#34;customStartEvent&#34;,     &#34;decisionElement&#34;,     &#34;inclusiveGateway&#34;,     &#34;createTask&#34;,     &#34;sendExternalEmail&#34;,     &#34;sendCompanyEmail&#34;,     &#34;sendPushNotification&#34;   ],   &#34;customWorkflowWaitEvent&#34;: [     &#34;customWorkflowWaitEvent&#34;   ] }" />
                    <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
                    <camunda:property name="currentStepDetails" value="{ &#34;required&#34;: true }" />
                    <camunda:property name="processVariablesDetails" value="[{  &#34;variableName&#34;: &#34;entityChangeType&#34;,   &#34;variableType&#34;: &#34;String&#34; }, {   &#34;variableName&#34;: &#34;Id&#34;,   &#34;variableType&#34;: &#34;String&#34; }, {   &#34;variableName&#34;: &#34;intuit_userid&#34;,  &#34;variableType&#34;: &#34;String&#34; }, {   &#34;variableName&#34;: &#34;intuit_realmid&#34;,   &#34;variableType&#34;: &#34;String&#34; }, {   &#34;variableName&#34;: &#34;SyncToken&#34;,  &#34;variableType&#34;: &#34;String&#34; }, {   &#34;variableName&#34;: &#34;entityType&#34;,   &#34;variableType&#34;: &#34;String&#34; }, {   &#34;variableName&#34;: &#34;sendCompanyEmail&#34;,   &#34;variableType&#34;: &#34;String&#34; }, {   &#34;variableName&#34;: &#34;sendExternalEmail&#34;,  &#34;variableType&#34;: &#34;String&#34; }, {   &#34;variableName&#34;: &#34;createTask&#34;,   &#34;variableType&#34;: &#34;String&#34; }, {   &#34;variableName&#34;: &#34;sendPushNotification&#34;,   &#34;variableType&#34;: &#34;String&#34; }]" />
                    <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;: &#34;appconnect&#34;, &#34;handlerId&#34;: &#34;intuit-workflows/custom-reminder-filter&#34;, &#34;actionName&#34;: &#34;executeWorkflowAction&#34;}" />
                    <camunda:property name="parameterDetails" value="{}" />
                    <camunda:property name="startableEvents" value="[&#34;created&#34;]" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:outgoing>Flow_0uuma6l</bpmn:outgoing>
        </bpmn:startEvent>
        <bpmn:sequenceFlow id="Flow_0uuma6l" sourceRef="customStartEvent" targetRef="decisionElement" />
        <bpmn:businessRuleTask id="decisionElement" name="DMN Rule Processor" camunda:resultVariable="decision" camunda:decisionRef="decisionElement" camunda:mapDecisionResult="singleResult">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{ "taskHandler": "was"}</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0uuma6l</bpmn:incoming>
            <bpmn:outgoing>Flow_0e5omq2</bpmn:outgoing>
            <bpmn:outgoing>Sequence_decision_result</bpmn:outgoing>
        </bpmn:businessRuleTask>
        <bpmn:sequenceFlow id="Flow_1jh72q4" sourceRef="Gateway_1rgsx47" targetRef="Event_1fgu7ic" />
        <bpmn:sendTask id="sendExternalEmail" name="Send external email" camunda:type="external" camunda:topic="reminders">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
                    <camunda:inputParameter name="handlerDetails" />
                    <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_sendExternalEmail</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_1xjrp7d</bpmn:outgoing>
        </bpmn:sendTask>
        <bpmn:serviceTask id="createTask" name="Create task" camunda:type="external" camunda:topic="reminders">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
                    <camunda:inputParameter name="handlerDetails">{}</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_createTask</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_137bn44</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:receiveTask id="customWorkflowWaitEvent" name="Wait for state change" messageRef="Message_064b9px">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{"taskHandler": "appconnect", "handlerId": "intuit-workflows/custom-reminder-filter", "actionName": "executeWorkflowAction" ,"recordType":"invoice"}</camunda:inputParameter>
                    <camunda:inputParameter name="currentStepDetails">{ "required": false }</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1necc4x</bpmn:incoming>
            <bpmn:outgoing>Flow_1xevg2t</bpmn:outgoing>
        </bpmn:receiveTask>
        <bpmn:endEvent id="Event_1fgu7ic" name="End the process if create task is false">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "handlerId": "",
                        "actionName": "updateProcessStatus"
                        }</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1jh72q4</bpmn:incoming>
            <bpmn:messageEventDefinition id="MessageEventDefinition_1qq1zcv" />
        </bpmn:endEvent>
        <bpmn:sequenceFlow id="Flow_0e5omq2" name="Conditions unmatched" sourceRef="decisionElement" targetRef="Event_1dx97ea">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decision.sendReminder == false}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:endEvent id="Event_1dx97ea" name="DMN condition not satisfied: End process">
            <bpmn:incoming>Flow_0e5omq2</bpmn:incoming>
            <bpmn:messageEventDefinition id="MessageEventDefinition_0swqckt" />
        </bpmn:endEvent>
        <bpmn:sequenceFlow id="Flow_1xevg2t" sourceRef="customWorkflowWaitEvent" targetRef="Event_1hhbrmg" />
        <bpmn:endEvent id="Event_1hhbrmg" name="State change occured">
            <bpmn:incoming>Flow_1xevg2t</bpmn:incoming>
            <bpmn:escalationEventDefinition id="EscalationEventDefinition_0ay2jrq" escalationRef="Escalation_0tyjh9j" />
        </bpmn:endEvent>
        <bpmn:subProcess id="Activity_108jjaa" triggeredByEvent="true">
            <bpmn:serviceTask id="closeTask" name="Close Project service task" camunda:type="external" camunda:topic="reminders">
                <bpmn:extensionElements>
                    <camunda:inputOutput>
                        <camunda:inputParameter name="handlerDetails">{
                            "taskHandler": "appconnect",
                            "handlerId": "intuit-workflows/was-update-task",
                            "actionName": "executeWorkflowAction"
                            }</camunda:inputParameter>
                        <camunda:inputParameter name="parameterDetails">{
                            "taskId": {
                            "fieldValue": [],
                            "possibleFieldValues": [],
                            "configurable": true,
                            "actionByUI": null,
                            "handlerFieldName": "Task",
                            "requiredByHandler": true,
                            "helpVariables": [],
                            "requiredByUI": false,
                            "multiSelect": false,
                            "fieldType": "string",
                            "valueType":"PROCESS_VARIABLE"
                            },
                            "projectId": {
                            "fieldValue": [],
                            "possibleFieldValues": [],
                            "configurable": true,
                            "actionByUI": null,
                            "handlerFieldName": "Project",
                            "requiredByHandler": true,
                            "helpVariables": [],
                            "requiredByUI": false,
                            "multiSelect": false,
                            "fieldType": "string",
                            "valueType":"PROCESS_VARIABLE"
                            },
                            "Status": {
                            "fieldValue": [
                            "Complete"
                            ],
                            "possibleFieldValues": [],
                            "configurable": true,
                            "actionByUI": null,
                            "requiredByHandler": true,
                            "requiredByUI": false,
                            "multiSelect": false,
                            "fieldType": "string"
                            }
                            }</camunda:inputParameter>
                        <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
                    </camunda:inputOutput>
                </bpmn:extensionElements>
                <bpmn:incoming>Flow_16ihqz2</bpmn:incoming>
                <bpmn:outgoing>Flow_0pxn6yi</bpmn:outgoing>
            </bpmn:serviceTask>
            <bpmn:startEvent id="Event_0vepyso" name="Close task">
                <bpmn:outgoing>Flow_16ihqz2</bpmn:outgoing>
                <bpmn:escalationEventDefinition id="EscalationEventDefinition_0ghanme" escalationRef="Escalation_0tyjh9j" />
            </bpmn:startEvent>
            <bpmn:endEvent id="Event_0ey8tt4" name="End process">
                <bpmn:extensionElements>
                    <camunda:inputOutput>
                        <camunda:inputParameter name="handlerDetails">{
                            "taskHandler": "was",
                            "handlerId": "",
                            "actionName": "updateProcessStatus"
                            }</camunda:inputParameter>
                    </camunda:inputOutput>
                </bpmn:extensionElements>
                <bpmn:incoming>Flow_0pxn6yi</bpmn:incoming>
                <bpmn:messageEventDefinition id="MessageEventDefinition_07qnodz" messageRef="Message_0p07vw4" camunda:type="external" camunda:topic="reminders" />
            </bpmn:endEvent>
            <bpmn:sequenceFlow id="Flow_0pxn6yi" sourceRef="closeTask" targetRef="Event_0ey8tt4" />
            <bpmn:sequenceFlow id="Flow_16ihqz2" sourceRef="Event_0vepyso" targetRef="closeTask" />
        </bpmn:subProcess>
        <bpmn:boundaryEvent id="Event_1tx361d" name="Expiry Event" attachedToRef="customWorkflowWaitEvent">
            <bpmn:outgoing>Flow_1fep73j</bpmn:outgoing>
            <bpmn:timerEventDefinition id="TimerEventDefinition_1dgqopk">
                <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">P15D</bpmn:timeDuration>
            </bpmn:timerEventDefinition>
        </bpmn:boundaryEvent>
        <bpmn:sequenceFlow id="Flow_1fep73j" sourceRef="Event_1tx361d" targetRef="Event_0kb35fk" />
        <bpmn:endEvent id="Event_0kb35fk" name="End process after the process expiration.">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "handlerId": "",
                        "actionName": "updateProcessStatus"
                        }</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1fep73j</bpmn:incoming>
            <bpmn:messageEventDefinition id="MessageEventDefinition_0vwg5fd" camunda:type="external" camunda:topic="reminders" />
        </bpmn:endEvent>
        <bpmn:inclusiveGateway id="Gateway_1rgsx47" name="create task?" default="Flow_1jh72q4">
            <bpmn:incoming>SequenceFlow_137bn44</bpmn:incoming>
            <bpmn:incoming>SequenceFlow_1xjrp7d</bpmn:incoming>
            <bpmn:incoming>SequenceFlow_0st8w12</bpmn:incoming>
            <bpmn:incoming>SequenceFlow_02eopl1</bpmn:incoming>
            <bpmn:outgoing>Flow_1jh72q4</bpmn:outgoing>
            <bpmn:outgoing>Flow_1necc4x</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:sendTask id="sendCompanyEmail" name="Send company email" camunda:type="external" camunda:topic="reminders">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
                    <camunda:inputParameter name="handlerDetails" />
                    <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_sendCompanyEmail</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_0st8w12</bpmn:outgoing>
        </bpmn:sendTask>
        <bpmn:sendTask id="sendPushNotification" name="Send push notification" camunda:type="external" camunda:topic="reminders">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
                    <camunda:inputParameter name="handlerDetails" />
                    <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_sendPushNotification</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_02eopl1</bpmn:outgoing>
        </bpmn:sendTask>
        <bpmn:sequenceFlow id="Sequence_decision_result" name="Rule Evaluation Passed" sourceRef="decisionElement" targetRef="inclusiveGateway">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decision.decisionResult == true}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="Flow_1necc4x" sourceRef="Gateway_1rgsx47" targetRef="customWorkflowWaitEvent">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${createTask == true}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:inclusiveGateway id="inclusiveGateway" name="Gateway">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Sequence_decision_result</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_createTask</bpmn:outgoing>
            <bpmn:outgoing>SequenceFlow_sendCompanyEmail</bpmn:outgoing>
            <bpmn:outgoing>SequenceFlow_sendExternalEmail</bpmn:outgoing>
            <bpmn:outgoing>SequenceFlow_sendPushNotification</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:sequenceFlow id="SequenceFlow_createTask" name="Create Task" sourceRef="inclusiveGateway" targetRef="createTask">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.hasVariable("createTask") == true}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="SequenceFlow_sendCompanyEmail" name="Send Company Email" sourceRef="inclusiveGateway" targetRef="sendCompanyEmail">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.hasVariable("sendCompanyEmail") == true}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="SequenceFlow_137bn44" sourceRef="createTask" targetRef="Gateway_1rgsx47" />
        <bpmn:sequenceFlow id="SequenceFlow_1xjrp7d" sourceRef="sendExternalEmail" targetRef="Gateway_1rgsx47" />
        <bpmn:sequenceFlow id="SequenceFlow_0st8w12" sourceRef="sendCompanyEmail" targetRef="Gateway_1rgsx47" />
        <bpmn:sequenceFlow id="SequenceFlow_02eopl1" sourceRef="sendPushNotification" targetRef="Gateway_1rgsx47" />
        <bpmn:sequenceFlow id="SequenceFlow_sendExternalEmail" name="Send External Email" sourceRef="inclusiveGateway" targetRef="sendExternalEmail">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.hasVariable("sendExternalEmail") == true}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="SequenceFlow_sendPushNotification" name="Send Push Notification" sourceRef="inclusiveGateway" targetRef="sendPushNotification">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.hasVariable("sendPushNotification") == true}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
    </bpmn:process>
    <bpmn:escalation id="Escalation_0tyjh9j" name="close_task" escalationCode="closetask" />
    <bpmn:message id="Message_0p07vw4" name="process_ended_message" />
    <bpmn:message id="Message_064b9px" name="testMessage" />
    <bpmndi:BPMNDiagram id="BPMNDiagram_1">
        <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="customReminder">
            <bpmndi:BPMNEdge id="SequenceFlow_1nuvc6b_di" bpmnElement="SequenceFlow_sendPushNotification">
                <di:waypoint x="575" y="455" />
                <di:waypoint x="575" y="560" />
                <di:waypoint x="720" y="560" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="732" y="606" width="55" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_0n3artf_di" bpmnElement="SequenceFlow_sendExternalEmail">
                <di:waypoint x="575" y="405" />
                <di:waypoint x="575" y="280" />
                <di:waypoint x="720" y="280" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="735" y="336" width="70" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_02eopl1_di" bpmnElement="SequenceFlow_02eopl1">
                <di:waypoint x="820" y="550" />
                <di:waypoint x="980" y="550" />
                <di:waypoint x="980" y="445" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_0st8w12_di" bpmnElement="SequenceFlow_0st8w12">
                <di:waypoint x="820" y="420" />
                <di:waypoint x="955" y="420" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_1xjrp7d_di" bpmnElement="SequenceFlow_1xjrp7d">
                <di:waypoint x="820" y="280" />
                <di:waypoint x="980" y="280" />
                <di:waypoint x="980" y="395" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_137bn44_di" bpmnElement="SequenceFlow_137bn44">
                <di:waypoint x="820" y="160" />
                <di:waypoint x="980" y="160" />
                <di:waypoint x="980" y="395" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_13dpi54_di" bpmnElement="SequenceFlow_sendCompanyEmail">
                <di:waypoint x="600" y="430" />
                <di:waypoint x="720" y="430" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="732" y="478" width="76" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_0gdfajo_di" bpmnElement="SequenceFlow_createTask">
                <di:waypoint x="575" y="405" />
                <di:waypoint x="575" y="160" />
                <di:waypoint x="720" y="160" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="740" y="213" width="59" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1necc4x_di" bpmnElement="Flow_1necc4x">
                <di:waypoint x="1005" y="420" />
                <di:waypoint x="1080" y="420" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0o8v5j8_di" bpmnElement="Sequence_decision_result">
                <di:waypoint x="400" y="430" />
                <di:waypoint x="550" y="430" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="428" y="448" width="78" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1fep73j_di" bpmnElement="Flow_1fep73j">
                <di:waypoint x="1130" y="478" />
                <di:waypoint x="1130" y="552" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1xevg2t_di" bpmnElement="Flow_1xevg2t">
                <di:waypoint x="1180" y="420" />
                <di:waypoint x="1262" y="420" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0e5omq2_di" bpmnElement="Flow_0e5omq2">
                <di:waypoint x="350" y="390" />
                <di:waypoint x="350" y="290" />
                <di:waypoint x="402" y="290" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="282" y="326" width="55" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1jh72q4_di" bpmnElement="Flow_1jh72q4">
                <di:waypoint x="1005" y="420" />
                <di:waypoint x="1020" y="420" />
                <di:waypoint x="1020" y="280" />
                <di:waypoint x="1142" y="280" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0uuma6l_di" bpmnElement="Flow_0uuma6l">
                <di:waypoint x="208" y="430" />
                <di:waypoint x="300" y="430" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="customStartEvent">
                <dc:Bounds x="172" y="412" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="159" y="455" width="63" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_109vnno_di" bpmnElement="decisionElement">
                <dc:Bounds x="300" y="390" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0a7dlcb_di" bpmnElement="sendExternalEmail">
                <dc:Bounds x="720" y="240" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1sjt7qd_di" bpmnElement="createTask">
                <dc:Bounds x="720" y="120" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1guy81g_di" bpmnElement="customWorkflowWaitEvent">
                <dc:Bounds x="1080" y="380" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_0xbnmwr_di" bpmnElement="Event_1fgu7ic">
                <dc:Bounds x="1142" y="262" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1115" y="219" width="89" height="40" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_1f1cf2c_di" bpmnElement="Event_1dx97ea">
                <dc:Bounds x="402" y="272" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="377" y="229" width="85" height="40" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_00zaz0i_di" bpmnElement="Event_1hhbrmg">
                <dc:Bounds x="1262" y="402" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1248" y="445" width="65" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_108jjaa_di" bpmnElement="Activity_108jjaa" isExpanded="true">
                <dc:Bounds x="160" y="550" width="350" height="200" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="Flow_16ihqz2_di" bpmnElement="Flow_16ihqz2">
                <di:waypoint x="236" y="650" />
                <di:waypoint x="280" y="650" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0pxn6yi_di" bpmnElement="Flow_0pxn6yi">
                <di:waypoint x="380" y="650" />
                <di:waypoint x="432" y="650" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="Activity_1t3pzu3_di" bpmnElement="closeTask">
                <dc:Bounds x="280" y="610" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_0vepyso_di" bpmnElement="Event_0vepyso">
                <dc:Bounds x="200" y="632" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="192" y="675" width="52" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_0ey8tt4_di" bpmnElement="Event_0ey8tt4">
                <dc:Bounds x="432" y="632" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="419" y="675" width="62" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_1risnpd_di" bpmnElement="Event_0kb35fk">
                <dc:Bounds x="1112" y="552" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1087" y="595" width="87" height="40" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_168ziyq_di" bpmnElement="Gateway_1rgsx47">
                <dc:Bounds x="955" y="395" width="50" height="50" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="910" y="445" width="60" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0p4g82p_di" bpmnElement="sendCompanyEmail">
                <dc:Bounds x="720" y="380" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0vgc65h_di" bpmnElement="sendPushNotification">
                <dc:Bounds x="720" y="510" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_0xoew6n_di" bpmnElement="inclusiveGateway">
                <dc:Bounds x="550" y="405" width="50" height="50" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="528" y="383" width="44" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_1fte3ra_di" bpmnElement="Event_1tx361d">
                <dc:Bounds x="1112" y="442" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1101" y="485" width="62" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</bpmn:definitions>
