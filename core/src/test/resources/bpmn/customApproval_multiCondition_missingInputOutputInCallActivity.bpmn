<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_1oo65x9" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.4.1">
  <bpmn:process id="customApproval" name="Multi Condition Approval" processType="None" isClosed="false" isExecutable="true" camunda:historyTimeToLive="7">
    <bpmn:startEvent id="customStartEvent_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585" name="transaction custom event">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="stepDetails" value="{}" />
          <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
          <camunda:property name="currentStepDetails" value="{ &#34;required&#34;: true }" />
          <camunda:property name="processVariablesDetails" value="[{&#34;variableName&#34;:&#34;entityChangeType&#34;,&#34;variableType&#34;:&#34;String&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;entityType&#34;,&#34;variableType&#34;:&#34;String&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;TxnDate&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;CompanyEmail&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;TxnAmount&#34;,&#34;variableType&#34;:&#34;double&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;intuit_userid&#34;,&#34;variableType&#34;:&#34;String&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;TxnBalanceAmount&#34;,&#34;variableType&#34;:&#34;double&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;CompanyName&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;TxnDueDate&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;CustomerEmail&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;DocNumber&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;Id&#34;,&#34;variableType&#34;:&#34;String&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;CustomerName&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;intuit_realmid&#34;,&#34;variableType&#34;:&#34;String&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;Location&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true}]" />
          <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;was&#34;}" />
          <camunda:property name="startableEvents" value="[&#34;created&#34;,&#34;updated&#34;]" />
          <camunda:property name="ignoreNonEntityProcessVariablesDetails" value="true" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_0zrvk3w_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0zrvk3w_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585" sourceRef="customStartEvent_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585" targetRef="decisionElement" />
    <bpmn:sequenceFlow id="de" name="Auto Approved" sourceRef="decisionElement" targetRef="Event_1hhbrmg">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == 'false'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:businessRuleTask id="decisionElement" name="Txn Rule Evaluation" implementation="##unspecified" camunda:type="external" camunda:topic="test-manujindal">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{ "taskHandler": "was","handlerId":"was_dmn_evaluator","actionName": "evaluateDMN"}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0zrvk3w_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585</bpmn:incoming>
      <bpmn:outgoing>de</bpmn:outgoing>
      <bpmn:outgoing>Flow_1pyoasn</bpmn:outgoing>
      <bpmn:outgoing>Flow_1bicpdx</bpmn:outgoing>
      <bpmn:outgoing>Flow_141sxww</bpmn:outgoing>
      <bpmn:outgoing>Flow_0egr0mp</bpmn:outgoing>
      <bpmn:outgoing>Flow_0e142zu</bpmn:outgoing>
      <bpmn:outgoing>Flow_05ykizl</bpmn:outgoing>
    </bpmn:businessRuleTask>
    <callActivity xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" id="action-2" name="sendForApproval" calledElement="sendForApproval">
      <extensionElements>
        <camunda:in source="" target="" businessKey="#{execution.processBusinessKey}" />
        <camunda:out source="" target="" variables="all" />
        <camunda:in source="" target="" variables="all" />
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
          <camunda:inputParameter name="Input_324cf1l" />
          <camunda:outputParameter name="rootProcessInstanceId">${null}</camunda:outputParameter>
        </camunda:inputOutput>
      </extensionElements>
      <incoming>Flow_1pyoasn</incoming>
      <outgoing>Flow_0tfq8te</outgoing>
    </callActivity>
    <callActivity xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" id="action-3" name="sendForApproval" calledElement="sendForApproval">
      <extensionElements>
        <camunda:in source="" target="" businessKey="#{execution.processBusinessKey}" />
        <camunda:out source="" target="" variables="all" />
        <camunda:in source="" target="" variables="all" />
      </extensionElements>
      <incoming>Flow_1bicpdx</incoming>
      <outgoing>Flow_1y7ccjw</outgoing>
    </callActivity>
    <callActivity xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" id="action-4" name="sendForApproval" calledElement="sendForApproval">
      <extensionElements>
        <camunda:in source="" target="" businessKey="#{execution.processBusinessKey}" />
        <camunda:out source="" target="" variables="all" />
        <camunda:in source="" target="" variables="all" />
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
          <camunda:outputParameter name="rootProcessInstanceId">${null}</camunda:outputParameter>
        </camunda:inputOutput>
      </extensionElements>
      <incoming>Flow_141sxww</incoming>
      <outgoing>Flow_19o02xu</outgoing>
    </callActivity>
    <bpmn:sequenceFlow id="Flow_1pyoasn" name="action-2 == true" sourceRef="decisionElement" targetRef="action-2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == 'action-2'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1bicpdx" name="action-3 ==true" sourceRef="decisionElement" targetRef="action-3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == 'action-3'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_141sxww" name="action-4 ==true" sourceRef="decisionElement" targetRef="action-4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == 'action-4'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:callActivity id="action-5" name="sendForApproval" calledElement="sendForApproval">
      <bpmn:extensionElements>
        <camunda:in source="" target="" businessKey="#{execution.processBusinessKey}" />
        <camunda:out source="" target="" variables="all" />
        <camunda:in source="" target="" variables="all" />
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
          <camunda:outputParameter name="rootProcessInstanceId">${null}</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0egr0mp</bpmn:incoming>
      <bpmn:outgoing>Flow_0hor84w</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:callActivity id="action-6" name="sendForApproval" calledElement="sendForApproval">
      <bpmn:extensionElements>
        <camunda:in source="" target="" businessKey="#{execution.processBusinessKey}" />
        <camunda:out source="" target="" variables="all" />
        <camunda:in source="" target="" variables="all" />
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
          <camunda:outputParameter name="rootProcessInstanceId">${null}</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0e142zu</bpmn:incoming>
      <bpmn:outgoing>Flow_0go33ro</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_0egr0mp" name="action-5 ==true" sourceRef="decisionElement" targetRef="action-5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == 'action-5'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0e142zu" name="action-6 == true" sourceRef="decisionElement" targetRef="action-6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == 'action-6'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:callActivity id="action-1" name="sendForApproval" calledElement="sendForApproval" camunda:variableMappingClass="">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
          <camunda:outputParameter name="rootProcessInstanceId">${null}</camunda:outputParameter>
        </camunda:inputOutput>
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_05ykizl</bpmn:incoming>
      <bpmn:outgoing>Flow_1lcm64c</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_05ykizl" name="action-1 ==true" sourceRef="decisionElement" targetRef="action-1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == 'action-1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1y7ccjw" sourceRef="action-3" targetRef="Event_1hhbrmg" />
    <bpmn:sequenceFlow id="Flow_0tfq8te" sourceRef="action-2" targetRef="Event_1hhbrmg" />
    <bpmn:sequenceFlow id="Flow_1lcm64c" sourceRef="action-1" targetRef="Event_1hhbrmg" />
    <bpmn:sequenceFlow id="Flow_19o02xu" sourceRef="action-4" targetRef="Event_1hhbrmg" />
    <bpmn:sequenceFlow id="Flow_0hor84w" sourceRef="action-5" targetRef="Event_1hhbrmg" />
    <bpmn:sequenceFlow id="Flow_0go33ro" sourceRef="action-6" targetRef="Event_1hhbrmg" />
    <bpmn:subProcess id="Activity_108jjaa" triggeredByEvent="true">
      <bpmn:sequenceFlow id="Flow_16ihqz2" sourceRef="Event_0vepyso" targetRef="closeApproval" />
      <bpmn:startEvent id="Event_0vepyso" name="Close current process">
        <bpmn:outgoing>Flow_16ihqz2</bpmn:outgoing>
        <bpmn:escalationEventDefinition id="EscalationEventDefinition_0rasiag" escalationRef="Escalation_211pfog" camunda:escalationCodeVariable="close_parent" />
      </bpmn:startEvent>
      <bpmn:sequenceFlow id="Flow_015hyoo" sourceRef="closeApproval" targetRef="Event_0ey8tt4" />
      <bpmn:callActivity id="closeApproval" name="Close Approval" calledElement="closeTask">
        <bpmn:extensionElements>
          <camunda:in businessKey="#{execution.processBusinessKey}" />
          <camunda:in variables="all" />
          <camunda:inputOutput>
            <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_16ihqz2</bpmn:incoming>
        <bpmn:outgoing>Flow_015hyoo</bpmn:outgoing>
      </bpmn:callActivity>
      <bpmn:endEvent id="Event_0ey8tt4" name="End process">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
              "taskHandler": "was",
              "handlerId": "",
              "actionName": "updateProcessStatus"
              }</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_015hyoo</bpmn:incoming>
        <bpmn:messageEventDefinition id="MessageEventDefinition_07qnodz" messageRef="Message_15131hb" camunda:type="external" camunda:topic="test-manujindal" />
      </bpmn:endEvent>
    </bpmn:subProcess>
    <bpmn:endEvent id="Event_1hhbrmg" name="State change occured">
      <bpmn:incoming>Flow_0go33ro</bpmn:incoming>
      <bpmn:incoming>Flow_0hor84w</bpmn:incoming>
      <bpmn:incoming>Flow_19o02xu</bpmn:incoming>
      <bpmn:incoming>Flow_1lcm64c</bpmn:incoming>
      <bpmn:incoming>Flow_0tfq8te</bpmn:incoming>
      <bpmn:incoming>Flow_1y7ccjw</bpmn:incoming>
      <bpmn:incoming>de</bpmn:incoming>
      <bpmn:escalationEventDefinition id="EscalationEventDefinition_0pqgwen" escalationRef="Escalation_211pfog" />
    </bpmn:endEvent>
    <bpmn:textAnnotation id="TextAnnotation_1yhsn59">
      <bpmn:text>Multi Condition evaluated at WAS</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_1tnubaf" sourceRef="decisionElement" targetRef="TextAnnotation_1yhsn59" />
  </bpmn:process>
  <bpmn:message id="Message_0oflesh" name="customWait" />
  <bpmn:escalation id="Escalation_025wo89" name="close_tasks" escalationCode="closeTasks" />
  <bpmn:escalation id="Escalation_00owd9n" name="close_task" escalationCode="closetask" />
  <bpmn:message id="Message_04xqo3b" name="process_ended_message" />
  <bpmn:message id="Message_0j828dx" name="deleted_voided_disable" />
  <bpmn:escalation id="Escalation_0d2bkim" name="close_task" escalationCode="closetask" />
  <bpmn:escalation id="Escalation_0ye464b" name="close_task" escalationCode="closetask" />
  <bpmn:escalation id="Escalation_0rubrjz" name="close_task" escalationCode="closetask" />
  <bpmn:message id="Message_04yyvx2" name="deleted_voided_disable" />
  <bpmn:escalation id="Escalation_01zakh4" name="close_task" escalationCode="closetask" />
  <bpmn:message id="Message_0hvjxp3" name="approved_rejected" />
  <bpmn:message id="Message_1r625qy" name="process_ended_message" />
  <bpmn:message id="Message_0tj20j4" name="custom_deleted" />
  <bpmn:escalation id="Escalation_1uy1le9" name="close_task" escalationCode="closetask" />
  <bpmn:message id="Message_0u11os6" name="process_ended_message" />
  <bpmn:escalation id="Escalation_0j3apf2" name="TERMINATE_PARENT" escalationCode="TERMINATE_PARENT" />
  <bpmn:escalation id="Escalation_060xgmj" name="close_task" escalationCode="closetask" />
  <bpmn:escalation id="Escalation_211pfog" name="close_parent" escalationCode="close_parent" />
  <bpmn:escalation id="Escalation_0hqql4d" name="close_task" escalationCode="closetask" />
  <bpmn:message id="Message_15131hb" name="process_ended_message" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="customApproval">
      <bpmndi:BPMNShape id="Event_0u1c60a_di" bpmnElement="customStartEvent_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585">
        <dc:Bounds x="172" y="412" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="160" y="455" width="66" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0e0x7c8_di" bpmnElement="decisionElement">
        <dc:Bounds x="330" y="390" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_96fd5293-6563-4fb4-8292-4fe66be1854f" bpmnElement="action-2">
        <dc:Bounds x="840" y="290" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_e9ed1eb7-315e-4613-adc7-47eea2f71e97" bpmnElement="action-3">
        <dc:Bounds x="840" y="390" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0f2573d3-7d39-4791-87f4-213d518a3bfc" bpmnElement="action-4">
        <dc:Bounds x="840" y="490" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_11dojvn" bpmnElement="action-5">
        <dc:Bounds x="840" y="590" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0l00ohy" bpmnElement="action-6">
        <dc:Bounds x="840" y="700" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_11phofd_di" bpmnElement="action-1">
        <dc:Bounds x="840" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_108jjaa_di" bpmnElement="Activity_108jjaa" isExpanded="true">
        <dc:Bounds x="190" y="700" width="350" height="200" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0tkfy0j_di" bpmnElement="Event_0vepyso">
        <dc:Bounds x="230" y="782" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="216" y="825" width="66" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0iqmdk1_di" bpmnElement="closeApproval">
        <dc:Bounds x="305" y="760" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0ey8tt4_di" bpmnElement="Event_0ey8tt4">
        <dc:Bounds x="462" y="782" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="449" y="825" width="62" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_16ihqz2_di" bpmnElement="Flow_16ihqz2">
        <di:waypoint x="266" y="800" />
        <di:waypoint x="305" y="800" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_015hyoo_di" bpmnElement="Flow_015hyoo">
        <di:waypoint x="405" y="800" />
        <di:waypoint x="462" y="800" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_1c9ytji_di" bpmnElement="Event_1hhbrmg">
        <dc:Bounds x="1252" y="452" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1238" y="495" width="65" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_1yhsn59_di" bpmnElement="TextAnnotation_1yhsn59">
        <dc:Bounds x="450" y="292" width="100" height="55" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0zrvk3w_di" bpmnElement="Flow_0zrvk3w_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585">
        <di:waypoint x="208" y="430" />
        <di:waypoint x="330" y="430" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05n9zzu_di" bpmnElement="de">
        <di:waypoint x="380" y="390" />
        <di:waypoint x="380" y="80" />
        <di:waypoint x="1270" y="80" />
        <di:waypoint x="1270" y="452" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="755" y="63" width="73" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pyoasn_di" bpmnElement="Flow_1pyoasn">
        <di:waypoint x="430" y="430" />
        <di:waypoint x="630" y="430" />
        <di:waypoint x="630" y="330" />
        <di:waypoint x="840" y="330" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="690" y="313" width="78" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bicpdx_di" bpmnElement="Flow_1bicpdx">
        <di:waypoint x="430" y="430" />
        <di:waypoint x="840" y="430" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="693" y="413" width="74" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_141sxww_di" bpmnElement="Flow_141sxww">
        <di:waypoint x="430" y="430" />
        <di:waypoint x="630" y="430" />
        <di:waypoint x="630" y="530" />
        <di:waypoint x="840" y="530" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="692" y="513" width="74" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0egr0mp_di" bpmnElement="Flow_0egr0mp">
        <di:waypoint x="430" y="430" />
        <di:waypoint x="630" y="430" />
        <di:waypoint x="630" y="630" />
        <di:waypoint x="840" y="630" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="693" y="613" width="74" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0e142zu_di" bpmnElement="Flow_0e142zu">
        <di:waypoint x="430" y="430" />
        <di:waypoint x="630" y="430" />
        <di:waypoint x="630" y="740" />
        <di:waypoint x="840" y="740" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="691" y="723" width="78" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05ykizl_di" bpmnElement="Flow_05ykizl">
        <di:waypoint x="430" y="430" />
        <di:waypoint x="630" y="430" />
        <di:waypoint x="630" y="230" />
        <di:waypoint x="840" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="693" y="213" width="74" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1y7ccjw_di" bpmnElement="Flow_1y7ccjw">
        <di:waypoint x="940" y="430" />
        <di:waypoint x="1110" y="430" />
        <di:waypoint x="1110" y="470" />
        <di:waypoint x="1252" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0tfq8te_di" bpmnElement="Flow_0tfq8te">
        <di:waypoint x="940" y="330" />
        <di:waypoint x="1110" y="330" />
        <di:waypoint x="1110" y="470" />
        <di:waypoint x="1252" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1lcm64c_di" bpmnElement="Flow_1lcm64c">
        <di:waypoint x="940" y="230" />
        <di:waypoint x="1110" y="230" />
        <di:waypoint x="1110" y="470" />
        <di:waypoint x="1252" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19o02xu_di" bpmnElement="Flow_19o02xu">
        <di:waypoint x="940" y="530" />
        <di:waypoint x="1110" y="530" />
        <di:waypoint x="1110" y="470" />
        <di:waypoint x="1252" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0hor84w_di" bpmnElement="Flow_0hor84w">
        <di:waypoint x="940" y="630" />
        <di:waypoint x="1110" y="630" />
        <di:waypoint x="1110" y="470" />
        <di:waypoint x="1252" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0go33ro_di" bpmnElement="Flow_0go33ro">
        <di:waypoint x="940" y="740" />
        <di:waypoint x="1110" y="740" />
        <di:waypoint x="1110" y="470" />
        <di:waypoint x="1252" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_1tnubaf_di" bpmnElement="Association_1tnubaf">
        <di:waypoint x="422" y="390" />
        <di:waypoint x="466" y="347" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>