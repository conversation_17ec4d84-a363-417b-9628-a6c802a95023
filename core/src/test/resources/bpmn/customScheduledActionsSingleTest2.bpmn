<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_0ghypro" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.4.0">
  <bpmn:process id="customScheduledActions_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" name="Testing Recurrence bpmn2" processType="None" isClosed="false" isExecutable="true">
    <bpmn:sequenceFlow id="Flow_07dv1jk_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" sourceRef="customStartEvent_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" targetRef="decisionElement_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" />
    <bpmn:businessRuleTask id="decisionElement_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" name="DMN Rule Processor" implementation="##unspecified" camunda:resultVariable="decision" camunda:decisionRef="decisionElement_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" camunda:mapDecisionResult="singleResult">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{ "taskHandler": "was"}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_07dv1jk_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_19rqyz0_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:outgoing>
    </bpmn:businessRuleTask>
    <bpmn:sequenceFlow id="SequenceFlow_19rqyz0_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" sourceRef="decisionElement_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" targetRef="recurrenceStartDate_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" />
    <bpmn:sequenceFlow id="SequenceFlow_0htwpqa_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" sourceRef="recurrenceStartDate_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" targetRef="recurrenceSchedule_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" />
    <bpmn:intermediateCatchEvent id="recurrenceStartDate_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" name="Start Date">
      <bpmn:incoming>SequenceFlow_19rqyz0_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0htwpqa_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:outgoing>
      <bpmn:timerEventDefinition>
        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">${startDate}</bpmn:timeDuration>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:startEvent id="customStartEvent_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" name="start process">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="currentStepDetails" value="{ &#34;required&#34;: true }" />
          <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
          <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;was&#34;}" />
          <camunda:property name="startableEvents" value="[&#34;created&#34;]" />
          <camunda:property name="processVariablesDetails" value="[{&#34;variableName&#34;:&#34;CompanyName&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;entityChangeType&#34;,&#34;variableType&#34;:&#34;String&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;Customer&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;CustomerEmail&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;CompanyEmail&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;Id&#34;,&#34;variableType&#34;:&#34;String&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;CustomerName&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;StatementDate&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;intuit_userid&#34;,&#34;variableType&#34;:&#34;String&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;intuit_realmid&#34;,&#34;variableType&#34;:&#34;String&#34;,&#34;overrideIfAbsent&#34;:true}]" />
          <camunda:property name="stepDetails" value="{&#34;customStartEvent_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21&#34;:[&#34;recurrenceStartDate_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21&#34;,&#34;customStartEvent_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21&#34;,&#34;recurrenceSchedule_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21&#34;,&#34;scheduledActions_subProcess_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21&#34;,&#34;decisionElement_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21&#34;,&#34;sendStatement_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21&#34;,&#34;subProcess_startEvent_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21&#34;]}" />
          <camunda:property name="recurrenceDetails" value="{&#34;recurrenceStartDate&#34;:&#34;recurrenceStartDate_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21&#34;,&#34;recurrenceSchedule&#34;:&#34;recurrenceSchedule_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21&#34;}" />
          <camunda:property name="recurrenceRule" value="{&#34;recurType&#34;:&#34;MONTHLY&#34;,&#34;interval&#34;:1,&#34;daysOfMonth&#34;:[1,25],&#34;startDate&#34;:{&#34;centuryOfEra&#34;:20,&#34;yearOfEra&#34;:2021,&#34;yearOfCentury&#34;:21,&#34;weekyear&#34;:2021,&#34;monthOfYear&#34;:8,&#34;weekOfWeekyear&#34;:30,&#34;hourOfDay&#34;:0,&#34;minuteOfHour&#34;:0,&#34;secondOfMinute&#34;:0,&#34;millisOfSecond&#34;:0,&#34;millisOfDay&#34;:0,&#34;secondOfDay&#34;:0,&#34;minuteOfDay&#34;:0,&#34;year&#34;:2021,&#34;dayOfMonth&#34;:1,&#34;dayOfWeek&#34;:7,&#34;era&#34;:1,&#34;dayOfYear&#34;:213,&#34;chronology&#34;:{&#34;zone&#34;:{&#34;uncachedZone&#34;:{&#34;cachable&#34;:true,&#34;fixed&#34;:false,&#34;id&#34;:&#34;Asia/Kolkata&#34;},&#34;fixed&#34;:false,&#34;id&#34;:&#34;Asia/Kolkata&#34;}},&#34;zone&#34;:{&#34;uncachedZone&#34;:{&#34;cachable&#34;:true,&#34;fixed&#34;:false,&#34;id&#34;:&#34;Asia/Kolkata&#34;},&#34;fixed&#34;:false,&#34;id&#34;:&#34;Asia/Kolkata&#34;},&#34;millis&#34;:1627756200000,&#34;afterNow&#34;:false,&#34;beforeNow&#34;:true,&#34;equalNow&#34;:false},&#34;active&#34;:true,&#34;$sdk_validated&#34;:true}" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_07dv1jk_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:intermediateCatchEvent id="recurrenceSchedule_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" name="Recur Event">
      <bpmn:incoming>SequenceFlow_0htwpqa_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:incoming>
      <bpmn:incoming>Flow_0ar3k7h_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_17vcf21_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_1hyy4xv">
        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">${cronExpression}</bpmn:timeDuration>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:subProcess id="SubProcess_0ynplnm_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" name="Close Process" triggeredByEvent="true">
      <bpmn:sequenceFlow id="SequenceFlow_18kou84_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" sourceRef="StartEvent_0c9w8if_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" targetRef="EndEvent_0ayhj3y_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" />
      <bpmn:endEvent id="EndEvent_0ayhj3y_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" name="End Process Event">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
              "taskHandler": "was",
              "handlerId": "",
              "actionName": "updateProcessStatus"
              }</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>SequenceFlow_18kou84_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:incoming>
        <bpmn:messageEventDefinition messageRef="Message_0n66iem" camunda:type="external" camunda:topic="scheduledActions" />
      </bpmn:endEvent>
      <bpmn:startEvent id="StartEvent_0c9w8if_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" name="End Process">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;: &#34;was&#34; }" />
            <camunda:property name="targetApi" value="trigger" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:outgoing>SequenceFlow_18kou84_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_1nlqu1b" messageRef="Message_0tschbo" />
      </bpmn:startEvent>
    </bpmn:subProcess>
    <bpmn:subProcess id="scheduledActions_subProcess_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" name="scheduledActions subprocess">
      <bpmn:incoming>SequenceFlow_17vcf21_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:incoming>
      <bpmn:outgoing>Flow_0ar3k7h_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0z5zmbj_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:outgoing>
      <bpmn:sequenceFlow id="SequenceFlow_18ykyxq_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" sourceRef="sendStatement_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" targetRef="subProcess_endEvent_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" />
      <bpmn:sequenceFlow id="SequenceFlow_0gtcv0e_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" sourceRef="subProcess_startEvent_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" targetRef="sendStatement_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21">
        <conditionExpression xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" id="conditionExpression_6044821a-b5c2-42d8-acea-0c738a45de18">${true}</conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sendTask id="sendStatement_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" name="Schedule Action" implementation="##WebService" camunda:type="external" camunda:topic="scheduledActions">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="parameterDetails">{"CC":{"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"BCC":{"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"Message":{"fieldValue":["Dear [[CustomerName]],\n\nHere's a friendly reminder to take a look at your statement dated [[StatementDate]].\n\nRegards,\n[[CompanyName]]"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"endDate":{"configurable":true,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string"},"SendTo":{"fieldValue":["[[CustomerEmail]]"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string","handlerFieldName":"To"},"CompanyEmail":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CompanyEmail"},"StatementDate":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"StatementDate"},"Subject":{"fieldValue":["Statement from [[CompanyName]]"],"configurable":true,"requiredByHandler":true,"requiredByUI":true,"multiSelect":false,"fieldType":"string"},"CompanyName":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CompanyName"},"CustomerEmail":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CustomerEmail"},"statementType":{"configurable":true,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string"},"customerIds":{"configurable":true,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string"},"CustomerName":{"configurable":false,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string","valueType":"PROCESS_VARIABLE","handlerFieldName":"CustomerName"},"startDate":{"configurable":true,"requiredByHandler":true,"requiredByUI":false,"multiSelect":false,"fieldType":"string"}}</camunda:inputParameter>
            <camunda:inputParameter name="handlerDetails">{"taskHandler":"appconnect","actionName":"executeWorkflowAction","handlerId":"intuit-workflows/was-send-statement","recordType":null,"responseFields":null,"handlerScope":null}</camunda:inputParameter>
            <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>SequenceFlow_0gtcv0e_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_18ykyxq_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:outgoing>
      </bpmn:sendTask>
      <bpmn:endEvent id="subProcess_endEvent_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" name="end">
        <bpmn:incoming>SequenceFlow_18ykyxq_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:startEvent id="subProcess_startEvent_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" name="start">
        <bpmn:outgoing>SequenceFlow_0gtcv0e_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:outgoing>
      </bpmn:startEvent>
    </bpmn:subProcess>
    <bpmn:sequenceFlow id="Flow_0ar3k7h_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" sourceRef="scheduledActions_subProcess_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" targetRef="recurrenceSchedule_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" />
    <bpmn:sequenceFlow id="SequenceFlow_17vcf21_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" sourceRef="recurrenceSchedule_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" targetRef="scheduledActions_subProcess_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" />
    <bpmn:endEvent id="EndEvent_14edhmm_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" name="End Process">
      <bpmn:incoming>SequenceFlow_0z5zmbj_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_0z5zmbj_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" sourceRef="scheduledActions_subProcess_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" targetRef="EndEvent_14edhmm_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" />
  </bpmn:process>
  <bpmn:message id="Message_10uji9x" name="approved_rejected" />
  <bpmn:message id="Message_1lsccjr" name="customWait" />
  <bpmn:message id="Message_1vqffdi" name="end_process" />
  <bpmn:escalation id="Escalation_1f2mfy4" name="close_task" escalationCode="closetask" />
  <bpmn:message id="Message_0n66iem" name="process_ended_message" />
  <bpmn:escalation id="Escalation_0aq0q52" name="end_process" escalationCode="endprocess" />
  <bpmn:message id="Message_0tschbo" name="deleted_voided_disable" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="customScheduledActions_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21">
      <bpmndi:BPMNEdge id="SequenceFlow_0z5zmbj_di" bpmnElement="SequenceFlow_0z5zmbj_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21">
        <di:waypoint x="1000" y="170" />
        <di:waypoint x="1082" y="170" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_17vcf21_di" bpmnElement="SequenceFlow_17vcf21_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21">
        <di:waypoint x="608" y="180" />
        <di:waypoint x="690" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ar3k7h_di" bpmnElement="Flow_0ar3k7h_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21">
        <di:waypoint x="845" y="260" />
        <di:waypoint x="845" y="300" />
        <di:waypoint x="590" y="300" />
        <di:waypoint x="590" y="198" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0htwpqa_di" bpmnElement="SequenceFlow_0htwpqa_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21">
        <di:waypoint x="498" y="180" />
        <di:waypoint x="572" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_19rqyz0_di" bpmnElement="SequenceFlow_19rqyz0_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21">
        <di:waypoint x="400" y="180" />
        <di:waypoint x="462" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07dv1jk_di" bpmnElement="Flow_07dv1jk_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21">
        <di:waypoint x="208" y="180" />
        <di:waypoint x="300" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_0io4hr8_di" bpmnElement="decisionElement_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21">
        <dc:Bounds x="300" y="140" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_1yp99e8_di" bpmnElement="recurrenceStartDate_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21">
        <dc:Bounds x="462" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="455" y="132" width="50" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_08rwoar_di" bpmnElement="customStartEvent_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21">
        <dc:Bounds x="172" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="158" y="138" width="63" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_10itik3_di" bpmnElement="recurrenceSchedule_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21">
        <dc:Bounds x="572" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="559" y="138" width="61" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="SubProcess_02sjcl2_di" bpmnElement="SubProcess_0ynplnm_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" isExpanded="true">
        <dc:Bounds x="200" y="330" width="258" height="130" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_18kou84_di" bpmnElement="SequenceFlow_18kou84_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21">
        <di:waypoint x="256" y="390" />
        <di:waypoint x="402" y="390" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_19xjaph_di" bpmnElement="EndEvent_0ayhj3y_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21">
        <dc:Bounds x="402" y="372" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="389" y="415" width="63" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_0shfj58_di" bpmnElement="StartEvent_0c9w8if_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21">
        <dc:Bounds x="220" y="372" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="207" y="415" width="63" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="SubProcess_1q4vnui_di" bpmnElement="scheduledActions_subProcess_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21" isExpanded="true">
        <dc:Bounds x="690" y="80" width="310" height="180" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0gtcv0e_di" bpmnElement="SequenceFlow_0gtcv0e_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21">
        <di:waypoint x="748" y="180" />
        <di:waypoint x="790" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_18ykyxq_di" bpmnElement="SequenceFlow_18ykyxq_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21">
        <di:waypoint x="890" y="180" />
        <di:waypoint x="942" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="SendTask_1mez1bd_di" bpmnElement="sendStatement_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21">
        <dc:Bounds x="790" y="140" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_05a3wl3_di" bpmnElement="subProcess_endEvent_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21">
        <dc:Bounds x="942" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="950" y="205" width="19" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_0k8yemu_di" bpmnElement="subProcess_startEvent_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21">
        <dc:Bounds x="712" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="720" y="205" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_14edhmm_di" bpmnElement="EndEvent_14edhmm_9130352478836126_b68fcd58-4947-483e-9b9f-f44382e83b21">
        <dc:Bounds x="1082" y="152" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1069" y="195" width="63" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
