<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="definitions_e09ab49a-7e13-4e70-ad5b-bbe68363eec9" targetNamespace="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL">
  <process camunda:historyTimeToLive="7" id="customReminder_4b93cecf62e999145694f2ce564fb89b" isClosed="false" isExecutable="true" name="Multi Condition Reminder" processType="None">
    <startEvent camunda:asyncBefore="true" id="startEvent" name="startEvent">
      <extensionElements>
        <camunda:properties>
          <camunda:property name="stepDetails" value="{}"/>
          <camunda:property name="taskDetails" value="{ &quot;required&quot;: true }"/>
          <camunda:property name="currentStepDetails" value="{ &quot;required&quot;: true }"/>
          <camunda:property name="processVariablesDetails" value="[     {         &quot;variableName&quot;: &quot;entityChangeType&quot;,         &quot;variableType&quot;: &quot;String&quot;     },     {         &quot;variableName&quot;: &quot;Id&quot;,         &quot;variableType&quot;: &quot;String&quot;     },     {         &quot;variableName&quot;: &quot;intuit_userid&quot;,         &quot;variableType&quot;: &quot;String&quot;     },     {         &quot;variableName&quot;: &quot;intuit_realmid&quot;,         &quot;variableType&quot;: &quot;String&quot;     },         {         &quot;variableName&quot;: &quot;DocNumber&quot;,         &quot;variableType&quot;: &quot;string&quot;,         &quot;overrideIfAbsent&quot;: true     },         {         &quot;variableName&quot;: &quot;CompanyName&quot;,         &quot;variableType&quot;: &quot;string&quot;,         &quot;overrideIfAbsent&quot;: true     },         {         &quot;variableName&quot;: &quot;entityType&quot;,         &quot;variableType&quot;: &quot;String&quot;,         &quot;overrideIfAbsent&quot;: true     } ]"/>
          <camunda:property name="handlerDetails" value="{  &quot;taskHandler&quot;: &quot;was&quot;}"/>
          <camunda:property name="startableEvents" value="[&quot;newCustomStart&quot;]"/>
          <camunda:property name="ignoreNonEntityProcessVariablesDetails" value="true"/>
          <camunda:property name="elementType" value="implicit"/>
        </camunda:properties>
      </extensionElements>
      <outgoing>sequenceFlow_to_businessRuleTask-1</outgoing>
    </startEvent>
    <sequenceFlow id="sequenceFlow_to_businessRuleTask-1" sourceRef="startEvent" targetRef="businessRuleTask-1_4b93cecf62e999145694f2ce564fb89b"/>
    <businessRuleTask camunda:topic="multi_custom_reminder" camunda:type="external" id="businessRuleTask-1_4b93cecf62e999145694f2ce564fb89b" name="Txn Rule Evaluation">
      <extensionElements>
        <camunda:properties>
          <camunda:property name="elementType" value="explicit"/>
        </camunda:properties>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{ "taskHandler": "was","handlerId":"decision_customReminder","actionName": "evaluateDMN"}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
        </camunda:inputOutput>
      </extensionElements>
      <incoming>sequenceFlow_to_businessRuleTask-1</incoming>
      <outgoing>sequenceFlow_to_callActivity-1</outgoing>
    </businessRuleTask>
    <sequenceFlow id="sequenceFlow_to_callActivity-1" sourceRef="businessRuleTask-1_4b93cecf62e999145694f2ce564fb89b" targetRef="callActivity-1">
      <conditionExpression id="conditionExpression_0b40b2f9-de0a-47be-93da-ae6400b879bd">${decisionResult=='callActivity-1'}</conditionExpression>
    </sequenceFlow>
    <callActivity calledElement="sendForReminder" camunda:calledElementBinding="latest" id="callActivity-1" name="Reminder">
      <extensionElements>
        <camunda:properties>
          <camunda:property name="elementType" value="explicit"/>
        </camunda:properties>
        <camunda:in businessKey="#{execution.processBusinessKey}" source="" target="" variables=""/>
        <camunda:in businessKey="" source="" target="" variables="all"/>
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
          <camunda:outputParameter name="rootProcessInstanceId">${null}</camunda:outputParameter>
        </camunda:inputOutput>
        <camunda:out source="" target="" variables="all"/>
      </extensionElements>
      <incoming>sequenceFlow_to_callActivity-1</incoming>
      <outgoing>sequenceFlow_82c86d63-1d71-4dcb-9566-899c142fae3e</outgoing>
    </callActivity>
    <endEvent id="endEvent" name="endEvent">
      <extensionElements>
        <camunda:properties>
          <camunda:property name="elementType" value="implicit"/>
        </camunda:properties>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
              "taskHandler": "was",
              "handlerId": "",
              "actionName": "updateProcessStatus"
              }</camunda:inputParameter>
        </camunda:inputOutput>
      </extensionElements>
      <incoming>sequenceFlow_82c86d63-1d71-4dcb-9566-899c142fae3e</incoming>
      <messageEventDefinition camunda:topic="custom-reminders" camunda:type="external" id="messageEventDefinition_9e38adb9-13e1-4ece-a2ba-f10b781eb0a8" messageRef="message_1a44acc3-00a9-4718-9f1b-df7a89c419c1"/>
    </endEvent>
    <sequenceFlow id="sequenceFlow_82c86d63-1d71-4dcb-9566-899c142fae3e" sourceRef="callActivity-1" targetRef="endEvent"/>
  </process>
  <message id="message_1a44acc3-00a9-4718-9f1b-df7a89c419c1" name="process_ended_message"/>
  <bpmndi:BPMNDiagram id="BPMNDiagram_a1e33dc8-de9a-4442-aab9-7e4d68a24e62">
    <bpmndi:BPMNPlane bpmnElement="customReminder_4b93cecf62e999145694f2ce564fb89b" id="BPMNPlane_f5005e77-e36f-4672-864c-8fc146804b77">
      <bpmndi:BPMNShape bpmnElement="startEvent" id="BPMNShape_bcca854a-1a40-4bcb-bfa3-9dac600d90bb">
        <dc:Bounds height="36.0" width="36.0" x="100.0" y="100.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="businessRuleTask-1_4b93cecf62e999145694f2ce564fb89b" id="BPMNShape_93e67453-7d5f-4975-9b06-2437b8f03575">
        <dc:Bounds height="80.0" width="100.0" x="186.0" y="78.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sequenceFlow_to_businessRuleTask-1" id="BPMNEdge_540ab5c9-c858-4de2-94a3-107777ec6db4">
        <di:waypoint x="136.0" y="118.0"/>
        <di:waypoint x="186.0" y="118.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape bpmnElement="callActivity-1" id="BPMNShape_a1d75efb-0351-47e7-92b0-17bd59166cbe">
        <dc:Bounds height="80.0" width="100.0" x="336.0" y="78.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sequenceFlow_to_callActivity-1" id="BPMNEdge_ce6a48c9-4ad7-40c3-9190-bc1f07a9479b">
        <di:waypoint x="286.0" y="118.0"/>
        <di:waypoint x="336.0" y="118.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape bpmnElement="endEvent" id="BPMNShape_0a5275c6-2366-472c-9b09-d554aaee52aa">
        <dc:Bounds height="36.0" width="36.0" x="486.0" y="100.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sequenceFlow_82c86d63-1d71-4dcb-9566-899c142fae3e" id="BPMNEdge_3cbddc71-d82c-4c2d-97a4-34b63993cf92">
        <di:waypoint x="436.0" y="118.0"/>
        <di:waypoint x="486.0" y="118.0"/>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>