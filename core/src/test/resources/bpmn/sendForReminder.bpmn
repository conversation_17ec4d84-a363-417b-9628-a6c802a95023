<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bioc="http://bpmn.io/schema/bpmn/biocolor/1.0" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:color="http://www.omg.org/spec/BPMN/non-normative/color/1.0" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_1ueeqzk" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.13.0">
  <bpmn:process id="sendForReminder" name="Reminder Template" processType="None" isClosed="false" isExecutable="true" camunda:historyTimeToLive="7">
    <bpmn:extensionElements />
    <bpmn:startEvent id="customStartEvent" name="start process" camunda:asyncBefore="true">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
          <camunda:property name="currentStepDetails" value="{ &#34;required&#34;: true }" />
          <camunda:property name="processVariablesDetails" value="[{&#34;variableName&#34;: &#34;entityChangeType&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;: &#34;Id&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;: &#34;intuit_userid&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;: &#34;intuit_realmid&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;: &#34;SyncToken&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;: &#34;entityType&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;: &#34;TxnStatus&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;: &#34;TxnPaymentStatus&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;: &#34;TxnSendStatus&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;: &#34;TxnDueDays&#34;, &#34;variableType&#34;: &#34;integer&#34;}]" />
          <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;: &#34;appconnect&#34;, &#34;handlerId&#34;: &#34;intuit-workflows/custom-reminder-start-process&#34;, &#34;actionName&#34;: &#34;executeWorkflowAction&#34;}" />
          <camunda:property name="parameterDetails" value="{}" />
          <camunda:property name="startableEvents" value="[&#34;newCustomStart&#34;]" />
          <camunda:property name="targetApi" value="evaluate-and-trigger" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_1hqa9g0</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:subProcess id="Activity_108jjaa" triggeredByEvent="true">
      <bpmn:serviceTask id="closeTask" name="Close Project service task" implementation="##WebService" camunda:type="external" camunda:topic="ondemand-approvals-psinha10">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
              "taskHandler": "appconnect",
              "handlerId": "intuit-workflows/taskmanager-update-task",
              "actionName": "executeDuzzitRestAction"
              }</camunda:inputParameter>
            <camunda:inputParameter name="parameterDetails">{
              "projectId": {
              "fieldValue": [],
              "handlerFieldName": "Project",
              "requiredByHandler": true,
              "requiredByUI": false,
              "fieldType": "string",
              "valueType": "PROCESS_VARIABLE"
              },
              "Status": {
              "fieldValue": [
              "Complete"
              ],
              "requiredByHandler": true,
              "requiredByUI": false,
              "multiSelect": false,
              "fieldType": "string"
              }
              }</camunda:inputParameter>
            <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_16ihqz2</bpmn:incoming>
        <bpmn:outgoing>Flow_0pxn6yi</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:endEvent id="Event_0ey8tt4" name="End process">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
              "taskHandler": "was",
              "handlerId": "",
              "actionName": "updateProcessStatus"
              }</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1w9beds</bpmn:incoming>
        <bpmn:messageEventDefinition id="MessageEventDefinition_07qnodz" messageRef="Message_0p07vw4" camunda:type="external" camunda:topic="ondemand-approvals-psinha10" />
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="Flow_0pxn6yi" sourceRef="closeTask" targetRef="eventTaskClosed" />
      <bpmn:sequenceFlow id="Flow_16ihqz2" sourceRef="Event_0vepyso" targetRef="closeTask" />
      <bpmn:intermediateThrowEvent id="eventTaskClosed" name="Audit: Task Closed">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="events" value="[&#34;start&#34;]" />
            <camunda:property name="audit" value="{&#34;name&#34;:&#34;customReminder_eventTaskClosed_name&#34;, &#34;description&#34;:&#34;customReminder_eventTaskClosed_description&#34; }" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0pxn6yi</bpmn:incoming>
        <bpmn:outgoing>Flow_1w9beds</bpmn:outgoing>
      </bpmn:intermediateThrowEvent>
      <bpmn:sequenceFlow id="Flow_1w9beds" sourceRef="eventTaskClosed" targetRef="Event_0ey8tt4" />
      <bpmn:startEvent id="Event_0vepyso" name="Close task">
        <bpmn:outgoing>Flow_16ihqz2</bpmn:outgoing>
        <bpmn:escalationEventDefinition id="EscalationEventDefinition_1mbhdmo" escalationRef="Escalation_0tyjh9j" />
      </bpmn:startEvent>
    </bpmn:subProcess>
    <bpmn:subProcess id="SubProcess_0g6pybq" triggeredByEvent="true">
      <bpmn:startEvent id="customDeleteVoidTransactionEvent" name="Downgrade">
        <bpmn:outgoing>SequenceFlow_10dp848</bpmn:outgoing>
        <bpmn:outgoing>SequenceFlow_0y51f29</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_02j8ti4" messageRef="Message_1o1rayd" />
      </bpmn:startEvent>
      <bpmn:sequenceFlow id="SequenceFlow_10dp848" sourceRef="customDeleteVoidTransactionEvent" targetRef="EndEvent_1adbxdq" />
      <bpmn:sequenceFlow id="SequenceFlow_0y51f29" sourceRef="customDeleteVoidTransactionEvent" targetRef="eventDowngrade" />
      <bpmn:intermediateThrowEvent id="eventDowngrade" name="Audit: downgrade">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="events" value="[&#34;start&#34;]" />
            <camunda:property name="audit" value="{&#34;name&#34;:&#34;customReminder_eventDowngrade_name&#34;, &#34;description&#34;:&#34;customReminder_eventDowngrade_description&#34; }" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>SequenceFlow_0y51f29</bpmn:incoming>
        <bpmn:outgoing>Flow_0yf95mn</bpmn:outgoing>
      </bpmn:intermediateThrowEvent>
      <bpmn:sequenceFlow id="Flow_0yf95mn" sourceRef="eventDowngrade" targetRef="EndEvent_1adbxdq" />
      <bpmn:endEvent id="EndEvent_1adbxdq" name="End process">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
              "taskHandler": "was",
              "handlerId": "",
              "actionName": "updateProcessStatus"
              }</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>SequenceFlow_10dp848</bpmn:incoming>
        <bpmn:incoming>Flow_0yf95mn</bpmn:incoming>
        <bpmn:messageEventDefinition id="MessageEventDefinition_0dzw8q3" messageRef="Message_0p07vw4" camunda:type="external" camunda:topic="ondemand-approvals-psinha10" />
      </bpmn:endEvent>
    </bpmn:subProcess>
    <bpmn:subProcess id="SubProcess_0jw9z9g" triggeredByEvent="true">
      <bpmn:startEvent id="StartEvent_06qdmrv" name="Entity Delete">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;: &#34;appconnect&#34;, &#34;handlerId&#34;: &#34;intuit-workflows/entity-deleted-custom-workflow&#34;, &#34;actionName&#34;: &#34;executeWorkflowAction&#34; }" />
            <camunda:property name="targetApi" value="trigger" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:outgoing>SequenceFlow_0yb8p84</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_19pdfcz" messageRef="Message_1qdj486" />
      </bpmn:startEvent>
      <bpmn:sequenceFlow id="SequenceFlow_0yb8p84" sourceRef="StartEvent_06qdmrv" targetRef="eventEntityDeleted" />
      <bpmn:endEvent id="EndEvent_02ic7kr" name="End process">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
              "taskHandler": "was",
              "handlerId": "",
              "actionName": "updateProcessStatus"
              }</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1858lvn</bpmn:incoming>
        <bpmn:escalationEventDefinition escalationRef="Escalation_0tyjh9j" />
      </bpmn:endEvent>
      <bpmn:intermediateThrowEvent id="eventEntityDeleted" name="Audit: Entity deleted">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="events" value="[&#34;start&#34;]" />
            <camunda:property name="audit" value="{&#34;name&#34;:&#34;customReminder_eventEntityDeleted_name&#34;, &#34;description&#34;:&#34;customReminder_eventEntityDeleted_description&#34; }" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>SequenceFlow_0yb8p84</bpmn:incoming>
        <bpmn:outgoing>Flow_1858lvn</bpmn:outgoing>
      </bpmn:intermediateThrowEvent>
      <bpmn:sequenceFlow id="Flow_1858lvn" sourceRef="eventEntityDeleted" targetRef="EndEvent_02ic7kr" />
    </bpmn:subProcess>
    <bpmn:subProcess id="Activity_014mphf" name="Send Reminder" triggeredByEvent="true">
      <bpmn:sendTask id="sendCompanyEmail" name="Send company email" implementation="##WebService" camunda:type="external" camunda:topic="ondemand-approvals-psinha10">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
            <camunda:inputParameter name="handlerDetails">{}</camunda:inputParameter>
            <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
          </camunda:inputOutput>
          <camunda:properties>
            <camunda:property name="events" value="[&#34;end&#34;]" />
            <camunda:property name="audit" value="{&#34;name&#34;:&#34;customReminder_sendCompanyEmail_name&#34;, &#34;description&#34;:&#34;customReminder_sendCompanyEmail_description&#34; }" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1px5oze</bpmn:incoming>
        <bpmn:outgoing>Flow_1s7npyr</bpmn:outgoing>
      </bpmn:sendTask>
      <bpmn:inclusiveGateway id="Gateway_15jcpzo" name="Gateway">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0udau96</bpmn:incoming>
        <bpmn:outgoing>Flow_18br3ql</bpmn:outgoing>
        <bpmn:outgoing>Flow_1px5oze</bpmn:outgoing>
        <bpmn:outgoing>Flow_0wzv9ti</bpmn:outgoing>
        <bpmn:outgoing>Flow_17icf4r</bpmn:outgoing>
      </bpmn:inclusiveGateway>
      <bpmn:serviceTask id="createTask" name="Create task" implementation="##WebService" camunda:type="external" camunda:topic="ondemand-approvals-psinha10">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
            <camunda:inputParameter name="handlerDetails">{}</camunda:inputParameter>
            <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
          </camunda:inputOutput>
          <camunda:properties>
            <camunda:property name="events" value="[&#34;end&#34;]" />
            <camunda:property name="audit" value="{&#34;name&#34;:&#34;customReminder_createTask_name&#34;, &#34;description&#34;:&#34;customReminder_createTask_description&#34; }" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_18br3ql</bpmn:incoming>
        <bpmn:outgoing>Flow_0i77wgr</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="Flow_18br3ql" sourceRef="Gateway_15jcpzo" targetRef="createTask">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${(execution.hasVariable("createTask") == true) &amp;&amp; (execution.getVariable("createTask") == true) &amp;&amp; (execution.getVariable("scheduleCount") == null)}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="Flow_0udau96" sourceRef="Event_0iykdeu" targetRef="Gateway_15jcpzo" />
      <bpmn:sequenceFlow id="Flow_1px5oze" sourceRef="Gateway_15jcpzo" targetRef="sendCompanyEmail">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${(execution.hasVariable("sendCompanyEmail") == true) &amp;&amp; (execution.getVariable("sendCompanyEmail") == true)}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sendTask id="sendPushNotification" name="Send push notification" implementation="##WebService" camunda:type="external" camunda:topic="ondemand-approvals-psinha10">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
            <camunda:inputParameter name="handlerDetails">{}</camunda:inputParameter>
            <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
          </camunda:inputOutput>
          <camunda:properties>
            <camunda:property name="events" value="[&#34;end&#34;]" />
            <camunda:property name="audit" value="{&#34;name&#34;:&#34;customReminder_sendPushNotification_name&#34;, &#34;description&#34;:&#34;customReminder_sendPushNotification_description&#34; }" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0wzv9ti</bpmn:incoming>
        <bpmn:outgoing>Flow_1gsnhmz</bpmn:outgoing>
      </bpmn:sendTask>
      <bpmn:sendTask id="sendExternalEmail" name="Send external email" implementation="##WebService" camunda:type="external" camunda:topic="ondemand-approvals-psinha10">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
            <camunda:inputParameter name="handlerDetails">{}</camunda:inputParameter>
            <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
          </camunda:inputOutput>
          <camunda:properties>
            <camunda:property name="events" value="[&#34;end&#34;]" />
            <camunda:property name="audit" value="{&#34;name&#34;:&#34;customReminder_sendCompanyEmail_name&#34;, &#34;description&#34;:&#34;customReminder_sendCompanyEmail_description&#34; }" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_17icf4r</bpmn:incoming>
        <bpmn:outgoing>Flow_0vlhqy1</bpmn:outgoing>
      </bpmn:sendTask>
      <bpmn:sequenceFlow id="Flow_0wzv9ti" sourceRef="Gateway_15jcpzo" targetRef="sendPushNotification">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${(execution.hasVariable("sendPushNotification") == true) &amp;&amp; (execution.getVariable("sendPushNotification") == true)}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:sequenceFlow id="Flow_17icf4r" sourceRef="Gateway_15jcpzo" targetRef="sendExternalEmail">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${(execution.hasVariable("sendCompanyEmail") == true) &amp;&amp; (execution.getVariable("sendCompanyEmail") == true)}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:startEvent id="Event_0iykdeu" name="Recur" isInterrupting="false">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;: &#34;appconnect&#34;, &#34;handlerId&#34;: &#34;intuit-workflows/custom-reminder-start-process&#34;, &#34;actionName&#34;: &#34;executeWorkflowAction&#34;}" />
            <camunda:property name="targetApi" value="evaluate-and-trigger" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:outgoing>Flow_0udau96</bpmn:outgoing>
        <bpmn:escalationEventDefinition id="EscalationEventDefinition_0tf7lux" escalationRef="Escalation_1g7ubps" />
      </bpmn:startEvent>
      <bpmn:inclusiveGateway id="Gateway_0nxdfvb" name="Gateway">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0i77wgr</bpmn:incoming>
        <bpmn:incoming>Flow_1s7npyr</bpmn:incoming>
        <bpmn:incoming>Flow_1gsnhmz</bpmn:incoming>
        <bpmn:incoming>Flow_0vlhqy1</bpmn:incoming>
        <bpmn:outgoing>Flow_106qonx</bpmn:outgoing>
      </bpmn:inclusiveGateway>
      <bpmn:endEvent id="Event_0cqxzmx" name="End event">
        <bpmn:incoming>Flow_106qonx</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="Flow_106qonx" sourceRef="Gateway_0nxdfvb" targetRef="Event_0cqxzmx" />
      <bpmn:sequenceFlow id="Flow_0i77wgr" sourceRef="createTask" targetRef="Gateway_0nxdfvb" />
      <bpmn:sequenceFlow id="Flow_1s7npyr" sourceRef="sendCompanyEmail" targetRef="Gateway_0nxdfvb" />
      <bpmn:sequenceFlow id="Flow_1gsnhmz" sourceRef="sendPushNotification" targetRef="Gateway_0nxdfvb" />
      <bpmn:sequenceFlow id="Flow_0vlhqy1" sourceRef="sendExternalEmail" targetRef="Gateway_0nxdfvb" />
    </bpmn:subProcess>
    <bpmn:subProcess id="Activity_1oe2dcg" name="Recurring reminder " triggeredByEvent="true">
      <bpmn:documentation>This is multi instance sub process which is triggered for each customRecur request from WAS.</bpmn:documentation>
      <bpmn:startEvent id="Event_0gsth1o" name="Start Recurrence" isInterrupting="false">
        <bpmn:extensionElements />
        <bpmn:outgoing>Flow_03hbe8q</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_13n2si2" messageRef="Message_17r62hk" />
      </bpmn:startEvent>
      <bpmn:intermediateThrowEvent id="Event_1wabcvu" name="Recur">
        <bpmn:incoming>Flow_0yqytin</bpmn:incoming>
        <bpmn:escalationEventDefinition id="EscalationEventDefinition_030wapg" escalationRef="Escalation_1g7ubps" />
      </bpmn:intermediateThrowEvent>
      <bpmn:exclusiveGateway id="Gateway_0qxww8v" name="Check if schedule count is exhausted and lastReminderDate is not today">
        <bpmn:extensionElements />
        <bpmn:incoming>Flow_0wzdm2s</bpmn:incoming>
        <bpmn:incoming>Flow_0fm5hit</bpmn:incoming>
        <bpmn:outgoing>Flow_1x9xkb2</bpmn:outgoing>
        <bpmn:outgoing>Flow_1v25082</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:sequenceFlow id="Flow_1x9xkb2" sourceRef="Gateway_0qxww8v" targetRef="Event_1ppeh14">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${(execution.getVariable("scheduleCount") &lt; 4)}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:intermediateThrowEvent id="Event_1ppeh14" name="Update schedule count">
        <bpmn:extensionElements>
          <camunda:executionListener expression="${execution.setVariable(&#34;scheduleCount&#34;, execution.getVariable(&#34;scheduleCount&#34;) + 1)}" event="end" />
          <camunda:executionListener expression="${execution.setVariable(&#34;lastReminderDate&#34;,now())}" event="end" />
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1x9xkb2</bpmn:incoming>
        <bpmn:outgoing>Flow_0yqytin</bpmn:outgoing>
      </bpmn:intermediateThrowEvent>
      <bpmn:sequenceFlow id="Flow_0yqytin" sourceRef="Event_1ppeh14" targetRef="Event_1wabcvu" />
      <bpmn:sequenceFlow id="Flow_03hbe8q" sourceRef="Event_0gsth1o" targetRef="Gateway_02rf68d" />
      <bpmn:exclusiveGateway id="Gateway_02rf68d" name="Check if schedule count is initialised" default="Flow_1073v6j">
        <bpmn:incoming>Flow_03hbe8q</bpmn:incoming>
        <bpmn:outgoing>Flow_0wzdm2s</bpmn:outgoing>
        <bpmn:outgoing>Flow_1073v6j</bpmn:outgoing>
      </bpmn:exclusiveGateway>
      <bpmn:sequenceFlow id="Flow_0wzdm2s" sourceRef="Gateway_02rf68d" targetRef="Gateway_0qxww8v">
        <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${(execution.getVariable("scheduleCount") != null)}</bpmn:conditionExpression>
      </bpmn:sequenceFlow>
      <bpmn:intermediateThrowEvent id="Event_152vkfs" name="Initialise scheduleCount">
        <bpmn:extensionElements>
          <camunda:executionListener expression="${execution.setVariable(&#34;scheduleCount&#34;,0)}" event="start" />
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1073v6j</bpmn:incoming>
        <bpmn:outgoing>Flow_0fm5hit</bpmn:outgoing>
      </bpmn:intermediateThrowEvent>
      <bpmn:sequenceFlow id="Flow_1073v6j" sourceRef="Gateway_02rf68d" targetRef="Event_152vkfs" />
      <bpmn:sequenceFlow id="Flow_0fm5hit" sourceRef="Event_152vkfs" targetRef="Gateway_0qxww8v" />
      <bpmn:intermediateThrowEvent id="Event_0bitcuf" name="Do nothing">
        <bpmn:extensionElements />
        <bpmn:incoming>Flow_1v25082</bpmn:incoming>
      </bpmn:intermediateThrowEvent>
      <bpmn:sequenceFlow id="Flow_1v25082" sourceRef="Gateway_0qxww8v" targetRef="Event_0bitcuf" />
    </bpmn:subProcess>
    <bpmn:endEvent id="Event_0kb35fk" name="End process after the process expiration.">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
            "taskHandler": "was",
            "handlerId": "",
            "actionName": "updateProcessStatus"
            }</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_18nekhy</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0vwg5fd" messageRef="Message_0p07vw4" camunda:type="external" camunda:topic="ondemand-approvals-psinha4" />
    </bpmn:endEvent>
    <bpmn:intermediateThrowEvent id="eventTaskExpired" name="Audit: Task Expired">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="events" value="[&#34;start&#34;]" />
          <camunda:property name="audit" value="{&#34;name&#34;:&#34;customReminder_eventTaskExpired_name&#34;, &#34;description&#34;:&#34;customReminder_eventTaskExpired_description&#34; }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1fep73j</bpmn:incoming>
      <bpmn:outgoing>Flow_18nekhy</bpmn:outgoing>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_18nekhy" sourceRef="eventTaskExpired" targetRef="Event_0kb35fk" />
    <bpmn:intermediateThrowEvent id="Event_08qj4b4" name="Recur">
      <bpmn:incoming>Flow_0zjj30n</bpmn:incoming>
      <bpmn:outgoing>Flow_12xec93</bpmn:outgoing>
      <bpmn:escalationEventDefinition id="EscalationEventDefinition_14wx2ha" escalationRef="Escalation_1g7ubps" />
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_1hqa9g0" sourceRef="customStartEvent" targetRef="publishReminderDetails" />
    <bpmn:endEvent id="Event_1hhbrmg" name="State change occured">
      <bpmn:incoming>Flow_1xevg2t</bpmn:incoming>
      <bpmn:escalationEventDefinition id="EscalationEventDefinition_0ay2jrq" escalationRef="Escalation_0tyjh9j" />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_12xec93" sourceRef="Event_08qj4b4" targetRef="customWorkflowWaitEvent" />
    <bpmn:receiveTask id="customWorkflowWaitEvent" name="Wait for state change" implementation="##WebService" messageRef="Message_064b9px">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{"taskHandler": "appconnect", "handlerId": "intuit-workflows/custom-reminder-wait", "actionName": "executeWorkflowAction" }</camunda:inputParameter>
          <camunda:inputParameter name="currentStepDetails">{ "required": false }</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
          <camunda:inputParameter name="targetApi">trigger</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_12xec93</bpmn:incoming>
      <bpmn:outgoing>Flow_1xevg2t</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:sequenceFlow id="Flow_1xevg2t" sourceRef="customWorkflowWaitEvent" targetRef="Event_1hhbrmg" />
    <bpmn:boundaryEvent id="Event_1tx361d" name="Expiry Event" attachedToRef="customWorkflowWaitEvent">
      <bpmn:outgoing>Flow_1fep73j</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_1dgqopk">
        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">P15D</bpmn:timeDuration>
      </bpmn:timerEventDefinition>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_1fep73j" sourceRef="Event_1tx361d" targetRef="eventTaskExpired" />
    <bpmn:serviceTask id="publishReminderDetails" name="Init Call Activity" implementation="##WebService" camunda:type="external" camunda:topic="ondemand-approvals-psinha10">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
    "taskHandler": "was",
    "handlerId": "",
    "actionName": "initCallActivity"
}</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
          <camunda:outputParameter name="projectServiceMap">
            <camunda:map />
          </camunda:outputParameter>
          <camunda:outputParameter name="projectTaskDetailIds">
            <camunda:list />
          </camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1hqa9g0</bpmn:incoming>
      <bpmn:outgoing>Flow_0zjj30n</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0zjj30n" sourceRef="publishReminderDetails" targetRef="Event_08qj4b4" />
  </bpmn:process>
  <bpmn:escalation id="Escalation_0tyjh9j" name="close_task" escalationCode="closetask" />
  <bpmn:message id="Message_0p07vw4" name="process_ended_message" />
  <bpmn:message id="Message_064b9px" name="customWait" />
  <bpmn:message id="Message_1o1rayd" name="deleted_voided_disable" />
  <bpmn:message id="Message_172hcy9" name="cleanup" />
  <bpmn:message id="Message_1qdj486" name="custom_deleted" />
  <bpmn:message id="Message_1qar486" name="custom_deleted1" />
  <bpmn:message id="Message_1gksdgr" name="start_new" />
  <bpmn:message id="Message_08f1m8l" name="escalated" />
  <bpmn:escalation id="Escalation_1g7ubps" name="escalated_1" escalationCode="escalated_1" />
  <bpmn:message id="Message_1oeiv49" />
  <bpmn:escalation id="Escalation_0vrhi87" name="close_tasks" escalationCode="closeTasks" />
  <bpmn:message id="Message_0aih20s" name="customRecur" />
  <bpmn:message id="Message_17r62hk" name="customRecur" />
  <bpmn:escalation id="Escalation_3cnubvc" name="customRecur" escalationCode="customRecur" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="sendForReminder">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="customStartEvent">
        <dc:Bounds x="1032" y="112" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1019" y="155" width="63" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_108jjaa_di" bpmnElement="Activity_108jjaa" isExpanded="true">
        <dc:Bounds x="750" y="480" width="440" height="200" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1t3pzu3_di" bpmnElement="closeTask">
        <dc:Bounds x="870" y="540" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0ey8tt4_di" bpmnElement="Event_0ey8tt4">
        <dc:Bounds x="1108" y="562" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1095" y="605" width="62" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_14geeeh" bpmnElement="eventTaskClosed" bioc:stroke="#0d4372" bioc:fill="#bbdefb" color:background-color="#bbdefb" color:border-color="#0d4372">
        <dc:Bounds x="1028" y="562" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1020" y="525" width="54" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1z04k75_di" bpmnElement="Event_0vepyso">
        <dc:Bounds x="790" y="562" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="782" y="605" width="52" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0pxn6yi_di" bpmnElement="Flow_0pxn6yi">
        <di:waypoint x="970" y="580" />
        <di:waypoint x="1028" y="580" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16ihqz2_di" bpmnElement="Flow_16ihqz2">
        <di:waypoint x="826" y="580" />
        <di:waypoint x="870" y="580" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1w9beds_di" bpmnElement="Flow_1w9beds">
        <di:waypoint x="1064" y="580" />
        <di:waypoint x="1108" y="580" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="SubProcess_0g6pybq_di" bpmnElement="SubProcess_0g6pybq" isExpanded="true">
        <dc:Bounds x="1250" y="480" width="350" height="200" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_1cl4dtq_di" bpmnElement="customDeleteVoidTransactionEvent">
        <dc:Bounds x="1290" y="562" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1281" y="605" width="57" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0uck4jr" bpmnElement="eventDowngrade" bioc:stroke="#0d4372" bioc:fill="#bbdefb" color:background-color="#bbdefb" color:border-color="#0d4372">
        <dc:Bounds x="1392" y="562" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1368" y="605" width="86" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1adbxdq_di" bpmnElement="EndEvent_1adbxdq">
        <dc:Bounds x="1502" y="562" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1489" y="605" width="62" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0y51f29_di" bpmnElement="SequenceFlow_0y51f29">
        <di:waypoint x="1326" y="580" />
        <di:waypoint x="1392" y="580" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yf95mn_di" bpmnElement="Flow_0yf95mn">
        <di:waypoint x="1428" y="580" />
        <di:waypoint x="1502" y="580" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="SubProcess_0jw9z9g_di" bpmnElement="SubProcess_0jw9z9g" isExpanded="true">
        <dc:Bounds x="1650" y="480" width="350" height="200" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_06qdmrv_di" bpmnElement="StartEvent_06qdmrv">
        <dc:Bounds x="1690" y="562" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1678" y="605" width="63" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_11fhe8y_di" bpmnElement="EndEvent_02ic7kr">
        <dc:Bounds x="1902" y="562" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1889" y="605" width="62" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_09j5ed6" bpmnElement="eventEntityDeleted" bioc:stroke="#0d4372" bioc:fill="#bbdefb" color:background-color="#bbdefb" color:border-color="#0d4372">
        <dc:Bounds x="1787" y="562" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1777" y="605" width="59" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0yb8p84_di" bpmnElement="SequenceFlow_0yb8p84">
        <di:waypoint x="1726" y="580" />
        <di:waypoint x="1787" y="580" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1858lvn_di" bpmnElement="Flow_1858lvn">
        <di:waypoint x="1823" y="580" />
        <di:waypoint x="1902" y="580" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_014mphf_di" bpmnElement="Activity_014mphf" isExpanded="true" bioc:stroke="rgb(251, 140, 0)" bioc:fill="rgb(255, 224, 178)">
        <dc:Bounds x="1330" y="730" width="780" height="560" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_17qde3n_di" bpmnElement="sendCompanyEmail">
        <dc:Bounds x="1730" y="930" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_15jcpzo_di" bpmnElement="Gateway_15jcpzo">
        <dc:Bounds x="1544" y="875" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1522" y="853" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1t6fi7p" bpmnElement="createTask">
        <dc:Bounds x="1730" y="760" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1obzsyq" bpmnElement="sendPushNotification">
        <dc:Bounds x="1730" y="1040" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_13djzk9" bpmnElement="sendExternalEmail">
        <dc:Bounds x="1730" y="1170" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_15w0bgo_di" bpmnElement="Event_0iykdeu">
        <dc:Bounds x="1372" y="882" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1377" y="925" width="30" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0nxdfvb_di" bpmnElement="Gateway_0nxdfvb">
        <dc:Bounds x="1915" y="945" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1893" y="923" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0cqxzmx_di" bpmnElement="Event_0cqxzmx">
        <dc:Bounds x="2032" y="952" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2026" y="995" width="50" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_18br3ql_di" bpmnElement="Flow_18br3ql">
        <di:waypoint x="1569" y="875" />
        <di:waypoint x="1569" y="800" />
        <di:waypoint x="1730" y="800" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0udau96_di" bpmnElement="Flow_0udau96">
        <di:waypoint x="1408" y="900" />
        <di:waypoint x="1544" y="900" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1px5oze_di" bpmnElement="Flow_1px5oze">
        <di:waypoint x="1569" y="925" />
        <di:waypoint x="1569" y="970" />
        <di:waypoint x="1730" y="970" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wzv9ti_di" bpmnElement="Flow_0wzv9ti">
        <di:waypoint x="1569" y="925" />
        <di:waypoint x="1569" y="1080" />
        <di:waypoint x="1730" y="1080" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_17icf4r_di" bpmnElement="Flow_17icf4r">
        <di:waypoint x="1569" y="925" />
        <di:waypoint x="1569" y="1210" />
        <di:waypoint x="1730" y="1210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_106qonx_di" bpmnElement="Flow_106qonx">
        <di:waypoint x="1965" y="970" />
        <di:waypoint x="2032" y="970" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0i77wgr_di" bpmnElement="Flow_0i77wgr">
        <di:waypoint x="1830" y="800" />
        <di:waypoint x="1940" y="800" />
        <di:waypoint x="1940" y="945" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1s7npyr_di" bpmnElement="Flow_1s7npyr">
        <di:waypoint x="1830" y="970" />
        <di:waypoint x="1915" y="970" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gsnhmz_di" bpmnElement="Flow_1gsnhmz">
        <di:waypoint x="1830" y="1080" />
        <di:waypoint x="1940" y="1080" />
        <di:waypoint x="1940" y="995" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vlhqy1_di" bpmnElement="Flow_0vlhqy1">
        <di:waypoint x="1830" y="1210" />
        <di:waypoint x="1940" y="1210" />
        <di:waypoint x="1940" y="995" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_0oifb8q_di" bpmnElement="Activity_1oe2dcg" isExpanded="true" bioc:stroke="rgb(251, 140, 0)" bioc:fill="rgb(255, 224, 178)">
        <dc:Bounds x="150" y="745" width="1050" height="335" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_076yhfb_di" bpmnElement="Event_0gsth1o">
        <dc:Bounds x="202" y="897" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="181" y="940" width="84" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1hajlm8" bpmnElement="Event_1wabcvu">
        <dc:Bounds x="1112" y="897" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1116" y="940" width="30" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0qxww8v_di" bpmnElement="Gateway_0qxww8v" isMarkerVisible="true">
        <dc:Bounds x="775" y="890" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="815" y="837" width="89" height="66" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_16pxio3" bpmnElement="Event_1ppeh14">
        <dc:Bounds x="962" y="897" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="939" y="940" width="83" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_02rf68d_di" bpmnElement="Gateway_02rf68d" isMarkerVisible="true">
        <dc:Bounds x="365" y="890" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="348" y="947" width="87" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_152vkfs_di" bpmnElement="Event_152vkfs">
        <dc:Bounds x="572" y="897" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="558" y="940" width="74" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_18n53ff" bpmnElement="Event_0bitcuf">
        <dc:Bounds x="782" y="992" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="774" y="1035" width="54" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1x9xkb2_di" bpmnElement="Flow_1x9xkb2">
        <di:waypoint x="825" y="915" />
        <di:waypoint x="962" y="915" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yqytin_di" bpmnElement="Flow_0yqytin">
        <di:waypoint x="998" y="915" />
        <di:waypoint x="1112" y="915" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03hbe8q_di" bpmnElement="Flow_03hbe8q">
        <di:waypoint x="238" y="915" />
        <di:waypoint x="365" y="915" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wzdm2s_di" bpmnElement="Flow_0wzdm2s">
        <di:waypoint x="390" y="890" />
        <di:waypoint x="390" y="815" />
        <di:waypoint x="800" y="815" />
        <di:waypoint x="800" y="890" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1073v6j_di" bpmnElement="Flow_1073v6j">
        <di:waypoint x="415" y="915" />
        <di:waypoint x="572" y="915" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fm5hit_di" bpmnElement="Flow_0fm5hit">
        <di:waypoint x="608" y="915" />
        <di:waypoint x="775" y="915" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1v25082_di" bpmnElement="Flow_1v25082">
        <di:waypoint x="800" y="940" />
        <di:waypoint x="800" y="992" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_1risnpd_di" bpmnElement="Event_0kb35fk">
        <dc:Bounds x="1582" y="322" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1557" y="365" width="87" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0krb04c_di" bpmnElement="eventTaskExpired" bioc:stroke="#0d4372" bioc:fill="#bbdefb" color:background-color="#bbdefb" color:border-color="#0d4372">
        <dc:Bounds x="1582" y="232" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1523" y="236" width="54" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0eyibie" bpmnElement="Event_08qj4b4">
        <dc:Bounds x="1402" y="112" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1406" y="155" width="30" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_00zaz0i_di" bpmnElement="Event_1hhbrmg">
        <dc:Bounds x="1772" y="112" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1758" y="155" width="65" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1guy81g_di" bpmnElement="customWorkflowWaitEvent">
        <dc:Bounds x="1550" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="publishApprovalDetails_di" bpmnElement="publishReminderDetails">
        <dc:Bounds x="1230" y="90" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1fte3ra_di" bpmnElement="Event_1tx361d">
        <dc:Bounds x="1582" y="142" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1571" y="185" width="62" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_18nekhy_di" bpmnElement="Flow_18nekhy">
        <di:waypoint x="1600" y="268" />
        <di:waypoint x="1600" y="322" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hqa9g0_di" bpmnElement="Flow_1hqa9g0">
        <di:waypoint x="1068" y="130" />
        <di:waypoint x="1230" y="130" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12xec93_di" bpmnElement="Flow_12xec93">
        <di:waypoint x="1438" y="130" />
        <di:waypoint x="1550" y="130" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xevg2t_di" bpmnElement="Flow_1xevg2t">
        <di:waypoint x="1650" y="130" />
        <di:waypoint x="1772" y="130" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fep73j_di" bpmnElement="Flow_1fep73j">
        <di:waypoint x="1600" y="178" />
        <di:waypoint x="1600" y="232" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zjj30n_di" bpmnElement="Flow_0zjj30n">
        <di:waypoint x="1330" y="130" />
        <di:waypoint x="1402" y="130" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
