<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_1b49cs1" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="3.3.1">
    <bpmn:process id="InvoiceApproval" name="Invoice Approval" isExecutable="true">
        <bpmn:extensionElements>
            <camunda:properties>
                <camunda:property name="description" value="Invoice approval" />
            </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:startEvent id="newInvoiceCreated_invoiceApproval" name="Invoice create event" camunda:asyncAfter="true">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;was&#34;, &#34;recordType&#34;: &#34;invoice&#34;, &#34;eventType&#34;: &#34;create&#34;}" />
                    <camunda:property name="stepDetails" value="{     &#34;newInvoiceCreated_invoiceApproval&#34;: [         &#34;newInvoiceCreated_invoiceApproval&#34;,         &#34;invoiceApprovalDecision_invoiceApproval_companyId_uuid&#34;,         &#34;sendApprovalEmail_invoiceApproval&#34;,         &#34;sendApprovalNotification_invoiceApproval&#34;,         &#34;waitForDeleteOrVoid_invoiceApproval&#34;     ],     &#34;waitForApproval1_invoiceApproval&#34;: [         &#34;waitForApproval1_invoiceApproval&#34;,         &#34;exclusiveGateway_invoiceApproval&#34;,         &#34;sequenceApproved_invoiceApproval&#34;,         &#34;sequenceRejected_invoiceApproval&#34;,         &#34;sendApproveNotification_invoiceApproval&#34;,         &#34;sendRejectNotification_invoiceApproval&#34;     ],     &#34;waitForTimerToElapse1_invoiceApproval&#34;: [         &#34;waitForTimerToElapse1_invoiceApproval&#34;,         &#34;evaluateUserDefinedAction_invoiceApproval&#34;,         &#34;sequenceAutoUpdate_invoiceApproval&#34;,         &#34;sequenceSendReminder_invoiceApproval&#34;,         &#34;sendReminderEmail_invoiceApproval&#34;,         &#34;autoUpdateAsApproved_invoiceApproval&#34;     ],     &#34;waitForApproval2_invoiceApproval&#34;: [         &#34;waitForApproval2_invoiceApproval&#34;,         &#34;exclusiveGateway_invoiceApproval&#34;,         &#34;sequenceApproved_invoiceApproval&#34;,         &#34;sequenceRejected_invoiceApproval&#34;,         &#34;sendApproveNotification_invoiceApproval&#34;,         &#34;sendRejectNotification_invoiceApproval&#34;     ],     &#34;waitForTimerToElapse2_invoiceApproval&#34;: [         &#34;waitForTimerToElapse2_invoiceApproval&#34;,         &#34;autoRejectInvoice_invoiceApproval&#34;     ] }" />
                    <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
                    <camunda:property name="workflowStepId" value="1_uuid" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:outgoing>SequenceFlow_0wlw2u2</bpmn:outgoing>
        </bpmn:startEvent>
        <bpmn:sequenceFlow id="SequenceFlow_0wlw2u2" sourceRef="newInvoiceCreated_invoiceApproval" targetRef="invoiceApprovalDecision_invoiceApproval_companyId_uuid" />
        <bpmn:businessRuleTask id="invoiceApprovalDecision_invoiceApproval_companyId_uuid" name="Invoice approval rule evaluation" camunda:resultVariable="decision" camunda:decisionRef="invoiceApprovalDecision_invoiceApproval_companyId_uuid" camunda:mapDecisionResult="singleResult">
            <bpmn:incoming>SequenceFlow_0wlw2u2</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_1tjh86i</bpmn:outgoing>
            <bpmn:outgoing>SequenceFlow_1b3jp0q</bpmn:outgoing>
            <bpmn:outgoing>SequenceFlow_0i7yvuf</bpmn:outgoing>
            <bpmn:outgoing>sequenceToWait1_invoiceApproval</bpmn:outgoing>
        </bpmn:businessRuleTask>
        <bpmn:sequenceFlow id="SequenceFlow_1tjh86i" name="Send Approval Mail" sourceRef="invoiceApprovalDecision_invoiceApproval_companyId_uuid" targetRef="sendApprovalEmail_invoiceApproval">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decision.sendApprovalEmail == true}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="SequenceFlow_1b3jp0q" name="Send Approval Notification" sourceRef="invoiceApprovalDecision_invoiceApproval_companyId_uuid" targetRef="sendApprovalNotification_invoiceApproval">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decision.sendApprovalNotification == true}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:endEvent id="end1_invoiceApproval" name="No Rules Matched">
            <bpmn:incoming>SequenceFlow_0i7yvuf</bpmn:incoming>
        </bpmn:endEvent>
        <bpmn:sequenceFlow id="SequenceFlow_0i7yvuf" name="No Rules matched" sourceRef="invoiceApprovalDecision_invoiceApproval_companyId_uuid" targetRef="end1_invoiceApproval">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">null</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:endEvent id="end2_invoiceApproval" name="Invoice deleted or voided">
            <bpmn:incoming>SequenceFlow_0yj2hhl</bpmn:incoming>
        </bpmn:endEvent>
        <bpmn:sequenceFlow id="SequenceFlow_0yj2hhl" sourceRef="waitForDeleteOrVoid_invoiceApproval" targetRef="end2_invoiceApproval" />
        <bpmn:parallelGateway id="parallelGateway_invoiceApproval">
            <bpmn:incoming>SequenceFlow_1np3xsy</bpmn:incoming>
            <bpmn:incoming>SequenceFlow_130o5z8</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_1k50exh</bpmn:outgoing>
        </bpmn:parallelGateway>
        <bpmn:sequenceFlow id="SequenceFlow_1np3xsy" sourceRef="sendApprovalEmail_invoiceApproval" targetRef="parallelGateway_invoiceApproval" />
        <bpmn:sequenceFlow id="SequenceFlow_130o5z8" sourceRef="sendApprovalNotification_invoiceApproval" targetRef="parallelGateway_invoiceApproval" />
        <bpmn:sequenceFlow id="SequenceFlow_1k50exh" sourceRef="parallelGateway_invoiceApproval" targetRef="waitForApproval1_invoiceApproval" />
        <bpmn:receiveTask id="waitForApproval1_invoiceApproval" name="Wait for approval" messageRef="Message_1kwhedj">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "recordType": "invoice",
                        "eventType": "update"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails" />
                    <camunda:inputParameter name="workflowStepId">3_uuid</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_1k50exh</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_1i082sj</bpmn:outgoing>
        </bpmn:receiveTask>
        <bpmn:boundaryEvent id="waitForTimerToElapse1_invoiceApproval" attachedToRef="waitForApproval1_invoiceApproval">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;camunda&#34;}" />
                    <camunda:property name="taskDetails" value="{&#34;required&#34;: true}" />
                    <camunda:property name="workflowStepId" value="4_uuid" />
                    <camunda:property name="parameterDetails" value="{  &#34;waitTime&#34;: {     &#34;fieldValue&#34; : [&#34;5&#34;],     &#34;configurable&#34; : true,     &#34;actionByUI&#34; : null,     &#34;requiredByhandler&#34; : false,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;integer&#34;   }}" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:outgoing>SequenceFlow_1s8x0ie</bpmn:outgoing>
            <bpmn:timerEventDefinition>
                <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">${approvalWaitTime}</bpmn:timeDuration>
            </bpmn:timerEventDefinition>
        </bpmn:boundaryEvent>
        <bpmn:exclusiveGateway id="evaluateUserDefinedAction_invoiceApproval" name="evaluate condition gateway" default="SequenceFlow_13ovayn">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;: &#34;UI&#34;}" />
                    <camunda:property name="taskDetails" value="{&#34;required&#34;: true}" />
                    <camunda:property name="parameterDetails" value="&#34;To&#34;: {                         &#34;fieldValue&#34; : [&#34;Admins&#34;],                         &#34;configurable&#34; : true,                         &#34;actionByUI&#34; : &#34;GET_ADMINS_ID&#34;,                         &#34;requiredByHandler&#34; : true,                         &#34;requiredByUI&#34;: true,                         &#34;multiSelect&#34;: true,                         &#34;fieldType&#34;: &#34;string&#34;                         },                         &#34;Subject&#34;: {                         &#34;fieldValue&#34; : [&#34;Hey you ahve an invoice to approve&#34;],                         &#34;configurable&#34; : true,                         &#34;actionByUI&#34; : null,                         &#34;requiredByhandler&#34; : true,                         &#34;requiredByUI&#34;: true,                         &#34;multiSelect&#34;: false,                         &#34;fieldType&#34;: &#34;string&#34;                         },                         &#34;Message&#34; : {                         &#34;fieldValue&#34; : [&#34;blah blah link blah&#34;],                         &#34;configurable&#34; : true,                         &#34;actionByUI&#34; : null,                         &#34;requiredByhandler&#34; : true,                         &#34;requiredByUI&#34;: true,                         &#34;helpVariables&#34;: &#34;QBOCompanyName&#34;,                         &#34;multiSelect&#34;: false,                         &#34;fieldType&#34;: &#34;string&#34;                         }                         }" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_1s8x0ie</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_13ovayn</bpmn:outgoing>
            <bpmn:outgoing>SequenceFlow_1ppvb4p</bpmn:outgoing>
        </bpmn:exclusiveGateway>
        <bpmn:sequenceFlow id="SequenceFlow_1s8x0ie" sourceRef="waitForTimerToElapse1_invoiceApproval" targetRef="evaluateUserDefinedAction_invoiceApproval" />
        <bpmn:sequenceFlow id="SequenceFlow_13ovayn" sourceRef="evaluateUserDefinedAction_invoiceApproval" targetRef="autoUpdateAsApproved_invoiceApproval" />
        <bpmn:endEvent id="end3_invoiceApproval" name="Invoice auto approved because of time out">
            <bpmn:incoming>SequenceFlow_0con4l0</bpmn:incoming>
        </bpmn:endEvent>
        <bpmn:sequenceFlow id="SequenceFlow_0con4l0" sourceRef="autoUpdateAsApproved_invoiceApproval" targetRef="end3_invoiceApproval" />
        <bpmn:serviceTask id="autoUpdateAsApproved_invoiceApproval" name="Auto approve invoice status" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:type="external" camunda:topic="workflow">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "actionName": "autoUpdateInvoiceStatus",
                        "handlerId": "5656",
                        "actionName": "appconnectWorkflowTaskHandler"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{
                        "required": true
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails">{
                        "invoiceId": {
                        "fieldValue" : ["12345"],
                        "configurable" : false,
                        "actionByUI" : null,
                        "requiredByhandler" : true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "long"
                        },
                        "invoiceStatus": {
                        "fieldValue" : ["approved"],
                        "configurable" : false,
                        "actionByUI" : null,
                        "requiredByhandler" : true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "displayName": {
                        "fieldValue" : ["auto approve invoice"],
                        "configurable" : false,
                        "actionByUI" : null,
                        "requiredByhandler" : false,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "string"
                        }
                        }</camunda:inputParameter>
                    <camunda:outputParameter name="outputResponse" />
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_13ovayn</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_0con4l0</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="SequenceFlow_1ppvb4p" name="Reminder action chosen by user" sourceRef="evaluateUserDefinedAction_invoiceApproval" targetRef="sendReminderEmail_invoiceApproval">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${userDefined.sendApprovalReminder == true}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sendTask id="sendReminderEmail_invoiceApproval" name="Send reminder" camunda:class="CamundaTaskDelegate">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "duzz/send-email",
                        "actionName": "executeDummyAction",
                        "actionName": "appconnectWorkflowTaskHandler"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails">{
                        "To": {
                        "fieldValue" : ["Admins"],
                        "configurable" : true,
                        "actionByUI" : "GET_ADMINS_ID",
                        "requiredByHandler" : true,
                        "requiredByUI": true,
                        "multiSelect": true,
                        "fieldType": "string"
                        },
                        "Subject": {
                        "fieldValue" : ["Hey you ahve an invoice to approve"],
                        "configurable" : true,
                        "actionByUI" : null,
                        "requiredByHandler" : true,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Message" : {
                        "fieldValue" : ["blah blah link blah"],
                        "configurable" : true,
                        "actionByUI" : null,
                        "requiredByhandler" : true,
                        "requiredByUI": true,
                        "hepVariables": "QBOCompanyName",
                        "multiSelect": false,
                        "fieldType": "string"
                        }
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{ "uiVisibility": true }</camunda:inputParameter>
                    <camunda:outputParameter name="outputRsponse" />
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_1ppvb4p</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_1fxku6c</bpmn:outgoing>
        </bpmn:sendTask>
        <bpmn:exclusiveGateway id="exclusiveGateway_invoiceApproval" name="approved?" camunda:asyncBefore="true" camunda:asyncAfter="true" default="SequenceFlow_1jc9t5y">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="handlerDetails" value="{                         &#34;taskHandler&#34;: &#34;appconnect&#34;,                         &#34;handlerId&#34;: &#34;1234&#34;,                         &#34;handlerName&#34;: &#34;sendEmail&#34;                         }" />
                    <camunda:property name="parameterDetails" value="{                         &#34;invoice.status&#34;: {                         &#34;fieldValue&#34; : [&#34;approved&#34;],                         &#34;configurable&#34; : false,                         &#34;actionByUI&#34; : null,                         &#34;requiredByhandler&#34; : false,                         &#34;requiredByUI&#34;: false,                         &#34;multiSelect&#34;: false,                         &#34;fieldType&#34;: &#34;string&#34;                         }}" />
                    <camunda:property name="taskDetails" value="{&#34;required&#34;: true}" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_1i082sj</bpmn:incoming>
            <bpmn:incoming>SequenceFlow_1b5d3z9</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_1jc9t5y</bpmn:outgoing>
            <bpmn:outgoing>SequenceFlow_1va0bl5</bpmn:outgoing>
        </bpmn:exclusiveGateway>
        <bpmn:sequenceFlow id="SequenceFlow_1i082sj" name="sequence_flow" sourceRef="waitForApproval1_invoiceApproval" targetRef="exclusiveGateway_invoiceApproval">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">null</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="SequenceFlow_1jc9t5y" name="no" sourceRef="exclusiveGateway_invoiceApproval" targetRef="sendRejectNotification_invoiceApproval" />
        <bpmn:sequenceFlow id="SequenceFlow_1va0bl5" name="yes" sourceRef="exclusiveGateway_invoiceApproval" targetRef="sendApproveNotification_invoiceApproval">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">null</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:serviceTask id="sendRejectNotification_invoiceApproval" name="Send notification to creator of invoice" camunda:class="CamundaTaskDelegate">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "9876",
                        "actionName":"sendNotificationToAuthor",
                        "actionName": "appconnectWorkflowTaskHandler"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails">{
                        "To": {
                        "fieldValue" : ["userId"],
                        "configurable" : true,
                        "actionByUI" : "GET_AUTHOR_ID",
                        "requiredByhandler" : true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "long"
                        },
                        "Subject": {
                        "fieldValue" : ["Hey you ahve an invoice to approve"],
                        "configurable" : true,
                        "actionByUI" : null,
                        "requiredByhandler" : true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "long"
                        }
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{ "required": false }</camunda:inputParameter>
                    <camunda:outputParameter name="outputRsponse" />
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_1jc9t5y</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_1o5mfah</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:serviceTask id="sendApproveNotification_invoiceApproval" name="Send notification to creator of invoice" camunda:class="CamundaTaskDelegate">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "5432",
                        "actionName": "sendNotificationToAuthor",
                        "actionName": "appconnectWorkflowTaskHandler"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails">{
                        "To": {
                        "fieldValue": ["creator"],
                        "configurable": true,
                        "actionByUI": "GET_AUTHOR_ID",
                        "requiredByhandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "long"
                        },
                        "Subject": {
                        "fieldValue": [
                        "hey your invoice rejected"
                        ],
                        "configurable": true,
                        "actionByUI": null,
                        "requiredByhandler": true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "long"
                        }
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{ "required": false }</camunda:inputParameter>
                    <camunda:outputParameter name="outputRsponse" />
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_1va0bl5</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_1barys0</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:receiveTask id="waitForApproval2_invoiceApproval" name="Wait for approval" messageRef="Message_1kwhedj">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "recordType": "invoice",
                        "eventType": "delete"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails" />
                    <camunda:inputParameter name="workflowStepId">5_uuid</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_1fxku6c</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_1b5d3z9</bpmn:outgoing>
        </bpmn:receiveTask>
        <bpmn:endEvent id="end5_invoiceApproval" name="Invoice approved">
            <bpmn:incoming>SequenceFlow_1o5mfah</bpmn:incoming>
        </bpmn:endEvent>
        <bpmn:sequenceFlow id="SequenceFlow_1o5mfah" sourceRef="sendRejectNotification_invoiceApproval" targetRef="end5_invoiceApproval" />
        <bpmn:endEvent id="end6_invoiceApproval" name="Invoice rejected">
            <bpmn:incoming>SequenceFlow_1barys0</bpmn:incoming>
        </bpmn:endEvent>
        <bpmn:sequenceFlow id="SequenceFlow_1barys0" sourceRef="sendApproveNotification_invoiceApproval" targetRef="end6_invoiceApproval" />
        <bpmn:boundaryEvent id="BoundaryEvent_0pff0pb" attachedToRef="waitForApproval2_invoiceApproval">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;camunda&#34;}" />
                    <camunda:property name="taskDetails" value="{&#34;required&#34;: false}" />
                    <camunda:property name="workflowStepId" value="6_uuid" />
                    <camunda:property name="parameterDetails" value="{  &#34;waitTime&#34;: {     &#34;fieldValue&#34; : [&#34;30&#34;],    &#34;configurable&#34; : true,     &#34;actionByUI&#34; : null,     &#34;requiredByhandler&#34; : false,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;integer&#34;   }}" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:outgoing>SequenceFlow_15d9ff3</bpmn:outgoing>
            <bpmn:timerEventDefinition>
                <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">${userDefined.approvalReminderWaitTime}</bpmn:timeDuration>
            </bpmn:timerEventDefinition>
        </bpmn:boundaryEvent>
        <bpmn:sequenceFlow id="SequenceFlow_15d9ff3" sourceRef="BoundaryEvent_0pff0pb" targetRef="autoRejectInvoice_invoiceApproval" />
        <bpmn:endEvent id="end4_invoiceApproval" name="Invoice status reset">
            <bpmn:incoming>SequenceFlow_0kbmcbj</bpmn:incoming>
        </bpmn:endEvent>
        <bpmn:sequenceFlow id="SequenceFlow_0kbmcbj" sourceRef="autoRejectInvoice_invoiceApproval" targetRef="end4_invoiceApproval" />
        <bpmn:sequenceFlow id="SequenceFlow_1b5d3z9" name="Wait for Approval" sourceRef="waitForApproval2_invoiceApproval" targetRef="exclusiveGateway_invoiceApproval">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">null</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="SequenceFlow_1fxku6c" sourceRef="sendReminderEmail_invoiceApproval" targetRef="waitForApproval2_invoiceApproval" />
        <bpmn:sendTask id="sendApprovalEmail_invoiceApproval" name="Send approval email" camunda:asyncAfter="true" camunda:type="external" camunda:topic="workflow">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "duzz/send-email",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails">{
                        "To": {
                        "fieldValue" : ["<EMAIL>"],
                        "configurable" : true,
                        "actionByUI" : "GET_ADMINS_ID",
                        "requiredByHandler" : true,
                        "requiredByUI": true,
                        "multiSelect": true,
                        "fieldType": "string"
                        },
                        "Subject": {
                        "fieldValue" : ["Invoice [[QB:DocNumber]] requires approval"],
                        "configurable" : true,
                        "actionByUI" : null,
                        "requiredByHandler" : true,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Message" : {
                        "fieldValue" : ["Invoice for [[QB:CustomerName]] for $[[QB:Amount]] requires approval.\nRegards"],
                        "configurable" : true,
                        "actionByUI" : null,
                        "requiredByHandler" : true,
                        "requiredByUI": true,
                        "helpVariables": "QBOCompanyName",
                        "multiSelect": false,
                        "fieldType": "string"
                        }
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
                    <camunda:outputParameter name="outputRsponse" />
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_1tjh86i</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_1np3xsy</bpmn:outgoing>
        </bpmn:sendTask>
        <bpmn:sendTask id="sendApprovalNotification_invoiceApproval" name="Send approval notification" camunda:asyncAfter="true" camunda:type="external" camunda:topic="workflow">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "5678",
                        "actionName": "appconnectWorkflowTaskHandler"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails">{
                        "To": {
                        "fieldValue" : ["Admins"],
                        "configurable" : true,
                        "actionByUI" : "GET_ADMINS_ID",
                        "requiredByhandler" : true,
                        "requiredByUI": true,
                        "multiSelect": true,
                        "fieldType": "string"
                        },
                        "Subject": {
                        "fieldValue" : ["Hey you ahve an invoice to approve"],
                        "configurable" : true,
                        "actionByUI" : null,
                        "requiredByhandler" : true,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "string"
                        }
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
                    <camunda:outputParameter name="outputRsponse" />
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_1b3jp0q</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_130o5z8</bpmn:outgoing>
        </bpmn:sendTask>
        <bpmn:receiveTask id="waitForDeleteOrVoid_invoiceApproval" name="Wait for delete or void">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "recordType": "invoice",
                        "eventType": "delete"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails" />
                    <camunda:inputParameter name="workflowStepId">2_uuid</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>sequenceToWait1_invoiceApproval</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_0yj2hhl</bpmn:outgoing>
        </bpmn:receiveTask>
        <bpmn:sequenceFlow id="sequenceToWait1_invoiceApproval" name="wait for delete or void" sourceRef="invoiceApprovalDecision_invoiceApproval_companyId_uuid" targetRef="waitForDeleteOrVoid_invoiceApproval">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${(decision.sendApprovalEmail == true) || (decision.sendApprovalNotification == true)}</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:serviceTask id="autoRejectInvoice_invoiceApproval" name="Auto reject invoice" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:class="CamundaTaskDelegate">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "actionName": "autoRejectInvoiceStatus",
                        "handlerId": "5555",
                        "actionName": "appconnectWorkflowTaskHandler"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="taskDetails">{
                        "required": true
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails">{
                        "invoiceId": {
                        "fieldValue" : ["12345"],
                        "configurable" : false,
                        "actionByUI" : null,
                        "requiredByhandler" : true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "long"
                        },
                        "invoiceStatus": {
                        "fieldValue" : ["approved"],
                        "configurable" : false,
                        "actionByUI" : null,
                        "requiredByhandler" : true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "displayName": {
                        "fieldValue" : ["auto approve invoice"],
                        "configurable" : false,
                        "actionByUI" : null,
                        "requiredByhandler" : false,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "string"
                        }
                        }</camunda:inputParameter>
                    <camunda:outputParameter name="outputResponse" />
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_15d9ff3</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_0kbmcbj</bpmn:outgoing>
        </bpmn:serviceTask>
    </bpmn:process>
    <bpmn:message id="Message_1kwhedj" name="Message_32llkbg" />
    <bpmndi:BPMNDiagram id="BPMNDiagram_1">
        <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="InvoiceApproval">
            <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="newInvoiceCreated_invoiceApproval">
                <dc:Bounds x="179" y="249" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="163" y="292" width="69" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_0wlw2u2_di" bpmnElement="SequenceFlow_0wlw2u2">
                <di:waypoint x="215" y="267" />
                <di:waypoint x="270" y="267" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="BusinessRuleTask_1dknt8m_di" bpmnElement="invoiceApprovalDecision_invoiceApproval_companyId_uuid">
                <dc:Bounds x="270" y="227" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_1tjh86i_di" bpmnElement="SequenceFlow_1tjh86i">
                <di:waypoint x="370" y="240" />
                <di:waypoint x="550" y="240" />
                <di:waypoint x="550" y="210" />
                <di:waypoint x="570" y="210" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="423" y="206" width="73" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_1b3jp0q_di" bpmnElement="SequenceFlow_1b3jp0q">
                <di:waypoint x="370" y="260" />
                <di:waypoint x="500" y="260" />
                <di:waypoint x="500" y="320" />
                <di:waypoint x="570" y="320" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="413" y="266" width="73" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="EndEvent_0w3m2og_di" bpmnElement="end1_invoiceApproval">
                <dc:Bounds x="302" y="82" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="297" y="52" width="46" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_0i7yvuf_di" bpmnElement="SequenceFlow_0i7yvuf">
                <di:waypoint x="320" y="227" />
                <di:waypoint x="320" y="118" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="327" y="156" width="46" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="EndEvent_1wtycrj_di" bpmnElement="end2_invoiceApproval">
                <dc:Bounds x="452" y="522" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="427" y="565" width="87" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_0yj2hhl_di" bpmnElement="SequenceFlow_0yj2hhl">
                <di:waypoint x="420" y="400" />
                <di:waypoint x="359" y="400" />
                <di:waypoint x="359" y="540" />
                <di:waypoint x="452" y="540" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="ParallelGateway_1ea0681_di" bpmnElement="parallelGateway_invoiceApproval">
                <dc:Bounds x="725" y="235" width="50" height="50" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_1np3xsy_di" bpmnElement="SequenceFlow_1np3xsy">
                <di:waypoint x="670" y="210" />
                <di:waypoint x="750" y="210" />
                <di:waypoint x="750" y="235" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_130o5z8_di" bpmnElement="SequenceFlow_130o5z8">
                <di:waypoint x="670" y="320" />
                <di:waypoint x="750" y="320" />
                <di:waypoint x="750" y="285" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_1k50exh_di" bpmnElement="SequenceFlow_1k50exh">
                <di:waypoint x="775" y="260" />
                <di:waypoint x="850" y="260" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="ReceiveTask_138kpyo_di" bpmnElement="waitForApproval1_invoiceApproval">
                <dc:Bounds x="850" y="220" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BoundaryEvent_0dnlwws_di" bpmnElement="waitForTimerToElapse1_invoiceApproval">
                <dc:Bounds x="882" y="282" width="36" height="36" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="ExclusiveGateway_1hj385z_di" bpmnElement="evaluateUserDefinedAction_invoiceApproval" isMarkerVisible="true">
                <dc:Bounds x="875" y="385" width="50" height="50" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="777" y="400" width="89" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_1s8x0ie_di" bpmnElement="SequenceFlow_1s8x0ie">
                <di:waypoint x="900" y="318" />
                <di:waypoint x="900" y="385" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_13ovayn_di" bpmnElement="SequenceFlow_13ovayn">
                <di:waypoint x="900" y="435" />
                <di:waypoint x="900" y="490" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="EndEvent_0dyaiff_di" bpmnElement="end3_invoiceApproval">
                <dc:Bounds x="702" y="512" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="681" y="555" width="78" height="53" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_0con4l0_di" bpmnElement="SequenceFlow_0con4l0">
                <di:waypoint x="850" y="530" />
                <di:waypoint x="738" y="530" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="ServiceTask_1pcitrg_di" bpmnElement="autoUpdateAsApproved_invoiceApproval">
                <dc:Bounds x="850" y="490" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_1ppvb4p_di" bpmnElement="SequenceFlow_1ppvb4p">
                <di:waypoint x="925" y="410" />
                <di:waypoint x="970" y="410" />
                <di:waypoint x="970" y="474" />
                <di:waypoint x="1080" y="474" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="979" y="426" width="81" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="SendTask_0wqss0t_di" bpmnElement="sendReminderEmail_invoiceApproval">
                <dc:Bounds x="1080" y="434" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="ExclusiveGateway_1nm0mns_di" bpmnElement="exclusiveGateway_invoiceApproval" isMarkerVisible="true">
                <dc:Bounds x="1145" y="235" width="50" height="50" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1104" y="283" width="52" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_1i082sj_di" bpmnElement="SequenceFlow_1i082sj">
                <di:waypoint x="950" y="260" />
                <di:waypoint x="1145" y="260" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1012" y="242" width="74" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_1jc9t5y_di" bpmnElement="SequenceFlow_1jc9t5y">
                <di:waypoint x="1170" y="235" />
                <di:waypoint x="1170" y="160" />
                <di:waypoint x="1350" y="160" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1179" y="195" width="13" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_1va0bl5_di" bpmnElement="SequenceFlow_1va0bl5">
                <di:waypoint x="1195" y="260" />
                <di:waypoint x="1350" y="260" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1264" y="242" width="18" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="ServiceTask_1xohxku_di" bpmnElement="sendRejectNotification_invoiceApproval">
                <dc:Bounds x="1350" y="120" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="ServiceTask_0kuqrzk_di" bpmnElement="sendApproveNotification_invoiceApproval">
                <dc:Bounds x="1350" y="220" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="ReceiveTask_1uytbw7_di" bpmnElement="waitForApproval2_invoiceApproval">
                <dc:Bounds x="1350" y="360" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="EndEvent_0k1ggpn_di" bpmnElement="end5_invoiceApproval">
                <dc:Bounds x="1492" y="142" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1468" y="185" width="84" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_1o5mfah_di" bpmnElement="SequenceFlow_1o5mfah">
                <di:waypoint x="1450" y="160" />
                <di:waypoint x="1492" y="160" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="EndEvent_1hlqqu6_di" bpmnElement="end6_invoiceApproval">
                <dc:Bounds x="1492" y="242" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1471" y="285" width="78" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_1barys0_di" bpmnElement="SequenceFlow_1barys0">
                <di:waypoint x="1450" y="260" />
                <di:waypoint x="1492" y="260" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="BoundaryEvent_17w2g4z_di" bpmnElement="BoundaryEvent_0pff0pb">
                <dc:Bounds x="1362" y="422" width="36" height="36" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_15d9ff3_di" bpmnElement="SequenceFlow_15d9ff3">
                <di:waypoint x="1380" y="458" />
                <di:waypoint x="1380" y="550" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="EndEvent_0dk3dss_di" bpmnElement="end4_invoiceApproval">
                <dc:Bounds x="1382" y="682" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1366" y="725" width="68" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_0kbmcbj_di" bpmnElement="SequenceFlow_0kbmcbj">
                <di:waypoint x="1400" y="630" />
                <di:waypoint x="1400" y="682" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_1b5d3z9_di" bpmnElement="SequenceFlow_1b5d3z9">
                <di:waypoint x="1350" y="370" />
                <di:waypoint x="1170" y="370" />
                <di:waypoint x="1170" y="285" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1209" y="336" width="84" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_1fxku6c_di" bpmnElement="SequenceFlow_1fxku6c">
                <di:waypoint x="1180" y="474" />
                <di:waypoint x="1265" y="474" />
                <di:waypoint x="1265" y="410" />
                <di:waypoint x="1350" y="410" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="SendTask_1xr6tvg_di" bpmnElement="sendApprovalEmail_invoiceApproval">
                <dc:Bounds x="570" y="170" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="SendTask_0j2jw61_di" bpmnElement="sendApprovalNotification_invoiceApproval">
                <dc:Bounds x="570" y="280" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="ReceiveTask_1cjhyad_di" bpmnElement="waitForDeleteOrVoid_invoiceApproval">
                <dc:Bounds x="420" y="360" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_07h3f0k_di" bpmnElement="sequenceToWait1_invoiceApproval">
                <di:waypoint x="370" y="290" />
                <di:waypoint x="395" y="290" />
                <di:waypoint x="395" y="380" />
                <di:waypoint x="420" y="380" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="299" y="332" width="82" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="ServiceTask_19olykb_di" bpmnElement="autoRejectInvoice_invoiceApproval">
                <dc:Bounds x="1350" y="550" width="100" height="80" />
            </bpmndi:BPMNShape>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</bpmn:definitions>
