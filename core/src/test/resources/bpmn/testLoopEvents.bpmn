<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0kjhz9s" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.0.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.17.0">
  <bpmn:process id="engagementOverwatchSystemTasks2" name="Engagement Overwatch System Tasks" isExecutable="true" camunda:historyTimeToLive="3">
    <bpmn:startEvent id="StartProcess" name="StartProcess">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="startableEvents" value="[&#34;created&#34;, &#34;updated&#34;]" />
          <camunda:property name="events" value="[&#34;start&#34;]" />
          <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;was&#34;, &#34;recordType&#34;: &#34;engagement&#34;}" />
          <camunda:property name="processVariablesDetails" value="[   {     &#34;variableName&#34;: &#34;engagementId&#34;,     &#34;variableType&#34;: &#34;String&#34;   } ]" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_0jw7bk0</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0jw7bk0" sourceRef="StartProcess" targetRef="SystemTask" />
    <bpmn:serviceTask id="SystemTask" name="SystemTask" camunda:type="external" camunda:topic="jatin-local">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="engagementRequest">
            <camunda:map>
              <camunda:entry key="method">GET</camunda:entry>
              <camunda:entry key="path">/v1/engagement/${engagementId}/deep</camunda:entry>
              <camunda:entry key="resultOperation">[   {     "outputVariableKey": "customerAuthId",     "jsonPath": "$.customerAuthId",     "singleValue": true   },   {     "outputVariableKey": "firmId",     "jsonPath": "$.firmId",     "singleValue": true   },   {     "outputVariableKey": "scope",     "jsonPath": "$.scope",     "singleValue": true   },   {     "outputVariableKey": "engagementId",     "jsonPath": "$.engagementId",     "singleValue": true   } ]</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="events" value="[&#34;start&#34;,&#34;created&#34;,&#34;update&#34;,&#34;completed&#34;,&#34;failed&#34;, &#34;end&#34;]" />
          <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;was&#34;, &#34;handlerId&#34;:&#34;test&#34;, &#34;actionName&#34;: &#34;workflowCustomTaskHandler&#34;}" />
          <camunda:property name="type" value="SYSTEM_TASK" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0jw7bk0</bpmn:incoming>
      <bpmn:outgoing>Flow_1pn5i51</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1pn5i51" sourceRef="SystemTask" targetRef="SystemTask2" />
    <bpmn:sequenceFlow id="Flow_0264039" sourceRef="SystemTask2" targetRef="EndProcess" />
    <bpmn:intermediateThrowEvent id="SystemTask2" name="SystemTask2">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="events" value="[&#34;start&#34;, &#34;end&#34;]" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1pn5i51</bpmn:incoming>
      <bpmn:outgoing>Flow_0264039</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1h55dil" messageRef="Message_3vh9cck" />
    </bpmn:intermediateThrowEvent>
    <bpmn:subProcess id="SubProcess" name="SubProcess" triggeredByEvent="true">
      <bpmn:extensionElements />
      <bpmn:endEvent id="SubEndEvent" name="SubEndEvent">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="events" value="[&#34;start&#34;,&#34;end&#34;]" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_01pe4vn</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="Flow_0jomrdl" sourceRef="SubStartProcess" targetRef="SystemTaskWithoutType" />
      <bpmn:sequenceFlow id="Flow_01pe4vn" sourceRef="SystemTaskWithoutType" targetRef="SubEndEvent" />
      <bpmn:startEvent id="SubStartProcess" name="SubStartProcess">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="events" value="[&#34;start&#34;,&#34;end&#34;]" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:outgoing>Flow_0jomrdl</bpmn:outgoing>
        <bpmn:escalationEventDefinition id="EscalationEventDefinition_0aucsft" camunda:escalationCodeVariable="escalate" />
      </bpmn:startEvent>
      <bpmn:serviceTask id="SystemTaskWithoutType" name="SystemTaskWithoutType" camunda:type="external" camunda:topic="jatin-local">
        <bpmn:extensionElements>
          <camunda:properties>
            <camunda:property name="events" value="[&#34;start&#34;,&#34;end&#34;]" />
            <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;was&#34;, &#34;handlerId&#34;:&#34;&#34;, &#34;actionName&#34;: &#34;updateProcessStatus&#34;}" />
          </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0jomrdl</bpmn:incoming>
        <bpmn:outgoing>Flow_01pe4vn</bpmn:outgoing>
      </bpmn:serviceTask>
    </bpmn:subProcess>
    <bpmn:endEvent id="EndProcess" name="EndProcess">
      <bpmn:incoming>Flow_0264039</bpmn:incoming>
      <bpmn:escalationEventDefinition id="EscalationEventDefinition_0in46w7" escalationRef="Escalation_3q48hdj" />
    </bpmn:endEvent>
  </bpmn:process>
  <bpmn:message id="Message_3vh9cck" name="test2" />
  <bpmn:escalation id="Escalation_3q48hdj" name="Escalation_3q48hdj" escalationCode="escalate" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="engagementOverwatchSystemTasks2">
      <bpmndi:BPMNEdge id="Flow_0264039_di" bpmnElement="Flow_0264039">
        <di:waypoint x="548" y="117" />
        <di:waypoint x="652" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pn5i51_di" bpmnElement="Flow_1pn5i51">
        <di:waypoint x="410" y="117" />
        <di:waypoint x="512" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jw7bk0_di" bpmnElement="Flow_0jw7bk0">
        <di:waypoint x="215" y="117" />
        <di:waypoint x="310" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartProcess">
        <dc:Bounds x="179" y="99" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="167" y="142" width="63" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_04mzyue_di" bpmnElement="SystemTask">
        <dc:Bounds x="310" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0gmnida_di" bpmnElement="SystemTask2">
        <dc:Bounds x="512" y="99" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="497" y="142" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_18ul6gk_di" bpmnElement="SubProcess" isExpanded="true">
        <dc:Bounds x="260" y="270" width="740" height="280" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_01pe4vn_di" bpmnElement="Flow_01pe4vn">
        <di:waypoint x="640" y="370" />
        <di:waypoint x="872" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jomrdl_di" bpmnElement="Flow_0jomrdl">
        <di:waypoint x="336" y="370" />
        <di:waypoint x="540" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_1kd1t4b_di" bpmnElement="SubEndEvent">
        <dc:Bounds x="872" y="352" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="856" y="395" width="68" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_12bd7mj_di" bpmnElement="SubStartProcess">
        <dc:Bounds x="300" y="352" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="277" y="395" width="83" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1p08a2p_di" bpmnElement="SystemTaskWithoutType">
        <dc:Bounds x="540" y="330" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0mov8my_di" bpmnElement="EndProcess">
        <dc:Bounds x="652" y="99" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="642" y="142" width="60" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>