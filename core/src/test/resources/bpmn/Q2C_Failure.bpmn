<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_0pl1pp0" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="3.3.1">
    <bpmn:process id="Q2C" name="Q2C BPMN Process" isExecutable="true">
        <bpmn:extensionElements>
            <camunda:properties>
                <camunda:property name="description" value="Q2C Use Case" />
            </camunda:properties>
        </bpmn:extensionElements>
        <bpmn:startEvent id="q2c_start_uuid" name="Start Process">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="taskDetails" value="{ &#34;required&#34;: false }" />
                    <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;was&#34;, &#34;recordType&#34;: &#34;invoice&#34;, &#34;eventType&#34;: &#34;create&#34;}" />
                    <camunda:property name="stepDetails" value="{   &#34;q2c_start_uuid&#34;: [     &#34;q2c_start_uuid&#34;   ],   &#34;boundary_event_uuid&#34;: [     &#34;boundary_event_uuid&#34;,     &#34;stage_data_fetch_uuid&#34;,     &#34;send_notification_uuid&#34;   ],   &#34;receiveTaskId_uuid&#34;: [     &#34;receiveTaskId_uuid&#34;   ] }" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:outgoing>SequenceFlow_0vvuqz6</bpmn:outgoing>
        </bpmn:startEvent>
        <bpmn:sequenceFlow id="SequenceFlow_0vvuqz6" sourceRef="q2c_start_uuid" targetRef="receiveTaskId_uuid" />
        <bpmn:boundaryEvent id="boundary_event_uuid" name="Timer Event" attachedToRef="receiveTaskId_uuid">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;camunda&#34;}" />
                    <camunda:property name="taskDetails" value="{&#34;required&#34;: true}" />
                    <camunda:property name="parameterDetails" value="{  &#34;waitTime&#34;: {     &#34;fieldValue&#34; : [&#34;5&#34;]     &#34;configurable&#34; : true,     &#34;actionByUI&#34; : null,     &#34;requiredByhandler&#34; : false,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;integer&#34;   }}" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:outgoing>SequenceFlow_0oate83</bpmn:outgoing>
            <bpmn:timerEventDefinition>
                <bpmn:timeCycle xsi:type="bpmn:tFormalExpression">PT7D</bpmn:timeCycle>
            </bpmn:timerEventDefinition>
        </bpmn:boundaryEvent>
        <bpmn:sequenceFlow id="SequenceFlow_0oate83" sourceRef="boundary_event_uuid" targetRef="stage_data_fetch_uuid" />
        <bpmn:sequenceFlow id="SequenceFlow_1ixnsq4" sourceRef="send_notification_uuid" targetRef="receiveTaskId_uuid">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">null</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:endEvent id="EndEvent_043v99h" name="End the Process">
            <bpmn:incoming>SequenceFlow_060qjoc</bpmn:incoming>
        </bpmn:endEvent>
        <bpmn:serviceTask id="stage_data_fetch_uuid" name="Get Data From Stage">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "5432",
                        "handlerName": "sendNotificationToAuthor"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails">{
                        "To": {
                        "fieldValue" : ["Admins"],
                        "configurable" : true,
                        "actionByUI" : "GET_ADMINS_ID",
                        "requiredByHandler" : true,
                        "requiredByUI": true,
                        "multiSelect": true,
                        "fieldType": "string"
                        },
                        "Subject": {
                        "fieldValue" : ["Hey you ahve an invoice to approve"],
                        "configurable" : true,
                        "actionByUI" : null,
                        "requiredByhandler" : true,
                        "requiredByUI": true,
                        "multiSelect": false,
                        "fieldType": "string"
                        },
                        "Message" : {
                        "fieldValue" : ["blah blah link blah"],
                        "configurable" : true,
                        "actionByUI" : null,
                        "requiredByhandler" : true,
                        "requiredByUI": true,
                        "helpVariables": "QBOCompanyName",
                        "multiSelect": false,
                        "fieldType": "string"
                        }
                        }</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_0oate83</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_08w0bh2</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:receiveTask id="receiveTaskId_uuid" name="Wait for End Message to Arrive" messageRef="Message_0yik0qa">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "9876",
                        "handlerName":"sendNotificationToAuthor"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails">{
                        "To": {
                        "fieldValue" : ["userId"],
                        "configurable" : true,
                        "actionByUI" : "GET_AUTHOR_ID",
                        "requiredByhandler" : true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "long"
                        },
                        "Subject": {
                        "fieldValue" : ["Hey you ahve an invoice to approve"],
                        "configurable" : true,
                        "actionByUI" : null,
                        "requiredByhandler" : true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "long"
                        }
                        }</camunda:inputParameter>
                </camunda:inputOutput>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_1ixnsq4</bpmn:incoming>
            <bpmn:incoming>SequenceFlow_0vvuqz6</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_060qjoc</bpmn:outgoing>
        </bpmn:receiveTask>
        <bpmn:sequenceFlow id="SequenceFlow_060qjoc" sourceRef="receiveTaskId_uuid" targetRef="EndEvent_043v99h">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">null</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sendTask id="send_notification_uuid" name="Send Notification">
            <bpmn:extensionElements>
                <camunda:inputOutput>
                    <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "9876",
                        "handlerName":"sendNotificationToAuthor"
                        }</camunda:inputParameter>
                    <camunda:inputParameter name="parameterDetails">{
                        "To": {
                        "fieldValue" : ["userId"],
                        "configurable" : true,
                        "actionByUI" : "GET_AUTHOR_ID",
                        "requiredByhandler" : true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "long"
                        },
                        "Subject": {
                        "fieldValue" : ["Hey you ahve an invoice to approve"],
                        "configurable" : true,
                        "actionByUI" : null,
                        "requiredByhandler" : true,
                        "requiredByUI": false,
                        "multiSelect": false,
                        "fieldType": "long"
                        }
                        }</camunda:inputParameter>
                </camunda:inputOutput>
                <camunda:properties>
                    <camunda:property />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>SequenceFlow_08w0bh2</bpmn:incoming>
            <bpmn:outgoing>SequenceFlow_1ixnsq4</bpmn:outgoing>
        </bpmn:sendTask>
        <bpmn:sequenceFlow id="SequenceFlow_08w0bh2" sourceRef="stage_data_fetch_uuid" targetRef="send_notification_uuid">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">null</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
    </bpmn:process>
    <bpmn:message id="Message_0yik0qa" name="Kill_the_process" />
    <bpmndi:BPMNDiagram id="BPMNDiagram_1">
        <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Q2C">
            <bpmndi:BPMNShape id="StartEvent_1mwixia_di" bpmnElement="q2c_start_uuid">
                <dc:Bounds x="172" y="262" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="157" y="305" width="67" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_0vvuqz6_di" bpmnElement="SequenceFlow_0vvuqz6">
                <di:waypoint x="208" y="280" />
                <di:waypoint x="260" y="280" />
                <di:waypoint x="260" y="120" />
                <di:waypoint x="290" y="120" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="BoundaryEvent_0lvwmf4_di" bpmnElement="boundary_event_uuid">
                <dc:Bounds x="292" y="142" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="281" y="185" width="59" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_0oate83_di" bpmnElement="SequenceFlow_0oate83">
                <di:waypoint x="310" y="178" />
                <di:waypoint x="310" y="400" />
                <di:waypoint x="360" y="400" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="SequenceFlow_1ixnsq4_di" bpmnElement="SequenceFlow_1ixnsq4">
                <di:waypoint x="690" y="110" />
                <di:waypoint x="390" y="110" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="EndEvent_043v99h_di" bpmnElement="EndEvent_043v99h">
                <dc:Bounds x="502" y="222" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="480" y="265" width="81" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="ServiceTask_1bjllvv_di" bpmnElement="stage_data_fetch_uuid">
                <dc:Bounds x="360" y="370" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="ReceiveTask_1n1eyii_di" bpmnElement="receiveTaskId_uuid">
                <dc:Bounds x="290" y="80" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_060qjoc_di" bpmnElement="SequenceFlow_060qjoc">
                <di:waypoint x="390" y="120" />
                <di:waypoint x="446" y="120" />
                <di:waypoint x="446" y="240" />
                <di:waypoint x="502" y="240" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="SendTask_1lnbxtp_di" bpmnElement="send_notification_uuid">
                <dc:Bounds x="690" y="70" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="SequenceFlow_08w0bh2_di" bpmnElement="SequenceFlow_08w0bh2">
                <di:waypoint x="460" y="410" />
                <di:waypoint x="740" y="410" />
                <di:waypoint x="740" y="150" />
            </bpmndi:BPMNEdge>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</bpmn:definitions>
