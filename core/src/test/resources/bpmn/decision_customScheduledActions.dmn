<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/DMN/20151101/dmn.xsd" xmlns:biodi="http://bpmn.io/schema/dmn/biodi/1.0" xmlns:ns0="http://camunda.org/schema/1.0/dmn" id="Definitions_086839o" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" exporter="Camunda Modeler" exporterVersion="3.3.1">
  <decision id="decisionElement_9130352478836126_14a20461-2cc5-42ff-94dc-8f98a4db2d2a" name="Jatin1-np-5">
    <extensionElements>
      <biodi:bounds x="150" y="81" width="180" height="80" />
    </extensionElements>
    <decisionTable id="decisionTable_1" hitPolicy="FIRST">
      <input id="input1" label="Customer" ns0:inputVariable="Customer">
        <inputExpression id="inputExpression_1" typeRef="string">
          <text>${Customer}</text>
        </inputExpression>
      </input>
      <output id="output_1" label="decisionResult" name="decisionResult" typeRef="boolean" />
      <rule id="rule_a84b765e-1878-4be2-8116-d9bf87b82988">
        <description></description>
        <inputEntry id="inputEntry_cb084280-e95d-452e-921e-015a41a6f3a9" expressionLanguage="juel">
          <text>Customer.equals("1")</text>
        </inputEntry>
        <outputEntry id="outputEntry_649ae39e-99f8-4d30-b80b-dfdfe4c7f3e8">
          <text>true</text>
        </outputEntry>
      </rule>
      <rule id="rule_62403c67-7a9c-4859-8e2f-4fae92f4f98a">
        <description></description>
        <inputEntry id="inputEntry_56b816c3-7538-431b-92a2-4c88dac756bc" expressionLanguage="juel">
          <text></text>
        </inputEntry>
        <outputEntry id="outputEntry_e289e844-d4f8-42ea-9563-7635cb128be5">
          <text>false</text>
        </outputEntry>
      </rule>
    </decisionTable>
  </decision>
</definitions>
