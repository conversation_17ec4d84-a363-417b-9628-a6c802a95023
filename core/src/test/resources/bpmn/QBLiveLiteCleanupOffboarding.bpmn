<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0tdkq4p" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.25.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.15.0">
  <bpmn:message id="Message_0vz4vdy" name="process_ended_message" />
  <bpmn:message id="Message_0ric24b" name="approval_message" />
  <bpmn:message id="Message_1qsrxjc" name="isApproved" />
  <bpmn:message id="Message_05nttfg" name="approval" />
  <bpmn:process id="engagementQBLiveLiteCleanupOffboarding" name="engagementQBLiveLiteCleanupOffboarding" isExecutable="true" camunda:versionTag="4" camunda:historyTimeToLive="15">
    <bpmn:startEvent id="StartEvent_1" name="startEvent" camunda:asyncBefore="true">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{&#34;recordType&#34;: &#34;engagement&#34;}" />
          <camunda:property name="startableEvents" value="[&#34;created&#34;]" />
          <camunda:property name="processVariablesDetails" value="[   {     &#34;variableName&#34;: &#34;engagementId&#34;,     &#34;variableType&#34;: &#34;String&#34;   },   {     &#34;variableName&#34;: &#34;customerAccountId&#34;,     &#34;variableType&#34;: &#34;String&#34;   }]" />
          <camunda:property name="stepDetails" value="{&#34;startEvent&#34;:[&#34;StartEvent_1&#34;]}" />
          <camunda:property name="events" value="[&#34;start&#34;]" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_0djufq4</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="Gateway_04dllb8" name="Has future appointments ?" default="Flow_08r0en4">
      <bpmn:incoming>Flow_0ksz18e</bpmn:incoming>
      <bpmn:outgoing>Flow_08r0en4</bpmn:outgoing>
      <bpmn:outgoing>Flow_11n4mv6</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="SendAppointentReminderEmail" name="Send Notification" implementation="##WebService" camunda:type="external" camunda:topic="engagement-bookkeeping">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;was&#34;, &#34;actionName&#34;: &#34;workflowCustomTaskHandler&#34;}" />
          <camunda:property name="type" value="NOTIFICATION_TASK" />
          <camunda:property name="serviceName" value="QB Live" />
          <camunda:property name="notificationDataType" value="UNRESPONSIVE_REMINDER" />
          <camunda:property name="taskDetails" value="{&#34;fatal&#34;: true}" />
        </camunda:properties>
        <camunda:inputOutput>
          <camunda:inputParameter name="notificationData">{
	"clientRealmId": "${customerAccountId}",
	"clientCompanyName": "${clientCompanyName}",
        "cancellationDate": "${cancellationDate}"
}</camunda:inputParameter>
          <camunda:inputParameter name="notificationMetaData">{"authId": -1}</camunda:inputParameter>
          <camunda:inputParameter name="notificationName">${offboardingDecision.notificationName}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_003n7do</bpmn:incoming>
      <bpmn:outgoing>Flow_1s2hv5d</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1lwiafz" name="Increment Offboard Check Count ">
      <bpmn:extensionElements>
        <camunda:executionListener expression="${execution.setVariable(&#34;offboardCheckCount&#34;,execution.getVariable(&#34;offboardCheckCount&#34;)+ 1)}" event="end" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_08r0en4</bpmn:incoming>
      <bpmn:incoming>Flow_1s2hv5d</bpmn:incoming>
      <bpmn:outgoing>Flow_0vf5y29</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_08r0en4" name="Yes" sourceRef="Gateway_04dllb8" targetRef="Gateway_1lwiafz" />
    <bpmn:sequenceFlow id="Flow_11n4mv6" name="No" sourceRef="Gateway_04dllb8" targetRef="GetCompanyInfoTask">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${appointmentList == '[]'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0djufq4" sourceRef="StartEvent_1" targetRef="Activity_0clo9yh" />
    <bpmn:intermediateCatchEvent id="SetTimerEvent" name="Set Timer">
      <bpmn:incoming>Flow_1tw6i0c</bpmn:incoming>
      <bpmn:outgoing>Flow_1ekm4e4</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_1vr2eog">
        <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${offboardingTimer}</bpmn:timeDate>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:sequenceFlow id="Flow_1s2hv5d" sourceRef="SendAppointentReminderEmail" targetRef="Gateway_1lwiafz" />
    <bpmn:serviceTask id="Activity_006opyk" name="Fetch Appointments for Engagement" implementation="##WebService" camunda:type="external" camunda:topic="engagement-bookkeeping">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="clientIntegrationRequest">
            <camunda:map>
              <camunda:entry key="clientType">DATA_API_CLIENT</camunda:entry>
              <camunda:entry key="method">POST</camunda:entry>
              <camunda:entry key="requestBody">{"query":"query AppointmentsByEngagement ($engagementId: String!) {\n  appointments {\n    appointments(\n      apptFilter: {\n        apptMetaDataFilter: {\n            apptMetaData: {\n                type: \"ENGAGEMENT\", \n                value: $engagementId\n            }\n        },\n        apptStatusFilter:[SCHEDULED, INPROGRESS, PROCESSING, STARTED]\n      }\n    ) {\n      appointmentList {\n        id\n        externalId\n        description\n        metaData {\n          type\n          value\n        }\n        status\n        substatus {\n          remarks\n          value\n        }\n        owner\n        organizer\n        scheduledDateTime\n        slotId\n        isWebRTCEnabled\n        isWorkflowEnabled\n        duration\n      }\n    }\n  }\n}\n","variables":{"engagementId":"${engagementId}"}}</camunda:entry>
              <camunda:entry key="resultOperation">[{"outputVariableKey":"appointmentList","jsonPath":"$.data.appointments.appointments.appointmentList","singleValue":false}]</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
          <camunda:inputParameter name="clientRealmId">${customerAccountId}</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="type" value="SYSTEM_TASK" />
          <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;:&#34;was&#34;,&#34;handlerId&#34;:&#34;intuit-workflows/expert-svc-client-integration-processor&#34;,&#34;actionName&#34;:&#34;workflowCustomTaskHandler&#34;}" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1k48gcn</bpmn:incoming>
      <bpmn:incoming>Flow_0904v60</bpmn:incoming>
      <bpmn:outgoing>Flow_0ksz18e</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0ksz18e" sourceRef="Activity_006opyk" targetRef="Gateway_04dllb8" />
    <bpmn:sequenceFlow id="Flow_1ekm4e4" sourceRef="SetTimerEvent" targetRef="Gateway_0hy6hnk" />
    <bpmn:sequenceFlow id="Flow_0vf5y29" sourceRef="Gateway_1lwiafz" targetRef="scuOffboardingDecision" />
    <bpmn:businessRuleTask id="scuOffboardingDecision" name="scuOffboardingDecision" camunda:resultVariable="offboardingDecision" camunda:decisionRef="SCUOffboardingDecision" camunda:mapDecisionResult="singleResult">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{ "taskHandler": "was"}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0vf5y29</bpmn:incoming>
      <bpmn:incoming>Flow_0n9w0co</bpmn:incoming>
      <bpmn:outgoing>Flow_1gxws6y</bpmn:outgoing>
    </bpmn:businessRuleTask>
    <bpmn:sequenceFlow id="Flow_1gxws6y" sourceRef="scuOffboardingDecision" targetRef="Activity_1q2hxoi" />
    <bpmn:exclusiveGateway id="Gateway_1vrcxww" name="Should Close Engagement ?" default="Flow_003n7do">
      <bpmn:incoming>Flow_0lcy42n</bpmn:incoming>
      <bpmn:outgoing>Flow_003n7do</bpmn:outgoing>
      <bpmn:outgoing>Flow_0leujo2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_003n7do" name="No" sourceRef="Gateway_1vrcxww" targetRef="SendAppointentReminderEmail" />
    <bpmn:sequenceFlow id="Flow_0leujo2" name="Yes" sourceRef="Gateway_1vrcxww" targetRef="Activity_1ex0qij">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${offboardingDecision.cancelService == 'true'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Activity_15t63mz" name="Close Engagement as Unresponsive" implementation="##WebService" camunda:type="external" camunda:topic="engagement-bookkeeping">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="clientIntegrationRequest">
            <camunda:map>
              <camunda:entry key="clientType">DATA_API_CLIENT</camunda:entry>
              <camunda:entry key="method">POST</camunda:entry>
              <camunda:entry key="requestBody">{"query":"mutation closeEngagement ($engagementId: String!, $statusReason: String!) {    \n    expert{        \n        engagement{            \n            closeEngagement(closeEngagementRequest: {engagementId : $engagementId,statusReason : $statusReason,closePreValidation: false })   \n            {  \n                engagementId \n            }        \n        }    \n    }\n}\n","variables":{"engagementId":"${engagementId}","statusReason":"Unresponsive|Full Refund|Unresponsive"}}</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
          <camunda:inputParameter name="clientRealmId">${customerAccountId}</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="type" value="SYSTEM_TASK" />
          <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;:&#34;was&#34;,&#34;handlerId&#34;:&#34;intuit-workflows/expert-svc-client-integration-processor&#34;,&#34;actionName&#34;:&#34;workflowCustomTaskHandler&#34;}" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1qm14gg</bpmn:incoming>
      <bpmn:outgoing>Flow_1ufitpi</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1ufitpi" sourceRef="Activity_15t63mz" targetRef="Event_0ms8ze9" />
    <bpmn:task id="Activity_0eq2jv8" name="Derive Cancellation Date For Notification">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="cancellationDate">${dateTime().plusDays(offboardingDecision. cancellationDateCount).toString("MMMM dd, yyyy h a z")}</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1gmpdng</bpmn:incoming>
      <bpmn:outgoing>Flow_1k48gcn</bpmn:outgoing>
    </bpmn:task>
    <bpmn:sequenceFlow id="Flow_1k48gcn" sourceRef="Activity_0eq2jv8" targetRef="Activity_006opyk" />
    <bpmn:task id="Activity_0clo9yh" name="Set offboardCheckCount to zero">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="offboardCheckCount">0</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0djufq4</bpmn:incoming>
      <bpmn:outgoing>Flow_0n9w0co</bpmn:outgoing>
    </bpmn:task>
    <bpmn:sequenceFlow id="Flow_0n9w0co" sourceRef="Activity_0clo9yh" targetRef="scuOffboardingDecision" />
    <bpmn:task id="Activity_1q2hxoi" name="Set Timer Value based on DMN">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="offboardingTimer">${dateTime().plusDays(offboardingDecision. timerIncrementInDays).toString()}</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1gxws6y</bpmn:incoming>
      <bpmn:outgoing>Flow_1tw6i0c</bpmn:outgoing>
    </bpmn:task>
    <bpmn:sequenceFlow id="Flow_1tw6i0c" sourceRef="Activity_1q2hxoi" targetRef="SetTimerEvent" />
    <bpmn:serviceTask id="GetCompanyInfoTask" name="Get Company Info" camunda:type="external" camunda:topic="engagement-bookkeeping">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="type" value="SYSTEM_TASK" />
          <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;:&#34;was&#34;,&#34;handlerId&#34;:&#34;intuit-workflows/expert-svc-client-integration-processor&#34;,&#34;actionName&#34;:&#34;workflowCustomTaskHandler&#34;}" />
        </camunda:properties>
        <camunda:inputOutput>
          <camunda:inputParameter name="clientIntegrationRequest">
            <camunda:map>
              <camunda:entry key="method">POST</camunda:entry>
              <camunda:entry key="clientType">IDENTITY_CLIENT</camunda:entry>
              <camunda:entry key="requestBody">{"query":"query account ($input: AccountInput!) {\n    account (input: $input) {\n        id\n        accountProfile {\n            id\n            businessInfo {\n                displayName\n            }\n        }\n    }\n}","variables":{"input":{"id":"${customerAccountId}"}}}</camunda:entry>
              <camunda:entry key="resultOperation">[   {     "outputVariableKey": "clientCompanyName",     "jsonPath": "$.data.account.accountProfile.businessInfo.displayName",     "singleValue": true   }]</camunda:entry>
            </camunda:map>
          </camunda:inputParameter>
          <camunda:inputParameter name="clientRealmId">${customerAccountId}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_11n4mv6</bpmn:incoming>
      <bpmn:outgoing>Flow_0lcy42n</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0lcy42n" sourceRef="GetCompanyInfoTask" targetRef="Gateway_1vrcxww" />
    <bpmn:serviceTask id="Activity_1ex0qij" name="Send Cancellation Notification" implementation="##WebService" camunda:type="external" camunda:topic="engagement-bookkeeping">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;was&#34;, &#34;actionName&#34;: &#34;workflowCustomTaskHandler&#34;}" />
          <camunda:property name="type" value="NOTIFICATION_TASK" />
          <camunda:property name="serviceName" value="QB Live" />
          <camunda:property name="notificationDataType" value="UNRESPONSIVE_REMINDER" />
          <camunda:property name="taskDetails" value="{&#34;fatal&#34;: true}" />
        </camunda:properties>
        <camunda:inputOutput>
          <camunda:inputParameter name="notificationData">{
	"clientRealmId": "${customerAccountId}",
	"clientCompanyName": "${clientCompanyName}",
        "cancellationDate": "${cancellationDate}"
}</camunda:inputParameter>
          <camunda:inputParameter name="notificationMetaData">{"authId": -1}</camunda:inputParameter>
          <camunda:inputParameter name="notificationName">${offboardingDecision.notificationName}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0leujo2</bpmn:incoming>
      <bpmn:outgoing>Flow_1qm14gg</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1qm14gg" sourceRef="Activity_1ex0qij" targetRef="Activity_15t63mz" />
    <bpmn:endEvent id="Event_0ms8ze9" name="End Offboarding Workflow">
      <bpmn:incoming>Flow_1ufitpi</bpmn:incoming>
      <bpmn:terminateEventDefinition id="TerminateEventDefinition_04zj685" />
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_0hy6hnk" name="Should derive cancellation date?" default="Flow_1gmpdng">
      <bpmn:incoming>Flow_1ekm4e4</bpmn:incoming>
      <bpmn:outgoing>Flow_1gmpdng</bpmn:outgoing>
      <bpmn:outgoing>Flow_0904v60</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1gmpdng" name="Yes" sourceRef="Gateway_0hy6hnk" targetRef="Activity_0eq2jv8" />
    <bpmn:sequenceFlow id="Flow_0904v60" name="No" sourceRef="Gateway_0hy6hnk" targetRef="Activity_006opyk">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${offboardingDecision.cancelService == 'true'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:textAnnotation id="TextAnnotation_1v4zncc">
      <bpmn:text>Pass the notification name from DMN and the derived cancellation date</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_0d1zs2i" associationDirection="None" sourceRef="SendAppointentReminderEmail" targetRef="TextAnnotation_1v4zncc" />
    <bpmn:textAnnotation id="TextAnnotation_1kle6bd">
      <bpmn:text>Cancellation date is derived only if cancel service is false, if true, then we use the date that was last derived and in memory</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_1wq2pri" associationDirection="None" sourceRef="Gateway_0hy6hnk" targetRef="TextAnnotation_1kle6bd" />
  </bpmn:process>
  <bpmn:message id="Message_3brdgq0" name="QBLiveOngoingMonthlyToAnnual" />
  <bpmn:message id="Message_2vdkqss" name="QBLiveOngoingAnnualToMonthly" />
  <bpmn:escalation id="Escalation_09vc9en" name="Close Engagement" escalationCode="engagement_close" />
  <bpmn:escalation id="Escalation_1q5ho2a" name="Close Subscription" escalationCode="subscription_close" />
  <bpmn:message id="Message_0wx0g5q" name="close_subscription" />
  <bpmn:escalation id="Escalation_1oh6om2" name="Close Engagement" escalationCode="engagement_close" />
  <bpmn:message id="Message_3cc5g6v" name="Message_3cc5g6v" />
  <bpmn:message id="Message_03l7v3b" name="onboarding_project_complete" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="engagementQBLiveLiteCleanupOffboarding">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="172" y="452" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="165" y="498" width="50" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1bapl3b" bpmnElement="Gateway_04dllb8" isMarkerVisible="true">
        <dc:Bounds x="1255" y="445" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1242" y="505" width="76" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1nvtlh0_di" bpmnElement="SendAppointentReminderEmail">
        <dc:Bounds x="1670" y="240" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1crnzf1_di" bpmnElement="Gateway_1lwiafz" isMarkerVisible="true">
        <dc:Bounds x="1695" y="445" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1769" y="460" width="77" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1n66yxb" bpmnElement="SetTimerEvent">
        <dc:Bounds x="772" y="452" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="768" y="495" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0a61hkd" bpmnElement="Activity_006opyk">
        <dc:Bounds x="1110" y="430" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BusinessRuleTask_06zco86_di" bpmnElement="scuOffboardingDecision">
        <dc:Bounds x="450" y="430" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1vrcxww_di" bpmnElement="Gateway_1vrcxww" isMarkerVisible="true">
        <dc:Bounds x="1395" y="255" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1394" y="314.5" width="72" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ndpzpa" bpmnElement="Activity_15t63mz">
        <dc:Bounds x="1680" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0eq2jv8_di" bpmnElement="Activity_0eq2jv8">
        <dc:Bounds x="920" y="300" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0clo9yh_di" bpmnElement="Activity_0clo9yh">
        <dc:Bounds x="280" y="430" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1q2hxoi_di" bpmnElement="Activity_1q2hxoi">
        <dc:Bounds x="600" y="430" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0s3vaju" bpmnElement="GetCompanyInfoTask">
        <dc:Bounds x="1230" y="300" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0pwq2i7" bpmnElement="Activity_1ex0qij">
        <dc:Bounds x="1510" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0z6vtt2_di" bpmnElement="Event_0ms8ze9">
        <dc:Bounds x="1882" y="102" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1860" y="145" width="80" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0hy6hnk_di" bpmnElement="Gateway_0hy6hnk" isMarkerVisible="true">
        <dc:Bounds x="855" y="445" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="838" y="502" width="89" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_1v4zncc_di" bpmnElement="TextAnnotation_1v4zncc">
        <dc:Bounds x="1820" y="260" width="100" height="113" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_1kle6bd_di" bpmnElement="TextAnnotation_1kle6bd">
        <dc:Bounds x="730" y="240" width="99.99274099883856" height="141.69570267131243" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_08r0en4_di" bpmnElement="Flow_08r0en4">
        <di:waypoint x="1305" y="470" />
        <di:waypoint x="1695" y="470" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1428" y="452" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11n4mv6_di" bpmnElement="Flow_11n4mv6">
        <di:waypoint x="1280" y="445" />
        <di:waypoint x="1280" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1253" y="394" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0djufq4_di" bpmnElement="Flow_0djufq4">
        <di:waypoint x="208" y="470" />
        <di:waypoint x="280" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1s2hv5d_di" bpmnElement="Flow_1s2hv5d">
        <di:waypoint x="1720" y="320" />
        <di:waypoint x="1720" y="445" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ksz18e_di" bpmnElement="Flow_0ksz18e">
        <di:waypoint x="1210" y="470" />
        <di:waypoint x="1255" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ekm4e4_di" bpmnElement="Flow_1ekm4e4">
        <di:waypoint x="808" y="470" />
        <di:waypoint x="855" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vf5y29_di" bpmnElement="Flow_0vf5y29">
        <di:waypoint x="1720" y="495" />
        <di:waypoint x="1720" y="610" />
        <di:waypoint x="500" y="610" />
        <di:waypoint x="500" y="510" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gxws6y_di" bpmnElement="Flow_1gxws6y">
        <di:waypoint x="550" y="470" />
        <di:waypoint x="600" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_003n7do_di" bpmnElement="Flow_003n7do">
        <di:waypoint x="1445" y="280" />
        <di:waypoint x="1670" y="280" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1472" y="263" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0leujo2_di" bpmnElement="Flow_0leujo2">
        <di:waypoint x="1420" y="255" />
        <di:waypoint x="1420" y="120" />
        <di:waypoint x="1510" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1401" y="93.00000000000003" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ufitpi_di" bpmnElement="Flow_1ufitpi">
        <di:waypoint x="1780" y="120" />
        <di:waypoint x="1882" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k48gcn_di" bpmnElement="Flow_1k48gcn">
        <di:waypoint x="1020" y="340" />
        <di:waypoint x="1055" y="340" />
        <di:waypoint x="1055" y="470" />
        <di:waypoint x="1110" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n9w0co_di" bpmnElement="Flow_0n9w0co">
        <di:waypoint x="380" y="470" />
        <di:waypoint x="450" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1tw6i0c_di" bpmnElement="Flow_1tw6i0c">
        <di:waypoint x="700" y="470" />
        <di:waypoint x="772" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lcy42n_di" bpmnElement="Flow_0lcy42n">
        <di:waypoint x="1280" y="300" />
        <di:waypoint x="1280" y="280" />
        <di:waypoint x="1395" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qm14gg_di" bpmnElement="Flow_1qm14gg">
        <di:waypoint x="1610" y="120" />
        <di:waypoint x="1680" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gmpdng_di" bpmnElement="Flow_1gmpdng">
        <di:waypoint x="880" y="445" />
        <di:waypoint x="880" y="340" />
        <di:waypoint x="920" y="340" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="886" y="390" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0904v60_di" bpmnElement="Flow_0904v60">
        <di:waypoint x="905" y="470" />
        <di:waypoint x="1110" y="470" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1000" y="452" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_0d1zs2i_di" bpmnElement="Association_0d1zs2i">
        <di:waypoint x="1770" y="278" />
        <di:waypoint x="1820" y="277" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_1wq2pri_di" bpmnElement="Association_1wq2pri">
        <di:waypoint x="872" y="453" />
        <di:waypoint x="830" y="362" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>