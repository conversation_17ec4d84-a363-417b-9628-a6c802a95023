<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_09ch9f6" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="billapproval_QBDT" name="Bills" processType="None" isClosed="false" isExecutable="true" camunda:versionTag="1" camunda:historyTimeToLive="14">
    <bpmn:extensionElements>
      <camunda:properties>
        <camunda:property name="description" value="Automate bill approval requests based on the conditions you define." />
      </camunda:properties>
    </bpmn:extensionElements>
    <bpmn:sendTask id="sendForApproval_txnApproval" name="Send for Approval" camunda:asyncBefore="true" implementation="##WebService" camunda:type="external" camunda:topic="test-premium-apps">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/was-send-email-workflows-qbdt",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
          <camunda:outputParameter name="ApprovalStatus">"pendingapproval"</camunda:outputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="parameterDetails" value="{  &#34;Approver #1&#34;: {   &#34;fieldValue&#34;: [],   &#34;possibleFieldValues&#34;: [],   &#34;handlerFieldName&#34;: &#34;To&#34;,   &#34;configurable&#34;: true,   &#34;actionByUI&#34;: &#34;GET_ADMINS_ID&#34;,   &#34;requiredByHandler&#34;: true,   &#34;requiredByUI&#34;: true,   &#34;multiSelect&#34;: true,   &#34;fieldType&#34;: &#34;string&#34;  },  &#34;DocNumber&#34;: {   &#34;fieldValue&#34;: [],   &#34;handlerFieldName&#34;: &#34;DocNumber&#34;,   &#34;configurable&#34;: false,   &#34;requiredByHandler&#34;: true,   &#34;requiredByUI&#34;: false,   &#34;multiSelect&#34;: false,   &#34;fieldType&#34;: &#34;string&#34;,   &#34;valueType&#34;: &#34;PROCESS_VARIABLE&#34;  },  &#34;CompanyName&#34;: {   &#34;fieldValue&#34;: [],   &#34;handlerFieldName&#34;: &#34;CompanyName&#34;,   &#34;configurable&#34;: false,   &#34;requiredByHandler&#34;: true,   &#34;requiredByUI&#34;: false,   &#34;multiSelect&#34;: false,   &#34;fieldType&#34;: &#34;string&#34;,   &#34;valueType&#34;: &#34;PROCESS_VARIABLE&#34;  },  &#34;ApprovalStatus&#34;: {   &#34;fieldValue&#34;: [],   &#34;handlerFieldName&#34;: &#34;Approval Status&#34;,   &#34;configurable&#34;: false,   &#34;requiredByHandler&#34;: true,   &#34;requiredByUI&#34;: false,   &#34;multiSelect&#34;: false,   &#34;fieldType&#34;: &#34;string&#34;,   &#34;valueType&#34;: &#34;PROCESS_VARIABLE&#34;  },  &#34;creatorName&#34;: {   &#34;fieldValue&#34;: [],   &#34;possibleFieldValues&#34;: [],   &#34;handlerFieldName&#34;: &#34;creatorName&#34;,   &#34;configurable&#34;: true,   &#34;requiredByHandler&#34;: true,   &#34;requiredByUI&#34;: false,   &#34;multiSelect&#34;: false,   &#34;fieldType&#34;: &#34;string&#34;,   &#34;valueType&#34;: &#34;PROCESS_VARIABLE&#34;  },  &#34;Send To&#34;: {   &#34;fieldValue&#34;: [    &#34;Approver #1 email&#34;   ],   &#34;possibleFieldValues&#34;: [],   &#34;configurable&#34;: true,   &#34;actionByUI&#34;: &#34;GET_ADMINS_ID&#34;,   &#34;requiredByHandler&#34;: false,   &#34;requiredByUI&#34;: true,   &#34;multiSelect&#34;: true,   &#34;fieldType&#34;: &#34;string&#34;  },  &#34;ApproverName&#34;: {   &#34;fieldValue&#34;: [],   &#34;possibleFieldValues&#34;: [],   &#34;handlerFieldName&#34;: &#34;ApproverName&#34;,   &#34;configurable&#34;: true,   &#34;requiredByHandler&#34;: true,   &#34;requiredByUI&#34;: false,   &#34;multiSelect&#34;: true,   &#34;fieldType&#34;: &#34;string&#34;  },  &#34;Subject&#34;: {   &#34;fieldValue&#34;: [    &#34;Bill [[DocNumber]] needs your approval&#34;   ],   &#34;possibleFieldValues&#34;: [],   &#34;configurable&#34;: true,   &#34;requiredByHandler&#34;: true,   &#34;helpVariables&#34;: [    &#34;VendorName&#34;,    &#34;Amount&#34;,    &#34;DocNumber&#34;,    &#34;TransactionDate&#34;,    &#34;DueDate&#34;,    &#34;Balance&#34;,    &#34;My Company Email&#34;   ],   &#34;requiredByUI&#34;: true,   &#34;multiSelect&#34;: false,   &#34;fieldType&#34;: &#34;string&#34;  },  &#34;Message&#34;: {   &#34;fieldValue&#34;: [    &#34;Hi&#34;   ],   &#34;possibleFieldValues&#34;: [],   &#34;configurable&#34;: true,   &#34;requiredByHandler&#34;: true,   &#34;requiredByUI&#34;: true,   &#34;helpVariables&#34;: [    &#34;CustomerName&#34;,    &#34;Amount&#34;,    &#34;DocNumber&#34;,    &#34;TransactionDate&#34;,    &#34;DueDate&#34;,    &#34;Balance&#34;,    &#34;My Company Email&#34;   ],   &#34;multiSelect&#34;: false,   &#34;fieldType&#34;: &#34;string&#34;  },  &#34;Id&#34;: {   &#34;fieldValue&#34;: [],   &#34;handlerFieldName&#34;: &#34;txnLocalId&#34;,   &#34;configurable&#34;: false,   &#34;requiredByHandler&#34;: true,   &#34;requiredByUI&#34;: false,   &#34;multiSelect&#34;: false,   &#34;fieldType&#34;: &#34;string&#34;,   &#34;valueType&#34;: &#34;PROCESS_VARIABLE&#34;  },  &#34;intuit_realmid&#34;: {   &#34;fieldValue&#34;: [],   &#34;handlerFieldName&#34;: &#34;realmId&#34;,   &#34;configurable&#34;: false,   &#34;requiredByHandler&#34;: true,   &#34;requiredByUI&#34;: false,   &#34;multiSelect&#34;: false,   &#34;fieldType&#34;: &#34;string&#34;,   &#34;valueType&#34;: &#34;PROCESS_VARIABLE&#34;  },  &#34;Send email&#34;: {   &#34;fieldValue&#34;: [    &#34;true&#34;   ],   &#34;handlerFieldName&#34;: &#34;Is Email&#34;,   &#34;configurable&#34;: false,   &#34;requiredByHandler&#34;: true,   &#34;requiredByUI&#34;: true,   &#34;multiSelect&#34;: false,   &#34;fieldType&#34;: &#34;boolean&#34;  },  &#34;Send notification&#34;: {   &#34;fieldValue&#34;: [    &#34;true&#34;   ],   &#34;handlerFieldName&#34;: &#34;Is Mobile&#34;,   &#34;configurable&#34;: false,   &#34;requiredByHandler&#34;: true,   &#34;requiredByUI&#34;: true,   &#34;multiSelect&#34;: false,   &#34;fieldType&#34;: &#34;boolean&#34;  },  &#34;CC&#34;: {   &#34;fieldValue&#34;: [],   &#34;possibleFieldValues&#34;: [],   &#34;configurable&#34;: true,   &#34;requiredByHandler&#34;: true,   &#34;requiredByUI&#34;: true,   &#34;helpVariables&#34;: [    &#34;My Company Email&#34;   ],   &#34;multiSelect&#34;: false,   &#34;fieldType&#34;: &#34;string&#34;  },  &#34;BCC&#34;: {   &#34;fieldValue&#34;: [],   &#34;possibleFieldValues&#34;: [],   &#34;configurable&#34;: true,   &#34;requiredByHandler&#34;: true,   &#34;requiredByUI&#34;: true,   &#34;helpVariables&#34;: [    &#34;My Company Email&#34;   ],   &#34;multiSelect&#34;: false,   &#34;fieldType&#34;: &#34;string&#34;  },  &#34;Workflow Type&#34;: {   &#34;fieldValue&#34;: [    &#34;TxnApproval&#34;   ],   &#34;possibleFieldValues&#34;: [],   &#34;configurable&#34;: false,   &#34;requiredByHandler&#34;: true,   &#34;requiredByUI&#34;: false,   &#34;multiSelect&#34;: false,   &#34;fieldType&#34;: &#34;string&#34;  } }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>sequenceToApproval_txnApproval</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0e5o342</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:receiveTask id="waitForApproval1_txnApproval" name="Wait for approval" implementation="##WebService" messageRef="Message_0upynr5">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "recordType": "bill"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": false}</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails" />
          <camunda:inputParameter name="currentStepDetails">{"required": false}</camunda:inputParameter>
          <camunda:outputParameter name="ApprovalStatus">
            <camunda:list />
          </camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_1v4ud5n</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0dawupp</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:boundaryEvent id="waitForTimerToElapse1_txnApproval" attachedToRef="waitForApproval1_txnApproval">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;camunda&#34;}" />
          <camunda:property name="taskDetails" value="{&#34;required&#34;: true}" />
          <camunda:property name="currentStepDetails" value="{ &#34;required&#34; : true}" />
          <camunda:property name="parameterDetails" value="{&#34;waitTime&#34;:{&#34;fieldValue&#34;:[&#34;7&#34;],&#34;configurable&#34;:true,&#34;requiredByhandler&#34;:false,&#34;requiredByUI&#34;:true,&#34;multiSelect&#34;:false,&#34;fieldType&#34;:&#34;integer&#34;},&#34;Not Approved&#34;:{&#34;fieldValue&#34;:[&#34;Not Approved&#34;],&#34;possibleFieldValues&#34;:[&#34;Not Approved&#34;],&#34;configurable&#34;:false,&#34;requiredByhandler&#34;:false,&#34;requiredByUI&#34;:true,&#34;multiSelect&#34;:true,&#34;fieldType&#34;:&#34;string&#34;},&#34;send reminder to approver&#34;:{&#34;fieldValue&#34;:[&#34;send reminder to approver&#34;],&#34;possibleFieldValues&#34;:[&#34;send reminder to approver&#34;,&#34;Auto approve&#34;],&#34;configurable&#34;:true,&#34;requiredByhandler&#34;:false,&#34;requiredByUI&#34;:true,&#34;multiSelect&#34;:false,&#34;fieldType&#34;:&#34;string&#34;},&#34;mappings&#34;:{&#34;fieldValue&#34;:[&#34;send reminder to approver:sendReminderEmail_txnApproval&#34;,&#34;Auto approve:autoUpdateAsApproved_txnApproval&#34;],&#34;required&#34;:true,&#34;configurable&#34;:true,&#34;requiredByhandler&#34;:false,&#34;requiredByUI&#34;:true,&#34;multiSelect&#34;:false,&#34;fieldType&#34;:&#34;string&#34;,&#34;possibleFieldValues&#34;:[&#34;send reminder to approver:sendReminderEmail_txnApproval&#34;,&#34;Auto approve:autoUpdateAsApproved_txnApproval&#34;]}}" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>SequenceFlow_0nfqqec</bpmn:outgoing>
      <bpmn:timerEventDefinition>
        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">${waitTime}</bpmn:timeDuration>
      </bpmn:timerEventDefinition>
    </bpmn:boundaryEvent>
    <bpmn:sendTask id="sendReminderEmail_txnApproval" name="Send reminder" implementation="##WebService" camunda:type="external" camunda:topic="test-premium-apps">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/was-send-email-workflows-qbdt",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{ "uiVisibility": true }</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="parameterDetails" value="{   &#34;Approver #1&#34;: {     &#34;fieldValue&#34;: [],     &#34;possibleFieldValues&#34;: [],     &#34;handlerFieldName&#34;: &#34;To&#34;,     &#34;configurable&#34;: true,     &#34;actionByUI&#34;: &#34;GET_ADMINS_ID&#34;,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: true,     &#34;fieldType&#34;: &#34;string&#34;   },   &#34;DocNumber&#34;: {     &#34;fieldValue&#34;: [],     &#34;handlerFieldName&#34;: &#34;DocNumber&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   }, &#34;CompanyName&#34;: {     &#34;fieldValue&#34;: [],     &#34;handlerFieldName&#34;: &#34;CompanyName&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },   &#34;Send To&#34;: {     &#34;fieldValue&#34;: [       &#34;Approver #1 email&#34;     ],     &#34;possibleFieldValues&#34;: [],     &#34;configurable&#34;: true,     &#34;actionByUI&#34;: &#34;GET_ADMINS_ID&#34;,     &#34;requiredByHandler&#34;: false,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: true,     &#34;fieldType&#34;: &#34;string&#34;   },   &#34;creatorName&#34;: {     &#34;fieldValue&#34;: [],     &#34;possibleFieldValues&#34;: [],     &#34;handlerFieldName&#34;: &#34;creatorName&#34;,     &#34;configurable&#34;: true,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },   &#34;Subject&#34;: {     &#34;fieldValue&#34;: [       &#34;Bill [[DocNumber]] needs your approval&#34;     ],     &#34;possibleFieldValues&#34;: [],     &#34;configurable&#34;: true,     &#34;requiredByHandler&#34;: true,     &#34;helpVariables&#34;: [       &#34;CustomerName&#34;,       &#34;Amount&#34;,       &#34;DocNumber&#34;,       &#34;TransactionDate&#34;,       &#34;DueDate&#34;,       &#34;Balance&#34;,       &#34;My Company Email&#34;     ],     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   },   &#34;Message&#34;: {     &#34;fieldValue&#34;: [     &#34;Hi,\n\nThe bill [[DocNumber]] from [[creatorName]] ([[CompanyName]]) is awaiting your approval.\n\nTo approve, go to Company &#62; Track and Approve Transactions in your QuickBooks.\n\nThanks,\n[[CompanyName]]&#34;   ],     &#34;possibleFieldValues&#34;: [],     &#34;configurable&#34;: true,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: true,     &#34;helpVariables&#34;: [       &#34;CustomerName&#34;,       &#34;Amount&#34;,       &#34;DocNumber&#34;,       &#34;TransactionDate&#34;,       &#34;DueDate&#34;,       &#34;Balance&#34;,       &#34;My Company Email&#34;     ],     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   },   &#34;Id&#34;: {     &#34;fieldValue&#34;: [],     &#34;handlerFieldName&#34;: &#34;txnLocalId&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },   &#34;intuit_realmid&#34;: {     &#34;fieldValue&#34;: [],     &#34;handlerFieldName&#34;: &#34;realmId&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },   &#34;Send email&#34;: {     &#34;fieldValue&#34;: [       &#34;true&#34;     ],     &#34;handlerFieldName&#34;: &#34;Is Email&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;boolean&#34;   },   &#34;Send notification&#34;: {     &#34;fieldValue&#34;: [       &#34;true&#34;     ],     &#34;handlerFieldName&#34;: &#34;Is Mobile&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;boolean&#34;   },   &#34;CC&#34;: {     &#34;fieldValue&#34;: [],     &#34;possibleFieldValues&#34;: [],     &#34;configurable&#34;: true,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: true,     &#34;helpVariables&#34;: [       &#34;My Company Email&#34;     ],     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   },   &#34;BCC&#34;: {     &#34;fieldValue&#34;: [],     &#34;possibleFieldValues&#34;: [],     &#34;configurable&#34;: true,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: true,     &#34;helpVariables&#34;: [       &#34;Customer Email&#34;     ],     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   },  &#34;Workflow Type&#34;: {     &#34;fieldValue&#34;: [       &#34;TxnApproval&#34;     ],     &#34;possibleFieldValues&#34;: [],     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   } }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>sequenceSendReminder_txnApproval</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0f9ufa8</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:sequenceFlow id="SequenceFlow_0f9ufa8" sourceRef="sendReminderEmail_txnApproval" targetRef="waitForApproval2_txnApproval" />
    <bpmn:receiveTask id="waitForApproval2_txnApproval" name="Wait for approval" implementation="##WebService" messageRef="Message_0upynr5">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "recordType": "bill"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
          <camunda:inputParameter name="currentStepDetails">{"required" : false}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_0f9ufa8</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1erbqak</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:sequenceFlow id="SequenceFlow_0nfqqec" sourceRef="waitForTimerToElapse1_txnApproval" targetRef="evaluateUserDefinedAction_txnApproval" />
    <bpmn:serviceTask id="autoUpdateAsApproved_txnApproval" name="Auto approve transaction" implementation="##WebService" camunda:type="external" camunda:topic="test-premium-apps">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/was-auto-approve-reject-txn-qbdt",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{
  "Id": {
    "fieldValue": [],
    "handlerFieldName": "txnId",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE"
  },
  "txnStatus": {
    "fieldValue": [
    "WORKFLOW_AUTO_APPROVED"
    ],
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string"
  },
  "intuit_realmid": {
    "fieldValue": [],
    "handlerFieldName": "realmId",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE"
  }
}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>sequenceAutoUpdate_txnApproval</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0necl5h</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:boundaryEvent id="waitForTimerToElapse2_txnApproval" attachedToRef="waitForApproval2_txnApproval">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;camunda&#34;}" />
          <camunda:property name="taskDetails" value="{&#34;required&#34;: false}" />
          <camunda:property name="currentStepDetails" value="{&#34;required&#34;: false}" />
          <camunda:property name="parameterDetails" value="{  &#34;waitTime&#34;: {     &#34;fieldValue&#34; : [&#34;30&#34;],    &#34;configurable&#34; : true,     &#34;requiredByhandler&#34; : false,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;integer&#34;   }}" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>SequenceFlow_reminderTimerElapsed</bpmn:outgoing>
      <bpmn:timerEventDefinition>
        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">P30D</bpmn:timeDuration>
      </bpmn:timerEventDefinition>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="SequenceFlow_reminderTimerElapsed" sourceRef="waitForTimerToElapse2_txnApproval" targetRef="autoRejectTxn_txnApproval" />
    <bpmn:exclusiveGateway id="exclusiveGateway_txnApproval" name="approved?" default="sequenceRejected_txnApproval">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{ &#34;taskHandler&#34;: &#34;appconnect&#34;,                         &#34;handlerId&#34;: &#34;1234&#34;,                         &#34;handlerName&#34;: &#34;sendEmail&#34;                         }" />
          <camunda:property name="parameterDetails" value="{ &#34;bill.status&#34;: { &#34;fieldValue&#34; : [&#34;approved&#34;],  &#34;configurable&#34; : false,  &#34;requiredByhandler&#34; : false,  &#34;requiredByUI&#34;: false,  &#34;multiSelect&#34;: false,  &#34;fieldType&#34;: &#34;string&#34;}}" />
          <camunda:property name="taskDetails" value="{&#34;required&#34;: true}" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_0dawupp</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_1erbqak</bpmn:incoming>
      <bpmn:outgoing>sequenceApproved_txnApproval</bpmn:outgoing>
      <bpmn:outgoing>sequenceRejected_txnApproval</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="sequenceApproved_txnApproval" name="yes" sourceRef="exclusiveGateway_txnApproval" targetRef="sendApproveNotification_txnApproval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${entityChangeType == 'approved'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:startEvent id="newTxnCreated_txnApproval" name="Transaction create event">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;was&#34;, &#34;recordType&#34;: &#34;bill&#34;}" />
          <camunda:property name="stepDetails" value="{&#34;newTxnCreated_txnApproval&#34;:[&#34;newTxnCreated_txnApproval&#34;,&#34;approveTxn_txnApproval&#34;,&#34;decision_billapproval_QBDT&#34;,&#34;sendForApproval_txnApproval&#34;,&#34;createProjectServiceTask_txnApproval&#34;],&#34;waitForApproval1_txnApproval&#34;:[&#34;waitForApproval1_txnApproval&#34;,&#34;exclusiveGateway_txnApproval&#34;,&#34;sendRejectNotification_txnApproval&#34;,&#34;sendApproveNotification_txnApproval&#34;],&#34;waitForTimerToElapse1_txnApproval&#34;:[&#34;waitForTimerToElapse1_txnApproval&#34;,&#34;evaluateUserDefinedAction_txnApproval&#34;,&#34;sendReminderEmail_txnApproval&#34;,&#34;autoUpdateAsApproved_txnApproval&#34;,&#34;sendNotificationToCreator_txnApproval&#34;],&#34;waitForApproval2_txnApproval&#34;:[&#34;waitForApproval2_txnApproval&#34;,&#34;exclusiveGateway_txnApproval&#34;,&#34;sendApproveNotification_txnApproval&#34;,&#34;sendRejectNotification_txnApproval&#34;],&#34;waitForTimerToElapse2_txnApproval&#34;:[&#34;waitForTimerToElapse2_txnApproval&#34;,&#34;autoRejectTxn_txnApproval&#34;,&#34;sendAutoRejectNotification_txnApproval&#34;]}" />
          <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
          <camunda:property name="currentStepDetails" value="{&#34;required&#34;: true}" />
          <camunda:property name="processVariablesDetails" value="[{&#34;variableName&#34;:&#34;ApprovalStatus&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;createdBy&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;approvedBy&#34;, &#34;variableType&#34;: &#34;String&#34;},{&#34;variableName&#34;:&#34;DocNumber&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;CompanyName&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;creatorName&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;approverName&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;TotalAmt&#34;, &#34;variableType&#34;: &#34;Double&#34;}, {&#34;variableName&#34;:&#34;VendorRef_value&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;VendorRef_name&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;VendorTypeRef_value&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;entityChangeType&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;Id&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;intuit_userid&#34;, &#34;variableType&#34;: &#34;String&#34;}, {&#34;variableName&#34;:&#34;intuit_realmid&#34;, &#34;variableType&#34;: &#34;String&#34;}]" />
          <camunda:property name="startableEvents" value="[&#34;created&#34;, &#34;updated&#34;]" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>SequenceFlow_09y5vl1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="SequenceFlow_0o7eg9h" sourceRef="sendRejectNotification_txnApproval" targetRef="end5_txnApproval" />
    <bpmn:sequenceFlow id="SequenceFlow_0v85vpc" sourceRef="sendApproveNotification_txnApproval" targetRef="end6_txnApproval" />
    <bpmn:serviceTask id="autoRejectTxn_txnApproval" name="Auto reject transaction" implementation="##WebService" camunda:type="external" camunda:topic="test-premium-apps">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/was-auto-approve-reject-txn-qbdt",
                        "actionName": "executeDummyAction"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{
                        "required": true
                        }</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{
  "Id": {
    "fieldValue": [],
    "handlerFieldName": "txnId",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE"
  },
  "txnStatus": {
    "fieldValue": [
      "WORKFLOW_AUTO_REJECTED"
    ],
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string"
  },
  "intuit_realmid": {
    "fieldValue": [],
    "handlerFieldName": "realmId",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE"
  }
}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_reminderTimerElapsed</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1mh685w</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="sendRejectNotification_txnApproval" name="Send notification to creator of transaction" implementation="##WebService" camunda:type="external" camunda:topic="test-premium-apps">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/was-send-email-workflows-qbdt",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{ "required": false }</camunda:inputParameter>
          <camunda:outputParameter name="ApprovalStatus">"rejected"</camunda:outputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="parameterDetails" value="{ &#34;intuit_userid&#34;: {     &#34;handlerFieldName&#34;: &#34;To&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },   &#34;DocNumber&#34;: {     &#34;fieldValue&#34;: [],     &#34;handlerFieldName&#34;: &#34;DocNumber&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   }, &#34;CompanyName&#34;: {     &#34;fieldValue&#34;: [],     &#34;handlerFieldName&#34;: &#34;CompanyName&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },   &#34;ApprovalStatus&#34;: {     &#34;fieldValue&#34;: [],     &#34;handlerFieldName&#34;: &#34;Approval Status&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },    &#34;approverName&#34;: {     &#34;fieldValue&#34;: [],     &#34;possibleFieldValues&#34;: [],     &#34;handlerFieldName&#34;: &#34;approverName&#34;,     &#34;configurable&#34;: true,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },   &#34;Subject&#34;: {     &#34;fieldValue&#34;: [       &#34;Bill [[DocNumber]] is rejected&#34;     ],     &#34;configurable&#34;: true,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   },   &#34;Message&#34;: {     &#34;fieldValue&#34;: [       &#34;Hi,\n\nYour bill [[DocNumber]] is rejected by [[approverName]].\n\nThanks,\n[[CompanyName]]&#34;     ],     &#34;configurable&#34;: true,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   },   &#34;Id&#34;: {     &#34;handlerFieldName&#34;: &#34;txnLocalId&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },   &#34;intuit_realmid&#34;: {     &#34;handlerFieldName&#34;: &#34;realmId&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },   &#34;Send email&#34;: {     &#34;fieldValue&#34;: [       &#34;true&#34;     ],     &#34;handlerFieldName&#34;: &#34;Is Email&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;boolean&#34;   },    &#34;Send notification&#34;: {     &#34;fieldValue&#34;: [       &#34;false&#34;     ],     &#34;handlerFieldName&#34;: &#34;isMobile&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;boolean&#34;   },    &#34;CC&#34;: {     &#34;configurable&#34;: true,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: true,     &#34;helpVariables&#34;: [       &#34;My Company Email&#34;     ],     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   },   &#34;BCC&#34;: {     &#34;configurable&#34;: true,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: true,     &#34;helpVariables&#34;: [       &#34;Customer Email&#34;     ],     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   },  &#34;Workflow Type&#34;: {     &#34;fieldValue&#34;: [       &#34;TxnApproval&#34;     ],     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   } }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>sequenceRejected_txnApproval</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0o7eg9h</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="sendApproveNotification_txnApproval" name="Send notification to creator of transaction" implementation="##WebService" camunda:type="external" camunda:topic="test-premium-apps">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/was-send-email-workflows-qbdt",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{
"intuit_userid": {
    "configurable": false,
    "handlerFieldName": "To",
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType":"PROCESS_VARIABLE"
  },
  "DocNumber": {
    "fieldValue": [],
    "handlerFieldName": "DocNumber",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType":"PROCESS_VARIABLE"
  },
"CompanyName": {
    "fieldValue": [],
    "handlerFieldName": "CompanyName",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType":"PROCESS_VARIABLE"
  },
 "ApprovalStatus": {
    "fieldValue":[],
    "handlerFieldName": "Approval Status",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType":"PROCESS_VARIABLE"
  },
   "approverName": {
    "fieldValue": [],
    "possibleFieldValues": [],
    "handlerFieldName": "approverName",
    "configurable": true,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType":"PROCESS_VARIABLE"
  },
  "Subject": {
    "fieldValue": [
      "Bill [[DocNumber]] is approved"
    ],
    "configurable": true,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string"
  },
  "Message": {
    "fieldValue": [
      "Hi,\n\nYour bill [[DocNumber]] is approved by [[approverName]].\n\nThanks,\n[[CompanyName]]"
    ],
    "configurable": true,
    "requiredByHandler": true,
    "requiredByUI": true,
    "multiSelect": false,
    "fieldType": "string"
  },
  "Id": {
    "handlerFieldName": "txnLocalId",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType":"PROCESS_VARIABLE"
  },
  "intuit_realmid": {
    "handlerFieldName": "realmId",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType":"PROCESS_VARIABLE"
  },
  "Send email": {
    "fieldValue": [
      "true"
    ],
    "handlerFieldName": "Is Email",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": true,
    "multiSelect": false,
    "fieldType": "boolean"
  },
  "Send notification": {
    "fieldValue": [
      "false"
    ],
    "handlerFieldName": "isMobile",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": true,
    "multiSelect": false,
    "fieldType": "boolean"
  },
   "CC": {
    "configurable": true,
    "requiredByHandler": true,
    "requiredByUI": true,
    "multiSelect": false,
    "fieldType": "string"
  },
  "BCC": {
    "configurable": true,
    "requiredByHandler": true,
    "requiredByUI": true,
    "helpVariables": [
      "Customer Email"
    ],
    "multiSelect": false,
    "fieldType": "string"
  },
  "Workflow Type": {
    "fieldValue": [
      "TxnApproval"
    ],
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string"
  }
}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{ "required": false }</camunda:inputParameter>
          <camunda:outputParameter name="ApprovalStatus">"approved"</camunda:outputParameter>
        </camunda:inputOutput>
        <camunda:field name="approvalstatus">
          <camunda:string>approved</camunda:string>
        </camunda:field>
        <camunda:properties>
          <camunda:property name="parameterDetails" value="{ &#34;intuit_userid&#34;: {     &#34;configurable&#34;: false,     &#34;handlerFieldName&#34;: &#34;To&#34;,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },   &#34;DocNumber&#34;: {     &#34;fieldValue&#34;: [],     &#34;handlerFieldName&#34;: &#34;DocNumber&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   }, &#34;CompanyName&#34;: {     &#34;fieldValue&#34;: [],     &#34;handlerFieldName&#34;: &#34;CompanyName&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },  &#34;ApprovalStatus&#34;: {     &#34;fieldValue&#34;:[],     &#34;handlerFieldName&#34;: &#34;Approval Status&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },    &#34;approverName&#34;: {     &#34;fieldValue&#34;: [],     &#34;possibleFieldValues&#34;: [],     &#34;handlerFieldName&#34;: &#34;approverName&#34;,     &#34;configurable&#34;: true,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },   &#34;Subject&#34;: {     &#34;fieldValue&#34;: [       &#34;Bill [[DocNumber]] is approved&#34;     ],     &#34;configurable&#34;: true,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   },   &#34;Message&#34;: {     &#34;fieldValue&#34;: [       &#34;Hi,\n\nYour bill [[DocNumber]] is approved by [[approverName]].\n\nThanks,\n[[CompanyName]]&#34;     ],     &#34;configurable&#34;: true,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   },   &#34;Id&#34;: {     &#34;handlerFieldName&#34;: &#34;txnLocalId&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },   &#34;intuit_realmid&#34;: {     &#34;handlerFieldName&#34;: &#34;realmId&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },   &#34;Send email&#34;: {     &#34;fieldValue&#34;: [       &#34;true&#34;     ],     &#34;handlerFieldName&#34;: &#34;Is Email&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;boolean&#34;   },   &#34;Send notification&#34;: {     &#34;fieldValue&#34;: [       &#34;false&#34;     ],     &#34;handlerFieldName&#34;: &#34;isMobile&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;boolean&#34;   },    &#34;CC&#34;: {     &#34;configurable&#34;: true,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   },   &#34;BCC&#34;: {     &#34;configurable&#34;: true,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: true,     &#34;helpVariables&#34;: [       &#34;Customer Email&#34;     ],     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   },   &#34;Workflow Type&#34;: {     &#34;fieldValue&#34;: [       &#34;TxnApproval&#34;     ],     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   } }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>sequenceApproved_txnApproval</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0v85vpc</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_0e5o342" sourceRef="sendForApproval_txnApproval" targetRef="parallelGateway_txnapproval" />
    <bpmn:businessRuleTask id="decision_billapproval_QBDT" name="Bill approval rule evaluation" implementation="##unspecified" camunda:type="external" camunda:topic="test-premium-apps">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{ "taskHandler": "was","handlerId":"was_dmn_evaluator","actionName": "evaluateDMN"}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_09y5vl1</bpmn:incoming>
      <bpmn:outgoing>sequenceToApproval_txnApproval</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0gmat5b</bpmn:outgoing>
      <bpmn:outgoing>sequenceToProjectService_txnApproval</bpmn:outgoing>
    </bpmn:businessRuleTask>
    <bpmn:sequenceFlow id="SequenceFlow_09y5vl1" sourceRef="newTxnCreated_txnApproval" targetRef="decision_billapproval_QBDT" />
    <bpmn:sequenceFlow id="sequenceToApproval_txnApproval" sourceRef="decision_billapproval_QBDT" targetRef="sendForApproval_txnApproval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approvalRequired == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0necl5h" sourceRef="autoUpdateAsApproved_txnApproval" targetRef="sendNotificationToCreator_txnApproval" />
    <bpmn:sequenceFlow id="SequenceFlow_04ggzbx" sourceRef="sendNotificationToCreator_txnApproval" targetRef="end3_txnapproval" />
    <bpmn:serviceTask id="sendNotificationToCreator_txnApproval" name="Send notification to creator of transaction" implementation="##WebService" camunda:type="external" camunda:topic="test-premium-apps">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/was-send-email-workflows-qbdt",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{
"intuit_userid": {
    "handlerFieldName": "To",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType":"PROCESS_VARIABLE"
  },
  "DocNumber": {
    "fieldValue": [],
    "handlerFieldName": "DocNumber",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType":"PROCESS_VARIABLE"
  },
"CompanyName": {
    "fieldValue": [],
    "handlerFieldName": "CompanyName",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType":"PROCESS_VARIABLE"
  },
 "ApprovalStatus": {
    "fieldValue": [],
    "handlerFieldName": "Approval Status",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType":"PROCESS_VARIABLE"
  },
  "Subject": {
    "fieldValue": [
      "Bill [[DocNumber]] is auto-approved"
    ],
    "configurable": true,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string"
  },
  "Message": {
    "fieldValue": [
      "Hi,\n\nYour bill [[DocNumber]] is auto-approved.\n\nThanks,\n[[CompanyName]]"
    ],
    "configurable": true,
    "requiredByHandler": true,
    "requiredByUI": true,
    "multiSelect": false,
    "fieldType": "string"
  },
  "Id": {
    "handlerFieldName": "txnLocalId",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType":"PROCESS_VARIABLE"
  },
  "intuit_realmid": {
    "handlerFieldName": "realmId",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType":"PROCESS_VARIABLE"
  },
   "Send email": {
    "fieldValue": [
      "true"
    ],
    "handlerFieldName": "Is Email",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": true,
    "multiSelect": false,
    "fieldType": "boolean"
  },
  "Send notification": {
    "fieldValue": [
      "false"
    ],
    "handlerFieldName": "isMobile",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": true,
    "multiSelect": false,
    "fieldType": "boolean"
  },
   "CC": {
    "configurable": true,
    "requiredByHandler": true,
    "requiredByUI": true,
    "helpVariables": [
      "My Company Email"
    ],
    "multiSelect": false,
    "fieldType": "string"
  },
  "BCC": {
    "configurable": true,
    "requiredByHandler": true,
    "requiredByUI": true,
    "helpVariables": [
      "Customer Email"
    ],
    "multiSelect": false,
    "fieldType": "string"
  },
 "Workflow Type": {
    "fieldValue": [
      "TxnApproval"
    ],
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string"
  }
}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{ "required": false }</camunda:inputParameter>
          <camunda:outputParameter name="ApprovalStatus">"autoapproved"</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_0necl5h</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_04ggzbx</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_1mh685w" sourceRef="autoRejectTxn_txnApproval" targetRef="sendAutoRejectNotification_txnApproval" />
    <bpmn:serviceTask id="sendAutoRejectNotification_txnApproval" name="Send notification to creator of transaction" implementation="##WebService" camunda:type="external" camunda:topic="test-premium-apps">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/was-send-email-workflows-qbdt",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{ "required": false }</camunda:inputParameter>
          <camunda:outputParameter name="ApprovalStatus">"autorejected"</camunda:outputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="parameterDetails" value="{ &#34;intuit_userid&#34;: {     &#34;handlerFieldName&#34;: &#34;To&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },   &#34;DocNumber&#34;: {     &#34;fieldValue&#34;: [],     &#34;handlerFieldName&#34;: &#34;DocNumber&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   }, &#34;CompanyName&#34;: {     &#34;fieldValue&#34;: [],     &#34;handlerFieldName&#34;: &#34;CompanyName&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },  &#34;ApprovalStatus&#34;: {     &#34;fieldValue&#34;: [],     &#34;handlerFieldName&#34;: &#34;Approval Status&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },    &#34;approverName&#34;: {     &#34;fieldValue&#34;: [],     &#34;possibleFieldValues&#34;: [],     &#34;handlerFieldName&#34;: &#34;approverName&#34;,     &#34;configurable&#34;: true,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },   &#34;Subject&#34;: {     &#34;fieldValue&#34;: [       &#34;Bill [[DocNumber]] is auto-rejected&#34;     ],     &#34;configurable&#34;: true,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   },   &#34;Message&#34;: {     &#34;fieldValue&#34;: [       &#34;Hi,\n\nYour bill [[DocNumber]] is auto-rejected as [[approverName]] didn&#39;t take any action for 30 days after the reminder was sent.\n\nYou need to duplicate this bill and resend for approval.\n\nThanks,\n[[CompanyName]]&#34;     ],     &#34;configurable&#34;: true,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   },   &#34;Id&#34;: {     &#34;handlerFieldName&#34;: &#34;txnLocalId&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },   &#34;intuit_realmid&#34;: {     &#34;handlerFieldName&#34;: &#34;realmId&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },    &#34;Send email&#34;: {     &#34;fieldValue&#34;: [       &#34;true&#34;     ],     &#34;handlerFieldName&#34;: &#34;Is Email&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;boolean&#34;   },    &#34;Send notification&#34;: {     &#34;fieldValue&#34;: [       &#34;false&#34;     ],     &#34;handlerFieldName&#34;: &#34;isMobile&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;boolean&#34;   },    &#34;CC&#34;: {     &#34;configurable&#34;: true,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: true,     &#34;helpVariables&#34;: [       &#34;My Company Email&#34;     ],     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   },   &#34;BCC&#34;: {     &#34;configurable&#34;: true,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: true,     &#34;helpVariables&#34;: [       &#34;Customer Email&#34;     ],     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   },   &#34;Workflow Type&#34;: {     &#34;fieldValue&#34;: [       &#34;TxnApproval&#34;     ],     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   } }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_1mh685w</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_02plsxx</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="sequenceRejected_txnApproval" name="no" sourceRef="exclusiveGateway_txnApproval" targetRef="sendRejectNotification_txnApproval" />
    <bpmn:subProcess id="deletedVoidedSubProcess_txnapproval" name="deleted voided disable handler subprocess" triggeredByEvent="true">
      <bpmn:startEvent id="deleted_voided_txnapproval" name="deleted voided disable txnapproval">
        <bpmn:outgoing>SequenceFlow_1y2n94c</bpmn:outgoing>
        <bpmn:messageEventDefinition messageRef="Message_02ejj4p" />
      </bpmn:startEvent>
      <bpmn:sequenceFlow id="SequenceFlow_1y2n94c" sourceRef="deleted_voided_txnapproval" targetRef="end2_txnApproval" />
      <bpmn:endEvent id="end2_txnApproval" name="Transaction deleted or voided or disable">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>SequenceFlow_1y2n94c</bpmn:incoming>
        <bpmn:escalationEventDefinition escalationRef="Escalation_1rnkx1y" />
      </bpmn:endEvent>
    </bpmn:subProcess>
    <bpmn:sequenceFlow id="SequenceFlow_0gmat5b" sourceRef="decision_billapproval_QBDT" targetRef="approveTxn_txnApproval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approvalRequired == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="approveTxn_txnApproval" name="Approve Txn" implementation="##WebService" camunda:type="external" camunda:topic="test-premium-apps">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/was-auto-approve-reject-txn-qbdt",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{
  "Id": {
    "fieldValue": [],
    "handlerFieldName": "txnId",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE"
  },
  "txnStatus": {
    "fieldValue": [
    "WORKFLOW_AUTO_APPROVED"
    ],
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string"
  },
  "intuit_realmid": {
    "fieldValue": [],
    "handlerFieldName": "realmId",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE"
  }
}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_0gmat5b</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0mrs5zp</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_0mrs5zp" sourceRef="approveTxn_txnApproval" targetRef="end7_txnapproval" />
    <bpmn:endEvent id="end7_txnapproval" name="Txn Auto Approved">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "handlerId": "",
                        "actionName": "updateProcessStatus"
                        }</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_0mrs5zp</bpmn:incoming>
      <bpmn:messageEventDefinition messageRef="Message_0n540t5" camunda:type="external" camunda:topic="qbdt_approval" />
    </bpmn:endEvent>
    <bpmn:serviceTask id="createProjectServiceTask_txnApproval" name="Create Project Service task" camunda:asyncBefore="true" implementation="##WebService" camunda:type="external" camunda:topic="test-premium-apps">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/taskmanager-create-task-qbdt",
                        "actionName": "executeWorkflowAction",
                        "responseFields": ["taskId", "projectId"]
                        }</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{ "required": true}</camunda:inputParameter>
          <camunda:outputParameter name="ApprovalStatus">"pendingapproval"</camunda:outputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="parameterDetails" value="{  &#34;Id&#34;: {     &#34;fieldValue&#34;: [],     &#34;handlerFieldName&#34;: &#34;TxnId&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   }, &#34;DocNumber&#34;: {     &#34;fieldValue&#34;: [],     &#34;handlerFieldName&#34;: &#34;DocNumber&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },   &#34;TotalAmt&#34;: {     &#34;fieldValue&#34;: [],     &#34;handlerFieldName&#34;: &#34;TxnAmount&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },  &#34;ApproverLocalId&#34;: {     &#34;fieldValue&#34;: [],     &#34;possibleFieldValues&#34;: [],     &#34;handlerFieldName&#34;: &#34;Approver&#34;,     &#34;configurable&#34;: true,     &#34;actionByUI&#34;: &#34;GET_ADMINS_ID&#34;,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   },  &#34;createdBy&#34;: {     &#34;fieldValue&#34;: [],     &#34;handlerFieldName&#34;: &#34;CreatedBy&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },   &#34;intuit_realmid&#34;: {     &#34;fieldValue&#34;: [],     &#34;handlerFieldName&#34;: &#34;realmId&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },   &#34;Name&#34;: {     &#34;fieldValue&#34;: [       &#34;Approve bill&#34;     ],     &#34;possibleFieldValues&#34;: [],     &#34;configurable&#34;: true,     &#34;requiredByHandler&#34;: true,     &#34;helpVariables&#34;: [],     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   },   &#34;Assignee&#34;: {     &#34;fieldValue&#34;: [],     &#34;possibleFieldValues&#34;: [],     &#34;configurable&#34;: true,     &#34;actionByUI&#34;: &#34;GET_ADMINS_ID&#34;,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: true,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   },   &#34;VendorRef_value&#34;: {     &#34;fieldValue&#34;: [],     &#34;handlerFieldName&#34;: &#34;clientId&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },  &#34;VendorRef_name&#34;: {     &#34;fieldValue&#34;: [],     &#34;handlerFieldName&#34;: &#34;VendorName&#34;,     &#34;configurable&#34;: false,     &#34;requiredByHandler&#34;: true,     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;,     &#34;valueType&#34;:&#34;PROCESS_VARIABLE&#34;   },   &#34;ProjectType&#34;: {     &#34;fieldValue&#34;: [       &#34;QB_BILL_APPROVAL&#34;     ],     &#34;possibleFieldValues&#34;: [],     &#34;configurable&#34;: true,     &#34;requiredByHandler&#34;: true,     &#34;helpVariables&#34;: [],     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   },   &#34;TaskType&#34;: {     &#34;fieldValue&#34;: [       &#34;QB_BILL&#34;     ],     &#34;possibleFieldValues&#34;: [],     &#34;configurable&#34;: true,     &#34;requiredByHandler&#34;: true,     &#34;helpVariables&#34;: [],     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   },   &#34;Due date&#34;: {     &#34;fieldValue&#34;: [],     &#34;possibleFieldValues&#34;: [],     &#34;configurable&#34;: true,     &#34;requiredByHandler&#34;: true,     &#34;helpVariables&#34;: [],     &#34;requiredByUI&#34;: false,     &#34;multiSelect&#34;: false,     &#34;fieldType&#34;: &#34;string&#34;   } }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>sequenceToProjectService_txnApproval</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1xpb0ei</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="sequenceToProjectService_txnApproval" sourceRef="decision_billapproval_QBDT" targetRef="createProjectServiceTask_txnApproval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approvalRequired == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:parallelGateway id="parallelGateway_txnapproval" camunda:asyncBefore="true">
      <bpmn:incoming>SequenceFlow_1xpb0ei</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_0e5o342</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1v4ud5n</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:sequenceFlow id="SequenceFlow_1xpb0ei" sourceRef="createProjectServiceTask_txnApproval" targetRef="parallelGateway_txnapproval" />
    <bpmn:sequenceFlow id="SequenceFlow_1v4ud5n" sourceRef="parallelGateway_txnapproval" targetRef="waitForApproval1_txnApproval" />
    <bpmn:subProcess id="SubProcess_1swkaoz" triggeredByEvent="true">
      <bpmn:serviceTask id="closeProjectServiceTask_txnapproval" name="Close Project service task" implementation="##WebService" camunda:type="external" camunda:topic="test-premium-apps">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/taskmanager-update-task-qbdt",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
            <camunda:inputParameter name="parameterDetails">{
"taskId": {
    "fieldValue": [],
    "possibleFieldValues": [],
    "configurable": true,
    "handlerFieldName": "Task",
    "requiredByHandler": true,
    "helpVariables": [],
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType":"PROCESS_VARIABLE"
  },
 "approvedBy": {
    "fieldValue": [],
    "handlerFieldName": "ApprovedBy",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType":"PROCESS_VARIABLE"
  },
 "ApprovalStatus": {
    "fieldValue": [],
    "possibleFieldValues": [],
    "configurable": true,
    "handlerFieldName": "ApprovalStatus",
    "requiredByHandler": true,
    "helpVariables": [],
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType":"PROCESS_VARIABLE"
  },
 "entityChangeType": {
    "fieldValue": [],
    "possibleFieldValues": [],
    "configurable": true,
    "handlerFieldName": "EntityChangeType",
    "requiredByHandler": true,
    "helpVariables": [],
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType":"PROCESS_VARIABLE"
  },
  "projectId": {
    "fieldValue": [],
    "possibleFieldValues": [],
    "configurable": true,
    "handlerFieldName": "Project",
    "requiredByHandler": true,
    "helpVariables": [],
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType":"PROCESS_VARIABLE"
  },
  "Status": {
    "fieldValue": [
"Complete"
    ],
    "possibleFieldValues": [],
    "configurable": true,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string"
  }
}</camunda:inputParameter>
            <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>SequenceFlow_161qiu0</bpmn:incoming>
        <bpmn:outgoing>SequenceFlow_02em4k8</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:sequenceFlow id="SequenceFlow_161qiu0" sourceRef="start_closeProject" targetRef="closeProjectServiceTask_txnapproval" />
      <bpmn:sequenceFlow id="SequenceFlow_02em4k8" sourceRef="closeProjectServiceTask_txnapproval" targetRef="end_txnapproval" />
      <bpmn:endEvent id="end_txnapproval" name="Transaction approval process ended">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "handlerId": "",
                        "actionName": "updateProcessStatus"
                        }</camunda:inputParameter>
            <camunda:inputParameter name="visibleToUI">false</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>SequenceFlow_02em4k8</bpmn:incoming>
        <bpmn:messageEventDefinition messageRef="Message_0n540t5" camunda:type="external" camunda:topic="qbdt_approval" />
      </bpmn:endEvent>
      <bpmn:startEvent id="start_closeProject" name="Close project event">
        <bpmn:outgoing>SequenceFlow_161qiu0</bpmn:outgoing>
        <bpmn:escalationEventDefinition escalationRef="Escalation_1rnkx1y" camunda:escalationCodeVariable="endEvent" />
      </bpmn:startEvent>
    </bpmn:subProcess>
    <bpmn:sequenceFlow id="SequenceFlow_0dawupp" sourceRef="waitForApproval1_txnApproval" targetRef="exclusiveGateway_txnApproval" />
    <bpmn:sequenceFlow id="SequenceFlow_1erbqak" sourceRef="waitForApproval2_txnApproval" targetRef="exclusiveGateway_txnApproval" />
    <bpmn:sequenceFlow id="SequenceFlow_02plsxx" sourceRef="sendAutoRejectNotification_txnApproval" targetRef="end4_txnApproval" />
    <bpmn:endEvent id="end6_txnApproval" name="Transaction approved">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_0v85vpc</bpmn:incoming>
      <bpmn:escalationEventDefinition escalationRef="Escalation_1rnkx1y" />
    </bpmn:endEvent>
    <bpmn:endEvent id="end5_txnApproval" name="Transaction rejected">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_0o7eg9h</bpmn:incoming>
      <bpmn:escalationEventDefinition escalationRef="Escalation_1rnkx1y" />
    </bpmn:endEvent>
    <bpmn:endEvent id="end4_txnApproval" name="Transaction auto rejected">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_02plsxx</bpmn:incoming>
      <bpmn:escalationEventDefinition escalationRef="Escalation_1rnkx1y" />
    </bpmn:endEvent>
    <bpmn:endEvent id="end3_txnapproval" name="Txn auto approved because of time out">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_04ggzbx</bpmn:incoming>
      <bpmn:escalationEventDefinition escalationRef="Escalation_1rnkx1y" />
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="evaluateUserDefinedAction_txnApproval">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{&#34;taskHandler&#34;: &#34;UI&#34;}" />
          <camunda:property name="taskDetails" value="{&#34;required&#34;: true}" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_0nfqqec</bpmn:incoming>
      <bpmn:outgoing>sequenceAutoUpdate_txnApproval</bpmn:outgoing>
      <bpmn:outgoing>sequenceSendReminder_txnApproval</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="sequenceAutoUpdate_txnApproval" sourceRef="evaluateUserDefinedAction_txnApproval" targetRef="autoUpdateAsApproved_txnApproval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${GW == autoUpdate}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="sequenceSendReminder_txnApproval" name="Reminder action chosen by user" sourceRef="evaluateUserDefinedAction_txnApproval" targetRef="sendReminderEmail_txnApproval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${sendApprovalReminder == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmn:message id="Message_02ejj4p" name="deleted_voided_disable" />
  <bpmn:message id="Message_0upynr5" name="approved_rejected" />
  <bpmn:message id="Message_0n540t5" name="process_ended_message" />
  <bpmn:message id="Message_0hnhsgd" name="closeProject" />
  <bpmn:escalation id="Escalation_1rnkx1y" name="close_task" escalationCode="closetask" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="billapproval_QBDT">
      <bpmndi:BPMNEdge id="SequenceFlow_109yat4_di" bpmnElement="sequenceSendReminder_txnApproval">
        <di:waypoint x="863" y="507" />
        <di:waypoint x="938" y="507" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="853" y="476" width="81" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0sz88v2_di" bpmnElement="sequenceAutoUpdate_txnApproval">
        <di:waypoint x="838" y="532" />
        <di:waypoint x="838" y="583" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_02plsxx_di" bpmnElement="SequenceFlow_02plsxx">
        <di:waypoint x="1400" y="592" />
        <di:waypoint x="1400" y="702" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1erbqak_di" bpmnElement="SequenceFlow_1erbqak">
        <di:waypoint x="1200" y="353" />
        <di:waypoint x="1200" y="312" />
        <di:waypoint x="1106" y="312" />
        <di:waypoint x="1106" y="268" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0dawupp_di" bpmnElement="SequenceFlow_0dawupp">
        <di:waypoint x="844" y="313" />
        <di:waypoint x="844" y="243" />
        <di:waypoint x="1081" y="243" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1v4ud5n_di" bpmnElement="SequenceFlow_1v4ud5n">
        <di:waypoint x="725" y="353" />
        <di:waypoint x="794" y="353" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1xpb0ei_di" bpmnElement="SequenceFlow_1xpb0ei">
        <di:waypoint x="610" y="500" />
        <di:waypoint x="700" y="500" />
        <di:waypoint x="700" y="378" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0eznuy1_di" bpmnElement="sequenceToProjectService_txnApproval">
        <di:waypoint x="350" y="365" />
        <di:waypoint x="350" y="500" />
        <di:waypoint x="510" y="500" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0mrs5zp_di" bpmnElement="SequenceFlow_0mrs5zp">
        <di:waypoint x="601" y="220" />
        <di:waypoint x="702" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0gmat5b_di" bpmnElement="SequenceFlow_0gmat5b">
        <di:waypoint x="380" y="325" />
        <di:waypoint x="440" y="325" />
        <di:waypoint x="440" y="220" />
        <di:waypoint x="501" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_08y2sd8_di" bpmnElement="sequenceRejected_txnApproval">
        <di:waypoint x="1106" y="218" />
        <di:waypoint x="1106" y="121" />
        <di:waypoint x="1268" y="121" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1089" y="190" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1mh685w_di" bpmnElement="SequenceFlow_1mh685w">
        <di:waypoint x="1252" y="552" />
        <di:waypoint x="1350" y="552" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_04ggzbx_di" bpmnElement="SequenceFlow_04ggzbx">
        <di:waypoint x="780" y="755" />
        <di:waypoint x="698" y="755" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0necl5h_di" bpmnElement="SequenceFlow_0necl5h">
        <di:waypoint x="830" y="663" />
        <di:waypoint x="830" y="715" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0utyhtt_di" bpmnElement="sequenceToApproval_txnApproval">
        <di:waypoint x="380" y="353" />
        <di:waypoint x="500" y="353" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_09y5vl1_di" bpmnElement="SequenceFlow_09y5vl1">
        <di:waypoint x="234" y="315" />
        <di:waypoint x="280" y="315" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0e5o342_di" bpmnElement="SequenceFlow_0e5o342">
        <di:waypoint x="600" y="353" />
        <di:waypoint x="675" y="353" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0v85vpc_di" bpmnElement="SequenceFlow_0v85vpc">
        <di:waypoint x="1368" y="243" />
        <di:waypoint x="1465" y="243" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0o7eg9h_di" bpmnElement="SequenceFlow_0o7eg9h">
        <di:waypoint x="1368" y="121" />
        <di:waypoint x="1465" y="121" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0y6uvoi_di" bpmnElement="sequenceApproved_txnApproval">
        <di:waypoint x="1131" y="243" />
        <di:waypoint x="1268" y="243" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1131" y="227" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_09zaj8t_di" bpmnElement="SequenceFlow_reminderTimerElapsed">
        <di:waypoint x="1201" y="451" />
        <di:waypoint x="1201" y="512" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0nfqqec_di" bpmnElement="SequenceFlow_0nfqqec">
        <di:waypoint x="838" y="411" />
        <di:waypoint x="838" y="482" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0f9ufa8_di" bpmnElement="SequenceFlow_0f9ufa8">
        <di:waypoint x="988" y="467" />
        <di:waypoint x="988" y="401" />
        <di:waypoint x="1150" y="401" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="SendTask_0cbehcx_di" bpmnElement="sendForApproval_txnApproval">
        <dc:Bounds x="500" y="313" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ReceiveTask_0u4i7br_di" bpmnElement="waitForApproval1_txnApproval">
        <dc:Bounds x="794" y="313" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="SendTask_1g3ea5h_di" bpmnElement="sendReminderEmail_txnApproval">
        <dc:Bounds x="938" y="467" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ReceiveTask_10ztdcz_di" bpmnElement="waitForApproval2_txnApproval">
        <dc:Bounds x="1150" y="353" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0fvublp_di" bpmnElement="autoUpdateAsApproved_txnApproval">
        <dc:Bounds x="788" y="583" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1gey5dp_di" bpmnElement="exclusiveGateway_txnApproval" isMarkerVisible="true">
        <dc:Bounds x="1081" y="218" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1040" y="255" width="52" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_053q9wi_di" bpmnElement="newTxnCreated_txnApproval">
        <dc:Bounds x="198" y="297" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="188" y="340" width="61" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1c9aodz_di" bpmnElement="autoRejectTxn_txnApproval">
        <dc:Bounds x="1152" y="512" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0tfr5e9_di" bpmnElement="sendRejectNotification_txnApproval">
        <dc:Bounds x="1268" y="81" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1od76nh_di" bpmnElement="sendApproveNotification_txnApproval">
        <dc:Bounds x="1268" y="203" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BusinessRuleTask_06zco86_di" bpmnElement="decision_billapproval_QBDT">
        <dc:Bounds x="280" y="285" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_01jstbc_di" bpmnElement="sendNotificationToCreator_txnApproval">
        <dc:Bounds x="780" y="715" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1le18uv_di" bpmnElement="sendAutoRejectNotification_txnApproval">
        <dc:Bounds x="1350" y="512" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="SubProcess_0v780n2_di" bpmnElement="deletedVoidedSubProcess_txnapproval" isExpanded="true">
        <dc:Bounds x="120" y="600" width="350" height="200" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1y2n94c_di" bpmnElement="SequenceFlow_1y2n94c">
        <di:waypoint x="178" y="714" />
        <di:waypoint x="292" y="714" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="StartEvent_1yipiqv_di" bpmnElement="deleted_voided_txnapproval">
        <dc:Bounds x="142" y="696" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="125" y="739" width="77" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1prsqov_di" bpmnElement="end2_txnApproval">
        <dc:Bounds x="292" y="696" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="268" y="659" width="85" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0jn1kw0_di" bpmnElement="approveTxn_txnApproval">
        <dc:Bounds x="501" y="180" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1xjldkk_di" bpmnElement="end7_txnapproval">
        <dc:Bounds x="702" y="202" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="698" y="245" width="48" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0y2u86l_di" bpmnElement="createProjectServiceTask_txnApproval">
        <dc:Bounds x="510" y="460" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ParallelGateway_0qj6d1j_di" bpmnElement="parallelGateway_txnapproval">
        <dc:Bounds x="675" y="328" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="SubProcess_092kqjz_di" bpmnElement="SubProcess_1swkaoz" isExpanded="true">
        <dc:Bounds x="120" y="840" width="350" height="200" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_02em4k8_di" bpmnElement="SequenceFlow_02em4k8">
        <di:waypoint x="345" y="930" />
        <di:waypoint x="397" y="930" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_161qiu0_di" bpmnElement="SequenceFlow_161qiu0">
        <di:waypoint x="183" y="930" />
        <di:waypoint x="245" y="930" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_1jg8qu9_di" bpmnElement="closeProjectServiceTask_txnapproval">
        <dc:Bounds x="245" y="890" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1r93sb6_di" bpmnElement="end_txnapproval">
        <dc:Bounds x="397" y="912" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="374" y="955" width="84" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_03d9dy8_di" bpmnElement="start_closeProject">
        <dc:Bounds x="147" y="912" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="133" y="955" width="65" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_0x9v1bs_di" bpmnElement="end6_txnApproval">
        <dc:Bounds x="1465" y="225" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1520" y="236" width="58" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_176ue2g_di" bpmnElement="end5_txnApproval">
        <dc:Bounds x="1465" y="103" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1525" y="114" width="58" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1cvcbl3_di" bpmnElement="end4_txnApproval">
        <dc:Bounds x="1382" y="702" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1363" y="745" width="82" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_0c7vrr2_di" bpmnElement="end3_txnapproval">
        <dc:Bounds x="662" y="737" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="641" y="780" width="78" height="53" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_026vdsv_di" bpmnElement="evaluateUserDefinedAction_txnApproval" isMarkerVisible="true">
        <dc:Bounds x="813" y="482" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BoundaryEvent_1gb6h60_di" bpmnElement="waitForTimerToElapse2_txnApproval">
        <dc:Bounds x="1184" y="415" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BoundaryEvent_0nb8g8w_di" bpmnElement="waitForTimerToElapse1_txnApproval">
        <dc:Bounds x="820" y="375" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="811" y="347" width="4" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
