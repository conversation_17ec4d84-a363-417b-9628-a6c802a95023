<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1oo65x9" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="3.7.0">
  <bpmn:process id="customApproval" isExecutable="true" camunda:historyTimeToLive="0">
    <bpmn:startEvent id="customStartEvent" name="transaction custom event">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="stepDetails" value="{  &#34;customStartEvent&#34;:  [&#34;customStartEvent&#34;,&#34;decisionElement&#34;, &#34;autoApprove_Txn&#34;, &#34;inclusiveGateway&#34;, &#34;sendCompanyEmail&#34;,&#34;createTask&#34;],     &#34;customWorkflowWaitEvent&#34;:      [&#34;customWorkflowWaitEvent&#34;, &#34;exclusiveGateway_txnApproval&#34;, &#34;sendRejectNotification_txnApproval&#34;, &#34;sendApproveNotification_txnApproval&#34;, &#34;autoRejectTxn_txnApproval&#34;, &#34;sendAutoRejectNotification_txnApproval&#34; ] }" />
          <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
          <camunda:property name="currentStepDetails" value="{ &#34;required&#34;: true }" />
          <camunda:property name="processVariablesDetails" value="[   {     &#34;variableName&#34;: &#34;entityChangeType&#34;,     &#34;variableType&#34;: &#34;String&#34;   },   {     &#34;variableName&#34;: &#34;Id&#34;,     &#34;variableType&#34;: &#34;String&#34;   },   {     &#34;variableName&#34;: &#34;intuit_userid&#34;,     &#34;variableType&#34;: &#34;String&#34;   },   {     &#34;variableName&#34;: &#34;intuit_realmid&#34;,     &#34;variableType&#34;: &#34;String&#34;   } ]" />
          <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;was&#34;}" />
          <camunda:property name="startableEvents" value="[&#34;created&#34;,&#34;updated&#34;]" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_0zrvk3w</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:businessRuleTask id="decisionElement" name="Txn Rule Evaluation" camunda:resultVariable="decision" camunda:decisionRef="decisionElement" camunda:mapDecisionResult="singleResult">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{ "taskHandler": "was"}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0zrvk3w</bpmn:incoming>
      <bpmn:outgoing>Flow_05n9zzu</bpmn:outgoing>
      <bpmn:outgoing>Flow_1ew03t0</bpmn:outgoing>
    </bpmn:businessRuleTask>
    <bpmn:receiveTask id="customWorkflowWaitEvent" name="Wait for state change" messageRef="Message_0hvjxp3">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="currentStepDetails">{ "required": false }</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
          <camunda:inputParameter name="targetApi">trigger</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_01bjpss</bpmn:incoming>
      <bpmn:outgoing>Flow_03rxzvl</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:subProcess id="Activity_1eq4x6t" triggeredByEvent="true">
      <bpmn:serviceTask id="Activity_0kcpk0l" name="Close Project service task" camunda:type="external" camunda:topic="workflow">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
              "taskHandler": "appconnect",
              "handlerId": "intuit-workflows/taskmanager-update-task",
              "actionName": "executeWorkflowAction"
              }</camunda:inputParameter>
            <camunda:inputParameter name="parameterDetails">{
  "projectId": {
    "fieldValue": [],
    "handlerFieldName": "Project",
    "requiredByHandler": true,
    "requiredByUI": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE"
  },
  "Status": {
    "fieldValue": [
      "Complete"
    ],
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string"
  }
}</camunda:inputParameter>
            <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_0twcurc</bpmn:incoming>
        <bpmn:outgoing>Flow_1s3y2z9</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:startEvent id="start_closeProject" name="Close task">
        <bpmn:outgoing>Flow_0twcurc</bpmn:outgoing>
        <bpmn:escalationEventDefinition id="EscalationEventDefinition_0pq4h1h" escalationRef="Escalation_00owd9n" />
      </bpmn:startEvent>
      <bpmn:endEvent id="Event_1v6ezlv" name="End process">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="handlerDetails">{
              "taskHandler": "was",
              "handlerId": "",
              "actionName": "updateProcessStatus"
              }</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_1s3y2z9</bpmn:incoming>
        <bpmn:messageEventDefinition id="MessageEventDefinition_1644u2c" messageRef="Message_04xqo3b" camunda:type="external" camunda:topic="bg-custom" />
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="Flow_0twcurc" sourceRef="start_closeProject" targetRef="Activity_0kcpk0l" />
      <bpmn:sequenceFlow id="Flow_1s3y2z9" sourceRef="Activity_0kcpk0l" targetRef="Event_1v6ezlv" />
    </bpmn:subProcess>
    <bpmn:inclusiveGateway id="approvalActions" name="approval actions">
      <bpmn:incoming>Flow_0pyokim</bpmn:incoming>
      <bpmn:incoming>Flow_1vb0zfp</bpmn:incoming>
      <bpmn:incoming>Flow_0t3cg7r</bpmn:incoming>
      <bpmn:incoming>Flow_18tv83r</bpmn:incoming>
      <bpmn:outgoing>Flow_01bjpss</bpmn:outgoing>
    </bpmn:inclusiveGateway>
    <bpmn:sendTask id="sendCompanyEmail" name="Send company email" camunda:type="external" camunda:topic="workflow">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
          <camunda:inputParameter name="handlerDetails">{}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1iho7gu</bpmn:incoming>
      <bpmn:outgoing>Flow_0pyokim</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:inclusiveGateway id="inclusiveGateway" name="Gateway">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1ew03t0</bpmn:incoming>
      <bpmn:outgoing>Flow_1iho7gu</bpmn:outgoing>
      <bpmn:outgoing>Flow_0t3cg7r</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ul5pyl</bpmn:outgoing>
      <bpmn:outgoing>Flow_0dusrsf</bpmn:outgoing>
    </bpmn:inclusiveGateway>
    <bpmn:boundaryEvent id="timerElapse_txnApproval" name="Expiry Event" attachedToRef="customWorkflowWaitEvent">
      <bpmn:outgoing>Flow_1jcy184</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_0t88qgy">
        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">P30D</bpmn:timeDuration>
      </bpmn:timerEventDefinition>
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_0zrvk3w" sourceRef="customStartEvent" targetRef="decisionElement" />
    <bpmn:sequenceFlow id="Flow_01bjpss" sourceRef="approvalActions" targetRef="customWorkflowWaitEvent" />
    <bpmn:sequenceFlow id="Flow_0pyokim" sourceRef="sendCompanyEmail" targetRef="approvalActions" />
    <bpmn:sequenceFlow id="Flow_1iho7gu" sourceRef="inclusiveGateway" targetRef="sendCompanyEmail">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.hasVariable("sendCompanyEmail") == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="autoApprove_Txn" name="Approve Txn" camunda:type="external" camunda:topic="workflow">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/was-update-invoice-status",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{
  "Id": {
    "fieldValue": [],
    "handlerFieldName": "txnId",
    "configurable": false,
    "actionByUI": null,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE"
  },
  "txnStatus": {
    "fieldValue": [
    "WORKFLOW_AUTO_APPROVED"
    ],
    "configurable": false,
    "actionByUI": null,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string"
  },
  "intuit_realmid": {
    "fieldValue": [],
    "handlerFieldName": "realmId",
    "configurable": false,
    "actionByUI": null,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE"
  }
}</camunda:inputParameter>
          <camunda:outputParameter name="outputResponse" />
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_05n9zzu</bpmn:incoming>
      <bpmn:outgoing>Flow_1ldl6yv</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_0mlq58t" name="Txn Auto Approved">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "handlerId": "",
                        "actionName": "updateProcessStatus"
                        }</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1ldl6yv</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1yd1rfc" messageRef="Message_0j828dx" camunda:type="external" camunda:topic="workflow" />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1ldl6yv" sourceRef="autoApprove_Txn" targetRef="Event_0mlq58t" />
    <bpmn:sequenceFlow id="Flow_05n9zzu" name="Rules not satisfied" sourceRef="decisionElement" targetRef="autoApprove_Txn">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decision.decisionResult == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0t3cg7r" sourceRef="inclusiveGateway" targetRef="approvalActions">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.hasVariable("sendPushNotification") == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="createTask" name="Create Project Service task" camunda:type="external" camunda:topic="workflow">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{}</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{ "required": true}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0ul5pyl</bpmn:incoming>
      <bpmn:outgoing>Flow_1vb0zfp</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="autoRejectTxn_txnApproval" name="Auto reject transaction" camunda:type="external" camunda:topic="workflow">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/was-update-invoice-status",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{
  "Id": {
    "fieldValue": [],
    "handlerFieldName": "txnId",
    "configurable": false,
    "actionByUI": null,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE"
  },
  "txnStatus": {
    "fieldValue": [
    "WORKFLOW_AUTO_REJECTED"
    ],
    "configurable": false,
    "actionByUI": null,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string"
  },
  "intuit_realmid": {
    "fieldValue": [],
    "handlerFieldName": "realmId",
    "configurable": false,
    "actionByUI": null,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType": "PROCESS_VARIABLE"
  }
}</camunda:inputParameter>
          <camunda:outputParameter name="outputResponse" />
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1jcy184</bpmn:incoming>
      <bpmn:outgoing>Flow_0uly9et</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="sendAutoRejectNotification_txnApproval" name="Send notification to creator of transaction" camunda:type="external" camunda:topic="workflow">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/was-send-notification",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{
"intuit_userid": {
    "handlerFieldName": "To",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType":"PROCESS_VARIABLE"
  },
  "Subject": {
    "fieldValue": [
      "~custom.approval.sendAutoRejectNotification.txnApproval.Subject"
    ],
    "configurable": true,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string"
  },
  "Message": {
    "fieldValue": [
      "~custom.approval.sendAutoRejectNotification.txnApproval.Message"
    ],
    "configurable": true,
    "requiredByHandler": true,
    "requiredByUI": true,
    "multiSelect": false,
    "fieldType": "string"
  },
  "Id": {
    "handlerFieldName": "txnLocalId",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType":"PROCESS_VARIABLE"
  },
  "intuit_realmid": {
    "handlerFieldName": "realmId",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType":"PROCESS_VARIABLE"
  },
  "Send email": {
    "fieldValue": [
      "true"
    ],
    "handlerFieldName": "isEmail",
    "configurable": true,
    "requiredByHandler": true,
    "requiredByUI": true,
    "multiSelect": false,
    "fieldType": "boolean"
  },
  "Send notification": {
    "fieldValue": [
      "true"
    ],
    "handlerFieldName": "isMobile",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": true,
    "multiSelect": false,
    "fieldType": "boolean"
  },
 "Workflow Type": {
    "fieldValue": [
      "TxnApproval"
    ],
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string"
  },
  "Mobile Notification Title": {
    "fieldValue": ["{{Txn}} [[DocNumber]] was auto-approved"],
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string"
  },
  "Mobile Notification Action": {
    "fieldValue": ["qb001://open/{{txn}}/?id=[[{{txn}}LocalId]]&amp;companyid=[[realmId]]"],
    "possibleFieldValues": [],
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string"
  },
  "Mobile Notification Subject": {
    "fieldValue": ["Now you can send it to your vendor"],
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string"
  }
}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{ "required": false }</camunda:inputParameter>
          <camunda:outputParameter name="outputResponse" />
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0uly9et</bpmn:incoming>
      <bpmn:outgoing>Flow_1suemrk</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_15jp7xs" name="Txn rejected because of time out">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1suemrk</bpmn:incoming>
      <bpmn:escalationEventDefinition id="EscalationEventDefinition_1ke515p" escalationRef="Escalation_0d2bkim" />
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="exclusiveGateway_txnApproval" name="approved?" default="sequenceRejected_txnApproval">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="handlerDetails" value="{ &#34;taskHandler&#34;: &#34;appconnect&#34;,                         &#34;handlerId&#34;: &#34;1234&#34;,                         &#34;handlerName&#34;: &#34;sendEmail&#34;                         }" />
          <camunda:property name="parameterDetails" value="{ &#34;{{txn}}.status&#34;: { &#34;fieldValue&#34; : [&#34;approved&#34;],  &#34;configurable&#34; : false,  &#34;actionByUI&#34; : null,  &#34;requiredByhandler&#34; : false,  &#34;requiredByUI&#34;: false,  &#34;multiSelect&#34;: false,  &#34;fieldType&#34;: &#34;string&#34;}}" />
          <camunda:property name="taskDetails" value="{&#34;required&#34;: true}" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_03rxzvl</bpmn:incoming>
      <bpmn:outgoing>sequenceApproved_txnApproval</bpmn:outgoing>
      <bpmn:outgoing>sequenceRejected_txnApproval</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="sendApproveNotification_txnApproval" name="Send notification to creator of transaction" camunda:type="external" camunda:topic="workflow">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/was-send-notification",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{
    "intuit_userid": {
        "handlerFieldName": "To",
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "Subject": {
        "fieldValue": [
            "~custom.approval.sendApproveNotification.txnApproval.Subject"
        ],
        "configurable": true,
        "actionByUI": null,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string"
    },
    "Message": {
        "fieldValue": [
            "~custom.approval.sendApproveNotification.txnApproval.Message"
        ],
        "configurable": true,
        "requiredByHandler": true,
        "requiredByUI": true,
        "multiSelect": false,
        "fieldType": "string"
    },
    "Id": {
        "handlerFieldName": "txnId",
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "intuit_realmid": {
        "handlerFieldName": "RealmId",
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "IsEmail": {
        "fieldValue": [
            "true"
        ],
        "configurable": true,
        "requiredByHandler": true,
        "requiredByUI": true,
        "multiSelect": false,
        "fieldType": "boolean"
    },
    "IsMobile": {
        "fieldValue": [
            "true"
        ],
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": true,
        "multiSelect": false,
        "fieldType": "boolean"
    },
    "Workflow Type": {
        "fieldValue": [
            "InvoiceApproval"
        ],
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string"
    },
    "NotificationAction": {
        "fieldValue": [
            "qb001://open/{{txn}}/?id=[[{{txn}}LocalId]]&amp;companyid=[[realmId]]"
        ],
        "configurable": false,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string"
    }
}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{ "required": false }</camunda:inputParameter>
          <camunda:outputParameter name="outputResponse" />
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>sequenceApproved_txnApproval</bpmn:incoming>
      <bpmn:outgoing>Flow_03h5end</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="sendRejectNotification_txnApproval" name="Send notification to creator of transaction" camunda:type="external" camunda:topic="workflow">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "appconnect",
                        "handlerId": "intuit-workflows/was-send-notification",
                        "actionName": "executeWorkflowAction"
                        }</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{
"intuit_userid": {
    "handlerFieldName": "To",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType":"PROCESS_VARIABLE"
  },
  "Subject": {
    "fieldValue": [
      "~custom.approval.sendRejectNotification.txnApproval.Subject"
    ],
    "configurable": true,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string"
  },
  "Message": {
    "fieldValue": [
      "~custom.approval.sendRejectNotification.txnApproval.Message"
    ],
    "configurable": true,
    "requiredByHandler": true,
    "requiredByUI": true,
    "multiSelect": false,
    "fieldType": "string"
  },
  "Id": {
    "handlerFieldName": "txnLocalId",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType":"PROCESS_VARIABLE"
  },
  "intuit_realmid": {
    "handlerFieldName": "realmId",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string",
    "valueType":"PROCESS_VARIABLE"
  },
  "Send email": {
    "fieldValue": [
      "true"
    ],
    "handlerFieldName": "isEmail",
    "configurable": true,
    "requiredByHandler": true,
    "requiredByUI": true,
    "multiSelect": false,
    "fieldType": "boolean"
  },
   "Send notification": {
    "fieldValue": [
      "true"
    ],
    "handlerFieldName": "isMobile",
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": true,
    "multiSelect": false,
    "fieldType": "boolean"
  },
 "Workflow Type": {
    "fieldValue": [
      "TxnApproval"
    ],
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string"
  },
  "Mobile Notification Title": {
    "fieldValue": ["{{Txn}} [[DocNumber]] wasn't approved"],
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string"
  },
  "Mobile Notification Action": {
    "fieldValue": ["qb001://open/{{txn}}/?id=[[{{txn}}LocalId]]&amp;companyid=[[realmId]]"],
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string"
  },
  "Mobile Notification Subject": {
    "fieldValue": ["You can edit it and request approval again."],
    "configurable": false,
    "requiredByHandler": true,
    "requiredByUI": false,
    "multiSelect": false,
    "fieldType": "string"
  }
}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{ "required": false }</camunda:inputParameter>
          <camunda:outputParameter name="outputResponse" />
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>sequenceRejected_txnApproval</bpmn:incoming>
      <bpmn:outgoing>Flow_0dr7p30</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_1a2394d" name="Transaction rejected">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0dr7p30</bpmn:incoming>
      <bpmn:escalationEventDefinition id="EscalationEventDefinition_0u4kh9z" escalationRef="Escalation_0ye464b" />
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_0nkpdiy" name="Transaction approved">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_03h5end</bpmn:incoming>
      <bpmn:escalationEventDefinition id="EscalationEventDefinition_00ppu84" escalationRef="Escalation_0rubrjz" />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_03rxzvl" sourceRef="customWorkflowWaitEvent" targetRef="exclusiveGateway_txnApproval" />
    <bpmn:sequenceFlow id="sequenceApproved_txnApproval" name="Yes" sourceRef="exclusiveGateway_txnApproval" targetRef="sendApproveNotification_txnApproval">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${entityChangeType == 'approved'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="sequenceRejected_txnApproval" name="No" sourceRef="exclusiveGateway_txnApproval" targetRef="sendRejectNotification_txnApproval" />
    <bpmn:sequenceFlow id="Flow_03h5end" sourceRef="sendApproveNotification_txnApproval" targetRef="Event_0nkpdiy" />
    <bpmn:sequenceFlow id="Flow_0dr7p30" sourceRef="sendRejectNotification_txnApproval" targetRef="Event_1a2394d" />
    <bpmn:sequenceFlow id="Flow_1jcy184" sourceRef="timerElapse_txnApproval" targetRef="autoRejectTxn_txnApproval" />
    <bpmn:sequenceFlow id="Flow_0uly9et" sourceRef="autoRejectTxn_txnApproval" targetRef="sendAutoRejectNotification_txnApproval" />
    <bpmn:sequenceFlow id="Flow_1suemrk" sourceRef="sendAutoRejectNotification_txnApproval" targetRef="Event_15jp7xs" />
    <bpmn:subProcess id="Activity_1u4eup2" name="deleted voided disable handler subprocess" triggeredByEvent="true">
      <bpmn:startEvent id="Event_0iambok" name="deleted voided disable txnapproval">
        <bpmn:outgoing>Flow_19we8wq</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_00zjz83" messageRef="Message_04yyvx2" />
      </bpmn:startEvent>
      <bpmn:endEvent id="Event_1pngpkg" name="Transaction deleted or voided or disable">
        <bpmn:extensionElements>
          <camunda:inputOutput>
            <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>
          </camunda:inputOutput>
        </bpmn:extensionElements>
        <bpmn:incoming>Flow_19we8wq</bpmn:incoming>
        <bpmn:escalationEventDefinition id="EscalationEventDefinition_0hjq3ny" escalationRef="Escalation_01zakh4" />
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="Flow_19we8wq" sourceRef="Event_0iambok" targetRef="Event_1pngpkg" />
    </bpmn:subProcess>
    <bpmn:sequenceFlow id="Flow_1vb0zfp" sourceRef="createTask" targetRef="approvalActions" />
    <bpmn:sequenceFlow id="Flow_1ew03t0" name="Rules  satisfied" sourceRef="decisionElement" targetRef="inclusiveGateway">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decision.decisionResult == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0ul5pyl" sourceRef="inclusiveGateway" targetRef="createTask">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sendTask id="sendPushNotification" name="Send push notification" camunda:type="external" camunda:topic="workflow">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="parameterDetails">{}</camunda:inputParameter>
          <camunda:inputParameter name="handlerDetails">{}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{ "required": true }</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0dusrsf</bpmn:incoming>
      <bpmn:outgoing>Flow_18tv83r</bpmn:outgoing>
    </bpmn:sendTask>
    <bpmn:sequenceFlow id="Flow_0dusrsf" sourceRef="inclusiveGateway" targetRef="sendPushNotification">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_18tv83r" sourceRef="sendPushNotification" targetRef="approvalActions" />
  </bpmn:process>
  <bpmn:message id="Message_0oflesh" name="customWait" />
  <bpmn:escalation id="Escalation_025wo89" name="close_task" escalationCode="closetask" />
  <bpmn:escalation id="Escalation_00owd9n" name="close_task" escalationCode="closetask" />
  <bpmn:message id="Message_04xqo3b" name="process_ended_message" />
  <bpmn:message id="Message_0j828dx" name="process_ended_message" />
  <bpmn:escalation id="Escalation_0d2bkim" name="close_task" escalationCode="closetask" />
  <bpmn:escalation id="Escalation_0ye464b" name="close_task" escalationCode="closetask" />
  <bpmn:escalation id="Escalation_0rubrjz" name="close_task" escalationCode="closetask" />
  <bpmn:message id="Message_04yyvx2" name="deleted_voided_disable" />
  <bpmn:escalation id="Escalation_01zakh4" name="close_task" escalationCode="closetask" />
  <bpmn:message id="Message_0hvjxp3" name="approved_rejected" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="customApproval">
      <bpmndi:BPMNEdge id="Flow_1ew03t0_di" bpmnElement="Flow_1ew03t0">
        <di:waypoint x="420" y="360" />
        <di:waypoint x="590" y="360" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="468" y="366" width="75" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vb0zfp_di" bpmnElement="Flow_1vb0zfp">
        <di:waypoint x="860" y="230" />
        <di:waypoint x="1000" y="230" />
        <di:waypoint x="1000" y="325" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1suemrk_di" bpmnElement="Flow_1suemrk">
        <di:waypoint x="1240" y="640" />
        <di:waypoint x="1302" y="640" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0uly9et_di" bpmnElement="Flow_0uly9et">
        <di:waypoint x="1190" y="550" />
        <di:waypoint x="1190" y="600" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jcy184_di" bpmnElement="Flow_1jcy184">
        <di:waypoint x="1190" y="408" />
        <di:waypoint x="1190" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dr7p30_di" bpmnElement="Flow_0dr7p30">
        <di:waypoint x="1370" y="80" />
        <di:waypoint x="1442" y="80" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03h5end_di" bpmnElement="Flow_03h5end">
        <di:waypoint x="1370" y="210" />
        <di:waypoint x="1442" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xfif4s_di" bpmnElement="sequenceRejected_txnApproval">
        <di:waypoint x="1190" y="185" />
        <di:waypoint x="1190" y="80" />
        <di:waypoint x="1270" y="80" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1198" y="130" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o3b2au_di" bpmnElement="sequenceApproved_txnApproval">
        <di:waypoint x="1215" y="210" />
        <di:waypoint x="1270" y="210" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1234" y="192" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03rxzvl_di" bpmnElement="Flow_03rxzvl">
        <di:waypoint x="1190" y="310" />
        <di:waypoint x="1190" y="235" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0t3cg7r_di" bpmnElement="Flow_0t3cg7r">
        <di:waypoint x="640" y="360" />
        <di:waypoint x="808" y="360" />
        <di:waypoint x="808" y="350" />
        <di:waypoint x="975" y="350" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05n9zzu_di" bpmnElement="Flow_05n9zzu">
        <di:waypoint x="370" y="320" />
        <di:waypoint x="370" y="270" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="275" y="297" width="90" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ldl6yv_di" bpmnElement="Flow_1ldl6yv">
        <di:waypoint x="370" y="190" />
        <di:waypoint x="370" y="128" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1iho7gu_di" bpmnElement="Flow_1iho7gu">
        <di:waypoint x="640" y="360" />
        <di:waypoint x="760" y="360" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="771.9999999999999" y="276" width="76" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pyokim_di" bpmnElement="Flow_0pyokim">
        <di:waypoint x="860" y="350" />
        <di:waypoint x="975" y="350" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01bjpss_di" bpmnElement="Flow_01bjpss">
        <di:waypoint x="1025" y="350" />
        <di:waypoint x="1140" y="350" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zrvk3w_di" bpmnElement="Flow_0zrvk3w">
        <di:waypoint x="228" y="360" />
        <di:waypoint x="320" y="360" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_0u1c60a_di" bpmnElement="customStartEvent">
        <dc:Bounds x="192" y="342" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="180" y="385" width="66" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0e0x7c8_di" bpmnElement="decisionElement">
        <dc:Bounds x="320" y="320" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0w63ezw_di" bpmnElement="customWorkflowWaitEvent">
        <dc:Bounds x="1140" y="310" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1eq4x6t_di" bpmnElement="Activity_1eq4x6t" isExpanded="true">
        <dc:Bounds x="290" y="530" width="350" height="200" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1s3y2z9_di" bpmnElement="Flow_1s3y2z9">
        <di:waypoint x="510" y="630" />
        <di:waypoint x="562" y="630" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0twcurc_di" bpmnElement="Flow_0twcurc">
        <di:waypoint x="366" y="630" />
        <di:waypoint x="410" y="630" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_0kcpk0l_di" bpmnElement="Activity_0kcpk0l">
        <dc:Bounds x="410" y="590" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0h9quic_di" bpmnElement="start_closeProject">
        <dc:Bounds x="330" y="612" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="322" y="655" width="52" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1v6ezlv_di" bpmnElement="Event_1v6ezlv">
        <dc:Bounds x="562" y="612" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="549" y="655" width="62" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0gv6vdr_di" bpmnElement="approvalActions">
        <dc:Bounds x="975" y="325" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1009" y="363" width="81" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ugfapy_di" bpmnElement="sendCompanyEmail">
        <dc:Bounds x="760" y="310" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0dlkaet_di" bpmnElement="inclusiveGateway">
        <dc:Bounds x="590" y="335" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="568" y="313" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_10hyjzr_di" bpmnElement="autoApprove_Txn">
        <dc:Bounds x="320" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0mlq58t_di" bpmnElement="Event_0mlq58t">
        <dc:Bounds x="352" y="92" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="346" y="54.5" width="48" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1u7acdq_di" bpmnElement="createTask">
        <dc:Bounds x="760" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_00imphd_di" bpmnElement="autoRejectTxn_txnApproval">
        <dc:Bounds x="1140" y="470" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0e18sgi_di" bpmnElement="sendAutoRejectNotification_txnApproval">
        <dc:Bounds x="1140" y="600" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_15jp7xs_di" bpmnElement="Event_15jp7xs">
        <dc:Bounds x="1302" y="622" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1281" y="665" width="78" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1u44vq2_di" bpmnElement="exclusiveGateway_txnApproval" isMarkerVisible="true">
        <dc:Bounds x="1165" y="185" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1124" y="222" width="52" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_16w49ba_di" bpmnElement="sendApproveNotification_txnApproval">
        <dc:Bounds x="1270" y="170" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1veg1jh_di" bpmnElement="sendRejectNotification_txnApproval">
        <dc:Bounds x="1270" y="40" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1a2394d_di" bpmnElement="Event_1a2394d">
        <dc:Bounds x="1442" y="62" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1491" y="66" width="58" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0nkpdiy_di" bpmnElement="Event_0nkpdiy">
        <dc:Bounds x="1442" y="192" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1491" y="196" width="58" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1u4eup2_di" bpmnElement="Activity_1u4eup2" isExpanded="true">
        <dc:Bounds x="290" y="790" width="350" height="200" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_19we8wq_di" bpmnElement="Flow_19we8wq">
        <di:waypoint x="388" y="880" />
        <di:waypoint x="512" y="880" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_0iambok_di" bpmnElement="Event_0iambok">
        <dc:Bounds x="352" y="862" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="338" y="905" width="72" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1pngpkg_di" bpmnElement="Event_1pngpkg">
        <dc:Bounds x="512" y="862" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="488" y="905" width="85" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_09vb67w_di" bpmnElement="timerElapse_txnApproval">
        <dc:Bounds x="1172" y="372" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1159" y="415" width="62" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0ul5pyl_di" bpmnElement="Flow_0ul5pyl">
        <di:waypoint x="615" y="335" />
        <di:waypoint x="615" y="230" />
        <di:waypoint x="760" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_1l1zd9h_di" bpmnElement="sendPushNotification">
        <dc:Bounds x="760" y="430" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0dusrsf_di" bpmnElement="Flow_0dusrsf">
        <di:waypoint x="615" y="385" />
        <di:waypoint x="615" y="470" />
        <di:waypoint x="760" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18tv83r_di" bpmnElement="Flow_18tv83r">
        <di:waypoint x="860" y="470" />
        <di:waypoint x="1000" y="470" />
        <di:waypoint x="1000" y="375" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
