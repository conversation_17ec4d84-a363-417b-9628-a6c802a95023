<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_1oo65x9" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.10.0">
  <bpmn:process id="customApproval_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" name="Multi Condition Approval" processType="None" isClosed="false" isExecutable="true" camunda:historyTimeToLive="7">
    <bpmn:startEvent id="customStartEvent_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" name="transaction custom event">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="stepDetails" value="{}" />
          <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
          <camunda:property name="currentStepDetails" value="{ &#34;required&#34;: true }" />
          <camunda:property name="processVariablesDetails" value="[{&#34;variableName&#34;:&#34;entityChangeType&#34;,&#34;variableType&#34;:&#34;String&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;entityType&#34;,&#34;variableType&#34;:&#34;String&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;TxnDate&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;CompanyEmail&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;TxnAmount&#34;,&#34;variableType&#34;:&#34;double&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;intuit_userid&#34;,&#34;variableType&#34;:&#34;String&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;TxnBalanceAmount&#34;,&#34;variableType&#34;:&#34;double&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;CompanyName&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;TxnDueDate&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;CustomerEmail&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;DocNumber&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;Id&#34;,&#34;variableType&#34;:&#34;String&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;CustomerName&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;intuit_realmid&#34;,&#34;variableType&#34;:&#34;String&#34;,&#34;overrideIfAbsent&#34;:true},{&#34;variableName&#34;:&#34;Location&#34;,&#34;variableType&#34;:&#34;string&#34;,&#34;overrideIfAbsent&#34;:true}]" />
          <camunda:property name="handlerDetails" value="{  &#34;taskHandler&#34;: &#34;was&#34;}" />
          <camunda:property name="startableEvents" value="[&#34;created&#34;,&#34;updated&#34;]" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_0zrvk3w_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0zrvk3w_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" sourceRef="customStartEvent_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" targetRef="decisionElement_1" />
    <bpmn:serviceTask id="autoApprove_Txn_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" name="Approve Txn" implementation="##WebService" camunda:type="external" camunda:topic="test-manujindal">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
    "taskHandler": "appconnect",
    "handlerId": "intuit-workflows/update-txn-status",
    "actionName": "executeWorkflowAction"
}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{
    "Id": {
        "fieldValue": [],
        "handlerFieldName": "txnId",
        "configurable": false,
        "actionByUI": null,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "txnStatus": {
        "fieldValue": [
            "AUTO_APPROVED"
        ],
        "configurable": false,
        "actionByUI": null,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string"
    },
    "intuit_realmid": {
        "fieldValue": [],
        "handlerFieldName": "realmId",
        "configurable": false,
        "actionByUI": null,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    }
}</camunda:inputParameter>
          <camunda:outputParameter name="outputResponse" />
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>de_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc</bpmn:incoming>
      <bpmn:outgoing>Flow_1ldl6yv_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_0mlq58t_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" name="Txn Auto Approved">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "handlerId": "",
                        "actionName": "updateProcessStatus"
                        }</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1ldl6yv_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1yd1rfc" messageRef="Message_0j828dx" camunda:type="external" camunda:topic="test-manujindal" />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1ldl6yv_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" sourceRef="autoApprove_Txn_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" targetRef="Event_0mlq58t_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" />
    <bpmn:sequenceFlow id="de_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" name="Rules not satisfied" sourceRef="decisionElement_1" targetRef="autoApprove_Txn_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == 'approval-0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:businessRuleTask id="decisionElement_1" name="Txn Rule Evaluation" implementation="##unspecified" camunda:type="external" camunda:topic="test-manujindal" camunda:decisionRef="decisionElement_1">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{ "taskHandler": "was","handlerId":"was_dmn_evaluator","actionName": "evaluateDMN"}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0zrvk3w_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc</bpmn:incoming>
      <bpmn:outgoing>de_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc</bpmn:outgoing>
      <bpmn:outgoing>Flow_1pyoasn_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc</bpmn:outgoing>
      <bpmn:outgoing>Flow_1bicpdx_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc</bpmn:outgoing>
      <bpmn:outgoing>Flow_141sxww_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc</bpmn:outgoing>
      <bpmn:outgoing>Flow_0egr0mp_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc</bpmn:outgoing>
      <bpmn:outgoing>Flow_0e142zu_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc</bpmn:outgoing>
      <bpmn:outgoing>Flow_0vi6h5v</bpmn:outgoing>
    </bpmn:businessRuleTask>
    <callActivity xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" id="action-21" name="sendForApproval" calledElement="sendForApproval">
      <extensionElements>
        <camunda:in source="" target="" businessKey="#{execution.processBusinessKey}" />
        <camunda:out source="" target="" variables="all" />
        <camunda:in source="" target="" variables="all" />
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
        </camunda:inputOutput>
      </extensionElements>
      <incoming>Flow_08m8vxc</incoming>
      <outgoing>Flow_02wijiy</outgoing>
    </callActivity>
    <callActivity xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" id="action-3" name="sendForApproval" calledElement="sendForApproval">
      <extensionElements>
        <camunda:in source="" target="" businessKey="#{execution.processBusinessKey}" />
        <camunda:out source="" target="" variables="all" />
        <camunda:in source="" target="" variables="all" />
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
        </camunda:inputOutput>
      </extensionElements>
      <incoming>Flow_1bicpdx_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc</incoming>
      <outgoing>Flow_1i9zlrj</outgoing>
    </callActivity>
    <callActivity xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" id="action-4_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" name="sendForApproval" calledElement="sendForApproval">
      <extensionElements>
        <camunda:in source="" target="" businessKey="#{execution.processBusinessKey}" />
        <camunda:out source="" target="" variables="all" />
        <camunda:in source="" target="" variables="all" />
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
        </camunda:inputOutput>
      </extensionElements>
      <incoming>Flow_141sxww_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc</incoming>
      <outgoing>Flow_1cy5j76</outgoing>
    </callActivity>
    <bpmn:sequenceFlow id="Flow_1pyoasn_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" name="action-2 == true" sourceRef="decisionElement_1" targetRef="action-2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == 'action-2'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1bicpdx_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" sourceRef="decisionElement_1" targetRef="action-3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == 'action-3'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_141sxww_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" sourceRef="decisionElement_1" targetRef="action-4_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == 'action-4'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:callActivity id="action-5" name="sendForApproval" calledElement="sendForApproval">
      <bpmn:extensionElements>
        <camunda:in source="" target="" businessKey="#{execution.processBusinessKey}" />
        <camunda:out source="" target="" variables="all" />
        <camunda:in source="" target="" variables="all" />
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0egr0mp_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc</bpmn:incoming>
      <bpmn:outgoing>Flow_01r6oe5</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:callActivity id="action-6_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" name="sendForApproval" calledElement="sendForApproval">
      <bpmn:extensionElements>
        <camunda:in source="" target="" businessKey="#{execution.processBusinessKey}" />
        <camunda:out source="" target="" variables="all" />
        <camunda:in source="" target="" variables="all" />
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0e142zu_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc</bpmn:incoming>
      <bpmn:outgoing>Flow_12p4z5c</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_0egr0mp_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" name="action4==true" sourceRef="decisionElement_1" targetRef="action-5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == 'action-5'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0e142zu_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" name="action5 == true" sourceRef="decisionElement_1" targetRef="action-6_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == 'action-6'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:callActivity id="act1234_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" name="sendForApproval" calledElement="sendForApproval">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0vi6h5v</bpmn:incoming>
      <bpmn:outgoing>Flow_08y0udf</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:subProcess id="Activity_1i6r6x3_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" name="deleted voided disable handler subprocess" triggeredByEvent="true">
      <bpmn:startEvent id="Event_030yjb4_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" name="deleted voided disable txnapproval">
        <bpmn:outgoing>Flow_11igzbv_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc</bpmn:outgoing>
        <bpmn:messageEventDefinition id="MessageEventDefinition_15nrjjc" messageRef="Message_07uu0db" />
      </bpmn:startEvent>
      <bpmn:endEvent id="Event_02v05w2_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc">
        <bpmn:incoming>Flow_11igzbv_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:sequenceFlow id="Flow_11igzbv_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" sourceRef="Event_030yjb4_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" targetRef="Event_02v05w2_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" />
    </bpmn:subProcess>
    <bpmn:serviceTask id="Activity_0qo1r2r" name="Approve Txn" implementation="##WebService" camunda:type="external" camunda:topic="test-manujindal">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
    "taskHandler": "appconnect",
    "handlerId": "intuit-workflows/update-txn-status",
    "actionName": "executeWorkflowAction"
}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
          <camunda:inputParameter name="parameterDetails">{
    "Id": {
        "fieldValue": [],
        "handlerFieldName": "txnId",
        "configurable": false,
        "actionByUI": null,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    },
    "txnStatus": {
        "fieldValue": [
            "AUTO_APPROVED"
        ],
        "configurable": false,
        "actionByUI": null,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string"
    },
    "intuit_realmid": {
        "fieldValue": [],
        "handlerFieldName": "realmId",
        "configurable": false,
        "actionByUI": null,
        "requiredByHandler": true,
        "requiredByUI": false,
        "multiSelect": false,
        "fieldType": "string",
        "valueType": "PROCESS_VARIABLE"
    }
}</camunda:inputParameter>
          <camunda:outputParameter name="outputResponse" />
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1ihs576</bpmn:incoming>
      <bpmn:outgoing>Flow_06blzvd</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_076bn3q" name="Txn Auto Approved">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="visibelToUI">false</camunda:inputParameter>
          <camunda:inputParameter name="handlerDetails">{
                        "taskHandler": "was",
                        "handlerId": "",
                        "actionName": "updateProcessStatus"
                        }</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_06blzvd</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0xvksks" messageRef="Message_0j828dx" camunda:type="external" camunda:topic="test-manujindal" />
    </bpmn:endEvent>
    <bpmn:businessRuleTask id="Activity_1rpwvvv" name="Txn Rule Evaluation" implementation="##unspecified" camunda:type="external" camunda:topic="test-manujindal" camunda:decisionRef="decisionElement_2">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{ "taskHandler": "was","handlerId":"was_dmn_evaluator","actionName": "evaluateDMN"}</camunda:inputParameter>
          <camunda:inputParameter name="taskDetails">{"required": true}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_02wijiy</bpmn:incoming>
      <bpmn:incoming>Flow_01r6oe5</bpmn:incoming>
      <bpmn:incoming>Flow_12p4z5c</bpmn:incoming>
      <bpmn:incoming>Flow_0wrk1o2</bpmn:incoming>
      <bpmn:incoming>Flow_1cy5j76</bpmn:incoming>
      <bpmn:incoming>Flow_19ts6qb</bpmn:incoming>
      <bpmn:outgoing>Flow_1ihs576</bpmn:outgoing>
      <bpmn:outgoing>Flow_0e3xxv8</bpmn:outgoing>
      <bpmn:outgoing>Flow_0gn0dd2</bpmn:outgoing>
      <bpmn:outgoing>Flow_1t7ivkq</bpmn:outgoing>
      <bpmn:outgoing>Flow_1dh10or</bpmn:outgoing>
      <bpmn:outgoing>Flow_02utk5p</bpmn:outgoing>
      <bpmn:outgoing>Flow_1gmhdi0</bpmn:outgoing>
    </bpmn:businessRuleTask>
    <bpmn:callActivity id="Activity_0s4qknc" name="sendForApproval" calledElement="sendForApproval">
      <bpmn:extensionElements>
        <camunda:in source="" target="" businessKey="#{execution.processBusinessKey}" />
        <camunda:out source="" target="" variables="all" />
        <camunda:in source="" target="" variables="all" />
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0e3xxv8</bpmn:incoming>
      <bpmn:outgoing>Flow_1tc1j2b</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:callActivity id="Activity_0dx80oq" name="sendForApproval" calledElement="sendForApproval">
      <bpmn:extensionElements>
        <camunda:in source="" target="" businessKey="#{execution.processBusinessKey}" />
        <camunda:out source="" target="" variables="all" />
        <camunda:in source="" target="" variables="all" />
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0gn0dd2</bpmn:incoming>
      <bpmn:outgoing>Flow_1qdyf5h</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:callActivity id="Activity_076zwlf" name="sendForApproval" calledElement="sendForApproval">
      <bpmn:extensionElements>
        <camunda:in source="" target="" businessKey="#{execution.processBusinessKey}" />
        <camunda:out source="" target="" variables="all" />
        <camunda:in source="" target="" variables="all" />
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1t7ivkq</bpmn:incoming>
      <bpmn:outgoing>Flow_143kjh5</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:callActivity id="Activity_0ebo8c0" name="sendForApproval" calledElement="sendForApproval">
      <bpmn:extensionElements>
        <camunda:in source="" target="" businessKey="#{execution.processBusinessKey}" />
        <camunda:out source="" target="" variables="all" />
        <camunda:in source="" target="" variables="all" />
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1dh10or</bpmn:incoming>
      <bpmn:outgoing>Flow_07859b9</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:callActivity id="Activity_195yifg" name="sendForApproval" calledElement="sendForApproval">
      <bpmn:extensionElements>
        <camunda:in source="" target="" businessKey="#{execution.processBusinessKey}" />
        <camunda:out source="" target="" variables="all" />
        <camunda:in source="" target="" variables="all" />
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_02utk5p</bpmn:incoming>
      <bpmn:outgoing>Flow_0b5o2yt</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:callActivity id="Activity_0ib3wl9" name="sendForApproval" calledElement="sendForApproval">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1gmhdi0</bpmn:incoming>
      <bpmn:outgoing>Flow_16aflxp</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_1ihs576" name="Rules not satisfied" sourceRef="Activity_1rpwvvv" targetRef="Activity_0qo1r2r">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == 'approval-0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_06blzvd" sourceRef="Activity_0qo1r2r" targetRef="Event_076bn3q" />
    <bpmn:sequenceFlow id="Flow_0e3xxv8" name="action-2 == true" sourceRef="Activity_1rpwvvv" targetRef="Activity_0s4qknc">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == 'action-2'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0gn0dd2" sourceRef="Activity_1rpwvvv" targetRef="Activity_0dx80oq">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == 'action-3'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1t7ivkq" sourceRef="Activity_1rpwvvv" targetRef="Activity_076zwlf">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == 'action-4'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1dh10or" name="action4==true" sourceRef="Activity_1rpwvvv" targetRef="Activity_0ebo8c0">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == 'action-5'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_02utk5p" name="action5 == true" sourceRef="Activity_1rpwvvv" targetRef="Activity_195yifg">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == 'action-6'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1gmhdi0" sourceRef="Activity_1rpwvvv" targetRef="Activity_0ib3wl9">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == 'act1234'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_1nboce3" name="End Process">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="handlerDetails">{
            "taskHandler": "was",
            "handlerId": "",
            "actionName": "updateProcessStatus"
            }</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1tc1j2b</bpmn:incoming>
      <bpmn:incoming>Flow_143kjh5</bpmn:incoming>
      <bpmn:incoming>Flow_07859b9</bpmn:incoming>
      <bpmn:incoming>Flow_0b5o2yt</bpmn:incoming>
      <bpmn:incoming>Flow_16aflxp</bpmn:incoming>
      <bpmn:incoming>Flow_1qdyf5h</bpmn:incoming>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0mgj9nv" messageRef="Message_1r625qy" camunda:type="external" camunda:topic="test-manujindal" />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1i9zlrj" sourceRef="action-3" targetRef="Activity_0n6p9te" />
    <bpmn:sequenceFlow id="Flow_02wijiy" sourceRef="action-21" targetRef="Activity_1rpwvvv" />
    <bpmn:sequenceFlow id="Flow_1cy5j76" sourceRef="action-4_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" targetRef="Activity_1rpwvvv" />
    <bpmn:sequenceFlow id="Flow_01r6oe5" sourceRef="action-5" targetRef="Activity_1rpwvvv" />
    <bpmn:sequenceFlow id="Flow_12p4z5c" sourceRef="action-6_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" targetRef="Activity_1rpwvvv" />
    <bpmn:sequenceFlow id="Flow_08y0udf" sourceRef="act1234_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" targetRef="Gateway_1dizfhd" />
    <bpmn:sequenceFlow id="Flow_1qdyf5h" sourceRef="Activity_0dx80oq" targetRef="Event_1nboce3" />
    <bpmn:sequenceFlow id="Flow_1tc1j2b" sourceRef="Activity_0s4qknc" targetRef="Event_1nboce3" />
    <bpmn:sequenceFlow id="Flow_143kjh5" sourceRef="Activity_076zwlf" targetRef="Event_1nboce3" />
    <bpmn:sequenceFlow id="Flow_07859b9" sourceRef="Activity_0ebo8c0" targetRef="Event_1nboce3" />
    <bpmn:sequenceFlow id="Flow_0b5o2yt" sourceRef="Activity_195yifg" targetRef="Event_1nboce3" />
    <bpmn:sequenceFlow id="Flow_16aflxp" sourceRef="Activity_0ib3wl9" targetRef="Event_1nboce3" />
    <bpmn:sequenceFlow id="Flow_0vi6h5v" sourceRef="decisionElement_1" targetRef="act1234_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${decisionResult == 'action-1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:callActivity id="Activity_0n6p9te" name="sendForApproval" calledElement="sendForApproval">
      <bpmn:extensionElements>
        <camunda:in source="" target="" businessKey="#{execution.processBusinessKey}" />
        <camunda:out source="" target="" variables="all" />
        <camunda:in source="" target="" variables="all" />
        <camunda:inputOutput>
          <camunda:inputParameter name="activityId">${decisionResult}</camunda:inputParameter>
          <camunda:inputParameter name="rootProcessInstanceId">#{execution.processInstanceId}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1i9zlrj</bpmn:incoming>
      <bpmn:outgoing>Flow_0wrk1o2</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_0wrk1o2" sourceRef="Activity_0n6p9te" targetRef="Activity_1rpwvvv" />
    <bpmn:inclusiveGateway id="action-2" name="Gateway">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1pyoasn_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc</bpmn:incoming>
      <bpmn:outgoing>Flow_08m8vxc</bpmn:outgoing>
    </bpmn:inclusiveGateway>
    <bpmn:sequenceFlow id="Flow_08m8vxc" sourceRef="action-2" targetRef="action-21" />
    <bpmn:inclusiveGateway id="Gateway_1dizfhd" name="Gateway">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="taskDetails" value="{ &#34;required&#34;: true }" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_08y0udf</bpmn:incoming>
      <bpmn:outgoing>Flow_19ts6qb</bpmn:outgoing>
    </bpmn:inclusiveGateway>
    <bpmn:sequenceFlow id="Flow_19ts6qb" sourceRef="Gateway_1dizfhd" targetRef="Activity_1rpwvvv" />
  </bpmn:process>
  <bpmn:message id="Message_0oflesh" name="customWait" />
  <bpmn:escalation id="Escalation_025wo89" name="close_task" escalationCode="closetask" />
  <bpmn:escalation id="Escalation_00owd9n" name="close_task" escalationCode="closetask" />
  <bpmn:message id="Message_04xqo3b" name="process_ended_message" />
  <bpmn:message id="Message_0j828dx" name="deleted_voided_disable" />
  <bpmn:escalation id="Escalation_0d2bkim" name="close_task" escalationCode="closetask" />
  <bpmn:escalation id="Escalation_0ye464b" name="close_task" escalationCode="closetask" />
  <bpmn:escalation id="Escalation_0rubrjz" name="close_task" escalationCode="closetask" />
  <bpmn:message id="Message_04yyvx2" name="deleted_voided_disable" />
  <bpmn:escalation id="Escalation_01zakh4" name="close_task" escalationCode="closetask" />
  <bpmn:message id="Message_0hvjxp3" name="approved_rejected" />
  <bpmn:message id="Message_1r625qy" name="process_ended_message" />
  <bpmn:message id="Message_0tj20j4" name="custom_deleted" />
  <bpmn:escalation id="Escalation_1uy1le9" name="close_task" escalationCode="closetask" />
  <bpmn:message id="Message_059dc2w" name="deleted_voided_disable" />
  <bpmn:escalation id="Escalation_0qater7" name="close_task" escalationCode="closetask" />
  <bpmn:escalation id="Escalation_0jgkmbs" name="sample_message" escalationCode="sample_message" />
  <bpmn:message id="Message_17j8xee" name="samle" />
  <bpmn:message id="Message_07uu0db" name="deleted_voided_disable" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="customApproval_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc">
      <bpmndi:BPMNEdge id="Flow_08m8vxc_di" bpmnElement="Flow_08m8vxc">
        <di:waypoint x="805" y="300" />
        <di:waypoint x="840" y="300" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wrk1o2_di" bpmnElement="Flow_0wrk1o2">
        <di:waypoint x="1020" y="420" />
        <di:waypoint x="1180" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vi6h5v_di" bpmnElement="Flow_0vi6h5v">
        <di:waypoint x="430" y="410" />
        <di:waypoint x="635" y="410" />
        <di:waypoint x="635" y="180" />
        <di:waypoint x="840" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16aflxp_di" bpmnElement="Flow_16aflxp">
        <di:waypoint x="1790" y="190" />
        <di:waypoint x="1891" y="190" />
        <di:waypoint x="1891" y="410" />
        <di:waypoint x="1992" y="410" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0b5o2yt_di" bpmnElement="Flow_0b5o2yt">
        <di:waypoint x="1790" y="720" />
        <di:waypoint x="1891" y="720" />
        <di:waypoint x="1891" y="410" />
        <di:waypoint x="1992" y="410" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07859b9_di" bpmnElement="Flow_07859b9">
        <di:waypoint x="1790" y="610" />
        <di:waypoint x="1891" y="610" />
        <di:waypoint x="1891" y="410" />
        <di:waypoint x="1992" y="410" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_143kjh5_di" bpmnElement="Flow_143kjh5">
        <di:waypoint x="1790" y="510" />
        <di:waypoint x="1891" y="510" />
        <di:waypoint x="1891" y="410" />
        <di:waypoint x="1992" y="410" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1tc1j2b_di" bpmnElement="Flow_1tc1j2b">
        <di:waypoint x="1790" y="290" />
        <di:waypoint x="1891" y="290" />
        <di:waypoint x="1891" y="410" />
        <di:waypoint x="1992" y="410" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qdyf5h_di" bpmnElement="Flow_1qdyf5h">
        <di:waypoint x="1760" y="410" />
        <di:waypoint x="1992" y="410" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08y0udf_di" bpmnElement="Flow_08y0udf">
        <di:waypoint x="940" y="190" />
        <di:waypoint x="1040" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12p4z5c_di" bpmnElement="Flow_12p4z5c">
        <di:waypoint x="940" y="720" />
        <di:waypoint x="1065" y="720" />
        <di:waypoint x="1065" y="420" />
        <di:waypoint x="1180" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01r6oe5_di" bpmnElement="Flow_01r6oe5">
        <di:waypoint x="940" y="610" />
        <di:waypoint x="1065" y="610" />
        <di:waypoint x="1065" y="420" />
        <di:waypoint x="1180" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1cy5j76_di" bpmnElement="Flow_1cy5j76">
        <di:waypoint x="940" y="510" />
        <di:waypoint x="1065" y="510" />
        <di:waypoint x="1065" y="420" />
        <di:waypoint x="1180" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02wijiy_di" bpmnElement="Flow_02wijiy">
        <di:waypoint x="940" y="290" />
        <di:waypoint x="1065" y="290" />
        <di:waypoint x="1065" y="420" />
        <di:waypoint x="1180" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1i9zlrj_di" bpmnElement="Flow_1i9zlrj">
        <di:waypoint x="880" y="420" />
        <di:waypoint x="920" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gmhdi0_di" bpmnElement="Flow_1gmhdi0">
        <di:waypoint x="1280" y="420" />
        <di:waypoint x="1485" y="420" />
        <di:waypoint x="1485" y="200" />
        <di:waypoint x="1690" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02utk5p_di" bpmnElement="Flow_02utk5p">
        <di:waypoint x="1280" y="420" />
        <di:waypoint x="1575" y="420" />
        <di:waypoint x="1575" y="720" />
        <di:waypoint x="1690" y="720" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1553" y="565" width="74" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1dh10or_di" bpmnElement="Flow_1dh10or">
        <di:waypoint x="1280" y="420" />
        <di:waypoint x="1575" y="420" />
        <di:waypoint x="1575" y="630" />
        <di:waypoint x="1690" y="630" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1556" y="523" width="68" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1t7ivkq_di" bpmnElement="Flow_1t7ivkq">
        <di:waypoint x="1280" y="420" />
        <di:waypoint x="1575" y="420" />
        <di:waypoint x="1575" y="530" />
        <di:waypoint x="1690" y="530" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0gn0dd2_di" bpmnElement="Flow_0gn0dd2">
        <di:waypoint x="1280" y="420" />
        <di:waypoint x="1660" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0e3xxv8_di" bpmnElement="Flow_0e3xxv8">
        <di:waypoint x="1280" y="420" />
        <di:waypoint x="1575" y="420" />
        <di:waypoint x="1575" y="300" />
        <di:waypoint x="1690" y="300" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1551" y="360" width="78" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06blzvd_di" bpmnElement="Flow_06blzvd">
        <di:waypoint x="1230" y="210" />
        <di:waypoint x="1230" y="148" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ihs576_di" bpmnElement="Flow_1ihs576">
        <di:waypoint x="1230" y="380" />
        <di:waypoint x="1230" y="290" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1173" y="483" width="90" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0e142zu_di" bpmnElement="Flow_0e142zu_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc">
        <di:waypoint x="430" y="410" />
        <di:waypoint x="725" y="410" />
        <di:waypoint x="725" y="720" />
        <di:waypoint x="840" y="720" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="703" y="561" width="74" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0egr0mp_di" bpmnElement="Flow_0egr0mp_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc">
        <di:waypoint x="430" y="410" />
        <di:waypoint x="725" y="410" />
        <di:waypoint x="725" y="630" />
        <di:waypoint x="840" y="630" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="706" y="516" width="68" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_141sxww_di" bpmnElement="Flow_141sxww_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc">
        <di:waypoint x="430" y="410" />
        <di:waypoint x="725" y="410" />
        <di:waypoint x="725" y="530" />
        <di:waypoint x="840" y="530" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bicpdx_di" bpmnElement="Flow_1bicpdx_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc">
        <di:waypoint x="430" y="410" />
        <di:waypoint x="725" y="410" />
        <di:waypoint x="725" y="430" />
        <di:waypoint x="780" y="430" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pyoasn_di" bpmnElement="Flow_1pyoasn_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc">
        <di:waypoint x="430" y="410" />
        <di:waypoint x="720" y="410" />
        <di:waypoint x="720" y="300" />
        <di:waypoint x="755" y="300" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="661" y="340" width="78" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05n9zzu_di" bpmnElement="de_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc">
        <di:waypoint x="380" y="370" />
        <di:waypoint x="380" y="290" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="285" y="358" width="90" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ldl6yv_di" bpmnElement="Flow_1ldl6yv_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc">
        <di:waypoint x="380" y="210" />
        <di:waypoint x="380" y="148" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zrvk3w_di" bpmnElement="Flow_0zrvk3w_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc">
        <di:waypoint x="208" y="410" />
        <di:waypoint x="330" y="410" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19ts6qb_di" bpmnElement="Flow_19ts6qb">
        <di:waypoint x="1065" y="215" />
        <di:waypoint x="1065" y="420" />
        <di:waypoint x="1180" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_0u1c60a_di" bpmnElement="customStartEvent_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc">
        <dc:Bounds x="172" y="392" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="160" y="435" width="66" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_10hyjzr_di" bpmnElement="autoApprove_Txn_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc">
        <dc:Bounds x="330" y="210" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0mlq58t_di" bpmnElement="Event_0mlq58t_9130358689478276_66cd2fba-ef97-4703-8b4f-698fb10d8585_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc">
        <dc:Bounds x="362" y="112" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="356" y="75" width="48" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0e0x7c8_di" bpmnElement="decisionElement_1">
        <dc:Bounds x="330" y="370" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_96fd5293-6563-4fb4-8292-4fe66be1854f" bpmnElement="action-21">
        <dc:Bounds x="840" y="250" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_e9ed1eb7-315e-4613-adc7-47eea2f71e97" bpmnElement="action-3">
        <dc:Bounds x="780" y="380" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0f2573d3-7d39-4791-87f4-213d518a3bfc" bpmnElement="action-4_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc">
        <dc:Bounds x="840" y="470" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_11dojvn" bpmnElement="action-5">
        <dc:Bounds x="840" y="570" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0l00ohy" bpmnElement="action-6_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc">
        <dc:Bounds x="840" y="680" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_11phofd_di" bpmnElement="act1234_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc">
        <dc:Bounds x="840" y="150" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1dizfhd_di" bpmnElement="Gateway_1dizfhd">
        <dc:Bounds x="1040" y="165" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1018" y="143" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1i6r6x3_di" bpmnElement="Activity_1i6r6x3_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc" isExpanded="true">
        <dc:Bounds x="160" y="620" width="350" height="200" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_11igzbv_di" bpmnElement="Flow_11igzbv_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc">
        <di:waypoint x="258" y="710" />
        <di:waypoint x="412" y="710" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_030yjb4_di" bpmnElement="Event_030yjb4_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc">
        <dc:Bounds x="222" y="692" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="208" y="735" width="72" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_02v05w2_di" bpmnElement="Event_02v05w2_9130356803092296_887a74d0-61e0-4e6a-bf9f-1d8a7d5a69fc">
        <dc:Bounds x="412" y="692" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0qo1r2r_di" bpmnElement="Activity_0qo1r2r">
        <dc:Bounds x="1180" y="210" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_076bn3q_di" bpmnElement="Event_076bn3q">
        <dc:Bounds x="1212" y="112" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1206" y="75" width="48" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1rpwvvv_di" bpmnElement="Activity_1rpwvvv">
        <dc:Bounds x="1180" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0s4qknc_di" bpmnElement="Activity_0s4qknc">
        <dc:Bounds x="1690" y="250" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0dx80oq_di" bpmnElement="Activity_0dx80oq">
        <dc:Bounds x="1660" y="370" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_076zwlf_di" bpmnElement="Activity_076zwlf">
        <dc:Bounds x="1690" y="470" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ebo8c0_di" bpmnElement="Activity_0ebo8c0">
        <dc:Bounds x="1690" y="570" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_195yifg_di" bpmnElement="Activity_195yifg">
        <dc:Bounds x="1690" y="680" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ib3wl9_di" bpmnElement="Activity_0ib3wl9">
        <dc:Bounds x="1690" y="150" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1nboce3_di" bpmnElement="Event_1nboce3">
        <dc:Bounds x="1992" y="392" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1979" y="435" width="63" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0n6p9te_di" bpmnElement="Activity_0n6p9te">
        <dc:Bounds x="920" y="380" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0biddy6_di" bpmnElement="action-2">
        <dc:Bounds x="755" y="275" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="733" y="253" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
