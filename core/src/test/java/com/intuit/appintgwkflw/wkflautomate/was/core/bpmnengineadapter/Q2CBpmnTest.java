package com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit4.SpringRunner;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.app.providers.helper.ProviderHelper;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.DomainEventConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.MultiStepConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowGlobalConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.ClientAccessConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.TemplateSaveUpdateConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.MultiStepTemplateBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ReadCustomDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.TemplateLabelsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.AuthDetailsServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.TemplateServiceImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.processor.SingleDefinitionProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.processor.SystemDefinitionProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.processor.UserDefinitionProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.validator.TemplateValidator;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.TemplateDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.impl.BpmnProcessorImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.workflowAI.services.WFAIService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TriggerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CreatorType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.VariabilityEngineService;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.Trigger;
import com.intuit.v4.workflows.WorkflowStep;

@Import(TemplateServiceImpl.class)
@RunWith(SpringRunner.class)
public class Q2CBpmnTest {

    private static final String Q2C_BPMN = "bpmn/Q2c_testCase.bpmn";
    private static final String FILE_Q2C_BPMN = "src/test/resources/bpmn/Q2c_testCase.bpmn";
    private static final String Q2C_BPMN_FAILURE = "bpmn/Q2C_Failure.bpmn";
    private static final String FILE_Q2C_BPMN_FAILURE = "src/test/resources/bpmn/Q2C_Failure.bpmn";
    private static final String OFFER_ID = "offerId";
    private static final String OWNER_ID = "1234";
    private static final String CREATED_BY = "1234";
    private static final String REALM_ID = "realmId";
    private static final String LOCAL_ID = "localId";
    @MockBean
    private AppConfig appConfig;
    @MockBean
    private WorkflowGlobalConfiguration workflowGlobalConfiguration;
    @MockBean
    private ReadCustomDefinitionHandler readCustomDefinitionHandler;
    @MockBean
    private TemplateLabelsService templateLabelsService;
    @MockBean
    private TemplateDomainEventHandler templateDomainEventHandler;
    @MockBean(name = WorkflowBeansConstants.TEMPLATE_BUILDER)
    private TemplateBuilder templateBuilder;
    @MockBean(name = WorkflowBeansConstants.MULTI_STEP_TEMPLATE_BUILDER)
    private MultiStepTemplateBuilder multiStepTemplateBuilder;
    @MockBean
    private WASContextHandler contextHandler;
    @MockBean
    private TemplateDetailsRepository templateDetailsRepository;
    @MockBean
    private TriggerDetailsRepository triggerDetailsRepository;
    @MockBean
    private ProviderHelper providerHelper;
    @MockBean
    private AuthDetailsService authDetailsService;
    @MockBean
    private AuthDetailsServiceHelper authDetailsServiceHelper;
    @MockBean
    private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;
    @MockBean
    private SingleDefinitionProcessor singleDefinitionProcessor;
    @MockBean
    private SystemDefinitionProcessor systemDefinitionProcessor;
    @MockBean
    private UserDefinitionProcessor userDefinitionProcessor;
    @MockBean
    private TemplateValidator templateValidator;
    @MockBean
    private WorkflowTaskConfig workflowTaskConfig;
    @MockBean
    private DomainEventConfig domainEventTopiConfig;
    @MockBean
    private FeatureFlagManager featureFlagManager;
    @MockBean
    private ClientAccessConfig clientAccessConfig;
    @MockBean
    private TranslationService translationService;
    @MockBean
    private CustomWorkflowConfig customWorkflowConfig;
    @MockBean
    private MultiStepConfig multiStepConfig;
    @MockBean
    private WFAIService wfaiService;
    @MockBean
    private VariabilityEngineService variabilityEngineService;
    @MockBean
    private ActivityDetailsRepository activityDetailsRepository;
    @Autowired
    private BpmnProcessorImpl bpmnProcessor;


    /**
     * @param fileName
     * @return : BPMN Model Instance
     */
    private static BpmnModelInstance readBPMNFile(String fileName) {
        return Bpmn.readModelFromStream(
                Q2CBpmnTest.class.getClassLoader().getResourceAsStream(fileName));
    }

    @Before
    public void setup() {
        Mockito.when(templateBuilder.buildTemplateDetails(Mockito.any(), Mockito.any())).thenReturn(new Template());
    }

    @Test
    public void fetchTemplateQ2C() throws IOException {

        TemplateDetails bpmnDetails = buildTemplateDetails(new File(FILE_Q2C_BPMN));

        Mockito.doReturn(Optional.of(bpmnDetails))
                .when(templateDetailsRepository)
                .findTopByIdOrderByVersionDesc(Mockito.any());

        BpmnModelInstance bpmnModelInstance = Q2CBpmnTest.readBPMNFile(Q2C_BPMN);
        Assert.assertNotNull(bpmnModelInstance);
        Mockito.when(workflowTaskConfig.isEnable()).thenReturn(true);
        Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
        Mockito.when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());
        DefinitionInstance definitionInstance = CustomWorkflowUtil.generateDefinitionInstanceForBpmnProcessing(
                bpmnModelInstance,
                null,
                bpmnDetails);

        Template template =
                (Template)
                        bpmnProcessor.processBpmn(
                                definitionInstance,
                                GlobalId.create(REALM_ID, LOCAL_ID),
                                false);

        Assert.assertNotNull(template);
        Assert.assertEquals(3, template.getWorkflowSteps().size());

        /** Testing mandatory parameters are set for the WorkflowStep */
        for (WorkflowStep workflowStep : template.getWorkflowSteps()) {
            Assert.assertNotNull(workflowStep.getId());
            Assert.assertNotNull(workflowStep.getName());
            if (null != workflowStep.getTrigger()) {
                Trigger trigger = workflowStep.getTrigger();
                Assert.assertNotNull(trigger.getId());
                Assert.assertNotNull(trigger.getName());
            }

            if (null != workflowStep.getActions()) {
                List<WorkflowStep.ActionMapper> actions = workflowStep.getActions();
                Assert.assertNotNull(actions);
                actions.forEach(
                        action -> {
                            Assert.assertNotNull(action.getAction());
                            Assert.assertNotNull(action.getAction().getId());
                            Assert.assertNotNull(action.getAction().getName());
                        });
            }
        }
    }

    /**
     * Introduced Empty Camunda Property to test if it fails with valid null pointer exception.
     * Ideally no such properties should be allowed there.
     *
     * @throws IOException
     */
    @Test(expected = NullPointerException.class)
    public void fetchTemplateQ2C_Failure() throws IOException {

        TemplateDetails bpmnDetails = buildTemplateDetails(new File(FILE_Q2C_BPMN_FAILURE));

        Mockito.doReturn(Optional.of(bpmnDetails))
                .when(templateDetailsRepository)
                .findTopByIdOrderByVersionDesc(Mockito.any());

        Mockito.when(workflowTaskConfig.isEnable()).thenReturn(true);
        Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
        Mockito.when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        BpmnModelInstance bpmnModelInstance = Q2CBpmnTest.readBPMNFile(Q2C_BPMN_FAILURE);
        Assert.assertNotNull(bpmnModelInstance);

        /**
         * * Here testing if user is setting null Camunda property. While validating the bpmn it should
         * throw error.
         */
        DefinitionInstance definitionInstance = CustomWorkflowUtil.generateDefinitionInstanceForBpmnProcessing(
                bpmnModelInstance,
                null,
                bpmnDetails);
        bpmnProcessor.processBpmn(
                definitionInstance,
                GlobalId.create(REALM_ID, LOCAL_ID),
                false);
    }

    private TemplateDetails buildTemplateDetails(File bpmnFile) throws IOException {

        FileInputStream fisBpmn = new FileInputStream(bpmnFile);

        return
                TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .status(Status.ENABLED)
                        .recordType(RecordType.INVOICE)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .build();
    }

    @TestConfiguration
    static class BpmnProcessorImplContextConfiguration {

        @Bean
        public BpmnProcessorImpl bpmnProcessor() {
            return new BpmnProcessorImpl();
        }
    }

}
