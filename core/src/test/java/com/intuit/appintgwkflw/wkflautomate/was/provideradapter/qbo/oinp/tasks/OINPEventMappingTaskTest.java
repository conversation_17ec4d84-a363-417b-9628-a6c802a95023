package com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.oinpqbo.bridge.NotificationGroup;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.config.OINPNotificationConfigMap;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants.OinpBridgeConstants;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class OINPEventMappingTaskTest {

  @Mock
  private OINPNotificationConfigMap oinpNotificationConfigMap;

  @Before
  public void init(){
    MockitoAnnotations.openMocks(this);
  }

  @Test
  public void testEventMapping_mobile(){
    NotificationGroup notificationGroup = new NotificationGroup();
    notificationGroup.setNotificationName("name");
    notificationGroup.setNotificationDataType("data");
    Map<String, List<String>> map =new HashMap<>(){{
      put("a", Collections.singletonList("aa"));
      put("b", Collections.singletonList("bb"));
    }};
    notificationGroup.setMappingAttributes(map);
    Mockito.when(oinpNotificationConfigMap.getNotificationGroup(OinpBridgeConstants.IS_MOBILE))
        .thenReturn(notificationGroup);
    State state = new State();
    state.addValue(OinpBridgeConstants.IS_MOBILE, true);
    state.addValue(OinpBridgeConstants.IS_EMAIL, false);
    state.addValue(OinpBridgeConstants.CONSOLIDATE_NOTIFICATIONS, false);
    state.addValue(OinpBridgeConstants.TEMPLATE_NAME, "billReminder");

    Map<String, String> output =new HashMap<>(){{
      put("a", "12");
      put("b", "13");
      put("c", "14");
    }};
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP, output);

    state = new RxExecutionChain(state)
        .next(new OINPEventMappingTask(oinpNotificationConfigMap))
        .execute();

    Map<String, String> assertMap = state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP);

    Assert.assertEquals("12", assertMap.get("aa"));
    Assert.assertEquals("13", assertMap.get("bb"));
    Assert.assertEquals("14", assertMap.get("c"));

    Assert.assertFalse(assertMap.containsKey("a"));
    Assert.assertFalse(assertMap.containsKey("b"));

    Assert.assertTrue(state.getValue(OinpBridgeConstants.IS_MOBILE));

  }

  @Test
  public void testEventMapping_Email(){
    NotificationGroup notificationGroup = new NotificationGroup();
    notificationGroup.setNotificationName("name");
    notificationGroup.setNotificationDataType("data");
    Map<String, List<String>> map =new HashMap<>(){{
      put("a", Collections.singletonList("aa"));
      put("b", Collections.singletonList("bb"));
    }};
    notificationGroup.setMappingAttributes(map);
    Mockito.when(oinpNotificationConfigMap.getNotificationGroup(OinpBridgeConstants.IS_EMAIL))
        .thenReturn(notificationGroup);
    State state = new State();
    state.addValue(OinpBridgeConstants.IS_MOBILE, false);
    state.addValue(OinpBridgeConstants.IS_EMAIL, true);
    state.addValue(OinpBridgeConstants.CONSOLIDATE_NOTIFICATIONS, false);
    state.addValue(OinpBridgeConstants.TEMPLATE_NAME, "billReminder");

    Map<String, String> output =new HashMap<>(){{
      put("a", "12");
      put("b", "13");
      put("c", "14");
    }};
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP, output);

    state = new RxExecutionChain(state)
        .next(new OINPEventMappingTask(oinpNotificationConfigMap))
        .execute();

    Map<String, String> assertMap = state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP);

    Assert.assertEquals("12", assertMap.get("aa"));
    Assert.assertEquals("13", assertMap.get("bb"));
    Assert.assertEquals("14", assertMap.get("c"));

    Assert.assertFalse(assertMap.containsKey("a"));
    Assert.assertFalse(assertMap.containsKey("b"));

    Assert.assertEquals("name", state.getValue(OinpBridgeConstants.NOTIFICATION_NAME));
    Assert.assertEquals("data", state.getValue(OinpBridgeConstants.NOTIFICATION_DATA_TYPE));

    Assert.assertTrue(state.getValue(OinpBridgeConstants.IS_EMAIL));

  }

  @Test
  public void testEventMapping_consolidateNotifications_notificationNameTypeFromInputVarsMap(){
    NotificationGroup notificationGroup = new NotificationGroup();
    notificationGroup.setNotificationName("name");
    notificationGroup.setNotificationDataType("data");
    Map<String, List<String>> map =new HashMap<>(){{
      put("a", Collections.singletonList("aa"));
      put("b", Collections.singletonList("bb"));
    }};
    notificationGroup.setMappingAttributes(map);
    Mockito.when(oinpNotificationConfigMap.getNotificationGroup(OinpBridgeConstants.CONSOLIDATE_NOTIFICATIONS))
        .thenReturn(notificationGroup);
    State state = new State();
    state.addValue(OinpBridgeConstants.IS_MOBILE, false);
    state.addValue(OinpBridgeConstants.IS_EMAIL, true);
    state.addValue(OinpBridgeConstants.CONSOLIDATE_NOTIFICATIONS, true);
    state.addValue(OinpBridgeConstants.TEMPLATE_NAME, "billReminder");

    Map<String, String> output =new HashMap<>(){{
      put("a", "12");
      put("b", "13");
      put("c", "14");
      put(OinpBridgeConstants.NOTIFICATION_NAME, "sampleNotificationName");
      put(OinpBridgeConstants.NOTIFICATION_TYPE, "sampleNotificationType");
    }};
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP, output);

    state = new RxExecutionChain(state)
        .next(new OINPEventMappingTask(oinpNotificationConfigMap))
        .execute();

    Map<String, String> assertMap = state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP);

    Assert.assertEquals("12", assertMap.get("aa"));
    Assert.assertEquals("13", assertMap.get("bb"));
    Assert.assertEquals("14", assertMap.get("c"));

    Assert.assertFalse(assertMap.containsKey("a"));
    Assert.assertFalse(assertMap.containsKey("b"));

    Assert.assertEquals("sampleNotificationName", state.getValue(OinpBridgeConstants.NOTIFICATION_NAME));
    Assert.assertEquals("sampleNotificationType", state.getValue(OinpBridgeConstants.NOTIFICATION_DATA_TYPE));

    Assert.assertTrue(state.getValue(OinpBridgeConstants.IS_EMAIL));

  }

  @Test
  public void testEventMapping_consolidateNotifications_notificationNameTypeFromOinpConfig(){
    NotificationGroup notificationGroup = new NotificationGroup();
    notificationGroup.setNotificationName("name");
    notificationGroup.setNotificationDataType("data");
    Map<String, List<String>> map =new HashMap<>(){{
      put("a", Collections.singletonList("aa"));
      put("b", Collections.singletonList("bb"));
    }};
    notificationGroup.setMappingAttributes(map);
    Mockito.when(oinpNotificationConfigMap.getNotificationGroup(OinpBridgeConstants.CONSOLIDATE_NOTIFICATIONS))
        .thenReturn(notificationGroup);
    State state = new State();
    state.addValue(OinpBridgeConstants.IS_MOBILE, false);
    state.addValue(OinpBridgeConstants.IS_EMAIL, true);
    state.addValue(OinpBridgeConstants.CONSOLIDATE_NOTIFICATIONS, true);
    state.addValue(OinpBridgeConstants.TEMPLATE_NAME, "billReminder");

    Map<String, String> output =new HashMap<>(){{
      put("a", "12");
      put("b", "13");
      put("c", "14");
    }};
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP, output);

    state = new RxExecutionChain(state)
        .next(new OINPEventMappingTask(oinpNotificationConfigMap))
        .execute();

    Map<String, String> assertMap = state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP);

    Assert.assertEquals("12", assertMap.get("aa"));
    Assert.assertEquals("13", assertMap.get("bb"));
    Assert.assertEquals("14", assertMap.get("c"));

    Assert.assertFalse(assertMap.containsKey("a"));
    Assert.assertFalse(assertMap.containsKey("b"));

    Assert.assertEquals("name", state.getValue(OinpBridgeConstants.NOTIFICATION_NAME));
    Assert.assertEquals("data", state.getValue(OinpBridgeConstants.NOTIFICATION_DATA_TYPE));

    Assert.assertTrue(state.getValue(OinpBridgeConstants.IS_EMAIL));
    Assert.assertTrue(state.getValue(OinpBridgeConstants.CONSOLIDATE_NOTIFICATIONS));

  }

  @Test
  public void testEventMapping_Email_With_TemplateName(){
    NotificationGroup notificationGroup = new NotificationGroup();
    notificationGroup.setNotificationName("name");
    notificationGroup.setNotificationDataType("data");
    Map<String, List<String>> map =new HashMap<>(){{
      put("a", Collections.singletonList("aa"));
      put("b", Collections.singletonList("bb"));
    }};
    notificationGroup.setMappingAttributes(map);
    Mockito.when(oinpNotificationConfigMap.getNotificationGroup("billReminder" + "_" + OinpBridgeConstants.IS_EMAIL))
        .thenReturn(notificationGroup);
    State state = new State();
    state.addValue(OinpBridgeConstants.IS_MOBILE, false);
    state.addValue(OinpBridgeConstants.IS_EMAIL, true);
    state.addValue(OinpBridgeConstants.CONSOLIDATE_NOTIFICATIONS, false);
    state.addValue(OinpBridgeConstants.TEMPLATE_NAME, "billReminder");

    Map<String, String> output =new HashMap<>(){{
      put("a", "12");
      put("b", "13");
      put("c", "14");
    }};
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP, output);

    state = new RxExecutionChain(state)
        .next(new OINPEventMappingTask(oinpNotificationConfigMap))
        .execute();

    Map<String, String> assertMap = state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP);

    Assert.assertEquals("12", assertMap.get("aa"));
    Assert.assertEquals("13", assertMap.get("bb"));
    Assert.assertEquals("14", assertMap.get("c"));

    Assert.assertFalse(assertMap.containsKey("a"));
    Assert.assertFalse(assertMap.containsKey("b"));

    Assert.assertTrue(state.getValue(OinpBridgeConstants.IS_EMAIL));

  }

}
