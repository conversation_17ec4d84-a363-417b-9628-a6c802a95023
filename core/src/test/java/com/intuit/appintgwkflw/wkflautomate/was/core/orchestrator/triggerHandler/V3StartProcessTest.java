package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class V3StartProcessTest {

  @Mock
  private BPMNEngineRunTimeServiceRest bpmnEngineRunTimeServiceRest;
  @Mock
  private V3RunTimeHelper runtimeHelper;
  @Mock
  private WASContextHandler contextHandler;

  @InjectMocks
  private V3StartProcess v3StartProcess;

  @Test
  public void startProcessTest() {
    TransactionEntity transactionEntity =
    	TransactionEntityFactory.getInstanceOf(
			TriggerHandlerTestData.prepareV3TriggerMessage("updated").getTriggerMessage(),
				contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("12345");
    Mockito.doReturn(TriggerHandlerTestData.getStartProcessResponse()).when(bpmnEngineRunTimeServiceRest).startProcess(Mockito.any());
    Map<String, Object> startProcessResult =v3StartProcess.startProcess(transactionEntity,TriggerHandlerTestData.getDefinitionDetails(
            TriggerHandlerTestData.getBPMNTemplateDetails()), initialStartEventExtensionProperties);
    Assert.assertNotNull(startProcessResult);
    Assert.assertEquals("SUCCESS", startProcessResult.get("status"));
    Mockito.verify((contextHandler), Mockito.times(1)).get(WASContextEnums.OWNER_ID);
  }

  @Test
  public void startProcessWithOnDemandTestTest() {
    TransactionEntity transactionEntity =
            TransactionEntityFactory.getInstanceOf(
                    TriggerHandlerTestData.prepareV3TriggerMessage("updated").getTriggerMessage(),
                    contextHandler);
    transactionEntity.getV3EntityPayload().put(WorkflowConstants.ON_DEMAND_APPROVAL,true);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("12345");
    Mockito.doReturn(TriggerHandlerTestData.getStartProcessResponse()).when(bpmnEngineRunTimeServiceRest).startProcess(Mockito.any());
    Map<String, Object> startProcessResult =v3StartProcess.startProcess(transactionEntity,TriggerHandlerTestData.getDefinitionDetails(
            TriggerHandlerTestData.getBPMNTemplateDetails()), initialStartEventExtensionProperties);
    Assert.assertNotNull(startProcessResult);
    Assert.assertEquals("SUCCESS", startProcessResult.get("status"));
    Mockito.verify((contextHandler), Mockito.times(1)).get(WASContextEnums.OWNER_ID);
  }

  @Test
  public void startProcessWithPOTriggerPayloadTest() {
    TransactionEntity transactionEntity =
    	TransactionEntityFactory.getInstanceOf(
			TriggerHandlerTestData.prepareV3TriggerMessage("created", "purchaseorder").getTriggerMessage(),
				contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("12345");
    Mockito.doReturn(TriggerHandlerTestData.getStartProcessResponse())
        .when(bpmnEngineRunTimeServiceRest)
        .startProcess(Mockito.any());
    Map<String, Object> startProcessResult =
        v3StartProcess.startProcess(
            transactionEntity,
            TriggerHandlerTestData.getDefinitionDetails(
                TriggerHandlerTestData.getBPMNTemplateDetails()),
                initialStartEventExtensionProperties);
    Assert.assertNotNull(startProcessResult);
    Assert.assertEquals("SUCCESS", startProcessResult.get("status"));
    Mockito.verify((contextHandler), Mockito.times(1)).get(WASContextEnums.OWNER_ID);
  }

  @Test
  public void startProcessWithEstimateTriggerPayloadTest() {
    TransactionEntity transactionEntity =
        TransactionEntityFactory.getInstanceOf(
            TriggerHandlerTestData.prepareV3TriggerMessage("created", "estimate").getTriggerMessage(),
            contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("12345");
    Mockito.doReturn(TriggerHandlerTestData.getStartProcessResponse())
        .when(bpmnEngineRunTimeServiceRest)
        .startProcess(Mockito.any());
    Map<String, Object> startProcessResult =
        v3StartProcess.startProcess(
            transactionEntity,
            TriggerHandlerTestData.getDefinitionDetails(
                TriggerHandlerTestData.getBPMNTemplateDetails()),
                initialStartEventExtensionProperties);
    Assert.assertNotNull(startProcessResult);
    Assert.assertEquals("SUCCESS", startProcessResult.get("status"));
    Mockito.verify((contextHandler), Mockito.times(1)).get(WASContextEnums.OWNER_ID);
  }

  @Test
  public void startProcessWithBusinessKey(){
    TransactionEntity transactionEntity =
    	TransactionEntityFactory.getInstanceOf(
			TriggerHandlerTestData.prepareV3TriggerMessage("businessKey").getTriggerMessage(),
				contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    Mockito.doReturn(TriggerHandlerTestData.getStartProcessResponse()).when(bpmnEngineRunTimeServiceRest).startProcess(Mockito.any());
    Map<String, Object> startProcessResult =v3StartProcess.startProcess(transactionEntity,TriggerHandlerTestData.getDefinitionDetails(
        TriggerHandlerTestData.getBPMNTemplateDetails()), initialStartEventExtensionProperties);
    Assert.assertNotNull(startProcessResult);
    Assert.assertEquals("SUCCESS", startProcessResult.get("status"));
    Mockito.verify((contextHandler), Mockito.times(0)).get(WASContextEnums.OWNER_ID);
  }

  @Test
  public void startProcessNoActionTest() {
    TransactionEntity transactionEntity =
    	TransactionEntityFactory.getInstanceOf(
			TriggerHandlerTestData.prepareV3TriggerMessage("deleted").getTriggerMessage(),
				contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    Map<String, Object> startProcessResult =
        v3StartProcess.startProcess(transactionEntity, TriggerHandlerTestData.getDefinitionDetails(TriggerHandlerTestData.getBPMNTemplateDetails()), initialStartEventExtensionProperties);
    Assert.assertNotNull(startProcessResult);
    Assert.assertNull(startProcessResult.get(WorkflowConstants.ID));
  }

  @SuppressWarnings({"unchecked"})
  @Test
  public void startProcessGlobalVarScopingTest() {
    TransactionEntity transactionEntity =
    	TransactionEntityFactory.getInstanceOf(
			TriggerHandlerTestData.prepareV3TriggerMessage("globalScoping").getTriggerMessage(),
				contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("12345");
    Mockito.doReturn(TriggerHandlerTestData.getStartProcessResponse())
        .when(bpmnEngineRunTimeServiceRest)
        .startProcess(Mockito.any());

    DefinitionDetails definitionDetails =
            TriggerHandlerTestData.getDefinitionDetails(
                TriggerHandlerTestData.getBPMNTemplateDetails());

    Mockito.when(
            runtimeHelper.getVariablesMap(
                transactionEntity,
                transactionEntity.getVariables().getGlobal(),
                true, initialStartEventExtensionProperties,
                definitionDetails,
                false,
                null,
                false))
        .thenReturn(
            ObjectConverter.fromJson(
                "{\"variables\":{\"TotalAmt\":{\"type\":\"Double\",\"value\":8000}}}",
                HashMap.class));

    Map<String, Object> startProcessResult =
        v3StartProcess.startProcess(
            transactionEntity,
            definitionDetails, initialStartEventExtensionProperties);
    Assert.assertNotNull(startProcessResult);
    Assert.assertEquals("SUCCESS", startProcessResult.get("status"));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void startProcessNoGlobalVarScopingTest() {
    TransactionEntity transactionEntity =
    	TransactionEntityFactory.getInstanceOf(
			TriggerHandlerTestData.prepareV3TriggerMessage("globalScoping").getTriggerMessage(),
				contextHandler);
    Map<String, Object> initialStartEventExtensionProperties = TriggerHandlerTestData.getStartEventExtensionPropertiesOfTheTemplate();

    DefinitionDetails definitionDetails =
        TriggerHandlerTestData.getDefinitionDetails(
            TriggerHandlerTestData.getBPMNTemplateDetails());

    // setting global as empty
    transactionEntity.getVariables().setGlobal(Collections.emptyMap());

    Mockito.when(
            runtimeHelper.getVariablesMap(
                transactionEntity,
                transactionEntity.getVariables().getGlobal(),
                true, initialStartEventExtensionProperties,
                definitionDetails,
                false,
                null,
                false))
        .thenThrow(new WorkflowGeneralException(WorkflowError.PROCESS_VARIABLE_DETAILS_NOT_FOUND));

    v3StartProcess.startProcess(transactionEntity, definitionDetails, initialStartEventExtensionProperties);
  }
}
