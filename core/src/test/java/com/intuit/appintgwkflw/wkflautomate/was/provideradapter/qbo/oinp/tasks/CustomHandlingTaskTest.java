package com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.IdentityService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.accounts.Email;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.accounts.Persona;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants.OinpBridgeConstants;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import java.util.HashMap;
import java.util.Map;
import java.util.SortedSet;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

@SuppressWarnings({"java:S5976", "java:S5778"})
public class CustomHandlingTaskTest {

  @Mock
  private IdentityService identityService;

  @Before
  public void init(){
    MockitoAnnotations.openMocks(this);
  }

  @Test
  public void testCustomHandling_EmptyToList(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getOwnerId()).thenReturn(123456L);

    Map<String, Persona> iusResponse = new HashMap<>();

    Mockito.when(identityService.getRealmPersonas(Mockito.any(), Mockito.any())).thenReturn(iusResponse);

    State state = new State();
    state.addValue(OinpBridgeConstants.WORKER_ACTION_REQUEST, workerActionRequest);
    state.addValue(OinpBridgeConstants.IS_EMAIL, true);
    Map<String, Object> outputMap = new HashMap<>(){{
      put(OinpBridgeConstants.TO_FIELD, "");
    }};
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP, outputMap);

    state = new RxExecutionChain(state).next(new CustomHandlingTask(identityService)).execute();

    Assert.assertNotNull(state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP));
    Assert.assertEquals("",
        ((Map)state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP)).get(OinpBridgeConstants.TO_FIELD));
  }

  @Test
  public void testCustomHandling_NullToList(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getOwnerId()).thenReturn(123456L);

    Map<String, Persona> iusResponse = new HashMap<>();

    Mockito.when(identityService.getRealmPersonas(Mockito.any(), Mockito.any())).thenReturn(iusResponse);

    State state = new State();
    state.addValue(OinpBridgeConstants.WORKER_ACTION_REQUEST, workerActionRequest);
    state.addValue(OinpBridgeConstants.IS_EMAIL, true);
    Map<String, Object> outputMap = new HashMap<>();
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP, outputMap);

    state = new RxExecutionChain(state).next(new CustomHandlingTask(identityService)).execute();

    Assert.assertNotNull(state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP));
    Assert.assertEquals("",
        ((Map)state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP)).get(OinpBridgeConstants.TO_FIELD));
  }

  @Test
  public void testCustomHandling_email_authIds_noEmailInToField(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getOwnerId()).thenReturn(123456L);

    Persona persona1 = new Persona();
    persona1.setPersonaId("expectedPersonaId1");
    persona1.setUserId("123455");
    Email email1 = new Email();
    email1.setEmailId("<EMAIL>");
    persona1.setEmail(email1);

    Persona persona2 = new Persona();
    persona2.setPersonaId("expectedPersonaId2");
    persona2.setUserId("123456");
    Email email2 = new Email();
    email2.setEmailId("<EMAIL>");
    persona2.setEmail(email2);

    Map<String, Persona> iusResponse = new HashMap<>(){{
      put("123455", persona1);
      put("123456", persona2);
    }};

    Mockito.when(identityService.getRealmPersonas(Mockito.any(), Mockito.any())).thenReturn(iusResponse);

    State state = new State();
    state.addValue(OinpBridgeConstants.WORKER_ACTION_REQUEST, workerActionRequest);
    state.addValue(OinpBridgeConstants.IS_EMAIL, true);
    Map<String, Object> outputMap = new HashMap<>(){{
      put(OinpBridgeConstants.TO_FIELD, "123455,123456");
    }};
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP, outputMap);

    state = new RxExecutionChain(state).next(new CustomHandlingTask(identityService)).execute();

    Assert.assertNotNull(state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP));
    Assert.assertEquals("<EMAIL>,<EMAIL>",
        ((Map)state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP)).get(OinpBridgeConstants.TO_FIELD));
    SortedSet<String> authIds = state.getValue(OinpBridgeConstants.AUTH_IDS);
    Assert.assertTrue(authIds.contains("123455"));
    Assert.assertTrue(authIds.contains("123456"));
    Assert.assertNotNull(state.getValue(OinpBridgeConstants.IUS_AUTHID_PERSONA_MAP));
    Map<String, Persona> iusResponseMap = state.getValue(OinpBridgeConstants.IUS_AUTHID_PERSONA_MAP);
    Assert.assertEquals("expectedPersonaId1", iusResponseMap.get("123455").getPersonaId());
  }

  @Test
  public void testCustomHandling_email_authIds_OneEmailInToField(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getOwnerId()).thenReturn(123456L);

    Persona persona1 = new Persona();
    persona1.setPersonaId("expectedPersonaId1");
    persona1.setUserId("123455");
    Email email1 = new Email();
    email1.setEmailId("<EMAIL>");
    persona1.setEmail(email1);

    Persona persona2 = new Persona();
    persona2.setPersonaId("expectedPersonaId2");
    persona2.setUserId("123456");
    Email email2 = new Email();
    email2.setEmailId("<EMAIL>");
    persona2.setEmail(email2);

    Map<String, Persona> iusResponse = new HashMap<>(){{
      put("123455", persona1);
      put("123456", persona2);
    }};

    Mockito.when(identityService.getRealmPersonas(Mockito.any(), Mockito.any())).thenReturn(iusResponse);

    State state = new State();
    state.addValue(OinpBridgeConstants.WORKER_ACTION_REQUEST, workerActionRequest);
    state.addValue(OinpBridgeConstants.IS_EMAIL, true);
    Map<String, Object> outputMap = new HashMap<>(){{
      put(OinpBridgeConstants.TO_FIELD, "123455,<EMAIL>,123456");
    }};
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP, outputMap);

    state = new RxExecutionChain(state).next(new CustomHandlingTask(identityService)).execute();

    Assert.assertNotNull(state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP));
    Assert.assertEquals("<EMAIL>,<EMAIL>,<EMAIL>",
        ((Map)state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP)).get(OinpBridgeConstants.TO_FIELD));
    SortedSet<String> authIds = state.getValue(OinpBridgeConstants.AUTH_IDS);
    Assert.assertTrue(authIds.contains("123455"));
    Assert.assertTrue(authIds.contains("123456"));
  }

  @Test
  public void testCustomHandling_email_NoAuthIds_OneEmailInToField(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getOwnerId()).thenReturn(123456L);

    Map<String, Persona> iusResponse = new HashMap<>();

    Mockito.when(identityService.getRealmPersonas(Mockito.any(), Mockito.any())).thenReturn(iusResponse);

    State state = new State();
    state.addValue(OinpBridgeConstants.WORKER_ACTION_REQUEST, workerActionRequest);
    state.addValue(OinpBridgeConstants.IS_EMAIL, true);
    Map<String, Object> outputMap = new HashMap<>(){{
      put(OinpBridgeConstants.TO_FIELD, "<EMAIL>");
    }};
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP, outputMap);

    state = new RxExecutionChain(state).next(new CustomHandlingTask(identityService)).execute();

    Assert.assertNotNull(state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP));
    Assert.assertEquals("<EMAIL>",
        ((Map)state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP)).get(OinpBridgeConstants.TO_FIELD));
    SortedSet<String> authIds = state.getValue(OinpBridgeConstants.AUTH_IDS);
    Assert.assertTrue(authIds.isEmpty());
  }

  @Test
  public void testCustomHandling_email_authIdsAndEmail_EmptyIUS(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getOwnerId()).thenReturn(123456L);

    Persona persona1 = new Persona();
    persona1.setPersonaId("expectedPersonaId1");
    persona1.setUserId("123455");
    Email email1 = new Email();
    email1.setEmailId("<EMAIL>");
    persona1.setEmail(email1);

    Map<String, Persona> iusResponse = new HashMap<>(){{
      put("123455", persona1);
    }};

    Mockito.when(identityService.getRealmPersonas(Mockito.any(), Mockito.any())).thenReturn(iusResponse);

    State state = new State();
    state.addValue(OinpBridgeConstants.WORKER_ACTION_REQUEST, workerActionRequest);
    state.addValue(OinpBridgeConstants.IS_EMAIL, true);
    Map<String, Object> outputMap = new HashMap<>(){{
      put(OinpBridgeConstants.TO_FIELD, "123455,<EMAIL>,123456");
    }};
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP, outputMap);

    state = new RxExecutionChain(state).next(new CustomHandlingTask(identityService)).execute();

    Assert.assertNotNull(state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP));
    Assert.assertEquals("<EMAIL>,<EMAIL>",
        ((Map)state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP)).get(OinpBridgeConstants.TO_FIELD));
    SortedSet<String> authIds = state.getValue(OinpBridgeConstants.AUTH_IDS);
    Assert.assertTrue(authIds.contains("123455"));
  }

  @Test
  public void testCustomHandling_email_twoAuthIdsAndEmail_IUS_oneAuthId(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getOwnerId()).thenReturn(123456L);

    Map<String, Persona> iusResponse = new HashMap<>();

    Mockito.when(identityService.getRealmPersonas(Mockito.any(), Mockito.any())).thenReturn(iusResponse);

    State state = new State();
    state.addValue(OinpBridgeConstants.WORKER_ACTION_REQUEST, workerActionRequest);
    state.addValue(OinpBridgeConstants.IS_EMAIL, true);
    Map<String, Object> outputMap = new HashMap<>(){{
      put(OinpBridgeConstants.TO_FIELD, "123455,<EMAIL>,123456");
    }};
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP, outputMap);

    state = new RxExecutionChain(state).next(new CustomHandlingTask(identityService)).execute();

    Assert.assertNotNull(state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP));
    Assert.assertEquals("<EMAIL>",
        ((Map)state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP)).get(OinpBridgeConstants.TO_FIELD));
    SortedSet<String> authIds = state.getValue(OinpBridgeConstants.AUTH_IDS);
    Assert.assertTrue(authIds.isEmpty());
  }

  @Test
  public void testCustomHandling_mobile_2AuthsIds_IUS_2AuthId(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getOwnerId()).thenReturn(123456L);

    Persona persona1 = new Persona();
    persona1.setPersonaId("expectedPersonaId1");
    persona1.setUserId("123455");
    Email email1 = new Email();
    email1.setEmailId("<EMAIL>");
    persona1.setEmail(email1);

    Persona persona2 = new Persona();
    persona2.setPersonaId("expectedPersonaId2");
    persona2.setUserId("123456");
    Email email2 = new Email();
    email2.setEmailId("<EMAIL>");
    persona2.setEmail(email2);

    Map<String, Persona> iusResponse = new HashMap<>(){{
      put("123455", persona1);
      put("123456", persona2);
    }};

    Mockito.when(identityService.getRealmPersonas(Mockito.any(), Mockito.any())).thenReturn(iusResponse);

    State state = new State();
    state.addValue(OinpBridgeConstants.WORKER_ACTION_REQUEST, workerActionRequest);
    state.addValue(OinpBridgeConstants.IS_EMAIL, false);
    Map<String, Object> outputMap = new HashMap<>(){{
      put(OinpBridgeConstants.TO_FIELD, "123455,123456");
    }};
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP, outputMap);

    state = new RxExecutionChain(state).next(new CustomHandlingTask(identityService)).execute();

    Assert.assertNotNull(state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP));
    Assert.assertEquals("123455,123456",
        ((Map)state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP)).get(OinpBridgeConstants.TO_FIELD));
    SortedSet<String> authIds = state.getValue(OinpBridgeConstants.AUTH_IDS);
    Assert.assertTrue(authIds.contains("123455"));
    Assert.assertTrue(authIds.contains("123456"));
  }

  @Test
  public void testCustomHandling_mobile_2AuthsIds_IUS_1AuthId(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getOwnerId()).thenReturn(123456L);

    Persona persona1 = new Persona();
    persona1.setPersonaId("expectedPersonaId1");
    persona1.setUserId("123455");
    Email email1 = new Email();
    email1.setEmailId("<EMAIL>");
    persona1.setEmail(email1);

    Map<String, Persona> iusResponse = new HashMap<>(){{
      put("123455", persona1);
    }};

    Mockito.when(identityService.getRealmPersonas(Mockito.any(), Mockito.any())).thenReturn(iusResponse);

    State state = new State();
    state.addValue(OinpBridgeConstants.WORKER_ACTION_REQUEST, workerActionRequest);
    state.addValue(OinpBridgeConstants.IS_EMAIL, false);
    Map<String, Object> outputMap = new HashMap<>(){{
      put(OinpBridgeConstants.TO_FIELD, "123455,123456");
    }};
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP, outputMap);

    state = new RxExecutionChain(state).next(new CustomHandlingTask(identityService)).execute();

    Assert.assertNotNull(state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP));
    Assert.assertEquals("123455",
        ((Map)state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP)).get(OinpBridgeConstants.TO_FIELD));
    SortedSet<String> authIds = state.getValue(OinpBridgeConstants.AUTH_IDS);
    Assert.assertTrue(authIds.contains("123455"));
    Assert.assertFalse(authIds.contains("123456"));
  }

  @Test
  public void testCustomHandling_mobile_1AuthsIds_IUS_Empty(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getOwnerId()).thenReturn(123456L);

    Map<String, Persona> iusResponse = new HashMap<>();

    Mockito.when(identityService.getRealmPersonas(Mockito.any(), Mockito.any())).thenReturn(iusResponse);

    State state = new State();
    state.addValue(OinpBridgeConstants.WORKER_ACTION_REQUEST, workerActionRequest);
    state.addValue(OinpBridgeConstants.IS_EMAIL, false);
    Map<String, Object> outputMap = new HashMap<>(){{
      put(OinpBridgeConstants.TO_FIELD, "123455");
    }};
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP, outputMap);

    state = new RxExecutionChain(state).next(new CustomHandlingTask(identityService)).execute();

    Assert.assertNotNull(state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP));
    Assert.assertEquals("",
        ((Map)state.getValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP)).get(OinpBridgeConstants.TO_FIELD));
    SortedSet<String> authIds = state.getValue(OinpBridgeConstants.AUTH_IDS);
    Assert.assertTrue(authIds.isEmpty());

  }

  @Test
  public void testCustomHandling_mobile_1AuthsIds_IUS_CallFailed(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getOwnerId()).thenReturn(123456L);

    Mockito.when(identityService.getRealmPersonas(Mockito.any(), Mockito.any()))
        .thenThrow(new WorkflowGeneralException(WorkflowError.IUS_GET_PERSONA_ERROR));

    State state = new State();
    state.addValue(OinpBridgeConstants.WORKER_ACTION_REQUEST, workerActionRequest);
    state.addValue(OinpBridgeConstants.IS_EMAIL, false);
    Map<String, Object> outputMap = new HashMap<>(){{
      put(OinpBridgeConstants.TO_FIELD, "123455");
    }};
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP, outputMap);

    Exception e = Assertions.assertThrows(WorkflowGeneralException.class,
        () -> new RxExecutionChain(state).next(new CustomHandlingTask(identityService)).execute());
    Assert.assertEquals(WorkflowError.IUS_GET_PERSONA_ERROR.getErrorMessage(), e.getMessage());

  }
}
