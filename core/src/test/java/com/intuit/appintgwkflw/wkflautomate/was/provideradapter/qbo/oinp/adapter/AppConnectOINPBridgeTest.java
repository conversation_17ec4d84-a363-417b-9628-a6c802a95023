package com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.adapter;

import static com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants.OinpBridgeConstants.SEND_NOTIFICATION_API_HANDLER_ID;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.IdentityService;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowBatchNotificationTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowNotificationTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.oinpqbo.bridge.NotificationGroup;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.accounts.Email;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.accounts.Persona;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.config.OINPNotificationConfigMap;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.config.OINPSendNotificationConfig;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants.OinpBridgeConstants;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;


public class AppConnectOINPBridgeTest {

  @InjectMocks
  private AppConnectOINPBridge appConnectOINPBridge;
  @Mock
  private WorkflowNotificationTask workflowNotificationTask;
  @Mock
  private WorkflowBatchNotificationTask workflowBatchNotificationTask;
  @Mock
  private IdentityService identityService;
  @Mock
  private OINPNotificationConfigMap oinpNotificationConfigMap;
  @Mock
  private OINPSendNotificationConfig oinpSendNotificationConfig;

  @Before
  public void init(){
    MockitoAnnotations.openMocks(this);
    Mockito.when(oinpSendNotificationConfig.isNotificationEnabled(Mockito.any(), Mockito.anyString(), Mockito.any()))
        .thenReturn(true);
  }

  @Test
  public void testExecuteNotificationAction_Positive(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getOwnerId()).thenReturn(123456L);
    Mockito.when(workerActionRequest.getTaskId()).thenReturn("task-id");
    Mockito.when(workerActionRequest.getActivityId()).thenReturn("activity-id");

    Map<String, String> inputMap = new HashMap<>();
    inputMap.put(OinpBridgeConstants.IS_EMAIL, "true");
    inputMap.put(OinpBridgeConstants.IS_MOBILE, "false");
    inputMap.put(OinpBridgeConstants.TO_FIELD, "912747484");

    Mockito.when(identityService.getRealmPersonas(Mockito.any(), Mockito.any())).thenReturn(Collections.emptyMap());

    NotificationGroup notificationGroup = new NotificationGroup();
    notificationGroup.setNotificationName("nname");
    notificationGroup.setNotificationDataType("ndtype");
    notificationGroup.setMappingAttributes(Collections.emptyMap());
    Mockito.when(oinpNotificationConfigMap.getNotificationGroup(OinpBridgeConstants.IS_EMAIL))
        .thenReturn(notificationGroup);

    WorkflowTaskResponse workflowTaskResponse = WorkflowTaskResponse.builder().status(
        ActivityConstants.TASK_STATUS_CREATED).build();
    Mockito.when(workflowNotificationTask.create(Mockito.any())).thenReturn(workflowTaskResponse);

    Map<String, Object> response = appConnectOINPBridge
        .executeNotificationAction(workerActionRequest, inputMap);

    Assert.assertNotNull(response);
    Assert.assertTrue(response.containsKey("activity-id_response"));
    Assert.assertTrue((Boolean)response.get("activity-id_response"));
  }

  @Test
  public void testExecuteNotificationAction_Negative_OINP_Failed(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getOwnerId()).thenReturn(123456L);
    Mockito.when(workerActionRequest.getTaskId()).thenReturn("task-id");
    Mockito.when(workerActionRequest.getActivityId()).thenReturn("activity-id");

    Map<String, String> inputMap = new HashMap<>();
    inputMap.put(OinpBridgeConstants.IS_EMAIL, "true");
    inputMap.put(OinpBridgeConstants.IS_MOBILE, "false");
    inputMap.put(OinpBridgeConstants.TO_FIELD, "912747484");

    Persona persona1 = new Persona();
    persona1.setPersonaId("expectedPersonaId1");
    persona1.setUserId("912747484");
    Email email1 = new Email();
    email1.setEmailId("<EMAIL>");
    persona1.setEmail(email1);
    Mockito.when(identityService.getRealmPersonas(Mockito.any(), Mockito.any())).thenReturn(
        new HashMap<>(){{
          put("912747484", persona1);
        }}
    );

    NotificationGroup notificationGroup = new NotificationGroup();
    notificationGroup.setNotificationName("nname");
    notificationGroup.setNotificationDataType("ndtype");
    notificationGroup.setMappingAttributes(Collections.emptyMap());
    Mockito.when(oinpNotificationConfigMap.getNotificationGroup(OinpBridgeConstants.IS_EMAIL))
        .thenReturn(notificationGroup);

    WorkflowTaskResponse workflowTaskResponse = WorkflowTaskResponse.builder().status(
        ActivityConstants.TASK_STATUS_FAILED).build();
    Mockito.when(workflowNotificationTask.create(Mockito.any())).thenThrow(WorkflowGeneralException.class);

    Assertions.assertThrows(WorkflowGeneralException.class,
        () -> appConnectOINPBridge
            .executeNotificationAction(workerActionRequest, inputMap));
  }

  @Test
  public void testExecuteNotificationAction_Negative_IUS_Failed(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getOwnerId()).thenReturn(123456L);
    Mockito.when(workerActionRequest.getTaskId()).thenReturn("task-id");
    Mockito.when(workerActionRequest.getActivityId()).thenReturn("activity-id");

    Map<String, String> inputMap = new HashMap<>();
    inputMap.put(OinpBridgeConstants.IS_EMAIL, "true");
    inputMap.put(OinpBridgeConstants.IS_MOBILE, "false");
    inputMap.put(OinpBridgeConstants.TO_FIELD, "912747484");

    Mockito.when(identityService.getRealmPersonas(Mockito.any(), Mockito.any())).thenThrow(new WorkflowGeneralException(WorkflowError.IUS_GET_PERSONA_ERROR));

    Exception e = Assertions.assertThrows(WorkflowGeneralException.class,
        () -> appConnectOINPBridge
            .executeNotificationAction(workerActionRequest, inputMap));
    Assert.assertEquals(WorkflowError.IUS_GET_PERSONA_ERROR.getErrorMessage(), e.getMessage());
  }

  @Test
  public void testInitiateBridgeWithMobile(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getOwnerId()).thenReturn(123456L);
    Mockito.when(workerActionRequest.getHandlerId()).thenReturn(OinpBridgeConstants.SEND_NOTIFICATION_HANDLER_ID);
    Map<String, String> inputVariableMap = new HashMap<>(){{
      put(OinpBridgeConstants.TEMPLATE_NAME, "billReminder");
    }};
    Mockito.when(workerActionRequest.getInputVariables()).thenReturn(inputVariableMap);
    Map<String, String> inputMap = new HashMap<>(){{
      put(OinpBridgeConstants.IS_MOBILE, Boolean.TRUE.toString());
    }};
    Assert.assertTrue(appConnectOINPBridge.initiateBridge(workerActionRequest, inputMap));
  }

  @Test
  public void testInitiateBridgeForMultiConditionWithMobile() {
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getOwnerId()).thenReturn(123456L);
    Mockito.when(workerActionRequest.getHandlerId()).thenReturn(SEND_NOTIFICATION_API_HANDLER_ID);
    Map<String, String> inputVariableMap = new HashMap<>() {{
      put(OinpBridgeConstants.TEMPLATE_NAME, "customApproval");
    }};
    Mockito.when(workerActionRequest.getInputVariables()).thenReturn(inputVariableMap);
    Map<String, String> inputMap = new HashMap<>() {{
      put(OinpBridgeConstants.IS_MOBILE, Boolean.TRUE.toString());
    }};
    Assert.assertTrue(appConnectOINPBridge.initiateBridge(workerActionRequest, inputMap));
  }


  @Test
  public void testInitiateBridgeWithEmail(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getOwnerId()).thenReturn(123456L);
    Mockito.when(workerActionRequest.getHandlerId()).thenReturn(OinpBridgeConstants.SEND_NOTIFICATION_HANDLER_ID);
    Map<String, String> inputVariableMap = new HashMap<>(){{
      put(OinpBridgeConstants.TEMPLATE_NAME, "billReminder");
    }};
    Mockito.when(workerActionRequest.getInputVariables()).thenReturn(inputVariableMap);
    Map<String, String> inputMap = new HashMap<>(){{
      put(OinpBridgeConstants.IS_EMAIL, Boolean.TRUE.toString());
    }};
    Assert.assertTrue(appConnectOINPBridge.initiateBridge(workerActionRequest, inputMap));
  }

  @Test
  public void testInitiateBridgeWithEmailAndAttachment(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getOwnerId()).thenReturn(123456L);
    Mockito.when(workerActionRequest.getHandlerId()).thenReturn(OinpBridgeConstants.SEND_NOTIFICATION_HANDLER_ID);
    Map<String, String> inputVariableMap = new HashMap<>(){{
      put(OinpBridgeConstants.TEMPLATE_NAME, "billReminder");
    }};
    Mockito.when(workerActionRequest.getInputVariables()).thenReturn(inputVariableMap);
    Map<String, String> inputMap = new HashMap<>(){{
      put(OinpBridgeConstants.IS_EMAIL, Boolean.TRUE.toString());
      put(OinpBridgeConstants.SEND_ATTACHMENT, Boolean.TRUE.toString());
    }};
    ArgumentCaptor<String> stringArgumentCaptor = ArgumentCaptor.forClass(String.class);
    Mockito.when(oinpSendNotificationConfig.isNotificationEnabled(Mockito.eq("billReminder"),
        stringArgumentCaptor.capture(), Mockito.eq(123456L)))
        .thenReturn(true);
    appConnectOINPBridge.initiateBridge(workerActionRequest, inputMap);
    Assert.assertEquals(OinpBridgeConstants.SEND_ATTACHMENT, stringArgumentCaptor.getValue());
  }

  @Test
  public void testInitiateBridgeWithEmailAndConsolidateNotifications(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getOwnerId()).thenReturn(123456L);
    Mockito.when(workerActionRequest.getHandlerId()).thenReturn(OinpBridgeConstants.SEND_NOTIFICATION_HANDLER_ID);
    Map<String, String> inputVariableMap = new HashMap<>(){{
      put(OinpBridgeConstants.TEMPLATE_NAME, "billReminder");
    }};
    Mockito.when(workerActionRequest.getInputVariables()).thenReturn(inputVariableMap);
    Map<String, String> inputMap = new HashMap<>(){{
      put(OinpBridgeConstants.IS_EMAIL, Boolean.TRUE.toString());
      put(OinpBridgeConstants.CONSOLIDATE_NOTIFICATIONS, Boolean.TRUE.toString());
    }};
    ArgumentCaptor<String> stringArgumentCaptor = ArgumentCaptor.forClass(String.class);
    Mockito.when(oinpSendNotificationConfig.isNotificationEnabled(Mockito.eq("billReminder"),
        stringArgumentCaptor.capture(), Mockito.eq(123456L)))
        .thenReturn(true);
    appConnectOINPBridge.initiateBridge(workerActionRequest, inputMap);
    Assert.assertEquals(OinpBridgeConstants.CONSOLIDATE_NOTIFICATIONS, stringArgumentCaptor.getValue());
  }

  @Test
  public void testInitiateBridgeWithNullNotificationType(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getOwnerId()).thenReturn(123456L);
    Mockito.when(workerActionRequest.getHandlerId()).thenReturn(OinpBridgeConstants.SEND_NOTIFICATION_HANDLER_ID);
    Map<String, String> inputVariableMap = new HashMap<>(){{
      put(OinpBridgeConstants.TEMPLATE_NAME, "billReminder");
    }};
    Mockito.when(workerActionRequest.getInputVariables()).thenReturn(inputVariableMap);
    Map<String, String> inputMap = new HashMap<>();
    Assert.assertFalse(appConnectOINPBridge.initiateBridge(workerActionRequest, inputMap));
  }

  @Test
  public void testInitiateBridgeWithInvalidHandler(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getOwnerId()).thenReturn(123456L);
    Mockito.when(workerActionRequest.getHandlerId()).thenReturn("test/handler");
    Mockito.when(workerActionRequest.getInputVariables()).thenReturn(new HashMap<>());
    Map<String, String> inputMap = new HashMap<>();
    Assert.assertFalse(appConnectOINPBridge.initiateBridge(workerActionRequest, inputMap));
  }
}
