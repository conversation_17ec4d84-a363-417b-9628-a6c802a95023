package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.taskcompletionhandler;

import static org.mockito.Mockito.never;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service.CamundaRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler.WorkflowTransitionEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.StateTransitionService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TransactionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskCompleted;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ExternalTaskDetail;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CamundaUpdateRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.ExtendExternalTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.ExternalTaskFailure;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowActivityAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class ExternalTaskCompletionHandlerTest {

  @Mock CamundaRunTimeServiceRest camundaRest;
  @Mock MetricLogger metricLogger;
  @Mock WorkflowTransitionEventHandler transitionEventHandler;
  @InjectMocks
  ExternalTaskCompletionHandler handler;
  @Mock private EventPublisherCapability eventPublishCapability;
  @Mock private StateTransitionService stateTransitionService;
  @Mock private ActivityProgressDetailsRepository activityProgressDetailRepo;

  @Captor
  private ArgumentCaptor<ExternalTaskFailure> taskFailureCaptor;

  @Captor
  private ArgumentCaptor<CamundaUpdateRequest> updateRequestCaptor;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testExecuteSuccess() throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.SUCCESS.getStatus())
            .variables(Collections.emptyMap())
            .localVariables(Collections.emptyMap())
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId:workerId");
    handler.completeTask(task, headers);
    Mockito.verify(camundaRest).completeTask(Mockito.any(), Mockito.eq("taskId"));
  }

  @Test
  public void testExecuteSuccessBlankWorkerId() throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.SUCCESS.getStatus())
            .variables(Collections.emptyMap())
            .localVariables(Collections.emptyMap())
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId: ");
    handler.completeTask(task, headers);
    Mockito.verify(camundaRest).customCompleteTask(Mockito.any(), Mockito.eq("taskId"));
  }

  @Test
  public void testExecuteFailure() throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.FAILED.getStatus())
            .errorMessage("errorMessage")
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId:workerId");
    handler.invokeFailure(task, headers);
    Mockito.verify(camundaRest).failureTask(taskFailureCaptor.capture(), Mockito.eq("taskId"));
    Assert.assertEquals("errorMessage", taskFailureCaptor.getValue().getErrorMessage());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testExecuteIncorrectEntityId() throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.FAILED.getStatus())
            .variables(Collections.emptyMap())
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "value");
    handler.invokeFailure(task, headers);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testExecuteCamundaExceptionOnComplete() throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.SUCCESS.getStatus())
            .variables(Collections.emptyMap())
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId:workerId");

    Mockito.doThrow(new WorkflowGeneralException(WorkflowError.CAMUNDA_COMPLETE_TASK_FAILED))
        .when(camundaRest)
        .completeTask(Mockito.any(), Mockito.any());
    handler.completeTask(task, headers);

    Mockito.verify(camundaRest).failureTask(Mockito.any(), Mockito.eq("taskId"));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testExecuteCamundaExceptionOnCompleteWorkerIdEmpty() throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.SUCCESS.getStatus())
            .variables(Collections.emptyMap())
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId: ");

    Mockito.doThrow(new WorkflowGeneralException(WorkflowError.CAMUNDA_COMPLETE_TASK_FAILED))
        .when(camundaRest)
        .customCompleteTask(Mockito.any(), Mockito.any());
    handler.completeTask(task, headers);

    Mockito.verify(camundaRest).customFailureTask(Mockito.any(), Mockito.eq("taskId"));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testExecuteCamundaExceptionOnCompleteBlankWorkerId() throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.SUCCESS.getStatus())
            .variables(Collections.emptyMap())
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId: ");

    Mockito.doThrow(new WorkflowGeneralException(WorkflowError.CAMUNDA_COMPLETE_TASK_FAILED))
        .when(camundaRest)
        .customCompleteTask(Mockito.any(), Mockito.any());
    handler.completeTask(task, headers);

    Mockito.verify(camundaRest).customFailureTask(Mockito.any(), Mockito.eq("taskId"));
  }

  @Test
  public void testExecuteCamundaExceptionOn404() throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.SUCCESS.getStatus())
            .variables(Collections.emptyMap())
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId:workerId");

    Mockito.doThrow(new WorkflowGeneralException(WorkflowError.CAMUNDA_TASK_NOT_FOUND))
        .when(camundaRest)
        .completeTask(Mockito.any(), Mockito.any());

    Mockito.doNothing().when(stateTransitionService).publishCompleteEvent(Mockito.any(ActivityProgressDetails.class),
    		Mockito.anyMap(), Mockito.anyString(), Mockito.any(Long.class));

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(),
        "{\"actionName\":\"wasCustomTaskHandler\"}");
    modelAttributes.put(WorkFlowVariables.EVENTS.getName(),
        "[\"completed\",\"update\"]");

    ActivityDetail activityDetail = ActivityDetail.builder()
        .activityId("actId").activityType("serviceTask").type(TaskType.HUMAN_TASK)
        .activityName("actName")
        .attributes(ObjectConverter
            .toJson(WorkflowActivityAttributes.builder().modelAttributes(modelAttributes).build()))
        .build();

    ActivityProgressDetails activityProgressDetails = ActivityProgressDetails.builder()
        .id("ext1")
        .activityDefinitionDetail(activityDetail)
        .status("in-process")
        .txnDetails(TransactionDetails.builder().txnId("txnId").build())
        .processDetails(ProcessDetails.builder()
            .ownerId(1l).recordId("rec1")
            .definitionDetails(DefinitionDetails.builder().definitionId("defId")
                .definitionName("defName")
                .recordType(RecordType.ENGAGEMENT)
                .version(1).build())
            .build())
        .build();

    Mockito.when(activityProgressDetailRepo.findById(Mockito.anyString()))
	.thenReturn(Optional.of(activityProgressDetails));

    Mockito.when(stateTransitionService.isStateTransitionEnabled(Mockito.any(ActivityProgressDetails.class),
		Mockito.anyString())).thenReturn(true);


    handler.completeTask(task, headers);

    Mockito.verify(camundaRest, never()).failureTask(Mockito.any(), Mockito.eq("taskId"));
    Mockito.verify(stateTransitionService, Mockito.times(1))
    	.publishCompleteEvent(Mockito.any(ActivityProgressDetails.class), Mockito.anyMap(),
    			Mockito.anyString(), Mockito.any(Long.class));
  }

  @Test
  public void testExecuteCamundaExceptionOn404WithWorkerIdEmpty() throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.SUCCESS.getStatus())
            .variables(Collections.emptyMap())
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId: ");

    Mockito.doThrow(new WorkflowGeneralException(WorkflowError.CAMUNDA_TASK_NOT_FOUND))
        .when(camundaRest)
        .customCompleteTask(Mockito.any(), Mockito.any());

    Mockito.doNothing()
        .when(stateTransitionService)
        .publishCompleteEvent(
            Mockito.any(ActivityProgressDetails.class),
            Mockito.anyMap(),
            Mockito.anyString(),
            Mockito.any(Long.class));

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(
        WorkFlowVariables.HANDLER_DETAILS_KEY.getName(),
        "{\"actionName\":\"wasCustomTaskHandler\"}");
    modelAttributes.put(WorkFlowVariables.EVENTS.getName(), "[\"completed\",\"update\"]");

    ActivityDetail activityDetail =
        ActivityDetail.builder()
            .activityId("actId")
            .activityType("serviceTask")
            .type(TaskType.HUMAN_TASK)
            .activityName("actName")
            .attributes(
                ObjectConverter.toJson(
                    WorkflowActivityAttributes.builder().modelAttributes(modelAttributes).build()))
            .build();

    ActivityProgressDetails activityProgressDetails =
        ActivityProgressDetails.builder()
            .id("ext1")
            .activityDefinitionDetail(activityDetail)
            .status("in-process")
            .txnDetails(TransactionDetails.builder().txnId("txnId").build())
            .processDetails(
                ProcessDetails.builder()
                    .ownerId(1l)
                    .recordId("rec1")
                    .definitionDetails(
                        DefinitionDetails.builder()
                            .definitionId("defId")
                            .definitionName("defName")
                            .recordType(RecordType.ENGAGEMENT)
                            .version(1)
                            .build())
                    .build())
            .build();

    Mockito.when(activityProgressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(activityProgressDetails));

    Mockito.when(
            stateTransitionService.isStateTransitionEnabled(
                Mockito.any(ActivityProgressDetails.class), Mockito.anyString()))
        .thenReturn(true);

    handler.completeTask(task, headers);

    Mockito.verify(camundaRest, never()).customFailureTask(Mockito.any(), Mockito.eq("taskId"));
    Mockito.verify(stateTransitionService, Mockito.times(1))
        .publishCompleteEvent(
            Mockito.any(ActivityProgressDetails.class),
            Mockito.anyMap(),
            Mockito.anyString(),
            Mockito.any(Long.class));
  }

  @Test(expected=WorkflowGeneralException.class)
  public void testExecuteCamundaExceptionOn404_activityDetailsStatusError() throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.SUCCESS.getStatus())
            .variables(Collections.emptyMap())
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId:workerId");

    Mockito.doThrow(new WorkflowGeneralException(WorkflowError.CAMUNDA_TASK_NOT_FOUND))
        .when(camundaRest)
        .completeTask(Mockito.any(), Mockito.any());

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(),
        "{\"actionName\":\"wasCustomTaskHandler\"}");
    modelAttributes.put(WorkFlowVariables.EVENTS.getName(),
        "[\"completed\",\"update\"]");

    ActivityDetail activityDetail = ActivityDetail.builder()
        .activityId("actId").activityType("serviceTask").type(TaskType.HUMAN_TASK)
        .activityName("actName")
        .attributes(ObjectConverter
            .toJson(WorkflowActivityAttributes.builder().modelAttributes(modelAttributes).build()))
        .build();

    ActivityProgressDetails activityProgressDetails = ActivityProgressDetails.builder()
        .id("ext1")
        .activityDefinitionDetail(activityDetail)
        .status("update")
        .txnDetails(TransactionDetails.builder().txnId("txnId").build())
        .processDetails(ProcessDetails.builder()
            .ownerId(1l).recordId("rec1")
            .definitionDetails(DefinitionDetails.builder().definitionId("defId")
                .definitionName("defName")
                .recordType(RecordType.ENGAGEMENT)
                .version(1).build())
            .build())
        .build();

    Mockito.when(activityProgressDetailRepo.findById(Mockito.anyString()))
	.thenReturn(Optional.of(activityProgressDetails));

    Mockito.when(stateTransitionService.isStateTransitionEnabled(Mockito.any(ActivityProgressDetails.class),
		Mockito.anyString())).thenReturn(true);

    Mockito.doThrow(new WorkflowGeneralException(WorkflowError.CAMUNDA_TASK_NOT_FOUND))
    	.when(stateTransitionService).publishCompleteEvent(Mockito.any(ActivityProgressDetails.class),
    			Mockito.anyMap(), Mockito.anyString(), Mockito.any(Long.class));

    handler.completeTask(task, headers);

  }

  @Test(expected = WorkflowGeneralException.class)
  public void testExecuteCamundaExceptionOn404_activityDetailsStatusErrorWithWorkerIdEmpty()
      throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.SUCCESS.getStatus())
            .variables(Collections.emptyMap())
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId: ");

    Mockito.doThrow(new WorkflowGeneralException(WorkflowError.CAMUNDA_TASK_NOT_FOUND))
        .when(camundaRest)
        .customCompleteTask(Mockito.any(), Mockito.any());

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(
        WorkFlowVariables.HANDLER_DETAILS_KEY.getName(),
        "{\"actionName\":\"wasCustomTaskHandler\"}");
    modelAttributes.put(WorkFlowVariables.EVENTS.getName(), "[\"completed\",\"update\"]");

    ActivityDetail activityDetail =
        ActivityDetail.builder()
            .activityId("actId")
            .activityType("serviceTask")
            .type(TaskType.HUMAN_TASK)
            .activityName("actName")
            .attributes(
                ObjectConverter.toJson(
                    WorkflowActivityAttributes.builder().modelAttributes(modelAttributes).build()))
            .build();

    ActivityProgressDetails activityProgressDetails =
        ActivityProgressDetails.builder()
            .id("ext1")
            .activityDefinitionDetail(activityDetail)
            .status("update")
            .txnDetails(TransactionDetails.builder().txnId("txnId").build())
            .processDetails(
                ProcessDetails.builder()
                    .ownerId(1l)
                    .recordId("rec1")
                    .definitionDetails(
                        DefinitionDetails.builder()
                            .definitionId("defId")
                            .definitionName("defName")
                            .recordType(RecordType.ENGAGEMENT)
                            .version(1)
                            .build())
                    .build())
            .build();

    Mockito.when(activityProgressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(activityProgressDetails));

    Mockito.when(
            stateTransitionService.isStateTransitionEnabled(
                Mockito.any(ActivityProgressDetails.class), Mockito.anyString()))
        .thenReturn(true);

    Mockito.doThrow(new WorkflowGeneralException(WorkflowError.CAMUNDA_TASK_NOT_FOUND))
        .when(stateTransitionService)
        .publishCompleteEvent(
            Mockito.any(ActivityProgressDetails.class),
            Mockito.anyMap(),
            Mockito.anyString(),
            Mockito.any(Long.class));

    handler.completeTask(task, headers);
  }

  @Test(expected=WorkflowGeneralException.class)
  public void testExecuteCamundaExceptionOn404_externalTaskNotFound() throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.SUCCESS.getStatus())
            .variables(Collections.emptyMap())
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId:workerId");

    Mockito.doThrow(new WorkflowGeneralException(WorkflowError.EXTERNAL_TASK_DETAILS_NOT_FOUND_ERROR))
        .when(camundaRest)
        .completeTask(Mockito.any(), Mockito.any());

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(),
        "{\"actionName\":\"wasCustomTaskHandler\"}");
    modelAttributes.put(WorkFlowVariables.EVENTS.getName(),
        "[\"completed\",\"update\"]");

    ActivityDetail activityDetail = ActivityDetail.builder()
        .activityId("actId").activityType("serviceTask").type(TaskType.HUMAN_TASK)
        .activityName("actName")
        .attributes(ObjectConverter
            .toJson(WorkflowActivityAttributes.builder().modelAttributes(modelAttributes).build()))
        .build();

    ActivityProgressDetails activityProgressDetails = ActivityProgressDetails.builder()
        .id("ext1")
        .activityDefinitionDetail(activityDetail)
        .status("update")
        .txnDetails(TransactionDetails.builder().txnId("txnId").build())
        .processDetails(ProcessDetails.builder()
            .ownerId(1l).recordId("rec1")
            .definitionDetails(DefinitionDetails.builder().definitionId("defId")
                .definitionName("defName")
                .recordType(RecordType.ENGAGEMENT)
                .version(1).build())
            .build())
        .build();

    Mockito.when(activityProgressDetailRepo.findById(Mockito.anyString()))
	.thenReturn(Optional.of(activityProgressDetails));

    Mockito.when(stateTransitionService.isStateTransitionEnabled(Mockito.any(ActivityProgressDetails.class),
		Mockito.anyString())).thenReturn(true);

    Mockito.doThrow(new WorkflowGeneralException(WorkflowError.EXTERNAL_TASK_DETAILS_NOT_FOUND_ERROR))
    	.when(stateTransitionService).publishCompleteEvent(Mockito.any(ActivityProgressDetails.class),
    			Mockito.anyMap(), Mockito.anyString(), Mockito.any(Long.class));

    handler.completeTask(task, headers);

  }

  @Test(expected = WorkflowGeneralException.class)
  public void testExecuteCamundaExceptionOn404_externalTaskNotFoundWithWorkerIdEmpty()
      throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.SUCCESS.getStatus())
            .variables(Collections.emptyMap())
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId: ");

    Mockito.doThrow(
            new WorkflowGeneralException(WorkflowError.EXTERNAL_TASK_DETAILS_NOT_FOUND_ERROR))
        .when(camundaRest)
        .customCompleteTask(Mockito.any(), Mockito.any());

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(
        WorkFlowVariables.HANDLER_DETAILS_KEY.getName(),
        "{\"actionName\":\"wasCustomTaskHandler\"}");
    modelAttributes.put(WorkFlowVariables.EVENTS.getName(), "[\"completed\",\"update\"]");

    ActivityDetail activityDetail =
        ActivityDetail.builder()
            .activityId("actId")
            .activityType("serviceTask")
            .type(TaskType.HUMAN_TASK)
            .activityName("actName")
            .attributes(
                ObjectConverter.toJson(
                    WorkflowActivityAttributes.builder().modelAttributes(modelAttributes).build()))
            .build();

    ActivityProgressDetails activityProgressDetails =
        ActivityProgressDetails.builder()
            .id("ext1")
            .activityDefinitionDetail(activityDetail)
            .status("update")
            .txnDetails(TransactionDetails.builder().txnId("txnId").build())
            .processDetails(
                ProcessDetails.builder()
                    .ownerId(1l)
                    .recordId("rec1")
                    .definitionDetails(
                        DefinitionDetails.builder()
                            .definitionId("defId")
                            .definitionName("defName")
                            .recordType(RecordType.ENGAGEMENT)
                            .version(1)
                            .build())
                    .build())
            .build();

    Mockito.when(activityProgressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(activityProgressDetails));

    Mockito.when(
            stateTransitionService.isStateTransitionEnabled(
                Mockito.any(ActivityProgressDetails.class), Mockito.anyString()))
        .thenReturn(true);

    Mockito.doThrow(
            new WorkflowGeneralException(WorkflowError.EXTERNAL_TASK_DETAILS_NOT_FOUND_ERROR))
        .when(stateTransitionService)
        .publishCompleteEvent(
            Mockito.any(ActivityProgressDetails.class),
            Mockito.anyMap(),
            Mockito.anyString(),
            Mockito.any(Long.class));

    handler.completeTask(task, headers);
  }

  @Test(expected = WorkflowRetriableException.class)
  public void testExecuteCamundaExceptionRetryable() throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.SUCCESS.getStatus())
            .variables(Collections.emptyMap())
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId:workerId");

    Mockito.doThrow(new WorkflowRetriableException(WorkflowError.EXTERNAL_TASK_HANDLER_ERROR))
        .when(camundaRest)
        .completeTask(Mockito.any(), Mockito.any());
    handler.completeTask(task, headers);

    Mockito.verify(camundaRest, never()).failureTask(Mockito.any(), Mockito.eq("taskId"));
  }

  @Test(expected = WorkflowRetriableException.class)
  public void testExecuteCamundaExceptionRetryableWithWorkerIdEmpty() throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.SUCCESS.getStatus())
            .variables(Collections.emptyMap())
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId: ");

    Mockito.doThrow(new WorkflowRetriableException(WorkflowError.EXTERNAL_TASK_HANDLER_ERROR))
        .when(camundaRest)
        .customCompleteTask(Mockito.any(), Mockito.any());
    handler.completeTask(task, headers);

    Mockito.verify(camundaRest, never()).failureTask(Mockito.any(), Mockito.eq("taskId"));
  }

	@Test(expected = WorkflowRetriableException.class)
	public void testExecuteCamundaExceptionRetryableBlankWorkerId() throws Exception {
		ExternalTaskCompleted task =
				ExternalTaskCompleted.builder()
						.status(ExternalTaskStatus.SUCCESS.getStatus())
						.variables(Collections.emptyMap())
						.build();

		Map<String, String> headers = new HashMap<>();
		headers.put(EventHeaderConstants.ENTITY_ID, "taskId: ");

		Mockito.doThrow(new WorkflowRetriableException(WorkflowError.EXTERNAL_TASK_HANDLER_ERROR))
				.when(camundaRest)
				.customCompleteTask(Mockito.any(), Mockito.any());
		handler.completeTask(task, headers);

		Mockito.verify(camundaRest, never()).failureTask(Mockito.any(), Mockito.eq("taskId"));
	}

  @Test(expected = WorkflowGeneralException.class)
  public void testExecuteCamundaExceptionOnFailure() throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.FAILED.getStatus())
            .errorMessage("errorMessage")
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId:workerId");
    Mockito.doThrow(new WorkflowGeneralException(WorkflowError.CAMUNDA_COMPLETE_TASK_FAILED))
        .when(camundaRest)
        .failureTask(Mockito.any(), Mockito.any());

    handler.invokeFailure(task, headers);
    Mockito.verify(camundaRest).failureTask(Mockito.any(), Mockito.eq("taskId"));
  }

  @Test
  public void testHandleFailure_success() throws Exception {
    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId:workerId");

    handler.handleFailure(headers, new WorkflowRetriableException(WorkflowError.EVENT_RETRIES_FAILED));
    Mockito.verify(camundaRest).failureTask(Mockito.any(), Mockito.eq("taskId"));
  }


  @Test
	public void getDefaultFailure_WorkflowGeneralException_NoWorkflowError() throws Exception {
		ExternalTaskFailure failure = (ExternalTaskFailure) ReflectionTestUtils
				.invokeMethod(handler, "getDefaultFailure", "w1",
						new WorkflowGeneralException(new RuntimeException("")));
		Assert.assertEquals("Exception while trying to close task. Creating incident",
				failure.getErrorMessage());
	}

	@Test
	public void getDefaultFailure_WorkflowError() throws Exception {
		ExternalTaskFailure failure = (ExternalTaskFailure) ReflectionTestUtils
				.invokeMethod(handler, "getDefaultFailure", "w1",
						new WorkflowRetriableException(WorkflowError.EVENT_RETRIES_FAILED));
		Assert.assertEquals(WorkflowError.EVENT_RETRIES_FAILED.name(),
				failure.getErrorMessage());
	}


	@Test
	public void getDefaultFailure_NotWorkflowError() throws Exception {
		ExternalTaskFailure failure = (ExternalTaskFailure) ReflectionTestUtils
				.invokeMethod(handler, "getDefaultFailure", "w1",
						new RuntimeException(""));
		Assert.assertEquals("Exception while trying to close task. Creating incident",
				failure.getErrorMessage());
	}


  @Test(expected = WorkflowGeneralException.class)
  public void testHandleFailure_failure() throws Exception {
    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId:workerId");
    Mockito.doThrow(new WorkflowGeneralException(WorkflowError.CAMUNDA_FAILURE_TASK_FAILED))
            .when(camundaRest)
            .failureTask(Mockito.any(), Mockito.any());

    handler.handleFailure(headers, new WorkflowRetriableException(WorkflowError.EVENT_RETRIES_FAILED));
    Mockito.verify(camundaRest).failureTask(Mockito.any(), Mockito.eq("taskId"));
  }

	@Test
	public void updateStatus_success() {
		Map<String, String> headers = new HashMap<>();
		headers.put(EventHeaderConstants.ENTITY_ID,
				"37378963-e59b-11eb-9e2e-acde48001122:INTUL18a26c1bd90546d28-ddf2-4062-b6f7-df0d0492bd11");
		headers.put(EventHeaderConstants.INTUIT_TID, UUID.randomUUID().toString());

		String externalTaskDtlStr = "{\"activityId\":\"Activity_1\",\"activityInstanceId\":\"Activity_1:37369efe-e59b-11eb-9e2e-acde48001122\",\"errorMessage\":null,\"executionId\":\"373677ed-e59b-11eb-9e2e-acde48001122\",\"id\":\"37378963-e59b-11eb-9e2e-acde48001122\",\"lockExpirationTime\":\"2021-07-16T02:04:14.348+0530\",\"processDefinitionId\":\"001e9fb5-e2ca-11eb-9f49-fe807654b1d6\",\"processDefinitionKey\":\"invoice_Process_SystemDef_Test_2\",\"processDefinitionVersionTag\":\"1\",\"processInstanceId\":\"37331c79-e59b-11eb-9e2e-acde48001122\",\"retries\":null,\"suspended\":false,\"workerId\":\"INTUL18a26c1bd05991dd5-a995-406b-afd4-0509116e30db\",\"topicName\":\"manishs-local\",\"tenantId\":null,\"priority\":0,\"businessKey\":\"9130354986673846\"}";
		ExternalTaskDetail externalTaskDtl = ObjectConverter.fromJson(externalTaskDtlStr,
				ExternalTaskDetail.class);

		Mockito.when(camundaRest.getExtenalTaskDetails(Mockito.anyString()))
				.thenReturn(externalTaskDtl);

		Mockito.doNothing().when(camundaRest)
				.updateExecutionVariables(Mockito.any(CamundaUpdateRequest.class));

		Map<String, String> modelAttributes = new HashMap<>();
	    modelAttributes.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(),
	        "{\"actionName\":\"wasCustomTaskHandler\"}");
	    modelAttributes.put(WorkFlowVariables.EVENTS.getName(),
	        "[\"completed\",\"update\"]");

	    ActivityDetail activityDetail = ActivityDetail.builder()
	        .activityId("actId").activityType("serviceTask").type(TaskType.HUMAN_TASK)
	        .activityName("actName")
	        .attributes(ObjectConverter
	            .toJson(WorkflowActivityAttributes.builder().modelAttributes(modelAttributes).build()))
	        .build();

	    ActivityProgressDetails activityProgressDetails = ActivityProgressDetails.builder()
	        .id("ext1")
	        .activityDefinitionDetail(activityDetail)
	        .status("update")
	        .txnDetails(TransactionDetails.builder().txnId("txnId").build())
	        .processDetails(ProcessDetails.builder()
	            .ownerId(1l).recordId("rec1")
	            .definitionDetails(DefinitionDetails.builder().definitionId("defId")
	                .definitionName("defName")
	                .recordType(RecordType.ENGAGEMENT)
	                .version(1).build())
	            .build())
	        .build();

	    Mockito.when(activityProgressDetailRepo.findById(Mockito.anyString()))
		.thenReturn(Optional.of(activityProgressDetails));

	    Mockito.when(stateTransitionService.isStateTransitionEnabled(Mockito.any(ActivityProgressDetails.class),
			Mockito.anyString())).thenReturn(true);

		Mockito.doNothing().when(stateTransitionService).publishUpdateEvent(Mockito.anyMap(),
				Mockito.any(ExternalTaskDetail.class), Mockito.any(ActivityProgressDetails.class), Mockito.any(Long.class));

		ExternalTaskCompleted event = ExternalTaskCompleted.builder().status("in-process").build();
		handler.updateStatus(event, headers);

		Mockito.verify(camundaRest, Mockito.times(1)).getExtenalTaskDetails(Mockito.anyString());
		Mockito.verify(camundaRest, Mockito.times(1))
				.updateExecutionVariables(Mockito.any(CamundaUpdateRequest.class));
		Mockito.verify(stateTransitionService, Mockito.times(1)).publishUpdateEvent(Mockito.anyMap(),
				Mockito.any(ExternalTaskDetail.class), Mockito.any(ActivityProgressDetails.class),
				Mockito.any(Long.class));
	}

	@Test
	public void updateStatus_publishStateTransition_success() {
		Map<String, String> headers = new HashMap<>();
		headers.put(EventHeaderConstants.ENTITY_ID,
				"37378963-e59b-11eb-9e2e-acde48001122:INTUL18a26c1bd90546d28-ddf2-4062-b6f7-df0d0492bd11");
		headers.put(EventHeaderConstants.INTUIT_TID, UUID.randomUUID().toString());

		String externalTaskDtlStr = "{\"activityId\":\"Activity_1\",\"activityInstanceId\":\"Activity_1:37369efe-e59b-11eb-9e2e-acde48001122\",\"errorMessage\":null,\"executionId\":\"373677ed-e59b-11eb-9e2e-acde48001122\",\"id\":\"37378963-e59b-11eb-9e2e-acde48001122\",\"lockExpirationTime\":\"2021-07-16T02:04:14.348+0530\",\"processDefinitionId\":\"001e9fb5-e2ca-11eb-9f49-fe807654b1d6\",\"processDefinitionKey\":\"invoice_Process_SystemDef_Test_2\",\"processDefinitionVersionTag\":\"1\",\"processInstanceId\":\"37331c79-e59b-11eb-9e2e-acde48001122\",\"retries\":null,\"suspended\":false,\"workerId\":\"INTUL18a26c1bd05991dd5-a995-406b-afd4-0509116e30db\",\"topicName\":\"manishs-local\",\"tenantId\":null,\"priority\":0,\"businessKey\":\"9130354986673846\"}";
		ExternalTaskDetail externalTaskDtl = ObjectConverter.fromJson(externalTaskDtlStr,
				ExternalTaskDetail.class);

		Mockito.when(camundaRest.getExtenalTaskDetails(Mockito.anyString()))
				.thenReturn(externalTaskDtl);

		Mockito.doNothing().when(camundaRest)
				.updateExecutionVariables(Mockito.any(CamundaUpdateRequest.class));

		Mockito.doNothing().when(stateTransitionService).publishUpdateEvent(Mockito.anyMap(),
				Mockito.any(ExternalTaskDetail.class), Mockito.any(ActivityProgressDetails.class),
				Mockito.any(Long.class));

	   Map<String, String> modelAttributes = new HashMap<>();
	    modelAttributes.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(),
	        "{\"actionName\":\"wasCustomTaskHandler\"}");
	    modelAttributes.put(WorkFlowVariables.EVENTS.getName(),
	        "[\"completed\",\"update\"]");

	    ActivityDetail activityDetail = ActivityDetail.builder()
	        .activityId("actId").activityType("serviceTask").type(TaskType.HUMAN_TASK)
	        .activityName("actName")
	        .attributes(ObjectConverter
	            .toJson(WorkflowActivityAttributes.builder().modelAttributes(modelAttributes).build()))
	        .build();

	    ActivityProgressDetails activityProgressDetails = ActivityProgressDetails.builder()
	        .id("ext1")
	        .activityDefinitionDetail(activityDetail)
	        .status("in-process")
	        .txnDetails(TransactionDetails.builder().txnId("txnId").build())
	        .processDetails(ProcessDetails.builder()
	            .ownerId(1l).recordId("rec1")
	            .definitionDetails(DefinitionDetails.builder().definitionId("defId")
	                .definitionName("defName")
	                .recordType(RecordType.ENGAGEMENT)
	                .version(1).build())
	            .build())
	        .build();

		Mockito.when(activityProgressDetailRepo.findById(Mockito.anyString()))
			.thenReturn(Optional.of(activityProgressDetails));

		Mockito.when(stateTransitionService.isStateTransitionEnabled(Mockito.any(ActivityProgressDetails.class),
				Mockito.anyString())).thenReturn(true);

		ExternalTaskCompleted event = ExternalTaskCompleted.builder().status("in-process").build();
		handler.updateStatus(event, headers);

		Mockito.verify(stateTransitionService, Mockito.times(1)).isStateTransitionEnabled(Mockito.any(ActivityProgressDetails.class),
				Mockito.anyString());
		Mockito.verify(activityProgressDetailRepo, Mockito.times(1)).findById(Mockito.anyString());

		Mockito.verify(camundaRest, Mockito.times(1)).getExtenalTaskDetails(Mockito.anyString());
		Mockito.verify(camundaRest, Mockito.times(1))
				.updateExecutionVariables(Mockito.any(CamundaUpdateRequest.class));

		Mockito.verify(stateTransitionService, Mockito.times(1)).publishUpdateEvent(Mockito.anyMap(),
				Mockito.any(ExternalTaskDetail.class), Mockito.any(ActivityProgressDetails.class), Mockito.any(Long.class));
	}


	@Test
	public void updateStatus_publishStateTransition_EventValidationFail() {
		Map<String, String> headers = new HashMap<>();
		headers.put(EventHeaderConstants.ENTITY_ID,
				"37378963-e59b-11eb-9e2e-acde48001122:INTUL18a26c1bd90546d28-ddf2-4062-b6f7-df0d0492bd11");
		headers.put(EventHeaderConstants.INTUIT_TID, UUID.randomUUID().toString());

		String externalTaskDtlStr = "{\"activityId\":\"Activity_1\",\"activityInstanceId\":\"Activity_1:37369efe-e59b-11eb-9e2e-acde48001122\",\"errorMessage\":null,\"executionId\":\"373677ed-e59b-11eb-9e2e-acde48001122\",\"id\":\"37378963-e59b-11eb-9e2e-acde48001122\",\"lockExpirationTime\":\"2021-07-16T02:04:14.348+0530\",\"processDefinitionId\":\"001e9fb5-e2ca-11eb-9f49-fe807654b1d6\",\"processDefinitionKey\":\"invoice_Process_SystemDef_Test_2\",\"processDefinitionVersionTag\":\"1\",\"processInstanceId\":\"37331c79-e59b-11eb-9e2e-acde48001122\",\"retries\":null,\"suspended\":false,\"workerId\":\"INTUL18a26c1bd05991dd5-a995-406b-afd4-0509116e30db\",\"topicName\":\"manishs-local\",\"tenantId\":null,\"priority\":0,\"businessKey\":\"9130354986673846\"}";
		ExternalTaskDetail externalTaskDtl = ObjectConverter.fromJson(externalTaskDtlStr,
				ExternalTaskDetail.class);

		Mockito.when(camundaRest.getExtenalTaskDetails(Mockito.anyString()))
				.thenReturn(externalTaskDtl);

		Mockito.doNothing().when(camundaRest)
				.updateExecutionVariables(Mockito.any(CamundaUpdateRequest.class));


	   Map<String, String> modelAttributes = new HashMap<>();
	    modelAttributes.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(),
	        "{\"actionName\":\"wasCustomTaskHandler\"}");
	    modelAttributes.put(WorkFlowVariables.EVENTS.getName(),
	        "[\"completed\",\"update\"]");

	    ActivityDetail activityDetail = ActivityDetail.builder()
	        .activityId("actId").activityType("serviceTask").type(TaskType.HUMAN_TASK)
	        .activityName("actName")
	        .attributes(ObjectConverter
	            .toJson(WorkflowActivityAttributes.builder().modelAttributes(modelAttributes).build()))
	        .build();

	    ActivityProgressDetails activityProgressDetails = ActivityProgressDetails.builder()
	        .id("ext1")
	        .activityDefinitionDetail(activityDetail)
	        .status("in-process")
	        .txnDetails(TransactionDetails.builder().txnId("txnId").build())
	        .processDetails(ProcessDetails.builder()
	            .ownerId(1l).recordId("rec1")
	            .definitionDetails(DefinitionDetails.builder().definitionId("defId")
	                .definitionName("defName")
	                .recordType(RecordType.ENGAGEMENT)
	                .version(1).build())
	            .build())
	        .build();

		Mockito.when(activityProgressDetailRepo.findById(Mockito.anyString()))
			.thenReturn(Optional.of(activityProgressDetails));

		Mockito.when(stateTransitionService.isStateTransitionEnabled(Mockito.any(ActivityProgressDetails.class),
				Mockito.anyString())).thenReturn(false);

		ExternalTaskCompleted event = ExternalTaskCompleted.builder().status("in-process").build();
		handler.updateStatus(event, headers);

		Mockito.verify(stateTransitionService, Mockito.times(1)).isStateTransitionEnabled(Mockito.any(ActivityProgressDetails.class),
				Mockito.anyString());
		Mockito.verify(activityProgressDetailRepo, Mockito.times(1)).findById(Mockito.anyString());

		Mockito.verify(camundaRest, Mockito.times(1)).getExtenalTaskDetails(Mockito.anyString());
		Mockito.verify(camundaRest, Mockito.times(1))
				.updateExecutionVariables(Mockito.any(CamundaUpdateRequest.class));

		Mockito.verify(stateTransitionService, Mockito.never()).publishUpdateEvent(Mockito.anyMap(),
				Mockito.any(ExternalTaskDetail.class), Mockito.any(ActivityProgressDetails.class), Mockito.any(Long.class));
	}

	@Test
	public void updateStatus_NO_ActivityProgressDetailFound() {
		Map<String, String> headers = new HashMap<>();
		headers.put(EventHeaderConstants.ENTITY_ID,
				"37378963-e59b-11eb-9e2e-acde48001122:INTUL18a26c1bd90546d28-ddf2-4062-b6f7-df0d0492bd11");
		headers.put(EventHeaderConstants.INTUIT_TID, UUID.randomUUID().toString());

		String externalTaskDtlStr = "{\"activityId\":\"Activity_1\",\"activityInstanceId\":\"Activity_1:37369efe-e59b-11eb-9e2e-acde48001122\",\"errorMessage\":null,\"executionId\":\"373677ed-e59b-11eb-9e2e-acde48001122\",\"id\":\"37378963-e59b-11eb-9e2e-acde48001122\",\"lockExpirationTime\":\"2021-07-16T02:04:14.348+0530\",\"processDefinitionId\":\"001e9fb5-e2ca-11eb-9f49-fe807654b1d6\",\"processDefinitionKey\":\"invoice_Process_SystemDef_Test_2\",\"processDefinitionVersionTag\":\"1\",\"processInstanceId\":\"37331c79-e59b-11eb-9e2e-acde48001122\",\"retries\":null,\"suspended\":false,\"workerId\":\"INTUL18a26c1bd05991dd5-a995-406b-afd4-0509116e30db\",\"topicName\":\"manishs-local\",\"tenantId\":null,\"priority\":0,\"businessKey\":\"9130354986673846\"}";
		ExternalTaskDetail externalTaskDtl = ObjectConverter.fromJson(externalTaskDtlStr,
				ExternalTaskDetail.class);

		Mockito.when(camundaRest.getExtenalTaskDetails(Mockito.anyString()))
				.thenReturn(externalTaskDtl);

		Mockito.doNothing().when(camundaRest)
				.updateExecutionVariables(Mockito.any(CamundaUpdateRequest.class));

		Mockito.when(activityProgressDetailRepo.findById(Mockito.anyString()))
			.thenReturn(Optional.ofNullable(null));

		ExternalTaskCompleted event = ExternalTaskCompleted.builder().status("in-process").build();
		handler.updateStatus(event, headers);

		Mockito.verify(stateTransitionService, Mockito.never()).isStateTransitionEnabled(Mockito.any(ActivityProgressDetails.class),
				Mockito.anyString());
		Mockito.verify(activityProgressDetailRepo, Mockito.times(1)).findById(Mockito.anyString());

		Mockito.verify(camundaRest, Mockito.times(1)).getExtenalTaskDetails(Mockito.anyString());
		Mockito.verify(camundaRest, Mockito.times(1))
				.updateExecutionVariables(Mockito.any(CamundaUpdateRequest.class));

		Mockito.verify(stateTransitionService, Mockito.never()).publishUpdateEvent(Mockito.anyMap(),
				Mockito.any(ExternalTaskDetail.class), Mockito.any(ActivityProgressDetails.class),
				Mockito.any(Long.class));
	}

	@Test
	public void updateStatus_noEnum_success() {
		Map<String, String> headers = new HashMap<>();
		headers.put(EventHeaderConstants.ENTITY_ID,
				"37378963-e59b-11eb-9e2e-acde48001122:INTUL18a26c1bd90546d28-ddf2-4062-b6f7-df0d0492bd11");
		headers.put(EventHeaderConstants.INTUIT_TID, UUID.randomUUID().toString());

		String externalTaskDtlStr = "{\"activityId\":\"Activity_1\",\"activityInstanceId\":\"Activity_1:37369efe-e59b-11eb-9e2e-acde48001122\",\"errorMessage\":null,\"executionId\":\"373677ed-e59b-11eb-9e2e-acde48001122\",\"id\":\"37378963-e59b-11eb-9e2e-acde48001122\",\"lockExpirationTime\":\"2021-07-16T02:04:14.348+0530\",\"processDefinitionId\":\"001e9fb5-e2ca-11eb-9f49-fe807654b1d6\",\"processDefinitionKey\":\"invoice_Process_SystemDef_Test_2\",\"processDefinitionVersionTag\":\"1\",\"processInstanceId\":\"37331c79-e59b-11eb-9e2e-acde48001122\",\"retries\":null,\"suspended\":false,\"workerId\":\"INTUL18a26c1bd05991dd5-a995-406b-afd4-0509116e30db\",\"topicName\":\"manishs-local\",\"tenantId\":null,\"priority\":0,\"businessKey\":\"9130354986673846\"}";
		ExternalTaskDetail externalTaskDtl = ObjectConverter.fromJson(externalTaskDtlStr,
				ExternalTaskDetail.class);

		Mockito.when(camundaRest.getExtenalTaskDetails(Mockito.anyString()))
				.thenReturn(externalTaskDtl);

		Mockito.doNothing().when(camundaRest)
				.updateExecutionVariables(Mockito.any(CamundaUpdateRequest.class));


		Map<String, String> modelAttributes = new HashMap<>();
	    modelAttributes.put(WorkFlowVariables.HANDLER_DETAILS_KEY.getName(),
	        "{\"actionName\":\"wasCustomTaskHandler\"}");
	    modelAttributes.put(WorkFlowVariables.EVENTS.getName(),
	        "[\"completed\",\"update\"]");

	    ActivityDetail activityDetail = ActivityDetail.builder()
	        .activityId("actId").activityType("serviceTask").type(TaskType.HUMAN_TASK)
	        .activityName("actName")
	        .attributes(ObjectConverter
	            .toJson(WorkflowActivityAttributes.builder().modelAttributes(modelAttributes).build()))
	        .build();

	    ActivityProgressDetails activityProgressDetails = ActivityProgressDetails.builder()
	        .id("ext1")
	        .activityDefinitionDetail(activityDetail)
	        .status("update")
	        .txnDetails(TransactionDetails.builder().txnId("txnId").build())
	        .processDetails(ProcessDetails.builder()
	            .ownerId(1l).recordId("rec1")
	            .definitionDetails(DefinitionDetails.builder().definitionId("defId")
	                .definitionName("defName")
	                .recordType(RecordType.ENGAGEMENT)
	                .version(1).build())
	            .build())
	        .build();

	    Mockito.when(activityProgressDetailRepo.findById(Mockito.anyString()))
		.thenReturn(Optional.of(activityProgressDetails));

	    Mockito.when(stateTransitionService.isStateTransitionEnabled(Mockito.any(ActivityProgressDetails.class),
			Mockito.anyString())).thenReturn(true);

		Mockito.doNothing().when(stateTransitionService).publishUpdateEvent(Mockito.anyMap(),
				Mockito.any(ExternalTaskDetail.class), Mockito.any(ActivityProgressDetails.class),
				Mockito.any(Long.class));

		ExternalTaskCompleted event = ExternalTaskCompleted.builder().status("in-progress").build();
		handler.updateStatus(event, headers);

		Mockito.verify(camundaRest, Mockito.times(1)).getExtenalTaskDetails(Mockito.anyString());
		Mockito.verify(camundaRest, Mockito.times(1))
				.updateExecutionVariables(Mockito.any(CamundaUpdateRequest.class));



		Mockito.verify(stateTransitionService, Mockito.times(1)).publishUpdateEvent(Mockito.anyMap(),
				Mockito.any(ExternalTaskDetail.class), Mockito.any(ActivityProgressDetails.class),
				Mockito.any(Long.class));

	}

	@Test(expected = WorkflowGeneralException.class)
	public void updateStatusBlocked_failure_externalTaskNotFound() {

		Map<String, String> headers = new HashMap<>();
		headers.put(EventHeaderConstants.ENTITY_ID,
				"37378963-e59b-11eb-9e2e-acde48001122:INTUL18a26c1bd90546d28-ddf2-4062-b6f7-df0d0492bd11");
		headers.put(EventHeaderConstants.INTUIT_TID, UUID.randomUUID().toString());

		Mockito.when(camundaRest.getExtenalTaskDetails(Mockito.anyString()))
				.thenThrow(
						new WorkflowGeneralException(WorkflowError.EXTERNAL_TASK_DETAILS_NOT_FOUND_ERROR));
		ExternalTaskCompleted event = ExternalTaskCompleted.builder().status("blocked").build();
		handler.updateStatus(event, headers);
	}

	@Test(expected = WorkflowGeneralException.class)
	public void updateStatusBlocked_failure_executionUpdateFailure() {

		Map<String, String> headers = new HashMap<>();
		headers.put(EventHeaderConstants.ENTITY_ID,
				"37378963-e59b-11eb-9e2e-acde48001122:INTUL18a26c1bd90546d28-ddf2-4062-b6f7-df0d0492bd11");
		headers.put(EventHeaderConstants.INTUIT_TID, UUID.randomUUID().toString());

		String externalTaskDtlStr = "{\"activityId\":\"Activity_1\",\"activityInstanceId\":\"Activity_1:37369efe-e59b-11eb-9e2e-acde48001122\",\"errorMessage\":null,\"executionId\":\"373677ed-e59b-11eb-9e2e-acde48001122\",\"id\":\"37378963-e59b-11eb-9e2e-acde48001122\",\"lockExpirationTime\":\"2021-07-16T02:04:14.348+0530\",\"processDefinitionId\":\"001e9fb5-e2ca-11eb-9f49-fe807654b1d6\",\"processDefinitionKey\":\"invoice_Process_SystemDef_Test_2\",\"processDefinitionVersionTag\":\"1\",\"processInstanceId\":\"37331c79-e59b-11eb-9e2e-acde48001122\",\"retries\":null,\"suspended\":false,\"workerId\":\"INTUL18a26c1bd05991dd5-a995-406b-afd4-0509116e30db\",\"topicName\":\"manishs-local\",\"tenantId\":null,\"priority\":0,\"businessKey\":\"9130354986673846\"}";
		ExternalTaskDetail externalTaskDtl = ObjectConverter.fromJson(externalTaskDtlStr,
				ExternalTaskDetail.class);

		Mockito.when(camundaRest.getExtenalTaskDetails(Mockito.anyString()))
				.thenReturn(externalTaskDtl);

		Mockito.doThrow(new WorkflowGeneralException(WorkflowError.UPDATE_EXECUTION_VARIABLE_FAILED))
				.when(camundaRest)
				.updateExecutionVariables(Mockito.any(CamundaUpdateRequest.class));
		ExternalTaskCompleted event = ExternalTaskCompleted.builder().status("blocked").build();
		handler.updateStatus(event, headers);

	}

	@Test(expected = WorkflowGeneralException.class)
	public void updateStatusBlocked_handleFailure_onlylogging() {
		Map<String, String> headers = new HashMap<>();
		headers.put(EventHeaderConstants.ENTITY_ID,
				"37378963-e59b-11eb-9e2e-acde48001122:INTUL18a26c1bd90546d28-ddf2-4062-b6f7-df0d0492bd11");
		headers.put(EventHeaderConstants.INTUIT_TID, UUID.randomUUID().toString());
		headers.put(WorkflowConstants.ACTIVITY_STATUS_VARIABLE, "blocked");
		Mockito.doNothing().when(metricLogger)
				.logErrorMetric(Mockito.any(MetricName.class), Mockito.any(Type.class),
						Mockito.any(Throwable.class));
	    Mockito.doThrow(new WorkflowGeneralException(WorkflowError.CAMUNDA_FAILURE_TASK_FAILED))
	            .when(camundaRest)
	            .failureTask(Mockito.any(), Mockito.anyString());

	    handler.handleFailure(headers, new WorkflowRetriableException(WorkflowError.UPDATE_EXECUTION_VARIABLE_FAILED));
	}


	@Test
	public void invokeComplete_metricLoggingAndFailureRestCall() {
		Map<String, String> headers = new HashMap<>();
		headers.put(EventHeaderConstants.ENTITY_ID,
				"37378963-e59b-11eb-9e2e-acde48001122:INTUL18a26c1bd90546d28-ddf2-4062-b6f7-df0d0492bd11");
		headers.put(EventHeaderConstants.INTUIT_TID, UUID.randomUUID().toString());
		Mockito.doNothing().when(camundaRest).failureTask(Mockito.any(), Mockito.any());
		Mockito.doNothing().when(metricLogger).logErrorMetric(Mockito.any(MetricName.class), Mockito.any(Type.class), Mockito.any(Throwable.class));
		handler.handleFailure(headers, new WorkflowGeneralException(WorkflowError.UPDATE_EXECUTION_VARIABLE_FAILED));
		Mockito.verify(camundaRest, Mockito.times(1)).failureTask(Mockito.any(), Mockito.any());
		Mockito.verify(metricLogger, Mockito.times(1)).logErrorMetric(Mockito.any(MetricName.class), Mockito.any(Type.class), Mockito.any(Throwable.class));

	}

	@Test
	public void test_updateStatusInCamunda() {
		Map<String, Object> localVariables = new HashMap<>();
		localVariables.put("abc", new HashMap<String, Object>());
		((Map)localVariables.get("abc")).put("type","string");
		((Map)localVariables.get("abc")).put("value","def");

		Mockito.doNothing().when(camundaRest)
			.updateExecutionVariables(updateRequestCaptor.capture());

		ReflectionTestUtils.invokeMethod(handler, "updateStatusInCamunda", ExternalTaskStatus.BLOCKED, "blocked",
				ExternalTaskDetail.builder().executionId("exec1").build(), ExternalTaskCompleted.builder().localVariables(localVariables).build());

		CamundaUpdateRequest updateRequest = updateRequestCaptor.getValue();
		Assert.assertTrue(updateRequest.getModifications().containsKey("abc"));
		Assert.assertTrue(updateRequest.getModifications().containsKey(WorkflowConstants.EXT_ACTIVITY_STATUS_VARIABLE));
	}

  @Test
  public void testExecuteCustomFailure() throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.FAILED.getStatus())
            .errorMessage("errorMessage")
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId: ");
    handler.invokeFailure(task, headers);
    Mockito.verify(camundaRest)
        .customFailureTask(taskFailureCaptor.capture(), Mockito.eq("taskId"));
    Assert.assertEquals("errorMessage", taskFailureCaptor.getValue().getErrorMessage());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testExecuteCamundaExceptionOnCompleteWithoutWorkerId() throws Exception {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.SUCCESS.getStatus())
            .variables(Collections.emptyMap())
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId: ");

    Mockito.doThrow(new WorkflowGeneralException(WorkflowError.CAMUNDA_COMPLETE_TASK_FAILED))
        .when(camundaRest)
        .customCompleteTask(Mockito.any(), Mockito.any());
    handler.completeTask(task, headers);

    Mockito.verify(camundaRest).customFailureTask(Mockito.any(), Mockito.eq("taskId"));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testExtendLockWithoutDuration() {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.EXTEND_LOCK.getStatus())
            .build();

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId:task-1234-37363");

    handler.extendLock(task, headers);
  }

  @Test
  public void testExtendLockSuccess() {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.EXTEND_LOCK.getStatus())
            .extendDuration(12345L)
            .build();
    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId:task-1234-37363");
    handler.extendLock(task, headers);
    ArgumentCaptor<ExtendExternalTask> argumentCaptor = ArgumentCaptor.forClass(ExtendExternalTask.class);
    Mockito.verify(camundaRest).extendLock(argumentCaptor.capture(), Mockito.eq("taskId"));
    Assert.assertEquals("task-1234-37363", argumentCaptor.getValue().getWorkerId());
    Assert.assertEquals(12345L, argumentCaptor.getValue().getNewDuration().longValue());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testExtendLockCamundaFailure() {
    ExternalTaskCompleted task =
        ExternalTaskCompleted.builder()
            .status(ExternalTaskStatus.EXTEND_LOCK.getStatus())
            .extendDuration(12345L)
            .build();
    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId:task-1234-37363");
    Mockito.doThrow(new WorkflowGeneralException(WorkflowError.CAMUNDA_TASK_NOT_FOUND))
        .when(camundaRest)
        .extendLock(Mockito.any(), Mockito.any());
    handler.extendLock(task, headers);
  }

  @Test
  public void test_invokeFailureWithRetry_RetriesNullInTaskResponse() {
    ExternalTaskCompleted task =
            ExternalTaskCompleted.builder()
                    .status(ExternalTaskStatus.FAIL_WITH_RETRY.getStatus())
                    .extendDuration(12345L)
                    .retries(2)
                    .build();
    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId:task-1234-37363");
    Mockito.when(camundaRest.getExtenalTaskDetails("taskId"))
            .thenReturn(ExternalTaskDetail.builder().retries(null).build());
    ArgumentCaptor<ExternalTaskFailure> captor = ArgumentCaptor.forClass(ExternalTaskFailure.class);
    Mockito.doNothing().when(camundaRest)
            .failureTask(captor.capture(), Mockito.eq("taskId"));
    handler.invokeFailureWithRetry(task, headers);
    Mockito.verify(camundaRest, Mockito.times(1)).failureTask(Mockito.any(), Mockito.eq("taskId"));
    Assert.assertEquals(2, captor.getValue().getRetries());
    Assert.assertEquals(12345L, captor.getValue().getRetryTimeout().longValue());
  }

  @Test
  public void test_invokeFailureWithRetry_RetriesPresentInTaskResponse() {
    ExternalTaskCompleted task =
            ExternalTaskCompleted.builder()
                    .status(ExternalTaskStatus.FAIL_WITH_RETRY.getStatus())
                    .extendDuration(12345L)
                    .retries(3)
                    .build();
    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId:task-1234-37363");
    Mockito.when(camundaRest.getExtenalTaskDetails("taskId"))
            .thenReturn(ExternalTaskDetail.builder().retries(2).build());
    ArgumentCaptor<ExternalTaskFailure> captor = ArgumentCaptor.forClass(ExternalTaskFailure.class);
    Mockito.doNothing().when(camundaRest)
            .failureTask(captor.capture(), Mockito.eq("taskId"));
    handler.invokeFailureWithRetry(task, headers);
    Mockito.verify(camundaRest, Mockito.times(1)).failureTask(Mockito.any(), Mockito.eq("taskId"));
    Assert.assertEquals(1, captor.getValue().getRetries());
    Assert.assertEquals(12345L, captor.getValue().getRetryTimeout().longValue());
  }

  @Test
  public void test_invokeFailureWithRetry_RetriesExhausted() {
    ExternalTaskCompleted task =
            ExternalTaskCompleted.builder()
                    .status(ExternalTaskStatus.FAIL_WITH_RETRY.getStatus())
                    .extendDuration(12345L)
                    .retries(3)
                    .build();
    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId:task-1234-37363");
    Mockito.when(camundaRest.getExtenalTaskDetails("taskId"))
            .thenReturn(ExternalTaskDetail.builder().retries(1).build());
    WorkflowGeneralException e = Assertions.assertThrows(WorkflowGeneralException.class,
            () -> handler.invokeFailureWithRetry(task, headers));
    Assert.assertEquals(WorkflowError.TASK_FAIL_WITH_RETRIES_EXHAUSTED, e.getWorkflowError());
    Mockito.verify(camundaRest, Mockito.times(0)).failureTask(Mockito.any(), Mockito.eq("taskId"));
  }
}
