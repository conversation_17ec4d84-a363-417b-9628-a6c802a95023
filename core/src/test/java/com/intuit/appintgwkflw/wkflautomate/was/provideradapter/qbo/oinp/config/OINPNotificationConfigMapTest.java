package com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.config;

import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.oinpqbo.bridge.NotificationGroup;
import org.junit.Assert;
import org.junit.Test;

public class OINPNotificationConfigMapTest {

  @Test
  public void testConfig(){
    OINPNotificationConfigMap oinpNotificationConfigMap = new OINPNotificationConfigMap();
    oinpNotificationConfigMap.initConfigMap();
    NotificationGroup notificationGroup = oinpNotificationConfigMap.getNotificationGroup("IsMobile");
    Assert.assertNotNull(notificationGroup);
    Assert.assertFalse(notificationGroup.getMappingAttributes().isEmpty());
  }
}
