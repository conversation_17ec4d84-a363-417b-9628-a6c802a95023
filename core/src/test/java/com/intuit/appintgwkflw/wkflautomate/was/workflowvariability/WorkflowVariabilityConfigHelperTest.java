package com.intuit.appintgwkflw.wkflautomate.was.workflowvariability;

import org.apache.commons.lang3.ObjectUtils;
import org.junit.Assert;
import org.junit.Test;

import java.util.Collections;

public class WorkflowVariabilityConfigHelperTest {

  @Test
  public void testConfig() {
    WorkflowVariabilityConfigHelper workflowVariabilityConfigHelper =
        new WorkflowVariabilityConfigHelper();
    workflowVariabilityConfigHelper.initConfigMap();
    WorkflowVariabilityRecordConfig workflowVariabilityRecords = workflowVariabilityConfigHelper.getOfferingWorkflowVariabilityRecords("BILLPAY_ELITE");
    Assert.assertNotNull(workflowVariabilityRecords);
    Assert.assertFalse(ObjectUtils.isEmpty(workflowVariabilityRecords));
    Assert.assertEquals(workflowVariabilityRecords.getInclusion().get(0).getEntityType(), "bill");
  }

  @Test
  public void testBudgetConfigForQBOAdvanced() {
    WorkflowVariabilityConfigHelper workflowVariabilityConfigHelper =
            new WorkflowVariabilityConfigHelper();
    workflowVariabilityConfigHelper.initConfigMap();
    WorkflowVariabilityRecordConfig workflowVariabilityRecords = workflowVariabilityConfigHelper.getOfferingWorkflowVariabilityRecords("QBO_ADVANCED");
    Assert.assertNotNull(workflowVariabilityRecords);
    Assert.assertFalse(ObjectUtils.isEmpty(workflowVariabilityRecords));
    Assert.assertEquals(workflowVariabilityRecords.getExclusion().get(1).getEntityType(), "budget");
    Assert.assertEquals(workflowVariabilityRecords.getExclusion().get(1).getWorkflowTypes(), Collections.singletonList("approval"));
  }

  @Test
  public void testBudgetConfigForQBOPlus() {
    WorkflowVariabilityConfigHelper workflowVariabilityConfigHelper =
            new WorkflowVariabilityConfigHelper();
    workflowVariabilityConfigHelper.initConfigMap();
    WorkflowVariabilityRecordConfig workflowVariabilityRecords = workflowVariabilityConfigHelper.getOfferingWorkflowVariabilityRecords("QBO_PLUS");
    Assert.assertNotNull(workflowVariabilityRecords);
    Assert.assertFalse(ObjectUtils.isEmpty(workflowVariabilityRecords));
    Assert.assertEquals(workflowVariabilityRecords.getExclusion().get(0).getEntityType(), "budget");
    Assert.assertEquals(workflowVariabilityRecords.getExclusion().get(0).getWorkflowTypes(), Collections.singletonList("approval"));
  }
}
