package com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowBatchNotificationTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowNotificationTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.accounts.Email;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.accounts.Persona;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.BatchNotificationTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.NotificationTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants.OinpBridgeConstants;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import java.util.HashMap;
import java.util.Map;
import java.util.SortedSet;
import java.util.TreeSet;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class OINPNotificationTaskTest {

  @Mock
  private WorkflowBatchNotificationTask workflowBatchNotificationTask;
  @Mock
  private WorkflowNotificationTask workflowNotificationTask;

  @Before
  public void init(){
    MockitoAnnotations.openMocks(this);
  }

  @Test
  public void testMobileNotification(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getTaskId()).thenReturn("task-id");

    Map<String, String> outputMap = new HashMap<>();
    outputMap.put(OinpBridgeConstants.TO_FIELD, "12345");

    State state = new State();
    state.addValue(OinpBridgeConstants.WORKER_ACTION_REQUEST, workerActionRequest);
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP, outputMap);
    state.addValue(OinpBridgeConstants.IS_MOBILE, true);
    state.addValue(OinpBridgeConstants.IS_EMAIL, false);
    state.addValue(OinpBridgeConstants.NOTIFICATION_NAME, "notificationName");
    state.addValue(OinpBridgeConstants.NOTIFICATION_DATA_TYPE, "notificationDataType");

    WorkflowTaskResponse workflowTaskResponse = WorkflowTaskResponse.builder().status(
        ActivityConstants.TASK_STATUS_CREATED).build();
    ArgumentCaptor<NotificationTask> notificationTaskArgumentCaptor = ArgumentCaptor.forClass(NotificationTask.class);
    Mockito.when(workflowNotificationTask.create(notificationTaskArgumentCaptor.capture())).thenReturn(workflowTaskResponse);

    state = new RxExecutionChain(state)
        .next(new OINPNotificationTask(workflowBatchNotificationTask, workflowNotificationTask))
        .execute();
    Assert.assertEquals(ActivityConstants.TASK_STATUS_CREATED, state.getValue(OinpBridgeConstants.NOTIFICATION_TASK_STATUS));
    Assert.assertEquals(12345, notificationTaskArgumentCaptor.getValue().getNotificationMetaData().getAuthId().longValue());
    Assert.assertTrue(notificationTaskArgumentCaptor.getValue().getNotificationData().containsKey(OinpBridgeConstants.TO_FIELD));
  }

  @Test
  public void testMobileNotification_Batch(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getTaskId()).thenReturn("task-id");

    Map<String, String> outputMap = new HashMap<>();
    outputMap.put(OinpBridgeConstants.TO_FIELD, "12345,123456");

    State state = new State();
    state.addValue(OinpBridgeConstants.WORKER_ACTION_REQUEST, workerActionRequest);
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP, outputMap);
    state.addValue(OinpBridgeConstants.IS_MOBILE, true);
    state.addValue(OinpBridgeConstants.IS_EMAIL, false);
    state.addValue(OinpBridgeConstants.NOTIFICATION_NAME, "notificationName");
    state.addValue(OinpBridgeConstants.NOTIFICATION_DATA_TYPE, "notificationDataType");

    WorkflowTaskResponse workflowTaskResponse = WorkflowTaskResponse.builder().status(
        ActivityConstants.TASK_STATUS_CREATED).build();
    ArgumentCaptor<BatchNotificationTask> notificationTaskArgumentCaptor = ArgumentCaptor.forClass(BatchNotificationTask.class);
    Mockito.when(workflowBatchNotificationTask.create(notificationTaskArgumentCaptor.capture())).thenReturn(workflowTaskResponse);

    state = new RxExecutionChain(state)
        .next(new OINPNotificationTask(workflowBatchNotificationTask, workflowNotificationTask))
        .execute();
    Assert.assertEquals(ActivityConstants.TASK_STATUS_CREATED, state.getValue(OinpBridgeConstants.NOTIFICATION_TASK_STATUS));
    Assert.assertEquals(2, notificationTaskArgumentCaptor.getValue().getNotificationTaskList().size());
    Assert.assertEquals(12345,
        notificationTaskArgumentCaptor.getValue()
            .getNotificationTaskList().get(0).getNotificationMetaData().getAuthId().longValue());
  }

  @Test
  public void testEmailNotification_withAuthId(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getTaskId()).thenReturn("task-id");

    Map<String, String> outputMap = new HashMap<>();
    outputMap.put(OinpBridgeConstants.TO_FIELD, "<EMAIL>");

    State state = new State();
    state.addValue(OinpBridgeConstants.WORKER_ACTION_REQUEST, workerActionRequest);
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP, outputMap);
    state.addValue(OinpBridgeConstants.IS_MOBILE, false);
    state.addValue(OinpBridgeConstants.IS_EMAIL, true);
    state.addValue(OinpBridgeConstants.CONSOLIDATE_NOTIFICATIONS, false);
    state.addValue(OinpBridgeConstants.NOTIFICATION_NAME, "notificationName");
    state.addValue(OinpBridgeConstants.NOTIFICATION_DATA_TYPE, "notificationDataType");

    SortedSet<String> authIds = new TreeSet<>();
    authIds.add("12345");
    state.addValue(OinpBridgeConstants.AUTH_IDS, authIds);

    WorkflowTaskResponse workflowTaskResponse = WorkflowTaskResponse.builder().status(
        ActivityConstants.TASK_STATUS_CREATED).build();
    ArgumentCaptor<NotificationTask> notificationTaskArgumentCaptor = ArgumentCaptor.forClass(NotificationTask.class);
    Mockito.when(workflowNotificationTask.create(notificationTaskArgumentCaptor.capture())).thenReturn(workflowTaskResponse);

    state = new RxExecutionChain(state)
        .next(new OINPNotificationTask(workflowBatchNotificationTask, workflowNotificationTask))
        .execute();
    Assert.assertEquals(ActivityConstants.TASK_STATUS_CREATED, state.getValue(OinpBridgeConstants.NOTIFICATION_TASK_STATUS));
    Assert.assertEquals(12345, notificationTaskArgumentCaptor.getValue().getNotificationMetaData().getAuthId().longValue());
    Assert.assertTrue(notificationTaskArgumentCaptor.getValue().getNotificationData().containsKey(OinpBridgeConstants.TO_FIELD));
    Assert.assertEquals("<EMAIL>", notificationTaskArgumentCaptor.getValue()
        .getNotificationData().get(OinpBridgeConstants.TO_FIELD));
  }

  @Test
  public void testBatchEmailNotification_withAuthId_consolidateNotificationsFalse(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getTaskId()).thenReturn("task-id");

    Persona persona1 = new Persona();
    persona1.setPersonaId("54321");
    persona1.setUserId("12345");
    Email email1 = new Email();
    email1.setEmailId("<EMAIL>");
    persona1.setEmail(email1);

    Persona persona2 = new Persona();
    persona2.setPersonaId("4321");
    persona2.setUserId("1234");
    Email email2 = new Email();
    email2.setEmailId("<EMAIL>");
    persona2.setEmail(email2);

    Map<String, Persona> iusResponseMap = new HashMap<>(){{
      put("12345", persona1);
      put("1234", persona2);
    }};

    Map<String, String> outputMap = new HashMap<>();
    outputMap.put(OinpBridgeConstants.TO_FIELD, "<EMAIL>,<EMAIL>");

    State state = new State();
    state.addValue(OinpBridgeConstants.WORKER_ACTION_REQUEST, workerActionRequest);
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP, outputMap);
    state.addValue(OinpBridgeConstants.IS_MOBILE, false);
    state.addValue(OinpBridgeConstants.IS_EMAIL, true);
    state.addValue(OinpBridgeConstants.CONSOLIDATE_NOTIFICATIONS, false);
    state.addValue(OinpBridgeConstants.NOTIFICATION_NAME, "notificationName");
    state.addValue(OinpBridgeConstants.NOTIFICATION_DATA_TYPE, "notificationDataType");
    state.addValue(OinpBridgeConstants.IUS_AUTHID_PERSONA_MAP, iusResponseMap);

    SortedSet<String> authIds = new TreeSet<>();
    authIds.add("12345");
    authIds.add("1234");
    state.addValue(OinpBridgeConstants.AUTH_IDS, authIds);

    WorkflowTaskResponse workflowTaskResponse = WorkflowTaskResponse.builder().status(
        ActivityConstants.TASK_STATUS_CREATED).build();
    ArgumentCaptor<BatchNotificationTask> notificationTaskArgumentCaptor = ArgumentCaptor.forClass(BatchNotificationTask.class);
    Mockito.when(workflowBatchNotificationTask.create(notificationTaskArgumentCaptor.capture())).thenReturn(workflowTaskResponse);

    state = new RxExecutionChain(state)
        .next(new OINPNotificationTask(workflowBatchNotificationTask, workflowNotificationTask))
        .execute();
    Assert.assertEquals(ActivityConstants.TASK_STATUS_CREATED, state.getValue(OinpBridgeConstants.NOTIFICATION_TASK_STATUS));
    Assert.assertEquals(2, notificationTaskArgumentCaptor.getValue().getNotificationTaskList().size());
    Assert.assertEquals(1234,
        notificationTaskArgumentCaptor.getValue()
            .getNotificationTaskList().get(0).getNotificationMetaData().getAuthId().longValue());
    Assert.assertTrue(notificationTaskArgumentCaptor.getValue()
        .getNotificationTaskList().get(0).getNotificationData().containsKey(OinpBridgeConstants.TO_FIELD));
    Assert.assertEquals("<EMAIL>", notificationTaskArgumentCaptor.getValue()
        .getNotificationTaskList().get(0).getNotificationData().get(OinpBridgeConstants.TO_FIELD));
  }

  @Test
  public void testBatchEmailNotification_withAuthId_consolidateNotificationsTrue(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getTaskId()).thenReturn("task-id");

    Map<String, String> outputMap = new HashMap<>();
    outputMap.put(OinpBridgeConstants.TO_FIELD, "<EMAIL>,<EMAIL>");

    Persona persona1 = new Persona();
    persona1.setPersonaId("54321");
    persona1.setUserId("12345");
    Email email1 = new Email();
    email1.setEmailId("<EMAIL>");
    persona1.setEmail(email1);

    Persona persona2 = new Persona();
    persona2.setPersonaId("4321");
    persona2.setUserId("1234");
    Email email2 = new Email();
    email2.setEmailId("<EMAIL>");
    persona2.setEmail(email2);

    Map<String, Persona> iusResponseMap = new HashMap<>(){{
      put("12345", persona1);
      put("1234", persona2);
    }};

    State state = new State();
    state.addValue(OinpBridgeConstants.WORKER_ACTION_REQUEST, workerActionRequest);
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP, outputMap);
    state.addValue(OinpBridgeConstants.IS_MOBILE, false);
    state.addValue(OinpBridgeConstants.IS_EMAIL, true);
    state.addValue(OinpBridgeConstants.CONSOLIDATE_NOTIFICATIONS, true);
    state.addValue(OinpBridgeConstants.NOTIFICATION_NAME, "notificationName");
    state.addValue(OinpBridgeConstants.NOTIFICATION_DATA_TYPE, "notificationDataType");
    state.addValue(OinpBridgeConstants.IUS_AUTHID_PERSONA_MAP, iusResponseMap);

    SortedSet<String> authIds = new TreeSet<>();
    authIds.add("12345");
    authIds.add("1234");
    state.addValue(OinpBridgeConstants.AUTH_IDS, authIds);

    WorkflowTaskResponse workflowTaskResponse = WorkflowTaskResponse.builder().status(
        ActivityConstants.TASK_STATUS_CREATED).build();
    ArgumentCaptor<BatchNotificationTask> notificationTaskArgumentCaptor = ArgumentCaptor.forClass(BatchNotificationTask.class);
    Mockito.when(workflowBatchNotificationTask.create(notificationTaskArgumentCaptor.capture())).thenReturn(workflowTaskResponse);

    state = new RxExecutionChain(state)
        .next(new OINPNotificationTask(workflowBatchNotificationTask, workflowNotificationTask))
        .execute();
    Assert.assertEquals(ActivityConstants.TASK_STATUS_CREATED, state.getValue(OinpBridgeConstants.NOTIFICATION_TASK_STATUS));
    Assert.assertEquals(2, notificationTaskArgumentCaptor.getValue().getNotificationTaskList().size());
    Assert.assertEquals(4321,
        notificationTaskArgumentCaptor.getValue()
            .getNotificationTaskList().get(0).getNotificationMetaData().getAuthId().longValue());
    Assert.assertTrue(notificationTaskArgumentCaptor.getValue()
        .getNotificationTaskList().get(0).getNotificationData().containsKey(OinpBridgeConstants.TO_FIELD));
    Assert.assertEquals("<EMAIL>", notificationTaskArgumentCaptor.getValue()
        .getNotificationTaskList().get(0).getNotificationData().get(OinpBridgeConstants.TO_FIELD));
    Assert.assertEquals(54321,
        notificationTaskArgumentCaptor.getValue()
            .getNotificationTaskList().get(1).getNotificationMetaData().getAuthId().longValue());
    Assert.assertTrue(notificationTaskArgumentCaptor.getValue()
        .getNotificationTaskList().get(1).getNotificationData().containsKey(OinpBridgeConstants.TO_FIELD));
    Assert.assertEquals("<EMAIL>", notificationTaskArgumentCaptor.getValue()
        .getNotificationTaskList().get(1).getNotificationData().get(OinpBridgeConstants.TO_FIELD));
  }

  @Test
  public void testEmailNotification_withNoAuthId(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getTaskId()).thenReturn("task-id");

    Map<String, String> outputMap = new HashMap<>();
    outputMap.put(OinpBridgeConstants.TO_FIELD, "<EMAIL>,<EMAIL>");

    State state = new State();
    state.addValue(OinpBridgeConstants.WORKER_ACTION_REQUEST, workerActionRequest);
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP, outputMap);
    state.addValue(OinpBridgeConstants.IS_MOBILE, false);
    state.addValue(OinpBridgeConstants.IS_EMAIL, true);
    state.addValue(OinpBridgeConstants.NOTIFICATION_NAME, "notificationName");
    state.addValue(OinpBridgeConstants.NOTIFICATION_DATA_TYPE, "notificationDataType");

    SortedSet<String> authIds = new TreeSet<>();
    state.addValue(OinpBridgeConstants.AUTH_IDS, authIds);

    WorkflowTaskResponse workflowTaskResponse = WorkflowTaskResponse.builder().status(
        ActivityConstants.TASK_STATUS_CREATED).build();
    ArgumentCaptor<NotificationTask> notificationTaskArgumentCaptor = ArgumentCaptor.forClass(NotificationTask.class);
    Mockito.when(workflowNotificationTask.create(notificationTaskArgumentCaptor.capture())).thenReturn(workflowTaskResponse);

    state = new RxExecutionChain(state)
        .next(new OINPNotificationTask(workflowBatchNotificationTask, workflowNotificationTask))
        .execute();
    Assert.assertEquals(ActivityConstants.TASK_STATUS_CREATED, state.getValue(OinpBridgeConstants.NOTIFICATION_TASK_STATUS));
    Assert.assertEquals(-1, notificationTaskArgumentCaptor.getValue().getNotificationMetaData().getAuthId().longValue());
    Assert.assertTrue(notificationTaskArgumentCaptor.getValue().getNotificationData().containsKey(OinpBridgeConstants.TO_FIELD));
  }

  @Test
  public void testEmailNotification_NoAction(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getTaskId()).thenReturn("task-id");

    Map<String, String> outputMap = new HashMap<>();
    outputMap.put(OinpBridgeConstants.TO_FIELD, "<EMAIL>,<EMAIL>");

    State state = new State();
    state.addValue(OinpBridgeConstants.WORKER_ACTION_REQUEST, workerActionRequest);
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP, outputMap);
    state.addValue(OinpBridgeConstants.IS_MOBILE, false);
    state.addValue(OinpBridgeConstants.IS_EMAIL, false);
    state.addValue(OinpBridgeConstants.NOTIFICATION_NAME, "notificationName");
    state.addValue(OinpBridgeConstants.NOTIFICATION_DATA_TYPE, "notificationDataType");

    state = new RxExecutionChain(state)
        .next(new OINPNotificationTask(workflowBatchNotificationTask, workflowNotificationTask))
        .execute();
    Assert.assertEquals(ActivityConstants.NO_ACTION, state.getValue(OinpBridgeConstants.NOTIFICATION_TASK_STATUS));
  }

  @Test
  public void testEmailNotification_EmptyToField_NoAction(){
    WorkerActionRequest workerActionRequest = Mockito.mock(WorkerActionRequest.class);
    Mockito.when(workerActionRequest.getTaskId()).thenReturn("task-id");

    Map<String, String> outputMap = new HashMap<>();
    outputMap.put(OinpBridgeConstants.TO_FIELD, "");

    State state = new State();
    state.addValue(OinpBridgeConstants.WORKER_ACTION_REQUEST, workerActionRequest);
    state.addValue(OinpBridgeConstants.BRIDGE_OUTPUT_MAP, outputMap);
    state.addValue(OinpBridgeConstants.IS_MOBILE, true);
    state.addValue(OinpBridgeConstants.IS_EMAIL, false);
    state.addValue(OinpBridgeConstants.NOTIFICATION_NAME, "notificationName");
    state.addValue(OinpBridgeConstants.NOTIFICATION_DATA_TYPE, "notificationDataType");

    state = new RxExecutionChain(state)
        .next(new OINPNotificationTask(workflowBatchNotificationTask, workflowNotificationTask))
        .execute();
    Assert.assertEquals(ActivityConstants.NO_ACTION, state.getValue(OinpBridgeConstants.NOTIFICATION_TASK_STATUS));
  }
}
