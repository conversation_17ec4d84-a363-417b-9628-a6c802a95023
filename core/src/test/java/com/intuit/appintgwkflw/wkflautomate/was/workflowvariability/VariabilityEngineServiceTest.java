package com.intuit.appintgwkflw.wkflautomate.was.workflowvariability;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowVariabilityClientConfig;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.variability.WorkflowFeatureMapping;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Template;

public class VariabilityEngineServiceTest {

	private static final String REALM_ID = "12345";

	@Mock
	private VariabilityEngineClient variabilityEngineClient;

	@Mock
	private WorkflowVariabilityClientConfig workflowVariabilityClientProperties;

	@InjectMocks
	private VariabilityEngineService service;

	@Before
	public void init() {
		MockitoAnnotations.openMocks(this);
	}

	@Test
	public void testfilterTemplatesVariabilityTrue() {
		Mockito.when(workflowVariabilityClientProperties.getEnabledRealms()).thenReturn(Set.of(-1L));
		Mockito.when(workflowVariabilityClientProperties.isEnable()).thenReturn(true);
		List<Template> templates = prepareMockDataForReadAllTemplates();
		Set<String> allFeatures = WorkflowFeatureMapping.getAllFeatures();
		Mockito.when(variabilityEngineClient.getVariabilityDecision(allFeatures))
				.thenReturn(allFeatures.stream().collect(Collectors.toMap(Function.identity(), decision -> true)));
		service.filterTemplates(templates);
		Assert.assertNotNull(templates);
		Assert.assertTrue(templates.size() == 5);
	}

	@Test
	public void testfilterTemplatesVariabilityFalse() {
		Mockito.when(workflowVariabilityClientProperties.getEnabledRealms()).thenReturn(Set.of(-1L));
		Mockito.when(workflowVariabilityClientProperties.isEnable()).thenReturn(true);
		List<Template> templates = prepareMockDataForReadAllTemplates();
		Set<String> allFeatures = WorkflowFeatureMapping.getAllFeatures();
		Mockito.when(variabilityEngineClient.getVariabilityDecision(allFeatures))
				.thenReturn(allFeatures.stream().collect(Collectors.toMap(Function.identity(), decision -> false)));
		service.filterTemplates(templates);
		Assert.assertNotNull(templates);
		Assert.assertTrue(templates.size() == 3);
	}

	@Test
	public void testfilterTemplatesVariabilityException() {
		Mockito.when(workflowVariabilityClientProperties.getEnabledRealms()).thenReturn(Set.of(-1L));
		Mockito.when(workflowVariabilityClientProperties.isEnable()).thenReturn(true);
		List<Template> templates = prepareMockDataForReadAllTemplates();
		Set<String> allFeatures = WorkflowFeatureMapping.getAllFeatures();
		Mockito.when(variabilityEngineClient.getVariabilityDecision(allFeatures)).thenThrow(new RuntimeException());
		service.filterTemplates(templates);
		Assert.assertNotNull(templates);
		Assert.assertTrue(templates.size() == 5);
	}

  @Test
  public void testfilterTemplatesVariabilityExceptionNew() {
    try {
      Mockito.when(workflowVariabilityClientProperties.getEnabledRealms()).thenReturn(Set.of(-1L));
      Mockito.when(workflowVariabilityClientProperties.isEnable()).thenReturn(true);
      Template template = mockConfigTemplateEntity("projectReminder", TemplateCategory.CUSTOM);
      //List<Template> templates = Arrays.asList(template);
      Set<String> allFeatures = WorkflowFeatureMapping.getAllFeatures();
      Mockito.when(variabilityEngineClient.getVariabilityDecision(allFeatures))
          .thenReturn(
              allFeatures.stream()
                  .collect(Collectors.toMap(Function.identity(), decision -> true)));
	  service.filterTemplates(Arrays.asList(template));
      service.filterTemplate(template);
    } catch (Exception e) {
      Assert.fail();
    }
  }

	@Test
	public void testfilterTemplatesVariabilityTrueSuccess() {
		Mockito.when(workflowVariabilityClientProperties.getEnabledRealms()).thenReturn(Set.of(-1L));
		Template template = mockConfigTemplateEntity("invoiceReminder", TemplateCategory.CUSTOM);
		Mockito.when(workflowVariabilityClientProperties.isEnable()).thenReturn(true);
		Set<String> allFeatures = WorkflowFeatureMapping.getAllFeatures();
		Mockito.when(variabilityEngineClient.getVariabilityDecision(allFeatures))
				.thenReturn(allFeatures.stream().collect(Collectors.toMap(Function.identity(), decision -> true)));
		service.filterTemplates(Arrays.asList(template));
		service.filterTemplate(template);
		Assert.assertNotNull(template);
		Assert.assertEquals(template.getId().getLocalId(), "invoiceReminder");
	}

	private List<Template> prepareMockDataForReadAllTemplates() {
		List<Template> templates = new ArrayList<>();
		Template template = mockDbTemplateEntity("projectNotiofication", TemplateCategory.HUB);
		template.recordType("Project");
		templates.add(template);
		template = mockDbTemplateEntity("invoiceapproval", TemplateCategory.HUB);
		templates.add(template);

		Template template1 = mockConfigTemplateEntity("projectReminder", TemplateCategory.CUSTOM);
		templates.add(template1);
		template1.recordType("Project");
		template1 = mockConfigTemplateEntity("invoiceoverduereminder", TemplateCategory.CUSTOM);
		templates.add(template1);
		template1 = mockConfigTemplateEntity("invoiceapproval", TemplateCategory.CUSTOM);
		templates.add(template1);
		return templates;
	}

	public static Template mockConfigTemplateEntity(String id, TemplateCategory templateCategory) {
		Template template = new Template();
		template.setDisplayName("Template DisplayName");
		template.setName(id);
		template.setId(GlobalId.create(REALM_ID, template.getTypeId(), id));
		template.setCategory(TemplateCategory.valueOf(templateCategory.name()).name());
		return template;
	}

	public static Template mockDbTemplateEntity(String id, TemplateCategory templateCategory) {
		Template template = new Template();
		template.setDisplayName("Template DisplayName");
		template.setName(id);
		template.setCategory(TemplateCategory.valueOf(templateCategory.name()).name());
		template.setId(GlobalId.create(REALM_ID, template.getTypeId(), "1234"));
		return template;
	}

}
