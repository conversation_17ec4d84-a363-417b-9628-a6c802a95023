package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.util.BpmnOverwatchUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.Scope;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.TemplateService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.async.execution.Task;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.TriggerStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggersResponse;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class MultipleUserDefinitionTriggerHandlerTest {

  @Mock
  private TemplateService templateService;

  @Mock
  private V3RunTimeHelper runtimeHelper;

  @Mock
  private ProcessDetailsRepository processDetailsRepository;

  @Mock
  private WASContextHandler contextHandler;

  @Mock
  private V3StartProcess v3StartProcess;

  @Mock
  private V3SignalProcess v3SignalProcess;

  @Mock private FilterTriggerUtil filterTriggerUtil;

  @Mock
  private DefinitionServiceHelper definitionServiceHelper;

  @Mock
  private TemplateDetailsRepository templateDetailsRepository;

  @InjectMocks
  private MultipleUserDefinitionTriggerHandler multipleUserDefinitionTriggerHandler;

  private final String ownerId = "9130347798120106";
  private static final String DEFINITION_ID = "53fcf816-82b5-4998-9098-fd58a1e4a048";
  private static final String RECORD_ID = "35";
  private static final int ASSERT_SIZE_ONE = 1;
  private static final String PROCESS_ID = "pid";
  private static final String CUSTOM_NOTIFICATION_BPMN = "bpmn/customNotification.bpmn";

  private TemplateDetails templateDetails;
  private List<DefinitionDetails> definitionDetailsList;



  @Before
  public void init() {

    MockitoAnnotations.initMocks(this);
    definitionDetailsList = new ArrayList<>();
    templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    definitionDetailsList.add(TriggerHandlerTestData.getDefinitionDetails(templateDetails));
    Mockito.when(
            definitionServiceHelper.processMarkedDefinitionsAndGetEnabledDefinitions(
                Mockito.any(), Mockito.any()))
        .thenReturn(definitionDetailsList);

    Mockito.when(runtimeHelper.getTemplateData(Mockito.any()))
        .thenReturn(templateDetails.getTemplateData());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testNullTemplateDetails() {

    TransactionEntity transactionEntity =
    	TransactionEntityFactory.getInstanceOf(getTriggerPayload(null).getTriggerMessage(),
    		contextHandler);
    Mockito.when(runtimeHelper.getTemplateData(transactionEntity)).thenReturn(null);
    BpmnModelInstance bpmnModelInstance = multipleUserDefinitionTriggerHandler.getBpmnModelInstance(transactionEntity);
    multipleUserDefinitionTriggerHandler.getStartEventOfTheTemplate(bpmnModelInstance);
  }

  @Test
  public void testStartEvent() {

    TransactionEntity transactionEntity =
    		TransactionEntityFactory.getInstanceOf(getTriggerPayload(null).getTriggerMessage(),
    	    		contextHandler);
    BpmnModelInstance bpmnModelInstance = multipleUserDefinitionTriggerHandler.getBpmnModelInstance(transactionEntity);
    Collection<StartEvent> startevent =
        multipleUserDefinitionTriggerHandler.getStartEventOfTheTemplate(bpmnModelInstance);
    Assert.assertNotNull(startevent);
  }

  @Test
  public void testProcessStart() {
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());
    Mockito.when(runtimeHelper.getDefaultResponseBuilder(Mockito.any()))
        .thenReturn(
            WorkflowGenericResponse.builder()
                .status(ResponseStatus.FAILURE)
                .response(WorkflowTriggerResponse.builder().build()));

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    definitionDetailsList.add(TriggerHandlerTestData.getDefinitionDetails(templateDetails));

    Mockito.when(contextHandler.get(Mockito.any())).thenReturn(ownerId);
    Mockito.when(v3StartProcess.startProcess(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(Collections.singletonMap("id", "processId"));

    WorkflowGenericResponse workflowGenericResponse =
        multipleUserDefinitionTriggerHandler.executeTrigger(getTriggerPayload(null));

    Assert.assertNotNull(workflowGenericResponse);
    Assert.assertEquals(
        TriggerStatus.PROCESS_STARTED,
        ((WorkflowTriggersResponse) workflowGenericResponse.getResponse())
            .getTriggers()
            .stream()
            .findFirst()
            .get()
            .getStatus());
  }

  @Test
  public void testProcessStartWithScopeAsTest() {

    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());

    Mockito.when(runtimeHelper.getDefaultResponseBuilder(Mockito.any()))
            .thenReturn(
                    WorkflowGenericResponse.builder()
                            .status(ResponseStatus.FAILURE)
                            .response(WorkflowTriggerResponse.builder().build()));

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    definitionDetailsList.add(TriggerHandlerTestData.getDefinitionDetails(templateDetails));

    Mockito.when(contextHandler.get(Mockito.any())).thenReturn(ownerId);
    Mockito.when(v3StartProcess.startProcess(Mockito.any(), Mockito.any(), Mockito.any()))
            .thenReturn(Collections.singletonMap("id", "processId"));

    TriggerProcessDetails triggerProcessDetails = getTriggerPayload(null);
    Map<String, Object> eventHeaders = (Map<String, Object>) triggerProcessDetails.getTriggerMessage().get("eventHeaders");
    eventHeaders.put("scope", Scope.TEST);

    TemplateDetails templateDetailsWithOverwatch = TriggerHandlerTestData.getBPMNTemplateDetails();
    templateDetailsWithOverwatch.setTemplateName(templateDetails.getTemplateName() + BpmnOverwatchUtil.OVERWATCH_SUFFIX);
    templateDetailsWithOverwatch.setDeployedDefinitionId(UUID.randomUUID().toString());

    Mockito.when(templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(templateDetails.getTemplateName() + BpmnOverwatchUtil.OVERWATCH_SUFFIX))
            .thenReturn(Optional.of(templateDetailsWithOverwatch));

    WorkflowGenericResponse workflowGenericResponse =
            multipleUserDefinitionTriggerHandler.executeTrigger(triggerProcessDetails);

    Mockito.verify(templateDetailsRepository, Mockito.times(1))
            .findTopByTemplateNameOrderByVersionDesc(templateDetails.getTemplateName() + BpmnOverwatchUtil.OVERWATCH_SUFFIX);

    Assert.assertNotNull(workflowGenericResponse);
    Assert.assertEquals(
            TriggerStatus.PROCESS_STARTED,
            ((WorkflowTriggersResponse) workflowGenericResponse.getResponse())
                    .getTriggers()
                    .stream()
                    .findFirst()
                    .get()
                    .getStatus());
  }

  @Test
  public void testProcessStartWithScopeAsTestWithNoTestTemplateInDB() {

    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());
    Mockito.when(runtimeHelper.getDefaultResponseBuilder(Mockito.any()))
            .thenReturn(
                    WorkflowGenericResponse.builder()
                            .status(ResponseStatus.FAILURE)
                            .response(WorkflowTriggerResponse.builder().build()));

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    definitionDetailsList.add(TriggerHandlerTestData.getDefinitionDetails(templateDetails));

    Mockito.when(contextHandler.get(Mockito.any())).thenReturn(ownerId);
    Mockito.when(v3StartProcess.startProcess(Mockito.any(), Mockito.any(), Mockito.any()))
            .thenReturn(Collections.singletonMap("id", "processId"));

    TriggerProcessDetails triggerProcessDetails = getTriggerPayload(null);
    Map<String, Object> eventHeaders = (Map<String, Object>) triggerProcessDetails.getTriggerMessage().get("eventHeaders");
    eventHeaders.put("scope", Scope.TEST);

    TemplateDetails templateDetailsWithOverwatch = TriggerHandlerTestData.getBPMNTemplateDetails();
    templateDetailsWithOverwatch.setTemplateName(templateDetails.getTemplateName() + BpmnOverwatchUtil.OVERWATCH_SUFFIX);
    templateDetailsWithOverwatch.setDeployedDefinitionId(UUID.randomUUID().toString());

    Mockito.when(templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(templateDetails.getTemplateName() + BpmnOverwatchUtil.OVERWATCH_SUFFIX))
            .thenReturn(Optional.empty());

    WorkflowGenericResponse workflowGenericResponse =
            multipleUserDefinitionTriggerHandler.executeTrigger(triggerProcessDetails);

    Mockito.verify(templateDetailsRepository, Mockito.times(1))
            .findTopByTemplateNameOrderByVersionDesc(templateDetails.getTemplateName() + BpmnOverwatchUtil.OVERWATCH_SUFFIX);

    Assert.assertNotNull(workflowGenericResponse);
    Assert.assertEquals(
            TriggerStatus.PROCESS_STARTED,
            ((WorkflowTriggersResponse) workflowGenericResponse.getResponse())
                    .getTriggers()
                    .stream()
                    .findFirst()
                    .get()
                    .getStatus());
  }

  @Test
  public void testPrepareStartMultipleProcessTasks_ScopeTest_TemplateDetailsNotPresent() throws Exception {
    Method method = MultipleUserDefinitionTriggerHandler.class.getDeclaredMethod("prepareStartMultipleProcessTasks", TransactionEntity.class, List.class);
    method.setAccessible(true);

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    definitionDetailsList.add(TriggerHandlerTestData.getDefinitionDetails(templateDetails));

    TriggerProcessDetails triggerProcessDetails = getTriggerPayload(null);
    Map<String, Object> eventHeaders = (Map<String, Object>) triggerProcessDetails.getTriggerMessage().get("eventHeaders");
    eventHeaders.put("scope", Scope.TEST);

    TransactionEntity transactionEntity = TransactionEntityFactory.getInstanceOf(
            triggerProcessDetails.getTriggerMessage(),
            contextHandler);

    Mockito.when(templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(templateDetails.getTemplateName() + BpmnOverwatchUtil.OVERWATCH_SUFFIX))
            .thenReturn(Optional.empty());

    String oldCamundaDeploymentId = definitionDetailsList.get(0).getTemplateDetails().getDeployedDefinitionId();

    Object result = method.invoke(multipleUserDefinitionTriggerHandler, transactionEntity, definitionDetailsList);

    List<Task> tasks = result == null ? null : (List<Task>) result;

    Assert.assertEquals(1, tasks.size());
    Assert.assertEquals(definitionDetailsList.get(0).getTemplateDetails().getDeployedDefinitionId(), oldCamundaDeploymentId);
  }

  @Test
  public void testPrepareStartMultipleProcessTasks_ScopeTest_TemplateDetailsPresent() throws Exception {
    Method method = MultipleUserDefinitionTriggerHandler.class.getDeclaredMethod("prepareStartMultipleProcessTasks", TransactionEntity.class, List.class);
    method.setAccessible(true);

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    definitionDetailsList.add(TriggerHandlerTestData.getDefinitionDetails(templateDetails));

    TriggerProcessDetails triggerProcessDetails = getTriggerPayload(null);
    Map<String, Object> eventHeaders = (Map<String, Object>) triggerProcessDetails.getTriggerMessage().get("eventHeaders");
    eventHeaders.put("scope", Scope.TEST);

    TransactionEntity transactionEntity = TransactionEntityFactory.getInstanceOf(
            triggerProcessDetails.getTriggerMessage(),
            contextHandler);

    TemplateDetails overwatchTemplateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    overwatchTemplateDetails.setTemplateName(overwatchTemplateDetails.getTemplateName() + BpmnOverwatchUtil.OVERWATCH_SUFFIX);

    String overwatchDeployedTemplateId = UUID.randomUUID().toString();
    overwatchTemplateDetails.setDeployedDefinitionId(overwatchDeployedTemplateId);
    overwatchTemplateDetails.setId(UUID.randomUUID().toString());

    Mockito.when(templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(templateDetails.getTemplateName() + BpmnOverwatchUtil.OVERWATCH_SUFFIX))
            .thenReturn(Optional.of(overwatchTemplateDetails));

    Object result = method.invoke(multipleUserDefinitionTriggerHandler, transactionEntity, definitionDetailsList);

    List<Task> tasks = result == null ? null : (List<Task>) result;

    Assert.assertEquals(1, tasks.size());
    Assert.assertEquals(definitionDetailsList.get(0).getTemplateDetails().getDeployedDefinitionId(), overwatchDeployedTemplateId);
  }

  @Test
  public void testProcessStartWithStaleDefinition() {
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());
    Mockito.when(runtimeHelper.getDefaultResponseBuilder(Mockito.any()))
        .thenReturn(
            WorkflowGenericResponse.builder()
                .status(ResponseStatus.FAILURE)
                .response(WorkflowTriggerResponse.builder().build()));

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    definitionDetailsList.add(TriggerHandlerTestData.getDefinitionDetails(templateDetails));

    definitionDetailsList.add(TriggerHandlerTestData.getDefinitionDetails(templateDetails));
    definitionDetailsList.get(1).setInternalStatus(InternalStatus.STALE_DEFINITION);

    Mockito.when(definitionServiceHelper.processMarkedDefinitionsAndGetEnabledDefinitions(
        Mockito.any(), Mockito.any())).thenReturn(definitionDetailsList);

    Mockito.when(contextHandler.get(Mockito.any())).thenReturn(ownerId);
    Mockito.when(v3StartProcess.startProcess(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(Collections.singletonMap("id", "processId"));

    WorkflowGenericResponse workflowGenericResponse =
        multipleUserDefinitionTriggerHandler.executeTrigger(getTriggerPayload(null));

    Assert.assertNotNull(workflowGenericResponse);
    Assert.assertEquals(
        TriggerStatus.PROCESS_STARTED,
        ((WorkflowTriggersResponse) workflowGenericResponse.getResponse())
            .getTriggers()
            .stream()
            .findFirst()
            .get()
            .getStatus());
  }

  @Test
  public void testProcessStartWithStaleDefinitionOnly() {
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());
    Mockito.when(runtimeHelper.getDefaultResponseBuilder(Mockito.any()))
        .thenReturn(
            WorkflowGenericResponse.builder()
                .status(ResponseStatus.FAILURE)
                .response(WorkflowTriggerResponse.builder().build()));

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    definitionDetailsList.add(TriggerHandlerTestData.getDefinitionDetails(templateDetails));

    definitionDetailsList.get(0).setInternalStatus(InternalStatus.STALE_DEFINITION);

    Mockito.when(definitionServiceHelper.processMarkedDefinitionsAndGetEnabledDefinitions(
        Mockito.any(), Mockito.any())).thenReturn(definitionDetailsList);

    Mockito.when(contextHandler.get(Mockito.any())).thenReturn(ownerId);

    WorkflowGenericResponse workflowGenericResponse =
        multipleUserDefinitionTriggerHandler.executeTrigger(getTriggerPayload(null));

    Assert.assertNotNull(workflowGenericResponse);
    Assert.assertEquals(ResponseStatus.FAILURE, workflowGenericResponse.getStatus());
  }

  @Test
  public void whenExecuteTrigger_withWorkflowId_thenSuccess() {
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());
    Mockito.when(runtimeHelper.getDefaultResponseBuilder(Mockito.any()))
        .thenReturn(
            WorkflowGenericResponse.builder()
                .status(ResponseStatus.FAILURE)
                .response(WorkflowTriggerResponse.builder().build()));

    Mockito.doReturn(ownerId).when(contextHandler).get(Mockito.any());
    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    Mockito.when(v3StartProcess.startProcess(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(Collections.singletonMap("id", "processId"));

    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setTemplateDetails(templateDetails);
    definitionDetails.setDefinitionData(templateDetails.getTemplateData());

    WorkflowGenericResponse workflowGenericResponse = multipleUserDefinitionTriggerHandler
        .executeTrigger(getTriggerPayload(UUID.randomUUID().toString()));

    Assert.assertNotNull(workflowGenericResponse);
    Assert.assertEquals(
        TriggerStatus.PROCESS_STARTED,
        ((WorkflowTriggersResponse) workflowGenericResponse.getResponse())
            .getTriggers()
            .stream()
            .findFirst()
            .get()
            .getStatus());
  }

  @Test
  public void tesMultipleProcessSignal() {
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());
    Mockito.when(runtimeHelper.getDefaultResponseBuilder(Mockito.any()))
        .thenReturn(
            WorkflowGenericResponse.builder()
                .status(ResponseStatus.FAILURE)
                .response(WorkflowTriggerResponse.builder().build()));

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    definitionDetailsList.add(TriggerHandlerTestData.getDefinitionDetails(templateDetails));

    Mockito.when(contextHandler.get(Mockito.any())).thenReturn(ownerId);

    Mockito.when(
        v3SignalProcess.signalProcessById(
            Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(true);

    DefinitionDetails def = new DefinitionDetails();
    def.setDefinitionId("53fcf816-82b5-4998-9098-fd58a1e4a048");

    ProcessDetails proccessDetails = new ProcessDetails();
    proccessDetails.setProcessId("pId");

    ProcessDetails proccessDetails1 = new ProcessDetails();
    proccessDetails1.setProcessId("pId1");

    proccessDetails.setDefinitionDetails(def);
    proccessDetails1.setDefinitionDetails(def);

    Mockito.when(
        processDetailsRepository
            .findByRecordIdAndOwnerIdAndProcessStatusInAndInternalStatusIsNullAndDefinitionDetailsInOrderByCreatedDateDesc(
                eq("35"),
                eq(Long.parseLong("9130347798120106")),
                eq(Arrays.asList(ProcessStatus.ACTIVE, ProcessStatus.ERROR)),
                Mockito.any()))
        .thenReturn(Optional.of(Arrays.asList(proccessDetails, proccessDetails1)));

    WorkflowGenericResponse workflowGenericResponse =
        multipleUserDefinitionTriggerHandler.executeTrigger(getTriggerPayload(null));

    Assert.assertNotNull(workflowGenericResponse);
    Assert.assertEquals(
        2, ((WorkflowTriggersResponse) workflowGenericResponse.getResponse()).getTriggers().size());

    Assert.assertEquals(
        TriggerStatus.PROCESS_SIGNALLED,
        ((WorkflowTriggersResponse) workflowGenericResponse.getResponse())
            .getTriggers()
            .stream()
            .findFirst()
            .get()
            .getStatus());
  }

  @Test
  public void testSingleProcessSignal() {
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());
    Mockito.when(runtimeHelper.getDefaultResponseBuilder(Mockito.any()))
        .thenReturn(
            WorkflowGenericResponse.builder()
                .status(ResponseStatus.FAILURE)
                .response(WorkflowTriggerResponse.builder().build()));

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    definitionDetailsList.add(TriggerHandlerTestData.getDefinitionDetails(templateDetails));

    Mockito.when(contextHandler.get(Mockito.any())).thenReturn(ownerId);

    Mockito.when(
        v3SignalProcess.signalProcessById(
            Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(true);

    DefinitionDetails def = new DefinitionDetails();
    def.setDefinitionId("53fcf816-82b5-4998-9098-fd58a1e4a048");

    ProcessDetails proccessDetails = new ProcessDetails();
    proccessDetails.setProcessId("pId");

    proccessDetails.setDefinitionDetails(def);

    Mockito.when(
        processDetailsRepository
            .findByRecordIdAndOwnerIdAndProcessStatusInAndInternalStatusIsNullAndDefinitionDetailsInOrderByCreatedDateDesc(
                eq("35"),
                eq(Long.parseLong("9130347798120106")),
                eq(Arrays.asList(ProcessStatus.ACTIVE, ProcessStatus.ERROR)),
                Mockito.any()))
        .thenReturn(Optional.of(Collections.singletonList(proccessDetails)));

    WorkflowGenericResponse workflowGenericResponse =
        multipleUserDefinitionTriggerHandler.executeTrigger(getTriggerPayload(null));

    Assert.assertNotNull(workflowGenericResponse);
    Assert.assertEquals(
        1, ((WorkflowTriggersResponse) workflowGenericResponse.getResponse()).getTriggers().size());

    Assert.assertEquals(
        TriggerStatus.PROCESS_SIGNALLED,
        ((WorkflowTriggersResponse) workflowGenericResponse.getResponse())
            .getTriggers()
            .stream()
            .findFirst()
            .get()
            .getStatus());
  }

  @SuppressWarnings("serial")
  private TriggerProcessDetails getTriggerPayload(String workflowId) {

    Map<String, Object> trigger = new HashMap<>();
    Map<String, Object> eventHeaders = new HashMap<>();
    Map<String, Object> entityPayload = new HashMap<>();
    eventHeaders.put("workflow", "approval");
    eventHeaders.put("entityType", RecordType.INVOICE);
    eventHeaders.put("entityChangeType", "created");
    eventHeaders.put("entityId", "35");
    if (StringUtils.isNotBlank(workflowId)) {
      eventHeaders.put("providerWorkflowId", workflowId);
    }
    trigger.put("eventHeaders", eventHeaders);
    entityPayload.put(
        "Invoice",
        new HashMap<String, String>() {
          {
            put("a", "b");
          }
        });
    trigger.put("entity", entityPayload);
    return TriggerProcessDetails.builder().triggerMessage(trigger).build();
  }

  @SuppressWarnings("serial")
  private TriggerProcessDetails getTriggerPayload(String workflowId, String workflowName,
      String source) {

    Map<String, Object> trigger = new HashMap<>();
    Map<String, Object> eventHeaders = new HashMap<>();
    Map<String, Object> entityPayload = new HashMap<>();
    eventHeaders.put("workflow", workflowName);
    eventHeaders.put("entityType", RecordType.INVOICE);
    eventHeaders.put("entityChangeType", WorkflowConstants.NEW_CUSTOM_START);
    eventHeaders.put("entityId", "35");

    if (StringUtils.isNotBlank(workflowId)) {
      eventHeaders.put("providerWorkflowId", workflowId);
    }
    trigger.put("eventHeaders", eventHeaders);
    entityPayload.put(
        "Invoice",
        new HashMap<String, String>() {
          {
            put("a", "b");
          }
        });
    trigger.put("entity", entityPayload);
      trigger.put(WorkflowConstants.SOURCE, source);
    return TriggerProcessDetails.builder().triggerMessage(trigger).build();
  }

  @Test
  public void testProcessStartWithStaleEmptyDefinition() {

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();

    Mockito.when(
            definitionServiceHelper.processMarkedDefinitionsAndGetEnabledDefinitions(
                    Mockito.any(), Mockito.any()))
            .thenReturn(definitionDetailsList);

    Mockito.when(runtimeHelper.getDefaultResponseBuilder(Mockito.any()))
            .thenReturn(
                    WorkflowGenericResponse.builder()
                            .status(ResponseStatus.FAILURE)
                            .response(WorkflowTriggerResponse.builder().build()));

    TransactionEntity transactionEntity =
    		TransactionEntityFactory.getInstanceOf(getTriggerPayload(null).getTriggerMessage(),
    	    		contextHandler);

    Mockito.when(contextHandler.get(Mockito.any())).thenReturn(ownerId);

    WorkflowGenericResponse workflowGenericResponse =
            multipleUserDefinitionTriggerHandler.executeTrigger(getTriggerPayload(null));

    Assert.assertNotNull(workflowGenericResponse);
    Assert.assertEquals(ResponseStatus.FAILURE, workflowGenericResponse.getStatus());
  }

  @Test
  public void testZeroProcessSignal() {
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());
    Mockito.when(runtimeHelper.getDefaultResponseBuilder(Mockito.any()))
            .thenReturn(
                    WorkflowGenericResponse.builder()
                            .status(ResponseStatus.FAILURE)
                            .response(WorkflowTriggerResponse.builder().build()));

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    definitionDetailsList.add(TriggerHandlerTestData.getDefinitionDetails(templateDetails));

    Mockito.when(contextHandler.get(Mockito.any())).thenReturn(ownerId);
    DefinitionDetails def = new DefinitionDetails();
    def.setDefinitionId(DEFINITION_ID);

    Mockito.when(
            processDetailsRepository
                    .findByRecordIdAndOwnerIdAndProcessStatusInAndInternalStatusIsNullAndDefinitionDetailsInOrderByCreatedDateDesc(
                            eq(RECORD_ID),
                            eq(Long.parseLong(ownerId)),
                            eq(Arrays.asList(ProcessStatus.ACTIVE, ProcessStatus.ERROR)),
                            Mockito.any()))
            .thenReturn(Optional.of(new ArrayList<>()));

    WorkflowGenericResponse workflowGenericResponse =
            multipleUserDefinitionTriggerHandler.executeTrigger(getTriggerPayload(null));

    Assert.assertNotNull(workflowGenericResponse);
    Assert.assertEquals(
            ASSERT_SIZE_ONE, ((WorkflowTriggersResponse) workflowGenericResponse.getResponse()).getTriggers().size());

    Assert.assertEquals(
            TriggerStatus.ERROR_STARTING_PROCESS,
            ((WorkflowTriggersResponse) workflowGenericResponse.getResponse())
                    .getTriggers()
                    .stream()
                    .findFirst()
                    .get()
                    .getStatus());
  }

  @Test
  public void testErrorProcessSignalStatus() {
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class)))
            .thenReturn(TestHelper.getTestStartEventActivityDetail());
    Mockito.when(runtimeHelper.getDefaultResponseBuilder(Mockito.any()))
            .thenReturn(
                    WorkflowGenericResponse.builder()
                            .status(ResponseStatus.FAILURE)
                            .response(WorkflowTriggerResponse.builder().build()));

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    definitionDetailsList.add(TriggerHandlerTestData.getDefinitionDetails(templateDetails));

    Mockito.when(contextHandler.get(Mockito.any())).thenReturn(ownerId);

    Mockito.when(
            v3SignalProcess.signalProcessById(
                    Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
            .thenReturn(true);

    DefinitionDetails def = new DefinitionDetails();
    def.setDefinitionId(DEFINITION_ID);

    ProcessDetails proccessDetails = new ProcessDetails();
    proccessDetails.setProcessId(PROCESS_ID);
    proccessDetails.setProcessStatus(ProcessStatus.ERROR);
    proccessDetails.setDefinitionDetails(def);

    Mockito.when(
            processDetailsRepository
                    .findByRecordIdAndOwnerIdAndProcessStatusInAndInternalStatusIsNullAndDefinitionDetailsInOrderByCreatedDateDesc(
                            eq(RECORD_ID),
                            eq(Long.parseLong(ownerId)),
                            eq(Arrays.asList(ProcessStatus.ACTIVE, ProcessStatus.ERROR)),
                            Mockito.any()))
            .thenReturn(Optional.of(Collections.singletonList(proccessDetails)));

    WorkflowGenericResponse workflowGenericResponse =
            multipleUserDefinitionTriggerHandler.executeTrigger(getTriggerPayload(null));

    Assert.assertNotNull(workflowGenericResponse);
    Assert.assertEquals(ASSERT_SIZE_ONE, ((WorkflowTriggersResponse) workflowGenericResponse.getResponse()).getTriggers().size());

    Assert.assertEquals(
            TriggerStatus.PROCESS_SIGNALLED,
            ((WorkflowTriggersResponse) workflowGenericResponse.getResponse())
                    .getTriggers()
                    .stream()
                    .findFirst()
                    .get()
                    .getStatus());
  }

  @Test
  public void checkBusinessKeyPresent(){
	  Mockito.when(runtimeHelper.getDefaultResponseBuilder(Mockito.any()))
		.thenReturn(WorkflowGenericResponse.builder().status(ResponseStatus.SUCCESS)
				.response(WorkflowTriggerResponse.builder().status(TriggerStatus.NO_ACTION).build()));
    TriggerProcessDetails triggerProcessDetails =
        TriggerHandlerTestData.prepareV3TriggerMessage("created");
    ((Map<String, Object>)triggerProcessDetails.getTriggerMessage().get("eventHeaders")).put("businessKey", 1234);
    Exception exception = Assertions.assertThrows(WorkflowGeneralException.class,
        () -> multipleUserDefinitionTriggerHandler.executeTrigger(triggerProcessDetails));
    Assert.assertEquals(WorkflowError.BUSINESS_KEY_NOT_ALLOWED.getErrorMessage(), exception.getMessage());
  }

  @Test
  public void test_prunedDefintionDetails_misMatchingRecordType(){
    TriggerProcessDetails triggerProcessDetails =
            TriggerHandlerTestData.prepareV3TriggerMessage("created");
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    templateDetails.setRecordType(RecordType.BILL);
    definitionDetailsList.add(TriggerHandlerTestData.getDefinitionDetails(templateDetails));
    TransactionEntity transactionEntity =
    		TransactionEntityFactory.getInstanceOf(getTriggerPayload(null).getTriggerMessage(),
    	    		contextHandler);
    List<DefinitionDetails> prunedDefs = multipleUserDefinitionTriggerHandler.pruneIneligibleDefinitions(transactionEntity, definitionDetailsList);
    Assert.assertTrue(prunedDefs.isEmpty());
  }

  @Test
  public void test_prunedDefintionDetails_matchingRecordType(){
    TriggerProcessDetails triggerProcessDetails =
            TriggerHandlerTestData.prepareV3TriggerMessage("created");
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    TemplateDetails templateDetails = TriggerHandlerTestData.getBPMNTemplateDetails();
    definitionDetailsList.add(TriggerHandlerTestData.getDefinitionDetails(templateDetails));
    TransactionEntity transactionEntity =
    		TransactionEntityFactory.getInstanceOf(getTriggerPayload(null).getTriggerMessage(),
    	    		contextHandler);
    List<DefinitionDetails> prunedDefs = multipleUserDefinitionTriggerHandler.pruneIneligibleDefinitions(transactionEntity, definitionDetailsList);
    Assert.assertFalse(prunedDefs.isEmpty());
  }

  @Test
  public void test_prunedDefintionDetails_matchingRecordType_nullList(){
    TriggerProcessDetails triggerProcessDetails =
            TriggerHandlerTestData.prepareV3TriggerMessage("created");
    List<DefinitionDetails> definitionDetailsList = null;
    TransactionEntity transactionEntity =
    	TransactionEntityFactory.getInstanceOf(getTriggerPayload(null).getTriggerMessage(),
    		contextHandler);
    List<DefinitionDetails> prunedDefs = multipleUserDefinitionTriggerHandler.pruneIneligibleDefinitions(transactionEntity, definitionDetailsList);
    Assert.assertTrue(prunedDefs.isEmpty());
  }

  @Test
  public void test_validateEntityChangeType_null_definitionList_activity_detail() {
    ActivityDetail activityDetail = ActivityDetail.builder().type(TaskType.MILESTONE).activityId("customStart").build();

    TransactionEntity transactionEntity =
            new TransactionEntity(getTriggerPayload(null).getTriggerMessage());
    List<DefinitionDetails> validatedDefs = multipleUserDefinitionTriggerHandler.filterDefsOnChangeType(activityDetail,
            null, transactionEntity);
    Assert.assertNull(validatedDefs);
  }

  @Test
  public void test_validateEntityChangeType_validdefinitionList_activity_detail() {
    ActivityDetail activityDetail = ActivityDetail.builder().type(TaskType.MILESTONE).activityId("customStart").build();
    TransactionEntity transactionEntity =
            new TransactionEntity(getTriggerPayload(null).getTriggerMessage());
    definitionDetailsList.get(0).setPlaceholderValue("{\n" +
            "\t\"user_variables\": {\n" +
            "\t\t\"customStart\": {\n" +
            "\t\t\t\"selected\": true,\n" +
            "\t\t\t\"parameters\": {\n" +
            "\t\t\t\t\"entityOperation\": {\n" +
            "\t\t\t\t\t\"fieldValue\": [\"created\"]\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}\n" +
            "\t\t}\n" +
            "\t}\n" +
            "}");
    List<DefinitionDetails> validatedDefs = multipleUserDefinitionTriggerHandler.filterDefsOnChangeType(activityDetail,
            definitionDetailsList,transactionEntity);
    Assert.assertFalse(validatedDefs.isEmpty());
  }

  @Test
  public void test_validateEntityChangeType_mismatch_txnEntity_activity_detail() {

    ActivityDetail activityDetail = ActivityDetail.builder().type(TaskType.MILESTONE).activityId("customStart").build();
    TransactionEntity transactionEntity =
            new TransactionEntity(getTriggerPayload(null).getTriggerMessage());
    transactionEntity.getEventHeaders().setEntityChangeType("update");
    definitionDetailsList.get(0).setPlaceholderValue("{\n" +
            "\t\"user_variables\": {\n" +
            "\t\t\"customStart\": {\n" +
            "\t\t\t\"selected\": true,\n" +
            "\t\t\t\"parameters\": {\n" +
            "\t\t\t\t\"entityOperation\": {\n" +
            "\t\t\t\t\t\"fieldValue\": [\"created\"]\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}\n" +
            "\t\t}\n" +
            "\t}\n" +
            "}");
    List<DefinitionDetails> validatedDefs = multipleUserDefinitionTriggerHandler.filterDefsOnChangeType(activityDetail,
            definitionDetailsList,transactionEntity);
    Assert.assertTrue(validatedDefs.isEmpty());
  }

  @Test
  public void test_validateEntityChangeType_no_startEvent_activity_detail() {
    ActivityDetail activityDetail = ActivityDetail.builder().type(TaskType.MILESTONE).activityId("customStart").build();

    TransactionEntity transactionEntity =
            new TransactionEntity(getTriggerPayload(null).getTriggerMessage());
    transactionEntity.getEventHeaders().setEntityChangeType("update");
    definitionDetailsList.get(0).setPlaceholderValue("{\n" +
            "\t\"user_variables\": {\n" +
            "\t\t\"customStartDummy\": {\n" +
            "\t\t\t\"selected\": true,\n" +
            "\t\t\t\"parameters\": {\n" +
            "\t\t\t\t\"entityOperation\": {\n" +
            "\t\t\t\t\t\"fieldValue\": [\"created\"]\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}\n" +
            "\t\t}\n" +
            "\t}\n" +
            "}");
    List<DefinitionDetails> validatedDefs = multipleUserDefinitionTriggerHandler.filterDefsOnChangeType(activityDetail,
            definitionDetailsList,transactionEntity);
    Assert.assertFalse(validatedDefs.isEmpty());
  }

  @Test
  public void test_validateEntityChangeType_no_parameters_activity_detail() {
    ActivityDetail activityDetail = ActivityDetail.builder().type(TaskType.MILESTONE).activityId("customStart").build();
    TransactionEntity transactionEntity =
            new TransactionEntity(getTriggerPayload(null).getTriggerMessage());
    transactionEntity.getEventHeaders().setEntityChangeType("update");
    definitionDetailsList.get(0).setPlaceholderValue("{\n" +
            "\t\"user_variables\": {\n" +
            "\t\t\"customStart\": {\n" +
            "\t\t\t\"selected\": true,\n" +
            "\t\t\t\"parametersDummy\": {\n" +
            "\t\t\t\t\"entityOperation\": {\n" +
            "\t\t\t\t\t\"fieldValue\": [\"created\"]\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}\n" +
            "\t\t}\n" +
            "\t}\n" +
            "}");
    List<DefinitionDetails> validatedDefs = multipleUserDefinitionTriggerHandler.filterDefsOnChangeType(activityDetail,
            definitionDetailsList,transactionEntity);
    Assert.assertFalse(validatedDefs.isEmpty());
  }

  @Test
  public void testProcessBpmnParsing_SkipParsing_Failure() {
    Mockito.when(runtimeHelper.getDefaultResponseBuilder(ArgumentMatchers.any())).thenReturn(WorkflowGenericResponse.builder()
            .status(ResponseStatus.FAILURE)
            .response(WorkflowTriggerResponse.builder().build()));
    Mockito.when(contextHandler.get(Mockito.any())).thenReturn(ownerId);

    TransactionEntity transactionEntity =
            TransactionEntityFactory.getInstanceOf(TriggerHandlerTestData.prepareV3TriggerMessage("created").getTriggerMessage(),
                    contextHandler);
    List<DefinitionDetails> definitionDetailsList1 = new ArrayList<>();
    definitionDetailsList1.add(TriggerHandlerTestData.getDefinitionDetails(templateDetails));
    transactionEntity.getEventHeaders().setEntityChangeType("update");
    definitionDetailsList1.get(0).setPlaceholderValue("{\n" +
            "\t\"user_variables\": {\n" +
            "\t\t\"startEvent\": {\n" +
            "\t\t\t\"selected\": true,\n" +
            "\t\t\t\"parameters\": {\n" +
            "\t\t\t\t\"entityOperation\": {\n" +
            "\t\t\t\t\t\"fieldValue\": [\"created\"]\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}\n" +
            "\t\t}\n" +
            "\t}\n" +
            "}");
    Mockito.when(definitionServiceHelper.processMarkedDefinitionsAndGetEnabledDefinitions(
            ArgumentMatchers.anyList(), ArgumentMatchers.anyString())).thenReturn(definitionDetailsList1);
    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class))).thenReturn(TestHelper.getTestStartEventActivityDetail());

    WorkflowGenericResponse startProcessResponse = multipleUserDefinitionTriggerHandler.executeTrigger(
                    TriggerHandlerTestData.prepareV3TriggerMessage("updated"));
    Mockito.verify(contextHandler, Mockito.atLeastOnce())
            .addKey(Mockito.eq(WASContextEnums.WORKFLOW), Mockito.anyString());
    Assert.assertEquals(ResponseStatus.FAILURE, startProcessResponse.getStatus());
    Mockito.verify(runtimeHelper, Mockito.times(1)).fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class));
  }

  @Test
  public void testProcessBpmnParsing_SkipParsing() {
    Mockito.when(runtimeHelper.getDefaultResponseBuilder(ArgumentMatchers.any())).thenReturn(WorkflowGenericResponse.builder()
            .status(ResponseStatus.FAILURE)
            .response(WorkflowTriggerResponse.builder().build()));
    Mockito.doReturn(TriggerHandlerTestData.getStartProcessResult())
            .when(v3StartProcess)
            .startProcess(any(), any(), any());
    Mockito.when(contextHandler.get(Mockito.any())).thenReturn(ownerId);

    ActivityDetail activityDetail = TestHelper.getTestStartEventActivityDetail();

    Mockito.when(runtimeHelper.fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class))).thenReturn(activityDetail);

    WorkflowGenericResponse workflowGenericResponse =
            multipleUserDefinitionTriggerHandler.executeTrigger(getTriggerPayload(null));

    Assert.assertNotNull(workflowGenericResponse);
    Assert.assertEquals(
            TriggerStatus.PROCESS_STARTED,
            ((WorkflowTriggersResponse) workflowGenericResponse.getResponse())
                    .getTriggers()
                    .stream()
                    .findFirst()
                    .get()
                    .getStatus());
    Mockito.verify(contextHandler, Mockito.atLeastOnce())
            .addKey(Mockito.eq(WASContextEnums.WORKFLOW), Mockito.anyString());
    Mockito.verify(runtimeHelper, Mockito.times(1)).fetchInitialStartEventActivityDetail(ArgumentMatchers.any(TransactionEntity.class));
  }
  
	@Test
	public void testProcessStart_DomainEventTrigger_FFEnabled() {
		Mockito.when(contextHandler.get(Mockito.any())).thenReturn(ownerId);
		Mockito.when(filterTriggerUtil.filterNotificationWorkflow(Mockito.any())).thenReturn(true);
		Mockito.when(runtimeHelper.getDefaultResponseBuilder(Mockito.any()))
				.thenReturn(WorkflowGenericResponse.builder().status(ResponseStatus.SUCCESS)
						.response(WorkflowTriggerResponse.builder().status(TriggerStatus.NO_ACTION).build()));

		WorkflowGenericResponse workflowGenericResponse = multipleUserDefinitionTriggerHandler
				.executeTrigger(getTriggerPayload(null, CustomWorkflowType.NOTIFICATION.getTemplateName(), null));

		Assert.assertNotNull(workflowGenericResponse);
		Assert.assertEquals(TriggerStatus.NO_ACTION,
				((WorkflowTriggerResponse) workflowGenericResponse.getResponse()).getStatus());
	}
}