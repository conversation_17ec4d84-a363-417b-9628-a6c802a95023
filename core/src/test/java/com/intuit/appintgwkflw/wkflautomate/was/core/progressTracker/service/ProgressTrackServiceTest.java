package com.intuit.appintgwkflw.wkflautomate.was.core.progressTracker.service;

import com.intuit.appintgwkflw.wkflautomate.was.core.progressTracker.config.MilestoneMeta;
import com.intuit.appintgwkflw.wkflautomate.was.core.progressTracker.config.ZeroStateConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.progressTracker.config.MilestoneProgressConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.progressTracker.response.WorkflowMilestoneStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.progressTracker.response.WorkflowMilestoneTrackResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;



@RunWith(MockitoJUnitRunner.class)
public class ProgressTrackServiceTest {

	@Mock
	private ProcessDetailsRepository processDetailsRepo;

	@Mock
	private MilestoneProgressConfig milestoneProgressConfig;

	@InjectMocks
	private ProgressTrackingService progressTrackService;

	@Mock
	private ActivityDetailsRepository activityDetailsRepo;

	@Mock
	private ActivityProgressDetailsRepository activityProgressDetailsRepo;

	@Before
	public void init() {

		List<MilestoneMeta> milestoneMetas = milestoneMetas();

		ZeroStateConfig metaConfig = new ZeroStateConfig();
		metaConfig.setTemplateId("template1");
		metaConfig.setMilestoneMeta(milestoneMetas);
		
		ZeroStateConfig metaConfigTTLiveFullService = new ZeroStateConfig();
		metaConfigTTLiveFullService.setWorkflow("engagementTTLiveFullService");
		metaConfigTTLiveFullService.setMilestoneMeta(milestoneMetas);
		metaConfigTTLiveFullService.setTemplates(Arrays.asList(metaConfig));

		Map<String, ZeroStateConfig> milestonMetaWorkflowConfigMap =
				Map.of(metaConfigTTLiveFullService.getWorkflow(),metaConfig);
		
		Map<String, Map<String, ZeroStateConfig>> milestonMetaWorkflowTemplateConfigMap =
				 Map.of(metaConfigTTLiveFullService.getWorkflow(), Map.of(metaConfig.getTemplateId(), metaConfig));
				
		ReflectionTestUtils.setField(progressTrackService, 
				"milestonMetaWorkflowConfigMap", milestonMetaWorkflowConfigMap);
		ReflectionTestUtils.setField(progressTrackService, 
				"milestonMetaWorkflowTemplateConfigMap", milestonMetaWorkflowTemplateConfigMap);
		
		ReflectionTestUtils.setField(progressTrackService, "formatter", 
				DateTimeFormatter.ISO_DATE_TIME);
	}


	private List<MilestoneMeta> milestoneMetas() {
		List<MilestoneMeta> milestoneMetas = new ArrayList<>();

		MilestoneMeta milestoneMeta1 = new MilestoneMeta();
		milestoneMeta1.setName("Client Unassigned");
		milestoneMeta1.setBeginElement("Event_1dst8o1");
		milestoneMeta1.setEndElement("Event_13x1jdz");
		milestoneMetas.add(milestoneMeta1);

		MilestoneMeta milestoneMeta2 = new MilestoneMeta();
		milestoneMeta2.setName("Client Assigned");
		milestoneMeta2.setBeginElement("Event_0tl0v1i");
		milestoneMeta2.setEndElement("Event_0uwmstq");
		milestoneMetas.add(milestoneMeta2);

		MilestoneMeta milestoneMeta3 = new MilestoneMeta();
		milestoneMeta3.setName("Welcome Call");
		milestoneMeta3.setBeginElement("Event_0c5dn9r");
		milestoneMeta3.setEndElement("Event_18ozc3h");
		milestoneMetas.add(milestoneMeta3);

		MilestoneMeta milestoneMeta4 = new MilestoneMeta();
		milestoneMeta4.setName("Gathering Info");
		milestoneMeta4.setBeginElement("Event_13zifud");
		milestoneMeta4.setEndElement("Event_0xoftqu");
		milestoneMetas.add(milestoneMeta4);

		MilestoneMeta milestoneMeta5 = new MilestoneMeta();
		milestoneMeta5.setName("Preparing Return");
		milestoneMeta5.setBeginElement("Event_1m2u0p3");
		milestoneMeta5.setEndElement("Event_1fhx7ai");
		milestoneMetas.add(milestoneMeta5);

		MilestoneMeta milestoneMeta6 = new MilestoneMeta();
		milestoneMeta6.setName("Evaluation");
		milestoneMeta6.setBeginElement("Event_0nucuju");
		milestoneMeta6.setEndElement("Event_0z9h1vr");
		milestoneMetas.add(milestoneMeta6);

		MilestoneMeta milestoneMeta7 = new MilestoneMeta();
		milestoneMeta7.setName("Client Review");
		milestoneMeta7.setBeginElement("Event_19jbr6k");
		milestoneMeta7.setEndElement("Event_1ueo2o0");
		milestoneMetas.add(milestoneMeta7);

		MilestoneMeta milestoneMeta8 = new MilestoneMeta();
		milestoneMeta8.setName("Filing");
		milestoneMeta8.setBeginElement("Event_0kymp5k");
		milestoneMeta8.setEndElement("Event_0udi3fn");
		milestoneMetas.add(milestoneMeta8);

		MilestoneMeta milestoneMeta9 = new MilestoneMeta();
		milestoneMeta9.setName("Post File");
		milestoneMeta9.setBeginElement("Event_0wmsmvo");
		milestoneMeta9.setEndElement("Event_19fen38");
		milestoneMetas.add(milestoneMeta9);
		return milestoneMetas;
	}

	
	public List<ActivityDetail> getActivityDetailData() {
		List<ActivityDetail> activityDetails = new ArrayList<>();
		ActivityDetail activityDetail1 =  ActivityDetail.builder()
				.activityId("Event_1dst8o1")
				.activityName("Begin Stage - Client Unassigned")
				.type(TaskType.MILESTONE)
				.attributes("{\n"
						+ "  \"modelAttributes\": {\n"
						+ "    \"events\": \"[\\\"start\\\"]\",\n"
						+ "    \"beginStage\": \"Client Unassigned\",\n"
						+ "    \"activityName\": \"Begin Stage - Client Unassigned\"\n"
						+ "  }\n"
						+ "}").build();
		activityDetails.add(activityDetail1);
		
		ActivityDetail activityDetail2 =  ActivityDetail.builder()
				.activityId("Event_13x1jdz")
				.activityName("End Stage + Milestone - Client Unassigned")
				.type(TaskType.MILESTONE)
				.attributes("{\n"
						+ "  \"modelAttributes\": {\n"
						+ "    \"events\": \"[\\\"start\\\"]\",\n"
						+ "    \"endStage\": \"Client Unassigned\",\n"
						+ "    \"traversalId\": \"1\",\n"
						+ "    \"activityName\": \"End Stage + Milestone - Client Unassigned\",\n"
						+ "    \"milestoneName\": \"Client Unassigned\",\n"
						+ "    \"stageDisplayName\": \"Client Unassigned\",\n"
						+ "    \"milestoneVisibility\": \"EXPERT_AND_CUSTOMER\",\n"
						+ "    \"milestoneDisplayName\": \"Client Unassigned\"\n"
						+ "  }\n"
						+ "}").build();
		activityDetails.add(activityDetail2);
		
		ActivityDetail activityDetail3 =  ActivityDetail.builder()
				.activityId("Event_0tl0v1i")
				.activityName("Begin Stage - Client Assigned")
				.type(TaskType.MILESTONE)
				.attributes("{\n"
						+ "  \"modelAttributes\": {\n"
						+ "    \"events\": \"[\\\"start\\\"]\",\n"
						+ "    \"beginStage\": \"Client Assigned\",\n"
						+ "    \"activityName\": \"Begin Stage - Client Assigned\"\n"
						+ "  }\n"
						+ "}").build();
		activityDetails.add(activityDetail3);
		
		ActivityDetail activityDetail4 =  ActivityDetail.builder()
				.activityId("Event_0uwmstq")
				.activityName("End Stage + Milestone - Client Assigned")
				.type(TaskType.MILESTONE)
				.attributes("{\n"
						+ "  \"modelAttributes\": {\n"
						+ "    \"events\": \"[\\\"start\\\"]\",\n"
						+ "    \"endStage\": \"Client Assigned\",\n"
						+ "    \"traversalId\": \"2\",\n"
						+ "    \"activityName\": \"End Stage + Milestone - Client Assigned\",\n"
						+ "    \"milestoneName\": \"Client Assigned\",\n"
						+ "    \"stageDisplayName\": \"Client Assigned\",\n"
						+ "    \"milestoneVisibility\": \"EXPERT_AND_CUSTOMER\",\n"
						+ "    \"milestoneDisplayName\": \"Client Assigned\"\n"
						+ "  }\n"
						+ "}").build();
		activityDetails.add(activityDetail4);
		
		ActivityDetail activityDetail5 =  ActivityDetail.builder()
				.activityId("Event_0c5dn9r")
				.activityName("Begin Stage - Welcome Call")
				.type(TaskType.MILESTONE)
				.attributes("{\n"
						+ "  \"modelAttributes\": {\n"
						+ "    \"events\": \"[\\\"start\\\"]\",\n"
						+ "    \"beginStage\": \"Welcome Call\",\n"
						+ "    \"activityName\": \"Begin Stage - Welcome Call\"\n"
						+ "  }\n"
						+ "}").build();
		activityDetails.add(activityDetail5);
		
		ActivityDetail activityDetail6 =  ActivityDetail.builder()
				.activityId("Event_18ozc3h")
				.activityName("End Stage + Milestone - Welcome Call")
				.type(TaskType.MILESTONE)
				.attributes("{\n"
						+ "  \"modelAttributes\": {\n"
						+ "    \"events\": \"[\\\"start\\\"]\",\n"
						+ "    \"endStage\": \"Welcome Call\",\n"
						+ "    \"traversalId\": \"3\",\n"
						+ "    \"activityName\": \"End Stage + Milestone - Welcome Call\",\n"
						+ "    \"milestoneName\": \"Welcome Call\",\n"
						+ "    \"stageDisplayName\": \"Welcome Call\",\n"
						+ "    \"milestoneVisibility\": \"EXPERT_AND_CUSTOMER\",\n"
						+ "    \"milestoneDisplayName\": \"Welcome Call\"\n"
						+ "  }\n"
						+ "}").build();
		activityDetails.add(activityDetail6);
		
		ActivityDetail activityDetail7 =  ActivityDetail.builder()
				.activityId("Event_13zifud")
				.activityName("Begin Stage - Gathering Info")
				.type(TaskType.MILESTONE)
				.attributes("{\n"
						+ "  \"modelAttributes\": {\n"
						+ "    \"events\": \"[\\\"start\\\"]\",\n"
						+ "    \"beginStage\": \"Gathering Info\",\n"
						+ "    \"activityName\": \"Begin Stage - Gathering Info\"\n"
						+ "  }\n"
						+ "}").build();
		activityDetails.add(activityDetail7);
		
		ActivityDetail activityDetail8 =  ActivityDetail.builder()
				.activityId("Event_0xoftqu")
				.activityName("End Stage Milestone - Gathering Info")
				.type(TaskType.MILESTONE)
				.attributes("{\n"
						+ "  \"modelAttributes\": {\n"
						+ "    \"events\": \"[\\\"start\\\"]\",\n"
						+ "    \"endStage\": \"Gathering Info\",\n"
						+ "    \"traversalId\": \"4\",\n"
						+ "    \"activityName\": \"End Stage Milestone - Gathering Info\",\n"
						+ "    \"milestoneName\": \"Gathering Info\",\n"
						+ "    \"stageDisplayName\": \"Gathering Info\",\n"
						+ "    \"milestoneVisibility\": \"EXPERT_AND_CUSTOMER\",\n"
						+ "    \"milestoneDisplayName\": \"Gathering Info\"\n"
						+ "  }\n"
						+ "}").build();
		activityDetails.add(activityDetail8);
		
		ActivityDetail activityDetail9 =  ActivityDetail.builder()
				.activityId("Event_1m2u0p3")
				.activityName("Begin Stage - Preparing Return")
				.type(TaskType.MILESTONE)
				.attributes("{\n"
						+ "  \"modelAttributes\": {\n"
						+ "    \"events\": \"[\\\"start\\\"]\",\n"
						+ "    \"beginStage\": \"Preparing Return\",\n"
						+ "    \"activityName\": \"Begin Stage - Preparing Return\"\n"
						+ "  }\n"
						+ "}").build();
		activityDetails.add(activityDetail9);
		
		ActivityDetail activityDetail10 =  ActivityDetail.builder()
				.activityId("Event_1fhx7ai")
				.activityName("End Stage + Milestone - Preparing Return")
				.type(TaskType.MILESTONE)
				.attributes("{\n"
						+ "  \"modelAttributes\": {\n"
						+ "    \"events\": \"[\\\"start\\\"]\",\n"
						+ "    \"endStage\": \"Preparing Return\",\n"
						+ "    \"traversalId\": \"5\",\n"
						+ "    \"activityName\": \"End Stage + Milestone - Preparing Return\",\n"
						+ "    \"milestoneName\": \"Preparing Return\",\n"
						+ "    \"stageDisplayName\": \"Preparing Return\",\n"
						+ "    \"milestoneVisibility\": \"EXPERT_AND_CUSTOMER\",\n"
						+ "    \"milestoneDisplayName\": \"Preparing Return\"\n"
						+ "  }\n"
						+ "}").build();
		activityDetails.add(activityDetail10);
		
		ActivityDetail activityDetail11 =  ActivityDetail.builder()
				.activityId("Event_0nucuju")
				.activityName("Begin Stage - Evaluation")
				.type(TaskType.MILESTONE)
				.attributes("{\n"
						+ "  \"modelAttributes\": {\n"
						+ "    \"events\": \"[\\\"start\\\"]\",\n"
						+ "    \"beginStage\": \"Evaluation\",\n"
						+ "    \"activityName\": \"Begin Stage - Evaluation\"\n"
						+ "  }\n"
						+ "}").build();
		activityDetails.add(activityDetail11);
		
		ActivityDetail activityDetail12 =  ActivityDetail.builder()
				.activityId("Event_0z9h1vr")
				.activityName("End Stage + Milestone - Evaluation")
				.type(TaskType.MILESTONE)
				.attributes("{\n"
						+ "  \"modelAttributes\": {\n"
						+ "    \"events\": \"[\\\"start\\\"]\",\n"
						+ "    \"endStage\": \"Evaluation\",\n"
						+ "    \"traversalId\": \"6\",\n"
						+ "    \"activityName\": \"End Stage + Milestone - Evaluation\",\n"
						+ "    \"milestoneName\": \"Evaluation\",\n"
						+ "    \"stageDisplayName\": \"Evaluation\",\n"
						+ "    \"milestoneVisibility\": \"EXPERT_AND_CUSTOMER\",\n"
						+ "    \"milestoneDisplayName\": \"Evaluation\"\n"
						+ "  }\n"
						+ "}").build();
		activityDetails.add(activityDetail12);
		
		ActivityDetail activityDetail13 =  ActivityDetail.builder()
				.activityId("Event_19jbr6k")
				.activityName("Begin Stage - Client Review")
				.type(TaskType.MILESTONE)
				.attributes("{\n"
						+ "  \"modelAttributes\": {\n"
						+ "    \"events\": \"[\\\"start\\\"]\",\n"
						+ "    \"beginStage\": \"Client Review\",\n"
						+ "    \"activityName\": \"Begin Stage - Client Review\"\n"
						+ "  }\n"
						+ "}").build();
		activityDetails.add(activityDetail13);
		
		ActivityDetail activityDetail14 =  ActivityDetail.builder()
				.activityId("Event_1ueo2o0")
				.activityName("End Stage + Milestone - Client Review")
				.type(TaskType.MILESTONE)
				.attributes("{\n"
						+ "  \"modelAttributes\": {\n"
						+ "    \"events\": \"[\\\"start\\\"]\",\n"
						+ "    \"endStage\": \"Client Review\",\n"
						+ "    \"traversalId\": \"7\",\n"
						+ "    \"activityName\": \"End Stage + Milestone - Client Review\",\n"
						+ "    \"milestoneName\": \"Client Review\",\n"
						+ "    \"stageDisplayName\": \"Client Review\",\n"
						+ "    \"milestoneVisibility\": \"EXPERT_AND_CUSTOMER\",\n"
						+ "    \"milestoneDisplayName\": \"Client Review\"\n"
						+ "  }\n"
						+ "}").build();
		activityDetails.add(activityDetail14);
		
//		ActivityDetail activityDetail15 =  ActivityDetail.builder()
//				.activityId("Event_0kymp5k")
//				.activityName("Begin Stage - Filing")
//				.type(TaskType.MILESTONE)
//				.attributes("{\n"
//						+ "  \"modelAttributes\": {\n"
//						+ "    \"events\": \"[\\\"start\\\"]\",\n"
//						+ "    \"beginStage\": \"Filing\",\n"
//						+ "    \"activityName\": \"Begin Stage - Filing\"\n"
//						+ "  }\n"
//						+ "}").build();
//		activityDetails.add(activityDetail15);
		
		ActivityDetail activityDetail16 =  ActivityDetail.builder()
				.activityId("Event_0udi3fn")
				.activityName("End Stage + Milestone - Filing")
				.type(TaskType.MILESTONE)
				.attributes("{\n"
						+ "  \"modelAttributes\": {\n"
						+ "    \"events\": \"[\\\"start\\\"]\",\n"
						+ "    \"endStage\": \"Filing\",\n"
						+ "    \"traversalId\": \"8\",\n"
						+ "    \"activityName\": \"End Stage + Milestone - Filing\",\n"
						+ "    \"milestoneName\": \"Filing\",\n"
						+ "    \"stageDisplayName\": \"Filing\",\n"
						+ "    \"milestoneVisibility\": \"EXPERT_AND_CUSTOMER\",\n"
						+ "    \"milestoneDisplayName\": \"Filing\"\n"
						+ "  }\n"
						+ "}").build();
		activityDetails.add(activityDetail16);
		
		ActivityDetail activityDetail17 =  ActivityDetail.builder()
				.activityId("Event_0wmsmvo")
				.activityName("Begin Stage - Post File")
				.type(TaskType.MILESTONE)
				.attributes("{\n"
						+ "  \"modelAttributes\": {\n"
						+ "    \"events\": \"[\\\"start\\\"]\",\n"
						+ "    \"beginStage\": \"Post File\",\n"
						+ "    \"activityName\": \"Begin Stage - Post File\"\n"
						+ "  }\n"
						+ "}").build();
		activityDetails.add(activityDetail17);
		
		ActivityDetail activityDetail18 =  ActivityDetail.builder()
				.activityId("Event_19fen38")
				.activityName("End Stage + Milestone - Post File")
				.type(TaskType.MILESTONE)
				.attributes("{\n"
						+ "  \"modelAttributes\": {\n"
						+ "    \"events\": \"[\\\"start\\\"]\",\n"
						+ "    \"endStage\": \"Post File\",\n"
						+ "    \"traversalId\": \"9\",\n"
						+ "    \"activityName\": \"End Stage + Milestone - Post File\",\n"
						+ "    \"milestoneName\": \"Post File\",\n"
						+ "    \"stageDisplayName\": \"Post File\",\n"
						+ "    \"milestoneVisibility\": \"EXPERT_AND_CUSTOMER\",\n"
						+ "    \"milestoneDisplayName\": \"Post File\"\n"
						+ "  }\n"
						+ "}").build();
		
		activityDetails.add(activityDetail18);
		
		ActivityDetail activityDetail19 =  ActivityDetail.builder()
				.activityId("Activity_0z1ajok")
				.activityName("Setup (StepFn)")
				.activityType("serviceTask")
				.type(TaskType.MILESTONE)
				.attributes("{\n"
						+ "  \"modelAttributes\": {\n"
						+ "    \"events\": \"[\\\"start\\\"]\",\n"
						+ "  }\n"
						+ "}").build();
		
		activityDetails.add(activityDetail19);
		
		return activityDetails;
		
	}
	
	
	List<ActivityProgressDetails> getActivityProgressDetailData(){
		List<ActivityProgressDetails> activityProgressDetails = new ArrayList<>();
		
		activityProgressDetails.add(ActivityProgressDetails.builder()
				.startTime(Timestamp.valueOf(LocalDateTime.now().minusHours(1)))
				.activityDefinitionDetail(ActivityDetail.builder().activityId("Event_1dst8o1").build()).build());
		
		activityProgressDetails.add(ActivityProgressDetails.builder()
				.startTime(Timestamp.valueOf(LocalDateTime.now().minusMinutes(55)))
				.activityDefinitionDetail(ActivityDetail.builder().activityId("Event_13x1jdz").build()).build());
		
		
		activityProgressDetails.add(ActivityProgressDetails.builder()
				.startTime(Timestamp.valueOf(LocalDateTime.now().minusMinutes(50)))
				.activityDefinitionDetail(ActivityDetail.builder().activityId("Event_0tl0v1i").build()).build());
		
		activityProgressDetails.add(ActivityProgressDetails.builder()
				.startTime(Timestamp.valueOf(LocalDateTime.now().minusMinutes(45)))
				.activityDefinitionDetail(ActivityDetail.builder().activityId("Event_0uwmstq").build()).build());
		
		activityProgressDetails.add(ActivityProgressDetails.builder()
				.startTime(Timestamp.valueOf(LocalDateTime.now().minusMinutes(40)))
				.activityDefinitionDetail(ActivityDetail.builder().activityId("Event_0c5dn9r").build()).build());
		
		activityProgressDetails.add(ActivityProgressDetails.builder()
				.startTime(Timestamp.valueOf(LocalDateTime.now().minusMinutes(30)))
				.activityDefinitionDetail(ActivityDetail.builder().activityId("Event_0udi3fn").build()).build());
		
		activityProgressDetails.add(ActivityProgressDetails.builder()
				.startTime(Timestamp.valueOf(LocalDateTime.now().minusMinutes(20)))
				.activityDefinitionDetail(ActivityDetail.builder().activityId("Activity_0z1ajok")
						.activityType("serviceTask").build()).build());
		
		activityProgressDetails.add(ActivityProgressDetails.builder()
				.startTime(Timestamp.valueOf(LocalDateTime.now().minusMinutes(10)))
				.activityDefinitionDetail(ActivityDetail.builder().activityId("Activity_0z1ajok")
						.activityType("serviceTask").build()).build());
		
		
		return activityProgressDetails;
	}

	@Test
	public void test_noFilterWithDuplicates() {
		ProcessDetails processDetail = ProcessDetails.builder().processId("process2").definitionDetails(
				DefinitionDetails.builder().templateDetails(
						TemplateDetails.builder().id("template2").templateName("engagementTTLiveFullService")
								.build()).build()).build();

		Mockito.when(processDetailsRepo.findProcessDetailsByRecordId(Mockito.anyString()))
				.thenReturn(Arrays.asList(new ProcessDetails[]{processDetail}));

		List<ActivityDetail> activityDetails = getActivityDetailData();
		Mockito.when(activityDetailsRepo.findByTemplateIdAndType(Mockito.anyString(),
				Mockito.eq(TaskType.MILESTONE))).thenReturn(activityDetails);

		List<ActivityProgressDetails> activityProgressDetails = getActivityProgressDetailData();
		// Add duplicate activity progress detail
		activityProgressDetails.add(ActivityProgressDetails.builder()
				.startTime(Timestamp.valueOf(LocalDateTime.now().minusHours(1)))
				.activityDefinitionDetail(ActivityDetail.builder().activityId("Event_1dst8o1").build())
				.build());

		Mockito.when(activityProgressDetailsRepo.findByProcessIdAndType(Mockito.anyString(),
				Mockito.eq(TaskType.MILESTONE))).thenReturn(activityProgressDetails);

		WorkflowMilestoneTrackResponse response = progressTrackService.getProgressDetails("Record1",
				null);
		Assert.assertEquals(9, response.getMilestones().size());
		Assert.assertEquals(1, response.getMilestones().stream()
				.filter(milestone -> milestone.getStatus().equals(WorkflowMilestoneStatus.IN_PROGRESS))
				.count());

		Assert.assertEquals(3, response.getMilestones().stream()
				.filter(milestone -> milestone.getStatus().equals(WorkflowMilestoneStatus.COMPLETED))
				.count());

		Assert.assertEquals(5, response.getMilestones().stream()
				.filter(milestone -> milestone.getStatus().equals(WorkflowMilestoneStatus.NOT_STARTED))
				.count());

	}
	@Test
	public void test_noFilter() {
		ProcessDetails processDetail = ProcessDetails.builder()
				.processId("process1")
				.definitionDetails(DefinitionDetails.builder()
						.templateDetails(TemplateDetails.builder().id("template1")
								.templateName("engagementTTLiveFullService").build())
						.build())
				.build();
		
		Mockito.when(processDetailsRepo.findProcessDetailsByRecordId(Mockito.anyString()))
				.thenReturn(Arrays.asList(new ProcessDetails[] {processDetail}));
		
		List<ActivityDetail> activityDetails = getActivityDetailData();
		Mockito.when(activityDetailsRepo.findByTemplateIdAndType(Mockito.anyString(),
				Mockito.eq(TaskType.MILESTONE))).thenReturn(activityDetails);
		
		List<ActivityProgressDetails> activityProgressDetails = getActivityProgressDetailData();
		Mockito.when(activityProgressDetailsRepo.findByProcessIdAndType(Mockito.anyString(),
				Mockito.eq(TaskType.MILESTONE))).thenReturn(activityProgressDetails);
		
		WorkflowMilestoneTrackResponse response = 
				progressTrackService.getProgressDetails("rec1", null);
		Assert.assertEquals("With No Filter response size should be 9", 9, response.getMilestones().size());
		Assert.assertEquals("More IN_PROGRESS Status milestone found",
				1, response.getMilestones().stream().filter(milestone -> 
				milestone.getStatus().equals(WorkflowMilestoneStatus.IN_PROGRESS)).count());
		
		Assert.assertEquals("More COMPLETED Status milestone found",
				3, response.getMilestones().stream().filter(milestone -> milestone.getStatus().equals(WorkflowMilestoneStatus.COMPLETED)).count());
		
		Assert.assertEquals("More NOT_STARTED Status milestone found",
				5, response.getMilestones().stream().filter(milestone -> milestone.getStatus().equals(WorkflowMilestoneStatus.NOT_STARTED)).count());
		
		response.getMilestones().stream().forEach(milestone -> {
			System.out.println(milestone.getMilestoneId());
			System.out.println(milestone.getCreatedDate());
		});
		
		Assert.assertEquals("Filing should be 1st milestone",
				"Filing",response.getMilestones().get(0).getMilestoneId());
		Assert.assertEquals("Filing milestone status should be COMPLETED",
				WorkflowMilestoneStatus.COMPLETED,response.getMilestones().get(0).getStatus());
		
		Assert.assertEquals("Welcome Call should be 2nd milestone",
				"Welcome Call",response.getMilestones().get(1).getMilestoneId());
		Assert.assertEquals("Welcome Call status should be IN_PROGRESS",
				WorkflowMilestoneStatus.IN_PROGRESS,response.getMilestones().get(1).getStatus());
		
		
		Assert.assertEquals("Client Assigned should be 3rd milestone",
				"Client Assigned",response.getMilestones().get(2).getMilestoneId());
		Assert.assertEquals("Client Assigned status should be COMPLETED",
				WorkflowMilestoneStatus.COMPLETED,response.getMilestones().get(2).getStatus());
		
		
		Assert.assertEquals("Client Unassigned should be 4th milestone",
				"Client Unassigned",response.getMilestones().get(3).getMilestoneId());
		Assert.assertEquals("Client Unassigned status should be COMPLETED",
				WorkflowMilestoneStatus.COMPLETED,response.getMilestones().get(3).getStatus());
		
	}
	
	
	@Test
	public void test_COMPLETED_Filter() {
		ProcessDetails processDetail = ProcessDetails.builder()
				.processId("process1")
				.definitionDetails(DefinitionDetails.builder()
						.templateDetails(TemplateDetails.builder().id("template1")
								.templateName("engagementTTLiveFullService").build())
						.build())
				.build();
		
		Mockito.when(processDetailsRepo.findProcessDetailsByRecordId(Mockito.anyString()))
				.thenReturn(Arrays.asList(new ProcessDetails[] {processDetail}));
		
		List<ActivityDetail> activityDetails = getActivityDetailData();
		Mockito.when(activityDetailsRepo.findByTemplateIdAndType(Mockito.anyString(),
				Mockito.eq(TaskType.MILESTONE))).thenReturn(activityDetails);
		
		List<ActivityProgressDetails> activityProgressDetails = getActivityProgressDetailData();
		Mockito.when(activityProgressDetailsRepo.findByProcessIdAndType(Mockito.anyString(),
				Mockito.eq(TaskType.MILESTONE))).thenReturn(activityProgressDetails);
		
		WorkflowMilestoneTrackResponse response = 
				progressTrackService.getProgressDetails("rec1", 
						new HashSet<>(Arrays.asList(new WorkflowMilestoneStatus[] {WorkflowMilestoneStatus.COMPLETED})));
		Assert.assertEquals("With Completed response size should be 3", 3, response.getMilestones().size());
		
		Assert.assertEquals("More COMPLETED Status milestone found",
				3, response.getMilestones().stream()
				.filter(milestone -> milestone.getStatus().equals(WorkflowMilestoneStatus.COMPLETED)).count());
		
		Assert.assertEquals("More NOT_STARTED Status milestone found",
				0, response.getMilestones().stream().
				filter(milestone -> milestone.getStatus().equals(WorkflowMilestoneStatus.NOT_STARTED)).count());
	
		Assert.assertEquals("More IN_PROGRESS Status milestone found",
				0, response.getMilestones().stream()
				.filter(milestone -> milestone.getStatus().equals(WorkflowMilestoneStatus.IN_PROGRESS)).count());
		
		response.getMilestones().stream().forEach(milestone -> {
			System.out.println(milestone.getMilestoneId());
			System.out.println(milestone.getCreatedDate());
		});
		
		Assert.assertEquals("Filing should be 1st milestone",
				"Filing",response.getMilestones().get(0).getMilestoneId());
		Assert.assertEquals("Filing milestone status should be COMPLETED",
				WorkflowMilestoneStatus.COMPLETED,response.getMilestones().get(0).getStatus());
		
		
		Assert.assertEquals("Client Assigned should be 2nd milestone",
				"Client Assigned",response.getMilestones().get(1).getMilestoneId());
		Assert.assertEquals("Client Assigned status should be COMPLETED",
				WorkflowMilestoneStatus.COMPLETED,response.getMilestones().get(1).getStatus());
		
		
		Assert.assertEquals("Client Unassigned should be 3rd milestone",
				"Client Unassigned",response.getMilestones().get(2).getMilestoneId());
		Assert.assertEquals("Client Unassigned status should be COMPLETED",
				WorkflowMilestoneStatus.COMPLETED,response.getMilestones().get(2).getStatus());
	}
	
	
	@Test
	public void test_ProcessNotFound() {
		Mockito.when(processDetailsRepo.findProcessDetailsByRecordId(Mockito.anyString()))
			.thenReturn(Arrays.asList(new ProcessDetails[] {}));
		
		WorkflowMilestoneTrackResponse response = 
				progressTrackService.getProgressDetails("rec1", 
						new HashSet<>(Arrays.asList(new WorkflowMilestoneStatus[] {WorkflowMilestoneStatus.COMPLETED})));	
		Assert.assertEquals("Zero Milestone to be return",
				0, response.getMilestones().size());
		
	}
	
	
	@Test
	public void test_NoMilestoneMeta() {
		ProcessDetails processDetail = ProcessDetails.builder()
				.processId("process1")
				.definitionDetails(DefinitionDetails.builder()
						.templateDetails(TemplateDetails.builder().id("template2")
								.templateName("engagementTTLiveFullService1").build())
						.build())
				.build();
		
		Mockito.when(processDetailsRepo.findProcessDetailsByRecordId(Mockito.anyString()))
				.thenReturn(Arrays.asList(new ProcessDetails[] {processDetail}));
		
		WorkflowMilestoneTrackResponse response = 
				progressTrackService.getProgressDetails("rec1", 
						new HashSet<>());
		Assert.assertEquals("response size should be 0", 0, response.getMilestones().size());
		
	}
	
	
	@Test
	public void test_DefaultTemplate_MilestoneMeta() {
		ProcessDetails processDetail = ProcessDetails.builder()
				.processId("process1")
				.definitionDetails(DefinitionDetails.builder()
						.templateDetails(TemplateDetails.builder().id("template2")
								.templateName("engagementTTLiveFullService").build())
						.build())
				.build();
		
		Mockito.when(processDetailsRepo.findProcessDetailsByRecordId(Mockito.anyString()))
				.thenReturn(Arrays.asList(new ProcessDetails[] {processDetail}));
		
		
		List<ActivityDetail> activityDetails = getActivityDetailData();
		Mockito.when(activityDetailsRepo.findByTemplateIdAndType(Mockito.anyString(),
				Mockito.eq(TaskType.MILESTONE))).thenReturn(activityDetails);
		
		List<ActivityProgressDetails> activityProgressDetails = getActivityProgressDetailData();
		Mockito.when(activityProgressDetailsRepo.findByProcessIdAndType(Mockito.anyString(),
				Mockito.eq(TaskType.MILESTONE))).thenReturn(activityProgressDetails);
		
		WorkflowMilestoneTrackResponse response = 
				progressTrackService.getProgressDetails("rec1", 
						new HashSet<>(Arrays.asList(new WorkflowMilestoneStatus[] {WorkflowMilestoneStatus.COMPLETED})));
		Assert.assertEquals("With Completed response size should be 3", 3, response.getMilestones().size());
		
		Assert.assertEquals("More COMPLETED Status milestone found",
				3, response.getMilestones().stream()
				.filter(milestone -> milestone.getStatus().equals(WorkflowMilestoneStatus.COMPLETED)).count());
		
		Assert.assertEquals("More NOT_STARTED Status milestone found",
				0, response.getMilestones().stream().
				filter(milestone -> milestone.getStatus().equals(WorkflowMilestoneStatus.NOT_STARTED)).count());
	
		Assert.assertEquals("More IN_PROGRESS Status milestone found",
				0, response.getMilestones().stream()
				.filter(milestone -> milestone.getStatus().equals(WorkflowMilestoneStatus.IN_PROGRESS)).count());
		
		response.getMilestones().stream().forEach(milestone -> {
			System.out.println(milestone.getMilestoneId());
			System.out.println(milestone.getCreatedDate());
		});
		
		Assert.assertEquals("Filing should be 1st milestone",
				"Filing",response.getMilestones().get(0).getMilestoneId());
		Assert.assertEquals("Filing milestone status should be COMPLETED",
				WorkflowMilestoneStatus.COMPLETED,response.getMilestones().get(0).getStatus());
		
		
		Assert.assertEquals("Client Assigned should be 2nd milestone",
				"Client Assigned",response.getMilestones().get(1).getMilestoneId());
		Assert.assertEquals("Client Assigned status should be COMPLETED",
				WorkflowMilestoneStatus.COMPLETED,response.getMilestones().get(1).getStatus());
		
		
		Assert.assertEquals("Client Unassigned should be 3rd milestone",
				"Client Unassigned",response.getMilestones().get(2).getMilestoneId());
		Assert.assertEquals("Client Unassigned status should be COMPLETED",
				WorkflowMilestoneStatus.COMPLETED,response.getMilestones().get(2).getStatus());
		
	}
	
	@Test
	public void test_afterPropertiesSet() {
		
		List<MilestoneMeta> milestoneMetas = milestoneMetas();

		ZeroStateConfig metaConfig = new ZeroStateConfig();
		metaConfig.setTemplateId("template1");
		metaConfig.setMilestoneMeta(milestoneMetas);
		
		ZeroStateConfig metaConfigTTLiveFullService = new ZeroStateConfig();
		metaConfigTTLiveFullService.setWorkflow("engagementTTLiveFullService");
		metaConfigTTLiveFullService.setMilestoneMeta(milestoneMetas);
		metaConfigTTLiveFullService.setTemplates(Arrays.asList(metaConfig));
		
		ZeroStateConfig metaConfigTestWorkflow = new ZeroStateConfig();
		metaConfigTestWorkflow.setWorkflow("testWorkflow");
		metaConfigTTLiveFullService.setMilestoneMeta(milestoneMetas);
		
		MilestoneProgressConfig milestoneProgressConfig = new MilestoneProgressConfig();
		milestoneProgressConfig.setZeroStateConfig(Arrays.asList(metaConfigTestWorkflow, metaConfigTTLiveFullService));
		ReflectionTestUtils.setField(progressTrackService, "milestoneProgressConfig", milestoneProgressConfig);
		progressTrackService.afterPropertiesSet();
	}
	

}
