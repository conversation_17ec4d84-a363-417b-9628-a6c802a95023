package com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.config;

import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.config.OINPSendNotificationConfig.WorkflowConstraints;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import org.junit.Assert;
import org.junit.Test;

public class OINPSendNotificationConfigTest {


  @Test
  public void testConfig_1(){
    Map<String, WorkflowConstraints> sendNotification = new HashMap<>();
    WorkflowConstraints workflowConstraints1 = new WorkflowConstraints();
    workflowConstraints1.setNotificationTypes(Set.of("IsMobile"));
    workflowConstraints1.setOwnerIds(Set.of(-1L));
    sendNotification.put("invoicecustomReminder", workflowConstraints1);

    WorkflowConstraints workflowConstraints2 = new WorkflowConstraints();
    workflowConstraints2.setNotificationTypes(Set.of("IsMobile"));
    workflowConstraints2.setOwnerIds(Set.of(1234L, 4567L));
    sendNotification.put("estimatecustomReminder", workflowConstraints2);

    OINPSendNotificationConfig oinpSendNotificationConfig = new OINPSendNotificationConfig();
    oinpSendNotificationConfig.setSendNotification(sendNotification);

    Assert.assertTrue(oinpSendNotificationConfig.isNotificationEnabled(
        "invoicecustomReminder", "IsMobile", 12345L));

    Assert.assertTrue(oinpSendNotificationConfig.isNotificationEnabled(
        "estimatecustomReminder", "IsMobile", 1234L));

    Assert.assertFalse(oinpSendNotificationConfig.isNotificationEnabled(
        "estimatecustomReminder", "IsEmail", 1234L));

    Assert.assertFalse(oinpSendNotificationConfig.isNotificationEnabled(
        "estimatecustomReminder", "IsMobile", 12345L));
  }
  @Test
  public void testConfig_2_nullTest(){
    OINPSendNotificationConfig oinpSendNotificationConfig = new OINPSendNotificationConfig();
    Assert.assertNull(oinpSendNotificationConfig.getSendNotification());
    Assert.assertFalse(oinpSendNotificationConfig
        .isNotificationEnabled("abc", "mobile", 1234L));
  }
}
