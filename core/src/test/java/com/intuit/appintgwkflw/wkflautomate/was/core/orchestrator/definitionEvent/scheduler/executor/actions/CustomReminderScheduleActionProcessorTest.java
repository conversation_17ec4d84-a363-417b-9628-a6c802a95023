package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions;

import static org.mockito.Mockito.times;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.IXPManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.threadPool.DuzzitPaginationConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.threadPool.RecordDuzzitPaginationConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.fetcher.RecordListFetcher;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.publisher.TriggerEventPublisher;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.SchedulerAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.TriggerTargetAPI;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.MetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.Trigger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.fetcher.RecordQueryConnectorResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.listner.EventScheduleMessageData;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class CustomReminderScheduleActionProcessorTest {
  @InjectMocks private CustomReminderScheduleActionProcessor customReminderScheduleActionProcessor;
  @Mock private RecordListFetcher recordListFetcher;
  @Mock private WASContextHandler wasContextHandler;
  @Mock private TriggerEventPublisher triggerEventPublisher;
  @Mock private EventScheduleHelper eventScheduleHelper;
  @Mock private IXPManager ixpManager;
  @Mock private DuzzitPaginationConfig paginationConfig;
  @Mock private DefinitionActivityDetailsRepository definitionActivityDetailsRepository;

  private static final long ownerId = 123456789;
  private static final String APPCONNECT_DUZZIT_RESPONSE =
      TestHelper.readResourceAsString("duzzitOutputs/appconnectCRResponse.json");
  private final RecordQueryConnectorResponse recordQueryConnectorResponse =
      ObjectConverter.fromJson(APPCONNECT_DUZZIT_RESPONSE, RecordQueryConnectorResponse.class);
  private static final String CUSTOM_REMINDER_WORKFLOW_DEF =
      TestHelper.readResourceAsString("bpmn/customWorkflowDefinition.bpmn");

  private static final String CUSTOM_REMINDER_WORKFLOW =
      TestHelper.readResourceAsString("bpmn/customReminderTest.bpmn");

  @Captor
  ArgumentCaptor<Trigger> triggerArgumentCaptor;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(
        customReminderScheduleActionProcessor, "recordListFetcher", recordListFetcher);
    ReflectionTestUtils.setField(
        customReminderScheduleActionProcessor, "wasContextHandler", wasContextHandler);
    ReflectionTestUtils.setField(
        customReminderScheduleActionProcessor, "triggerEventPublisher", triggerEventPublisher);
    ReflectionTestUtils.setField(
        customReminderScheduleActionProcessor, "eventScheduleHelper", eventScheduleHelper);
    ReflectionTestUtils.setField(
            customReminderScheduleActionProcessor, "ixpManager", ixpManager);
    ReflectionTestUtils.setField(
    	customReminderScheduleActionProcessor, "paginationConfig", paginationConfig);
    ReflectionTestUtils.setField(
        customReminderScheduleActionProcessor, "definitionActivityDetailsRepository",
        definitionActivityDetailsRepository);
    Mockito.when(
            eventScheduleHelper.isEventSchedulingEnabledForWorkflow(Mockito.any(), Mockito.any()))
        .thenReturn(true);
  }

  @Test
  public void testProcess() {
    Mockito.when(recordListFetcher.fetchRecords(Mockito.any()))
        .thenReturn(recordQueryConnectorResponse);
    customReminderScheduleActionProcessor.process(
        getSchedulerDetails(CUSTOM_REMINDER_WORKFLOW_DEF), getMessageData());
    Mockito.verify(recordListFetcher, times(1)).fetchRecords(Mockito.any());
    Mockito.verify(triggerEventPublisher, times(5)).publishTriggerEvent(Mockito.any());
  }

  @Test
  public void testTriggerMetaData() {
    Mockito.when(recordListFetcher.fetchRecords(Mockito.any()))
            .thenReturn(recordQueryConnectorResponse);
    customReminderScheduleActionProcessor.process(
            getSchedulerDetails(CUSTOM_REMINDER_WORKFLOW_DEF), getMessageData());
    Mockito.verify(recordListFetcher, times(1)).fetchRecords(Mockito.any());
    Mockito.verify(triggerEventPublisher, times(5))
    	.publishTriggerEvent(Mockito.any());

    Mockito.verify(triggerEventPublisher, times(5))
    	.publishTriggerEvent(triggerArgumentCaptor.capture());

    Trigger trigger = triggerArgumentCaptor.getValue();
    Assert.assertFalse(trigger.getMetaData().isBlockProcessOnSignalFailure());
  }

  @Test
  public void testProcessWorkflowNotEnabled() {
    Mockito.when(
        eventScheduleHelper.isEventSchedulingEnabledForWorkflow(Mockito.any(), Mockito.any()))
        .thenReturn(false);
    customReminderScheduleActionProcessor.process(
        getSchedulerDetails(CUSTOM_REMINDER_WORKFLOW_DEF), getMessageData());
    Mockito.verify(recordListFetcher, times(0)).fetchRecords(Mockito.any());
  }

  @Test
  public void testProcessEmptyReponse() {
    Mockito.when(recordListFetcher.fetchRecords(Mockito.any()))
        .thenReturn(new RecordQueryConnectorResponse());
    customReminderScheduleActionProcessor.process(
        getSchedulerDetails(CUSTOM_REMINDER_WORKFLOW_DEF), getMessageData());
    Mockito.verify(recordListFetcher, times(1)).fetchRecords(Mockito.any());
    Mockito.verify(triggerEventPublisher, times(0)).publishTriggerEvent(Mockito.any());
  }

  @Test
  public void testProcess_withoutParameterDetails() {
    Mockito.when(recordListFetcher.fetchRecords(Mockito.any()))
        .thenReturn(recordQueryConnectorResponse);
    customReminderScheduleActionProcessor.process(
        getSchedulerDetails(CUSTOM_REMINDER_WORKFLOW), getMessageData());
    Mockito.verify(recordListFetcher, times(1)).fetchRecords(Mockito.any());
  }
  
  
  @Test
  public void testProcess_NewInputParam() {
    Mockito.when(recordListFetcher.fetchRecords(Mockito.any()))
        .thenReturn(recordQueryConnectorResponse);
    Mockito.when(ixpManager.getBoolean(Mockito.anyString(), 
    		Mockito.anyString())).thenReturn(true);
    
    Map<RecordType, RecordDuzzitPaginationConfig> recordConfig = new HashMap<>();
    recordConfig.put(RecordType.INVOICE, new RecordDuzzitPaginationConfig());
    Mockito.when(paginationConfig.getRecordConfig()).thenReturn(recordConfig);
    		
    customReminderScheduleActionProcessor.process(
        getSchedulerDetails(CUSTOM_REMINDER_WORKFLOW_DEF), getMessageData());
    Mockito.verify(recordListFetcher, times(1)).fetchRecords(Mockito.any());
    Mockito.verify(triggerEventPublisher, times(5)).publishTriggerEvent(Mockito.any());
  }
  
  
  @Test
  public void testProcess_NewInputParam_IXPFalse() {
    Mockito.when(recordListFetcher.fetchRecords(Mockito.any()))
        .thenReturn(recordQueryConnectorResponse);
    Mockito.when(ixpManager.getBoolean(Mockito.anyString(), 
    		Mockito.anyString())).thenReturn(false);
    
    Map<RecordType, RecordDuzzitPaginationConfig> recordConfig = new HashMap<>();
    recordConfig.put(RecordType.INVOICE, new RecordDuzzitPaginationConfig());
    Mockito.when(paginationConfig.getRecordConfig()).thenReturn(recordConfig);
    		
    customReminderScheduleActionProcessor.process(
        getSchedulerDetails(CUSTOM_REMINDER_WORKFLOW_DEF), getMessageData());
    Mockito.verify(recordListFetcher, times(1)).fetchRecords(Mockito.any());
    Mockito.verify(triggerEventPublisher, times(5)).publishTriggerEvent(Mockito.any());
  }
  
  @Test
  public void testProcess_NewInputParam_DifferentRecordType() {
    Mockito.when(recordListFetcher.fetchRecords(Mockito.any()))
        .thenReturn(recordQueryConnectorResponse);
    
    Map<RecordType, RecordDuzzitPaginationConfig> recordConfig = new HashMap<>();
    recordConfig.put(RecordType.BILL, new RecordDuzzitPaginationConfig());
    Mockito.when(paginationConfig.getRecordConfig()).thenReturn(recordConfig);
    		
    customReminderScheduleActionProcessor.process(
        getSchedulerDetails(CUSTOM_REMINDER_WORKFLOW_DEF), getMessageData());
    Mockito.verify(recordListFetcher, times(1)).fetchRecords(Mockito.any());
    Mockito.verify(triggerEventPublisher, times(5)).publishTriggerEvent(Mockito.any());
  }
  
  @Test
  public void testProcess_OtherScheduleAction_WithTestScope() {
    Mockito.when(recordListFetcher.fetchRecords(Mockito.any()))
        .thenReturn(recordQueryConnectorResponse);
    SchedulerDetails schedulerDetails = getSchedulerDetails(CUSTOM_REMINDER_WORKFLOW_DEF);
    schedulerDetails.setSchedulerAction(SchedulerAction.CUSTOM_REMINDER_CUSTOM_WAIT);
    EventScheduleMessageData eventScheduleMessageData = getMessageData();
    eventScheduleMessageData.setScope("test");
	customReminderScheduleActionProcessor.process(schedulerDetails, eventScheduleMessageData);
    Mockito.verify(recordListFetcher, times(1)).fetchRecords(Mockito.any());
    Mockito.verify(triggerEventPublisher, times(5)).publishTriggerEvent(Mockito.any());
  }

  @Test
  public void testProcess_OtherScheduleAction() {
    Mockito.when(recordListFetcher.fetchRecords(Mockito.any()))
            .thenReturn(recordQueryConnectorResponse);
    SchedulerDetails schedulerDetails = getSchedulerDetails(CUSTOM_REMINDER_WORKFLOW_DEF);
    schedulerDetails.setSchedulerAction(SchedulerAction.CUSTOM_REMINDER_CUSTOM_WAIT);
    customReminderScheduleActionProcessor.process(
            schedulerDetails, getMessageData());
    Mockito.verify(recordListFetcher, times(1)).fetchRecords(Mockito.any());
    Mockito.verify(triggerEventPublisher, times(5)).publishTriggerEvent(Mockito.any());
  }

  @Test
  public void testPrepareTriggerEventWithTestScope() throws Exception {
    String scope = "test";
    Map<String, String> data = Map.of("ID_KEY", "entityId");

    // Use reflection to access the private method
    Method method = CustomReminderScheduleActionProcessor.class.getDeclaredMethod("prepareTriggerEvent", SchedulerDetails.class, Map.class, String.class);
    method.setAccessible(true);
    // Act
    Trigger result = (Trigger) method.invoke(customReminderScheduleActionProcessor, getSchedulerDetails(CUSTOM_REMINDER_WORKFLOW_DEF), data, scope);

    // Assert
    MetaData metaData = result.getMetaData();
    Assert.assertEquals("invoice", metaData.getEntityType());
    Assert.assertEquals("test", metaData.getScope());
    Assert.assertEquals(TriggerTargetAPI.EVALUATE_AND_TRIGGER_V2, metaData.getTargetApi());
  }

  @Test
  public void testPrepareTriggerEventWithoutScope() throws Exception {
    Map<String, String> data = Map.of("ID_KEY", "entityId");

    // Use reflection to access the private method
    Method method = CustomReminderScheduleActionProcessor.class.getDeclaredMethod("prepareTriggerEvent", SchedulerDetails.class, Map.class, String.class);
    method.setAccessible(true);
    // Act
    Trigger result = (Trigger) method.invoke(customReminderScheduleActionProcessor, getSchedulerDetails(CUSTOM_REMINDER_WORKFLOW_DEF), data, null);

    // Assert
    MetaData metaData = result.getMetaData();
    Assert.assertEquals("invoice", metaData.getEntityType());
    Assert.assertNull(metaData.getScope());
    Assert.assertEquals(TriggerTargetAPI.EVALUATE_AND_TRIGGER_V2, metaData.getTargetApi());
  }
  
  @Test
  public void test_getWorkflowName() {
	  Assert.assertEquals(WorkflowNameEnum.CUSTOM_REMINDER,
			  customReminderScheduleActionProcessor.getWorkflowName());
  }

  private SchedulerDetails getSchedulerDetails(String data) {
    return SchedulerDetails.builder()
        .definitionDetails(getDefinitionDetails(data))
        .schedulerId("1234")
        .schedulerAction(SchedulerAction.CUSTOM_REMINDER_CUSTOM_START)
        .ownerId(ownerId)
        .build();
  }

  private EventScheduleMessageData getMessageData() {
    EventScheduleMessageData eventScheduleMessageData = new EventScheduleMessageData();
    eventScheduleMessageData.setMessageId("mes123");
    eventScheduleMessageData.setScheduleId("1234");
    return eventScheduleMessageData;
  }

  private DefinitionDetails getDefinitionDetails(String data) {
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("def123");
    definitionDetails.setOwnerId(Long.valueOf(ownerId));
    definitionDetails.setRecordType(RecordType.INVOICE);
    definitionDetails.setDefinitionData(data.getBytes());
    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setOfferingId("off123");
    definitionDetails.setTemplateDetails(templateDetails);
    return definitionDetails;
  }

  @Test
  public void testMultiConditionProcess_CustomStart() {
    Mockito.when(recordListFetcher.fetchRecords(Mockito.any()))
        .thenReturn(recordQueryConnectorResponse);

    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setTemplateCategory(TemplateCategory.CUSTOM.toString());
    templateDetails.setDefinitionType(DefinitionType.SINGLE);

    SchedulerDetails schedulerDetails = getSchedulerDetails(CUSTOM_REMINDER_WORKFLOW_DEF);
    schedulerDetails.getDefinitionDetails().setDefinitionData(null);
    schedulerDetails.getDefinitionDetails().setTemplateDetails(templateDetails);

    DefinitionActivityDetail definitionActivityDetail = new DefinitionActivityDetail();
    definitionActivityDetail.setUserAttributes("{\"selected\": false, \"parameters\": {\"isRecurring\": {\"fieldValue\": [\"false\"]}, \"recurFrequency\": {\"fieldValue\": [\"2\"]}, \"FilterCondition\": {\"fieldValue\": [\"{\\\"rules\\\":[{\\\"parameterType\\\":\\\"DAYS\\\",\\\"parameterName\\\":\\\"TxnDueDays\\\",\\\"conditionalExpression\\\":\\\"BF 2\\\",\\\"$sdk_validated\\\":true},{\\\"parameterType\\\":\\\"DOUBLE\\\",\\\"parameterName\\\":\\\"TxnAmount\\\",\\\"conditionalExpression\\\":\\\"BTW 20000,20010\\\",\\\"$sdk_validated\\\":true}]}\"]}, \"FilterRecordType\": {\"fieldValue\": [\"invoice\"]}, \"FilterCloseTaskConditions\": {\"fieldValue\": [\"txn_paid\"]}}}");
    definitionActivityDetail.setActivityId("callActivity-1");

    List<DefinitionActivityDetail> definitionActivityDetailsList = new ArrayList<>();
    definitionActivityDetailsList.add(definitionActivityDetail);

    Mockito.when(definitionActivityDetailsRepository.findAllByDefinitionDetails_DefinitionId(Mockito.any()))
        .thenReturn(Optional.of(definitionActivityDetailsList));

    customReminderScheduleActionProcessor.process(schedulerDetails, getMessageData());

    Mockito.verify(recordListFetcher, times(1)).fetchRecords(Mockito.any());
    Mockito.verify(triggerEventPublisher, times(5)).publishTriggerEvent(Mockito.any());
  }

  @Test
  public void testMultiConditionProcess_CustomWait() {
    Mockito.when(recordListFetcher.fetchRecords(Mockito.any()))
        .thenReturn(recordQueryConnectorResponse);

    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setTemplateCategory(TemplateCategory.CUSTOM.toString());
    templateDetails.setDefinitionType(DefinitionType.SINGLE);

    SchedulerDetails schedulerDetails = getSchedulerDetails(CUSTOM_REMINDER_WORKFLOW_DEF);
    schedulerDetails.setSchedulerAction(SchedulerAction.CUSTOM_REMINDER_CUSTOM_WAIT);
    schedulerDetails.getDefinitionDetails().setDefinitionData(null);
    schedulerDetails.getDefinitionDetails().setTemplateDetails(templateDetails);

    DefinitionActivityDetail definitionActivityDetail = new DefinitionActivityDetail();
    definitionActivityDetail.setUserAttributes("{\"selected\": false, \"parameters\": {\"isRecurring\": {\"fieldValue\": [\"false\"]}, \"recurFrequency\": {\"fieldValue\": [\"2\"]}, \"FilterCondition\": {\"fieldValue\": [\"{\\\"rules\\\":[{\\\"parameterType\\\":\\\"DAYS\\\",\\\"parameterName\\\":\\\"TxnDueDays\\\",\\\"conditionalExpression\\\":\\\"BF 2\\\",\\\"$sdk_validated\\\":true},{\\\"parameterType\\\":\\\"DOUBLE\\\",\\\"parameterName\\\":\\\"TxnAmount\\\",\\\"conditionalExpression\\\":\\\"BTW 20000,20010\\\",\\\"$sdk_validated\\\":true}]}\"]}, \"FilterRecordType\": {\"fieldValue\": [\"invoice\"]}, \"FilterCloseTaskConditions\": {\"fieldValue\": [\"txn_paid\"]}}}");
    definitionActivityDetail.setActivityId("callActivity-1");

    List<DefinitionActivityDetail> definitionActivityDetailsList = new ArrayList<>();
    definitionActivityDetailsList.add(definitionActivityDetail);

    Mockito.when(definitionActivityDetailsRepository.findAllByDefinitionDetails_DefinitionId(Mockito.any()))
        .thenReturn(Optional.of(definitionActivityDetailsList));

    customReminderScheduleActionProcessor.process(schedulerDetails, getMessageData());

    Mockito.verify(recordListFetcher, times(1)).fetchRecords(Mockito.any());
    Mockito.verify(triggerEventPublisher, times(5)).publishTriggerEvent(Mockito.any());
  }

  @Test
  public void testMultiConditionProcess_CustomRecur() {
    Mockito.when(recordListFetcher.fetchRecords(Mockito.any()))
        .thenReturn(recordQueryConnectorResponse);

    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setTemplateCategory(TemplateCategory.CUSTOM.toString());
    templateDetails.setDefinitionType(DefinitionType.SINGLE);

    SchedulerDetails schedulerDetails = getSchedulerDetails(CUSTOM_REMINDER_WORKFLOW_DEF);
    schedulerDetails.setSchedulerAction(SchedulerAction.CUSTOM_REMINDER_CUSTOM_WAIT);
    schedulerDetails.getDefinitionDetails().setDefinitionData(null);
    schedulerDetails.getDefinitionDetails().setTemplateDetails(templateDetails);

    DefinitionActivityDetail definitionActivityDetail = new DefinitionActivityDetail();
    definitionActivityDetail.setUserAttributes("{\"selected\": false, \"parameters\": {\"isRecurring\": {\"fieldValue\": [\"true\"]}, \"recurFrequency\": {\"fieldValue\": [\"2\"]}, \"FilterCondition\": {\"fieldValue\": [\"{\\\"rules\\\":[{\\\"parameterType\\\":\\\"DAYS\\\",\\\"parameterName\\\":\\\"TxnDueDays\\\",\\\"conditionalExpression\\\":\\\"BF 2\\\",\\\"$sdk_validated\\\":true},{\\\"parameterType\\\":\\\"DOUBLE\\\",\\\"parameterName\\\":\\\"TxnAmount\\\",\\\"conditionalExpression\\\":\\\"BTW 20000,20010\\\",\\\"$sdk_validated\\\":true}]}\"]}, \"FilterRecordType\": {\"fieldValue\": [\"invoice\"]}, \"FilterCloseTaskConditions\": {\"fieldValue\": [\"txn_paid\"]}}}");
    definitionActivityDetail.setActivityId("callActivity-1");

    DefinitionActivityDetail noPathDefinitionActivityDetail = new DefinitionActivityDetail();
    noPathDefinitionActivityDetail.setUserAttributes("{\"selected\": false, \"parameters\": {\"isRecurring\": {\"fieldValue\": [\"false\"]}, \"recurFrequency\": {\"fieldValue\": []}, \"FilterCondition\": {\"fieldValue\": [\"{\\\"rules\\\":[{\\\"parameterType\\\":\\\"DAYS\\\",\\\"parameterName\\\":\\\"TxnDueDays\\\",\\\"conditionalExpression\\\":\\\"BF 2\\\",\\\"$sdk_validated\\\":true},{\\\"parameterType\\\":\\\"DOUBLE\\\",\\\"parameterName\\\":\\\"TxnAmount\\\",\\\"conditionalExpression\\\":\\\"NOT_BTW 20000,20010\\\",\\\"$sdk_validated\\\":true}]}\"]}, \"FilterRecordType\": {\"fieldValue\": [\"invoice\"]}, \"FilterCloseTaskConditions\": {\"fieldValue\": [\"txn_paid\"]}}}");
    noPathDefinitionActivityDetail.setActivityId("callActivity-2");

    List<DefinitionActivityDetail> definitionActivityDetailsList = new ArrayList<>();
    definitionActivityDetailsList.add(definitionActivityDetail);
    definitionActivityDetailsList.add(noPathDefinitionActivityDetail);

    Mockito.when(definitionActivityDetailsRepository.findAllByDefinitionDetails_DefinitionId(Mockito.any()))
        .thenReturn(Optional.of(definitionActivityDetailsList));

    customReminderScheduleActionProcessor.process(schedulerDetails, getMessageData());

    // 2 wait calls (1 per call activity and 1 recur call)
    Mockito.verify(recordListFetcher, times(3)).fetchRecords(Mockito.any());
    Mockito.verify(triggerEventPublisher, times(15)).publishTriggerEvent(Mockito.any());
  }
}
