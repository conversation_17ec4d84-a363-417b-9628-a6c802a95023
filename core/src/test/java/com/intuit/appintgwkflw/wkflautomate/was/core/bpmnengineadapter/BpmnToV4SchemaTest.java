package com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowGlobalConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ReadCustomDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.TemplateLabelsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.impl.BpmnProcessorImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.ConfigurationDefinitionUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CreatorType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Template;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
public class BpmnToV4SchemaTest {

    private static final String PATH_INVOICE_APPROVAL_BPMN = "src/test/resources/bpmn/invoiceapproval.bpmn";
    private static final String INVOICE_APPROVAL_BPMN = "bpmn/invoiceapproval.bpmn";
    private static final String INVOICE_APPROVAL_DMN =
            "dmn/decision_invoiceapproval.dmn";
    private static final String stepDetails =
            "{   \"newInvoiceCreated_invoiceapproval\": [     \"newInvoiceCreated_invoiceapproval\",     \"decision_invoiceapproval\",     \"sendForApproval_invoiceapproval\"   ],  \"waitForApproval1_invoiceApproval\": [     \"waitForApproval1_invoiceApproval\",     \"exclusiveGateway_invoiceApproval\",     \"sendRejectNotification_invoiceApproval\",     \"sendApproveNotification_invoiceApproval\"   ],   \"waitForTimerToElapse1_invoiceApproval\": [     \"waitForTimerToElapse1_invoiceApproval\",     \"evaluateUserDefinedAction_invoiceApproval\",     \"sendReminderEmail_invoiceApproval\",     \"autoUpdateAsApproved_invoiceApproval\",     \"sendNotificationToCreator_invoiceapproval\"   ],   \"waitForApproval2_invoiceApproval\": [     \"waitForApproval2_invoiceApproval\",     \"exclusiveGateway_invoiceApproval\",     \"sendApproveNotification_invoiceApproval\",     \"sendRejectNotification_invoiceApproval\"   ],   \"waitForTimerToElapse2_invoiceApproval\": [     \"waitForTimerToElapse2_invoiceApproval\",     \"autoRejectInvoice_invoiceApproval\",     \"sendAutoRejectNotification_invoiceApproval\"   ] }";
    private static final String startEventId = "newInvoiceCreated_invoiceapproval";
    private static final String OFFER_ID = "offerId";
    private static final String OWNER_ID = "1234";
    private static final String CREATED_BY = "1234";
    private static final String REALM_ID = "realmId";
    private static final String LOCAL_ID = "localId";
    private static final String NAME = "name";
    private static final String DESCRIPTION = "description";
    @MockBean
    private WorkflowGlobalConfiguration workflowGlobalConfiguration;
    @MockBean
    private ReadCustomDefinitionHandler readCustomDefinitionHandler;
    @MockBean
    private WASContextHandler wasContextHandler;
    @MockBean
    private TranslationService translationService;
    @MockBean
    private TemplateLabelsService templateLabelsService;
    @MockBean
    private TemplateBuilder templateBuilder;
    @MockBean
    private FeatureFlagManager featureFlagManager;
    @Autowired
    private BpmnProcessorImpl bpmnProcessor;

    /**
     * @param fileName
     * @return : BPMN Model Instance
     */
    private static BpmnModelInstance readBPMNFile(String fileName) {
        return Bpmn.readModelFromStream(
                BpmnToV4SchemaTest.class.getClassLoader().getResourceAsStream(fileName));
    }

    /**
     * @param fileName
     * @return : DMN Model Instance
     */
    private static DmnModelInstance readDMNFile(String fileName) {
        return Dmn.readModelFromStream(
                BpmnToV4SchemaTest.class.getClassLoader().getResourceAsStream(fileName));
    }

    @Before
    public void setup() {
        Mockito.when(templateBuilder.buildTemplateDetails(Mockito.any(), Mockito.any())).thenReturn(new Template());
    }

    @Test
    public void checkNull() {
        BpmnModelInstance modelInstance = BpmnToV4SchemaTest.readBPMNFile(INVOICE_APPROVAL_BPMN);
        Assert.assertNotNull(modelInstance);
        List<DmnModelInstance> dmnModelInstances =
                Collections.singletonList(BpmnToV4SchemaTest.readDMNFile(INVOICE_APPROVAL_DMN));
        Assert.assertNotNull(dmnModelInstances);
    }

    @Test
    public void checkForTotalWorkFlowSteps() throws IOException {
        File bpmnFile = new File(PATH_INVOICE_APPROVAL_BPMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateDetails bpmnDetails =
                TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(NAME)
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .description(DESCRIPTION)
                        .status(Status.ENABLED)
                        .recordType(RecordType.INVOICE)
                        .templateName("customApproval")
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .build();
        BpmnModelInstance modelInstance = BpmnToV4SchemaTest.readBPMNFile(INVOICE_APPROVAL_BPMN);
        List<DmnModelInstance> dmnModelInstances =
                Collections.singletonList(BpmnToV4SchemaTest.readDMNFile(INVOICE_APPROVAL_DMN));
        Assert.assertNotNull(modelInstance);
        Assert.assertNotNull(dmnModelInstances);

        DefinitionInstance definitionInstance = CustomWorkflowUtil.generateDefinitionInstanceForBpmnProcessing(
                modelInstance,
                dmnModelInstances,
                bpmnDetails);
        Template t =
                (Template)
                        bpmnProcessor.processBpmn(
                                definitionInstance,
                                GlobalId.create(REALM_ID, LOCAL_ID),
                                false);
        Assert.assertNotNull(t);
        Assert.assertEquals(2, t.getWorkflowSteps().size());
    }

    @Test
    public void checkTotalActionsAndTriggers() throws IOException {
        File bpmnFile = new File(PATH_INVOICE_APPROVAL_BPMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateDetails bpmnDetails =
                TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(NAME)
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .description(DESCRIPTION)
                        .templateName("customApproval")
                        .status(Status.ENABLED)
                        .recordType(RecordType.INVOICE)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .build();
        BpmnModelInstance modelInstance = BpmnToV4SchemaTest.readBPMNFile(INVOICE_APPROVAL_BPMN);
        List<DmnModelInstance> dmnModelInstances =
                Collections.singletonList(BpmnToV4SchemaTest.readDMNFile(INVOICE_APPROVAL_DMN));
        Assert.assertNotNull(modelInstance);
        Assert.assertNotNull(dmnModelInstances);

        DefinitionInstance definitionInstance = CustomWorkflowUtil.generateDefinitionInstanceForBpmnProcessing(
                modelInstance,
                dmnModelInstances,
                bpmnDetails);

        Template template =
                (Template)
                        bpmnProcessor.processBpmn(
                                definitionInstance,
                                GlobalId.create(REALM_ID, LOCAL_ID),
                                false);
        Assert.assertNotNull(template);
        Assert.assertEquals(2, template.getWorkflowSteps().size());
        template
                .getWorkflowSteps()
                .forEach(
                        workflowStep -> {
                            Assert.assertNotNull(workflowStep.getTrigger());
                        });
    }

    @Test
    public void checkDmns() {
        List<DmnModelInstance> dmnModelInstances =
                Collections.singletonList(BpmnToV4SchemaTest.readDMNFile(INVOICE_APPROVAL_DMN));
        Assert.assertNotNull(dmnModelInstances);
        dmnModelInstances.forEach(
                t -> {
                    Assert.assertNotNull(t);
                });
    }

    @Test
    public void testConfigurationDefinitionUtil() {
        BpmnModelInstance modelInstance = BpmnToV4SchemaTest.readBPMNFile(INVOICE_APPROVAL_BPMN);
        Collection<Process> processes = modelInstance.getModelElementsByType(Process.class);
        final List<?>[] startEvent = new List<?>[1];
        processes.forEach(
                t -> {
                    startEvent[0] =
                            t.getFlowElements().stream()
                                    .filter(
                                            u ->
                                                    u.getElementType()
                                                            .getTypeName()
                                                            .equalsIgnoreCase(BpmnComponentType.START_EVENT.getName()))
                                    .collect(Collectors.toList());
                });
        Assert.assertEquals(1, startEvent[0].size());
        Collection<StartEvent> startEvents = modelInstance.getModelElementsByType(StartEvent.class);
        Assert.assertEquals(2, startEvents.size());
        List<StartEvent> list =
                startEvents.stream()
                        .filter(t -> t.getId().equalsIgnoreCase(startEventId))
                        .collect(Collectors.toList());
        Assert.assertNotNull(list);
        Assert.assertEquals(2, startEvents.size());
        FlowElement flowElement = list.get(0);
        Assert.assertEquals(
                stepDetails,
                ConfigurationDefinitionUtil.getConfigurationsAsMap(flowElement)
                        .get(WorkFlowVariables.STEP_DETAILS_KEY.getName()));
    }

    /**
     * Spring Boot provides @TestConfiguration annotation that can be used on classes in src/test/java
     * to indicate that they should not be picked up by scanning.
     */
    @TestConfiguration
    static class BpmnProcessorImplContextConfiguration {

        @Bean
        public BpmnProcessorImpl bpmnProcessor() {
            return new BpmnProcessorImpl();
        }
    }
}
