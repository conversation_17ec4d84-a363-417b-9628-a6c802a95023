package com.intuit.appintgwkflw.wkflautomate.was.workflowvariability;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.Mockito.when;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowVariabilityClientConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.service.adaptor.VariabilityConnector;

public class VariabilityEngineClientTest {

	@Mock
	private WASContextHandler contextHandler;

	@Mock
	private WorkflowVariabilityClientConfig workflowVariabilityClientProperties;
	
	@Mock
	private VariabilityConnector variabilityConnector;

	@InjectMocks
	private VariabilityEngineClient client;

	@Before
	public void init() {
		MockitoAnnotations.openMocks(this);
	}

	@Test
	public void testGetVariabilityDecision() {
		Mockito.when(workflowVariabilityClientProperties.getEnabledRealms()).thenReturn(Set.of(-1L));
		Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("12345");
		Map<String, Boolean> decision = client.getVariabilityDecision(Set.of("WORKFLOWS:PROJECT"));
		Assert.assertNotNull(decision);
		Assert.assertTrue(decision.entrySet().stream().findFirst().get().getValue());
	}
	
	@Test
	public void testGetVariabilityDecisionRealm() {
		Mockito.when(workflowVariabilityClientProperties.getEnabledRealms()).thenReturn(Set.of(12345L));
		Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("12345");
		Map<String, Boolean> decision = client.getVariabilityDecision(Set.of("WORKFLOWS:PROJECT"));
		Assert.assertNotNull(decision);
		Assert.assertTrue(decision.entrySet().stream().findFirst().get().getValue());
	}

	@Test
	public void testGetVariabilityDecisionException() {
		Mockito.when(workflowVariabilityClientProperties.getEnabledRealms()).thenThrow(new RuntimeException());
		Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("12345");
		Map<String, Boolean> decision = client.getVariabilityDecision(Set.of("WORKFLOWS:PROJECT"));
		Assert.assertNotNull(decision);
		Assert.assertFalse(decision.isEmpty());
	}
	
	@Test
    public void test_returns_map_of_boolean_values() {
        // Arrange
        Set<String> featureNames = new HashSet<>();
        featureNames.add("feature1");
        featureNames.add("feature2");
    
        Map<String, Boolean> expected = new HashMap<>();
        expected.put("feature1", true);
        expected.put("feature2", true);
    
        when(workflowVariabilityClientProperties.getEnabledRealms()).thenReturn(new HashSet<>());
        when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("1");
        when(variabilityConnector.getDecisions(anyList(), anyMap())).thenReturn(expected);
    
        // Act
        Map<String, Boolean> result = client.getVariabilityDecision(featureNames);
    
        // Assert
        assertEquals(expected, result);
    }
	
	@Test
    public void test_returns_empty_map_on_exception() {
        // Arrange
        Set<String> featureNames = new HashSet<>();
        featureNames.add("feature1");
        featureNames.add("feature2");

        Map<String, Boolean> expected = new HashMap<>();
        expected.put("feature1", false);
        expected.put("feature2", false);
    
        when(workflowVariabilityClientProperties.getEnabledRealms()).thenReturn(new HashSet<>());
        when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("1");
        when(variabilityConnector.getDecisions(anyList(), anyMap())).thenThrow(new RuntimeException());
    
        // Act
        Map<String, Boolean> result = client.getVariabilityDecision(featureNames);
    
        // Assert
        Assert.assertFalse(result.isEmpty());
        Assert.assertEquals(result,expected);
    }
	
	@Test
    public void test_returns_feature_map_on_enabled_realm() {
        // Arrange
        Set<String> featureNames = new HashSet<>();
        featureNames.add("feature1");
        featureNames.add("feature2");
    
        when(workflowVariabilityClientProperties.getEnabledRealms()).thenReturn(Set.of(-1L));
        when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("1");
    
        Map<String, Boolean> expected = new HashMap<>();
        expected.put("feature1", true);
        expected.put("feature2", true);
        
        // Act
        Map<String, Boolean> result = client.getVariabilityDecision(featureNames);
    
        // Assert
        assertEquals(expected,result);
    }
	
	@Test
    public void test_returns_map_of_boolean_values_if_variability_call_returns_null() {
        // Arrange
        Set<String> featureNames = new HashSet<>();
        featureNames.add("feature1");
        featureNames.add("feature2");
    
        when(workflowVariabilityClientProperties.getEnabledRealms()).thenReturn(new HashSet<>());
        when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("1");
        when(variabilityConnector.getDecisions(anyList(), anyMap())).thenReturn(null);
    
        // Act
        Map<String, Boolean> result = client.getVariabilityDecision(featureNames);
    
        // Assert
        assertNull(result);
    }
}
