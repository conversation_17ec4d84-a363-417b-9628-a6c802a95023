package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import static com.intuit.v4.GlobalId.create;
import static org.springframework.util.ObjectUtils.isEmpty;

import com.google.common.annotations.VisibleForTesting;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.annotations.Tracer;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.app.providers.helper.ProviderHelper;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.IXPManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.AccessVerifier;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.DateUtils;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.DefinitionRbacConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WasUtils;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.CustomWorkflowDecisionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ActivityInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionCrudHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders.MultiStepDefinitionActivityBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.DynamicBpmnDefinitionProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.TemplateService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.UserContributionService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.LookupKeysMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.RecurrenceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.AuthDetailsServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.validator.MultiStepValidator;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.IdentityService;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.impl.BpmnProcessorImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.impl.MultiStepBpmnProcessorImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.DmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.OverWatchConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.CrudOperation;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.UcsVerifyAccessRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.BpmnResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DmnResponse;
import com.intuit.v4.Authorization;
import com.intuit.v4.GlobalId;
import com.intuit.v4.RequestContext;
import com.intuit.v4.common.Metadata;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.providers.ListResult;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;
import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import org.apache.commons.lang.StringUtils;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

@Service
public class DefinitionServiceImpl implements DefinitionService {

  @Autowired
  @Qualifier(WorkflowBeansConstants.CREATE_DEFINITION_HANDLER)
  private DefinitionCrudHandler<DefinitionInstance> createDefinitionHandler;

  @Autowired
  @Qualifier(WorkflowBeansConstants.CREATE_CUSTOM_DEFINITION_HANDLER)
  private DefinitionCrudHandler<DefinitionInstance> customCreateDefinitionHandler;

  @Autowired
  @Qualifier(WorkflowBeansConstants.CREATE_MULTI_STEP_DEFINITION_HANDLER)
  private DefinitionCrudHandler<DefinitionInstance> createMultiStepDefinitionHandler;

  @Autowired
  @Qualifier(WorkflowBeansConstants.UPDATE_DEFINITION_HANDLER)
  private DefinitionCrudHandler<DefinitionInstance> updateDefinitionHandler;

  @Autowired
  @Qualifier(WorkflowBeansConstants.DELETE_DEFINITION_HANDLER)
  private DefinitionCrudHandler<Definition> deleteDefinitionHandler;

  @Autowired
  @Qualifier(WorkflowBeansConstants.DISABLE_DEFINITION_HANDLER)
  private DefinitionCrudHandler<DefinitionInstance> disableDefinitionHandler;

  @Autowired
  @Qualifier(WorkflowBeansConstants.ENABLE_DEFINITION_HANDLER)
  private DefinitionCrudHandler<DefinitionInstance> enableDefinitionHandler;

  @Autowired
  private TemplateService templateService;

  @Autowired
  private DefinitionServiceHelper definitionServiceHelper;

  @Autowired
  private IdentityService identityService;
  @Autowired
  private BpmnProcessorImpl bpmnProcessor;
  @Autowired
  private MultiStepDefinitionActivityBuilder multiStepDefinitionActivityBuilder;
  @Autowired
  private MultiStepBpmnProcessorImpl multiStepBpmnProcessorImpl;
  @Autowired
  private ProviderHelper providerHelper;

  @Autowired
  private AccessVerifier accessVerifier;

  @Autowired
  private DefinitionRbacConfig definitionRbacConfig;

  @Autowired
  private AuthDetailsServiceHelper authDetailsServiceHelper;

  @Autowired
  private CustomWorkflowDecisionHandler customWorkflowDecisionHandler;

  @Autowired
  private SingleDefinitionRead singleDefinitionRead;

  @Autowired
  private UserDefinitionRead userDefinitionRead;

  @Autowired
  private FeatureFlagManager featureFlagManager;

  @Autowired
  private OverWatchConfig overwatchConfig;

  @Autowired
  private WASContextHandler contextHandler;
  @Autowired private MetricLogger metricLogger;

  @Autowired private DynamicBpmnDefinitionProcessor dynamicBpmnDefinitionProcessor;

  @Autowired private MultiStepValidator multiStepValidator;
  @Autowired private UserContributionService userContributionService;
  @Autowired
  private MultiStepReminderTransformer multiStepReminderTransformer;


  /**
   * Centralized RBAC verification method for all definition operations
   * @param workflowType The workflow type (e.g., 'default', 'approval')
   * @param operation The CRUD operation (CREATE, READ, UPDATE, DELETE, ENABLED, DISABLED)
   * @throws WorkflowGeneralException if access is denied
   */
  private void verifyRbacAccess(String workflowType, String operation) {
    boolean rbacEnabled = definitionRbacConfig.isEnabledForOperation(operation);

    if(rbacEnabled && workflowType != null) {
      boolean hasAccess = accessVerifier.verifyUserAccess(workflowType, operation);

      if(!hasAccess) {
        throw new WorkflowGeneralException(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS);
      }
    }
  }


  @Override
  @Metric(name = MetricName.CREATE_WORKFLOW_DEFINITION, type = Type.API_METRIC)
  public Definition createDefinition(
          final Definition definition, final Authorization authorization) {
    String workflowType = getWorkflowTypeFromDefinition(definition);
    verifyRbacAccess(workflowType, CrudOperation.CREATE.name());

    providerHelper.validateRequest(definition, authorization);
    final DefinitionCrudHandler<DefinitionInstance> handler = getCreateDefinitionHandler(definition);
    final DefinitionInstance builtDefinitionInstance = buildDefinitionInstance(definition);
    final DefinitionInstance definitionInstance =
            handler.process(builtDefinitionInstance, authorization.getRealm());

    return definitionServiceHelper.executeAsyncWorkflowTasks(
            definitionInstance, authorization, false);
  }

  // This method is used to decide whether to build the DefinitionInstance dynamically or not and call the appropriate method for it
  private DefinitionInstance buildDefinitionInstance(Definition definition){

    // validate multi-condition payload before creating a workflow dynamically
    if (MultiStepUtil.isMultiCondition(definition) &&
            multiStepValidator.validateMultiConditionPayload(definition) &&
            DynamicBpmnUtil.canGenerateBpmnDynamically(definition)) {
      return dynamicBpmnDefinitionProcessor.buildDefinitionInstanceDynamically(definition);
    }

    return buildDefinitionInstanceForTemplates(definition);
  }

  // Return CreateDefinitionHandler based on the usecase (Custom workflow vs precanned workflows)
  private DefinitionCrudHandler<DefinitionInstance> getCreateDefinitionHandler(
      Definition definition) {
    if (CustomWorkflowUtil.isCustomWorkflow(definition.getTemplate())) {
      return MultiStepUtil.isMultiCondition(definition) ? createMultiStepDefinitionHandler :
          customCreateDefinitionHandler;
    }
    return createDefinitionHandler;
  }

  @Override
  @Metric(name = MetricName.UPDATE_WORKFLOW_DEFINITION, type = Type.API_METRIC)
  public Definition updateDefinition(
      final Definition definition, final Authorization authorization) {
    String workflowType = definitionServiceHelper.getWorkflowType(definition.getId().getLocalId(), authorization.getRealm());
    verifyRbacAccess(workflowType, CrudOperation.UPDATE.name());

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message("Execute status activation/deactivation if available"));
    // This check is to determine if a definition is already existing for a given template.
    // Throws Exception in case definition already exists.
    providerHelper.validateRequest(definition, authorization);
    final DefinitionInstance definitionInstance =
        updateDefinitionHandler.process(
            buildDefinitionInstance(definition), authorization.getRealm());

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(
                    "Prepare for updating other attributes for this definition, unwrap the ids"));
    // Un-Wrap the ids
    if (definitionInstance.isAttributeUpdate()) {

      if (!definitionInstance.getDefinition().getWorkflowSteps().isEmpty()) {
        final String uuid =
            WasUtils.getUUID(
                definitionInstance.getDefinition().getWorkflowSteps().get(0).getId(),
                authorization.getRealm());
        definitionInstance.setUuid(uuid);
      }
      definitionServiceHelper.unWrapDefinitionWithIds(definitionInstance, authorization.getRealm());

      // Create definition out of it

      // Throttling check performed here - if the number of definitions
      // updated in a timeframe exceeds threshold, exception is thrown at this point
      getCreateDefinitionHandler(definition).process(definitionInstance, authorization.getRealm());
    }

    WorkflowLogger.info(
        () -> WorkflowLoggerRequest.builder().message("Execute database/app-connect operation"));

    // update in db, camunda, app-connect
    return definitionServiceHelper.executeAsyncWorkflowTasks(
        definitionInstance, authorization, true);
  }

  /**
   * * Returns List of Definition
   *
   * @param authorization : Authorization required for creating Global ID
   * @return
   */
  @Override
  public List<Definition> getDefinitionList(
      final Authorization authorization, final QueryHelper query) {
    final Optional<List<DefinitionDetails>> definitionList =
        definitionServiceHelper.getDefinitionListWithoutWorkflowSteps(
            Long.parseLong(authorization.getRealm()), query);
    return (definitionList.isEmpty() || CollectionUtils.isEmpty(definitionList.get()))
        ? Collections.emptyList()
        : definitionList.get().stream()
            .map(definition -> prepareDefinition(authorization, definition))
            .collect(Collectors.toList());
  }

  // TODO : Multi-threading will be handled later

  /**
   * Return List of Definitions without WorkflowSteps. If WorkflowStep is not set by offering
   * ReadAll Defns, the below query is invoked, If workflow steps are needed, call ->
   * getDefinitionList()
   *
   * @param authorization : Authorization required for creating Global ID
   * @return
   */
  @Override
  public List<Definition> getDefinitionListWithWorkflowSteps(
      final Authorization authorization, QueryHelper query) {
    final Optional<List<DefinitionDetails>> definitionLists =
        definitionServiceHelper.getDefinitionList(Long.parseLong(authorization.getRealm()), query);
    // Returning emptyList in case of No Records Found
    return (!definitionLists.isPresent() || CollectionUtils.isEmpty(definitionLists.get()))
        ? Collections.emptyList()
        : definitionLists.get().stream()
            .map(
                definitionDetails -> {
                  final GlobalId<? extends GlobalId> globalId =
                      create(
                          authorization.getRealm(),
                          WorkflowConstants.DEFINITION_TYPE_ID,
                          definitionDetails.getDefinitionId());
                  final Definition definition =
                      getDefinitionReadOne(definitionDetails.getDefinitionId(), globalId, false);
                  definition.setId(globalId);
                  definition.setStatus(
                      WorkflowStatusEnum.fromValue(
                          definitionDetails.getStatus().getStatus().toUpperCase()));
                  // preparing meta
                  definition.setMeta(
                      definitionServiceHelper.getMetadata(authorization, definitionDetails));
                  // Setting Definition name and description
                  definition.setDescription(definitionDetails.getDescription());
                  definition.setDisplayName(definitionDetails.getDefinitionName());
                  definition.setCreatedAsSystemUser(isSystemCreated(definitionDetails));
                  // Setting RecordType
                  definition.setRecordType(
                      isEmpty(definitionDetails.getRecordType())
                          ? null
                          : definitionDetails.getRecordType().getRecordType());
                  definition.setVersion(String.valueOf(definitionDetails.getVersion()));
                  return definition;
                })
            .collect(Collectors.toList());
  }

  private boolean isSystemCreated(DefinitionDetails definitionDetails) {
    return !Objects.isNull(definitionDetails.getCreatedByUserId())
        && WorkflowConstants.SYSTEM_OWNER_ID.equals(
        String.valueOf(definitionDetails.getCreatedByUserId()));
  }

  /**
   * @param definition definition
   * @return new DefinitionInstance
   */
  private DefinitionInstance buildDefinitionInstanceForTemplates(final Definition definition) {
    final TemplateDetails templateDetails =
        providerHelper
            .getTemplateDetails(definition)
            .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.TEMPLATE_DOES_NOT_EXIST));

    final BpmnModelInstance bpmnModelInstance =
        BpmnProcessorUtil.readBPMN(templateDetails.getTemplateData());

    final List<TemplateDetails> dmnDetailsList =
        templateService.getReferencedChildTemplates(templateDetails).orElse(new ArrayList<>());

    List<DmnModelInstance> dmnInstanceList =
        dmnDetailsList.stream()
            .map(dmnDetail -> BpmnProcessorUtil.readDmn(dmnDetail.getTemplateData()))
            .collect(Collectors.toList());

    // Preparing DMN skeleton from config for custom workflows. Adding all the input variables here
    // as the DMN code expects all the input attributes should be present so that based on the rule
    // supplied it can match appropriate one and append the empty rule.

    // For multi-condition workflows, we are doing this step just before creating
    // rules in dmn in MultiConditionBusinessRuleTaskHandler
    if (CustomWorkflowUtil.isCustomWorkflow(definition.getTemplate()) &&
        !MultiStepUtil.isMultiCondition(definition)) {
      boolean useFeelExpr = CustomWorkflowUtil.shouldUseFeelExpr(featureFlagManager,
          templateDetails);
      dmnInstanceList =
          dmnInstanceList.stream()
              .map(
                  dmnDetail ->
                      customWorkflowDecisionHandler.createDecisionInputs(dmnDetail, definition, useFeelExpr))
              .collect(Collectors.toList());
    }

    // update bpmn name
    BpmnProcessorUtil.updateBpmnName(bpmnModelInstance, definition.getDisplayName());
    // update the name of dmn with the supplied name for definition name[Display name as provided by
    // the user].
    dmnInstanceList = DmnProcessorUtil.updateDmnName(dmnInstanceList, definition.getDisplayName());
    // update name to be sent to ui as display name to avoid confusion. Definition name will be the
    // template name.
    definition.setName(templateDetails.getTemplateName());

    //Add Workflow in Context.
    contextHandler.addKey(WASContextEnums.WORKFLOW, templateDetails.getTemplateName());

    return new DefinitionInstance(definition, bpmnModelInstance, dmnInstanceList, templateDetails);
  }

  @Override
  @Metric(name = MetricName.DELETE_WORKFLOW_DEFINITION, type = Type.API_METRIC)
  public Definition deleteDefinition(
      final Definition definition, final Authorization authorization) {
    String workflowType = definitionServiceHelper.getWorkflowType(definition.getId().getLocalId(), authorization.getRealm());
    verifyRbacAccess(workflowType, CrudOperation.DELETE.name());

    return deleteDefinitionHandler.process(definition, authorization.getRealm());
  }

  /**
   * @return if MultiCondition Return multiStepBpmnProcessorImpl else return bpmnProcessor
   */
  private Template processBpmnAndGenerateTemplate(DefinitionInstance definitionInstance, final GlobalId id,
      List<DmnResponse> dmnResponses, boolean isMultiStep) throws IOException {

    if (isMultiStep) {
      final Instant start = Instant.now();
      String definitionId = definitionInstance.getDefinition().getId().getLocalId();
      // get a map of activityId with corresponding ActivityInstance object
      Map<String, ActivityInstance> activityInstanceMap =
          multiStepDefinitionActivityBuilder.generateActivityInstanceMap(definitionId,
              dmnResponses);
      definitionInstance.setActivityInstanceMap(activityInstanceMap);
      Template template = (Template) multiStepBpmnProcessorImpl.processBpmn(
          definitionInstance,
          create(
              id.getRealmId(),
              WorkflowConstants.TEMPLATE_TYPE_ID,
              definitionInstance.getTemplateDetails().getId()),
          true);
      metricLogger.logLatencyMetric(MetricName.READ_ONE_MULTI_STEP_DEFINITION, Type.API_METRIC, start);
      return template;
    }
    return (Template)
        bpmnProcessor.processBpmn(
            definitionInstance,
            create(
                id.getRealmId(),
                WorkflowConstants.TEMPLATE_TYPE_ID,
                definitionInstance.getTemplateDetails().getId()),
            true);
  }

  @Override
  @Metric(name = MetricName.READ_ONE_DEFINITION, type = Type.API_METRIC)
  public Definition getDefinitionReadOne(
      @Tracer(key = WASContextEnums.DEFINITION_ID) final String definitionId,
      GlobalId id,
      final boolean isTemplateDataPassed) {
    String workflowType = definitionServiceHelper.getWorkflowType(definitionId, id.getRealmId());
    verifyRbacAccess(workflowType, "READ");

//    This check is needed since SDEF reminders will not have call-activity details and will also go through the multi condition flow and fail
    boolean isActivityDetailsPresent = definitionServiceHelper.isActivityDetailsPresent(
        id.getLocalId());
    boolean isMultiStep = (isTemplateDataPassed && isActivityDetailsPresent);
    Definition readDefinition = readOneDefinition(definitionId, id, isMultiStep, false);
    transformReadDefinitionResponsePayload(isActivityDetailsPresent,isTemplateDataPassed,readDefinition,definitionId);
    return readDefinition;
  }
 
  private Definition readOneDefinition(
      @Tracer(key = WASContextEnums.DEFINITION_ID) final String definitionId,
      GlobalId id,
      final boolean isMultiStep,
      boolean allowCrossRealmAccess) {
    List<DmnResponse> dmnResponses = null;
    List<String> dmnXmlStrings = null;

    final List<DefinitionDetails> definitionDetails =
        definitionServiceHelper.findByDefinitionIdOrParentId(definitionId);
    final String realmId = contextHandler.get(WASContextEnums.OWNER_ID);
    final DefinitionDetails bpmnDefinitionDetail =
        definitionDetails.stream()
            .filter(
                definitionDetail ->
                    definitionDetail.getDefinitionId().equalsIgnoreCase(definitionId))
            .findAny()
            .orElseThrow(
                () -> new WorkflowGeneralException(WorkflowError.BPMN_DEFINITION_NOT_FOUND));

    // Request has come from Read One definition, check realm id miss match and throw exception.
    // This is the use case if user from another realm tries to Read Definition for another Realm
    // and not coming
    // Obfuscated Flow (UCS)
    if (!allowCrossRealmAccess) {
      // Check if resource realm is not equal to requesting owner's realm then throw exception
      WorkflowVerfiy.verify(
          !realmId.equalsIgnoreCase(String.valueOf(bpmnDefinitionDetail.getOwnerId())),
          WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS);
    }

    /**
     * This call is guarded by the flag. Once UCS is live in production then will remove this if
     * check.
     */
    userContributionService.verifyAccess(
        String.valueOf(bpmnDefinitionDetail.getOwnerId()),
        UcsVerifyAccessRequest.builder()
            .resourceId(bpmnDefinitionDetail.getDefinitionKey())
            .requesterOwnerId(realmId)
            .requesterUserId(contextHandler.get(WASContextEnums.INTUIT_USERID))
            .build());

    /** Add Workflow in Context. Using TemplateName as deleteDefinition can be a call from SLA. */
    contextHandler.addKey(
        WASContextEnums.WORKFLOW, bpmnDefinitionDetail.getTemplateDetails().getTemplateName());

    final List<DefinitionDetails> dmnDefinitionDetails =
        definitionDetails.stream()
            .filter(t -> !t.getDefinitionId().equalsIgnoreCase(definitionId))
            .collect(Collectors.toList());

    final DefinitionDetailsRead definitionDetailsReadService =
        getDefinitionReadService(bpmnDefinitionDetail);

    final BpmnResponse bpmnResponse =
        definitionDetailsReadService.getBPMNXMLDefinition(bpmnDefinitionDetail);

    if (!CollectionUtils.isEmpty(dmnDefinitionDetails)) {
      dmnResponses = definitionServiceHelper.getDMNXMLDefinition(dmnDefinitionDetails);
    }

    final Definition definition = new Definition();

    // Handling for Obfuscate Flows where GlobalId will be null as Global ID should be exact Id of
    // the realm through which definition is created, else it fails to get the correct DMN
    // definition in BpmnProcessorImpl.getDMNModelInstance() method.
    if (Objects.isNull(id.getRealmId()) || isEmpty(id.getRealmId())) {
      final String definitionRealmId = String.valueOf(bpmnDefinitionDetail.getOwnerId());
      definition.setId(
          create(
              isEmpty(id.getRealmId()) ? definitionRealmId : id.getRealmId(),
              WorkflowConstants.DEFINITION_TYPE_ID,
              definitionId));
      // Set GlobalId with correct definition Realm
      id = create(definitionRealmId, WorkflowConstants.DEFINITION_TYPE_ID, definitionId);
    } else {
      // default flow, Setting definition ID
      definition.setId(create(id.getRealmId(), WorkflowConstants.DEFINITION_TYPE_ID, definitionId));
    }

    if (!isEmpty(bpmnDefinitionDetail.getRecordType())) {
      definition.setRecordType(bpmnDefinitionDetail.getRecordType().getRecordType());
    }
    // connectorWorkflowId is only used for automation tests. This flag will be enabled in pre-prod
    // and disabled in prod.
    if (overwatchConfig.isConnectorWorkflowId()) {
      definition.setConnectorWorkflowId(bpmnDefinitionDetail.getWorkflowId());
    }

    definition.setStatus(
        WorkflowStatusEnum.fromValue(bpmnDefinitionDetail.getStatus().getStatus().toUpperCase()));
    // for custom workflow use case, set the record type in template from definition
    if (CustomWorkflowUtil.isCustomWorkflow(
        new Template().name(bpmnDefinitionDetail.getTemplateDetails().getTemplateName()))) {
      bpmnDefinitionDetail.getTemplateDetails().setRecordType(bpmnDefinitionDetail.getRecordType());
    }

    /**
     * DMN Processing is required only when it has a DMN Reference, which may or may not be the case
     * always.
     */
    if (!isEmpty(dmnResponses)) {
      dmnXmlStrings =
          dmnResponses.stream().map(DmnResponse::getDmnXml).collect(Collectors.toList());
    }

    DefinitionInstance definitionInstance =
        new DefinitionInstance(
            definition,
            BpmnProcessorUtil.getBpmnModelInstanceFromXml(bpmnResponse.getBpmn20Xml()),
            BpmnProcessorUtil.getDmnModelInstanceListFromXml(dmnXmlStrings),
            bpmnDefinitionDetail.getTemplateDetails());

    try {
      Template template =
          processBpmnAndGenerateTemplate(definitionInstance, id, dmnResponses, isMultiStep);
      definition.setTemplate(template);
      // Setting definition name as Template name. Display name will be the user provided name.
      definition.setName(template.getName());
      definition.setWorkflowSteps(template.getWorkflowSteps());
      // Adding description, display name and created Date
      definition.setDescription(bpmnDefinitionDetail.getDescription());
      definition.setDisplayName(bpmnDefinitionDetail.getDefinitionName());
      definition.setCreatedAsSystemUser(isSystemCreated(bpmnDefinitionDetail));
      // Adding setter for RecordType
      definition.setRecordType(template.getRecordType());
      /** Setting createdDate into Meta object */
      definition.setMeta(
          definitionServiceHelper.getMetadata(WASContext.getAuthContext(), bpmnDefinitionDetail));
      // For the Legacy Invoice Approval Definition(s), DefinitionData was was not present as this
      // column was introduced later. This will
      // cause NPE for such definitions. Also, We're storing definitionData for all the definitions
      // in WAS now. Therefore, this will be skipped for Legacy definitions as there's no concept of
      // Recurrence for them. JIRA: https://jira.intuit.com/browse/QBOES-10920 (Please refer for
      // more details)
      if (Objects.nonNull(bpmnDefinitionDetail.getDefinitionData())) {
        definition.setRecurrence(
                RecurrenceUtil.prepareRecurrence(bpmnDefinitionDetail.getDefinitionData()));
      }
      // Set lookup keys if present
      definition.setLookupKeys(
          LookupKeysMapper.getListOfLookupKeysFromString(bpmnDefinitionDetail.getLookupKeys())
              .orElse(Collections.emptyList()));
      // Set definitionKey in the response
      definition.setDefinitionKey(bpmnDefinitionDetail.getDefinitionKey());
      definitionDetailsReadService.substituteRecurrence(definition, bpmnDefinitionDetail);
      if (!isMultiStep) {
        definitionDetailsReadService.substitutePlaceHolder(definition, bpmnDefinitionDetail);
        definitionDetailsReadService.verifyPlaceholderDefinition(
            definition, bpmnDefinitionDetail, dmnXmlStrings, id.getRealmId());
      }
    } catch (final Exception e) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .className(this.getClass().getSimpleName())
                  .stackTrace(e)
                  .message("Error Fetching Definition"));
    }
    return definition;
  }

  /**
   * Get the service instance based on pre-conditions defined
   * Gets whether Single Definition Read is feasible or not based on the following
   *    1) Placeholder value is present in database for the Definition Detail
   *
   * @param definitionDetails {@link DefinitionDetails}
   * @return {@link DefinitionDetailsRead}
   */
  @VisibleForTesting
  DefinitionDetailsRead getDefinitionReadService(final DefinitionDetails definitionDetails) {
    //For phase 1 changes we just check if the read flag is enabled then return a single def helper.
    //TO DO in phase 2 check needs to be placed here for Single/USER definition type.
    return Objects.nonNull(definitionDetails.getPlaceholderValue())
        ? singleDefinitionRead : userDefinitionRead;
  }

  private Definition prepareDefinition(
      final Authorization authorization, final DefinitionDetails definitionDetails) {
    final Definition definition = new Definition();
    // Setting Definition ID
    definition.setId(
        create(
            authorization.getRealm(),
            WorkflowConstants.DEFINITION_TYPE_ID,
            definitionDetails.getDefinitionId()));
    // setting the template name as definition name
    definition.setName(definitionDetails.getTemplateDetails().getTemplateName());

    final Metadata meta = definitionServiceHelper.getMetadata(authorization, definitionDetails);
    definition.setMeta(meta);

    final Template template = new Template();
    TemplateDetails templateDetails = definitionDetails.getTemplateDetails();
    final GlobalId<? extends GlobalId> templateGlobalId =
        create(
            authorization.getRealm(),
            WorkflowConstants.TEMPLATE_TYPE_ID,
            definitionDetails.getTemplateDetails().getId());
    template.setId(templateGlobalId);
    template.setName(templateDetails.getTemplateName());
    template.setCategory(templateDetails.getTemplateCategory());
    template.setVersion(String.valueOf(templateDetails.getVersion()));
    definition.setTemplate(template);
    definition.setDescription(definitionDetails.getDescription());
    definition.setDisplayName(definitionDetails.getDefinitionName());
    // Adding Record Type
    definition.setRecordType(
        isEmpty(definitionDetails.getRecordType())
            ? null
            : definitionDetails.getRecordType().getRecordType());
    definition.setCreatedAsSystemUser(isSystemCreated(definitionDetails));
    definition.setStatus(
        WorkflowStatusEnum.fromValue(definitionDetails.getStatus().getStatus().toUpperCase()));
    definition.setVersion(String.valueOf(definitionDetails.getVersion()));
    //set lookup keys in the definition
    definition.setLookupKeys(
        LookupKeysMapper.getListOfLookupKeysFromString(definitionDetails.getLookupKeys())
            .orElse(Collections.emptyList()));
    //set definitionKey in the definition
    definition.setDefinitionKey(definitionDetails.getDefinitionKey());

    // Since existing definitions won't have originalSetupDate value so returning createdDate
    definition.setOriginalSetupDate(Objects.nonNull(definitionDetails.getOriginalSetupDate()) ?
            DateUtils.getDateTime(definitionDetails.getOriginalSetupDate()) :
            DateUtils.getDateTime(definitionDetails.getCreatedDate()));

    // As existing definitions won't have originalSetupUser value so returning createdByUser
    definition.setOriginalSetupUser(Objects.nonNull(definitionDetails.getOriginalSetupUser()) ?
            String.valueOf(definitionDetails.getOriginalSetupUser()) :
            String.valueOf(definitionDetails.getCreatedByUserId()));

    return definition;
  }

  /**
   * disable the given definition for given company
   */
  @Override
  @Metric(name = MetricName.DISABLE_WORKFLOW_DEFINITION, type = Type.API_METRIC)
  public Definition disableDefinition(
      final Definition definition, final Authorization authorization) {
    String workflowType = definitionServiceHelper.getWorkflowType(definition.getId().getLocalId(), authorization.getRealm());
    verifyRbacAccess(workflowType, CrudOperation.DISABLED.name());

    final DefinitionInstance definitionInstance =
        disableDefinitionHandler.process(
            new DefinitionInstance(definition, null, null, null), authorization.getRealm());

    definition.setMeta(definitionServiceHelper.getMetadata(authorization,
        definitionInstance.getDefinitionDetails()));
    return definition;
  }

  /**
   * enable the given definition for given company
   */
  @Override
  @Metric(name = MetricName.ENABLE_WORKFLOW_DEFINITION, type = Type.API_METRIC)
  public Definition enableDefinition(
      final Definition definition, final Authorization authorization) {
    String workflowType = definitionServiceHelper.getWorkflowType(definition.getId().getLocalId(), authorization.getRealm());
    verifyRbacAccess(workflowType, CrudOperation.ENABLED.name());

    final DefinitionInstance definitionInstance =
        enableDefinitionHandler.process(
            new DefinitionInstance(definition, null, null, null), authorization.getRealm());

    definition.setMeta(definitionServiceHelper.getMetadata(authorization,
        definitionInstance.getDefinitionDetails()));
    return definition;
  }

  @Override
  @Metric(name = MetricName.READ_ALL_DEFINITION, type = Type.API_METRIC)
  public ListResult<Definition> getAllDefinitions(
      final Authorization authorization, final QueryHelper query) {
    // RBAC check for READ operation - using "default" workflow type for read all operations
    verifyRbacAccess("default", "READ");

    final boolean isWorkflowSteps = providerHelper.checkForWorkflowSteps(query);
    authDetailsServiceHelper.populateAuthDetails(authorization);
    if (isWorkflowSteps) {
      final List<Definition> definitionList =
          getDefinitionListWithWorkflowSteps(authorization, query);
      return new ListResult<>(definitionList);
    }

    return new ListResult<>(getDefinitionList(authorization, query));

  }

  @Override
  @Metric(name = MetricName.CREATE_WORKFLOW_DEFINITION, type = Type.API_METRIC)
  public Definition createDefinitionWithDefaultValues(
      String templateName, boolean isCreatedAsSystemUser, Authorization authorization) throws WorkflowGeneralException {
    WorkflowLogger.logInfo(
        "Creating definition for templateName=%s with default values", templateName);
    WorkflowVerfiy.verify(StringUtils.isEmpty(templateName), WorkflowError.INVALID_INPUT);
    Template template = templateService.getConfigTemplateById(templateName, false);
    WorkflowVerfiy.verify(ObjectUtils.isEmpty(template), WorkflowError.TEMPLATE_DOES_NOT_EXIST);

    Definition definition = new Definition();
    // Set definition defaults
    definition.setDisplayName(template.getDisplayName());
    definition.setRecordType(template.getRecordType());
    definition.setStatus(WorkflowStatusEnum.ENABLED);
    definition.setWorkflowSteps(template.getWorkflowSteps());
    definition.setCreatedAsSystemUser(isCreatedAsSystemUser);
    //set recurrence rule in definition based on defaults set in template config
    definition.setRecurrence(RecurrenceUtil.getRecurrenceForPrecannedTemplates(template, featureFlagManager, contextHandler.get(WASContextEnums.OWNER_ID)));
    // Use logged in user to set assignee fieldvalue. We need personaId for creating task which
    // is not available from the authorization header. We need to call ius to get persona id if
    // needed.
    populatePersonaIdInDefinition(definition, authorization);
    return createDefinition(definition, authorization);
  }

  /**
   * Reading Definition with obfuscated/redacted values
   *
   * @param context
   * @param queryHelper
   * @param isTemplateDataPassed
   * @return
   */
  @Override
  @Metric(name = MetricName.READ_ONE_OBFUSCATE_DEFINITION, type = Type.API_METRIC)
  public Definition getDefinitionWithObfuscatedValues(
      RequestContext context, QueryHelper queryHelper, boolean isTemplateDataPassed) {
    String definitionId = null;
    boolean obfuscate = false;
    Optional<Object> recordMetadataInput = queryHelper.getArg(WorkflowConstants.BY);
    if (recordMetadataInput.isPresent()) {
      final Map<String, String> metaDataInput = (HashMap<String, String>) recordMetadataInput.get();
      definitionId = String.valueOf(metaDataInput.get(WorkflowConstants.DEFINITION_ID));
      WorkflowVerfiy.verify(isEmpty(definitionId), WorkflowError.INVALID_INPUT);

      // RBAC check for READ operation
      String realmId = contextHandler.get(WASContextEnums.OWNER_ID);
      String workflowType = definitionServiceHelper.getWorkflowType(definitionId, realmId);
      verifyRbacAccess(workflowType, "READ");

      WorkflowVerfiy.verify(
          isEmpty(metaDataInput.get(WorkflowConstants.OBFUSCATE)), WorkflowError.INVALID_INPUT);
      obfuscate =
          Boolean.parseBoolean(String.valueOf(metaDataInput.get(WorkflowConstants.OBFUSCATE)));
      final GlobalId<? extends GlobalId> globalId =
          create(null, WorkflowConstants.DEFINITION_TYPE_ID, definitionId);

      //    This check is needed since SDEF reminders will not have call-activity details and will also go through the multi condition flow and fail
      boolean isActivityDetailsPresent = definitionServiceHelper.isActivityDetailsPresent(definitionId);
      boolean isMultiStep = (isTemplateDataPassed && isActivityDetailsPresent);
      final Definition definition = readOneDefinition(definitionId, globalId, isMultiStep,true);
      transformReadDefinitionResponsePayload(isActivityDetailsPresent,isTemplateDataPassed,definition,definitionId);

      if (obfuscate) {
        userContributionService.obfuscateDefinitionDetails(
            definition, definition.getRecordType(), isMultiStep);
      }
      return definition;
    }
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message("Returning No definitions as Obfuscate parameter was incorrectly passed"));
    WorkflowVerfiy.verify(Boolean.TRUE, WorkflowError.INVALID_INPUT);
    return null;
  }

  private Definition populatePersonaIdInDefinition(Definition definition, Authorization authorization) {

    AtomicReference<String> personaId = new AtomicReference<>();
    final String userGlobalId = authorization.getAuthId();
    if (!ObjectUtils.isEmpty(definition.getWorkflowSteps())) {
      definition.getWorkflowSteps().stream()
          .forEach(
              workflowStep -> {
                workflowStep.getActions().stream()
                    .filter(
                        actionMapper ->
                            !ObjectUtils.isEmpty(actionMapper.getAction().getParameters()))
                    .forEach(
                        actionMapper -> {
                          actionMapper.getAction().getParameters().stream()
                              .filter(
                                  inputParameter ->
                                      !ObjectUtils.isEmpty(inputParameter.getFieldValues()))
                              .forEach(
                                  inputParameter -> {
                                    if (inputParameter
                                        .getFieldValues()
                                        .contains(WorkflowConstants.CURRENT_USER_GLOBAL)) {
                                      inputParameter.setFieldValues(
                                          inputParameter.getFieldValues().stream()
                                              .map(
                                                  value ->
                                                      value.replace(
                                                          WorkflowConstants.CURRENT_USER_GLOBAL,
                                                          userGlobalId))
                                              .collect(Collectors.toList()));
                                    } else if (inputParameter
                                        .getFieldValues()
                                        .contains(WorkflowConstants.CURRENT_USER_PERSONA)) {
                                      // Fetch persona Id only if fieldValue is current_user_persona and not
                                      // fetched already
                                      if (personaId.get() == null) {
                                        personaId.set(
                                            identityService.getPersonaId(
                                                authorization.getRealm(),
                                                authorization.getAuthId()));
                                      }
                                      inputParameter.setFieldValues(
                                          inputParameter.getFieldValues().stream()
                                              .map(
                                                  value ->
                                                      value.replace(
                                                          WorkflowConstants.CURRENT_USER_PERSONA,
                                                          personaId.get()))
                                              .collect(Collectors.toList()));
                                    }
                                  });
                        });
              });
    }
    return definition;
  }

  /**
   * This function transforms the SDEF payload for reminders to MCR, if Multi condition is enabled for the realm
   * @param isActivityDetailsPresent
   * @param isTemplateDataPassed
   * @param definition
   * @param definitionId
   */
  private void transformReadDefinitionResponsePayload(boolean isActivityDetailsPresent,
      boolean isTemplateDataPassed, Definition definition, String definitionId) {
    TransformationDecisionDTO transformationDecisionDTO = TransformationDecisionDTO.builder()
        .isActivityDetailsPresent(isActivityDetailsPresent)
        .isTemplateDataPassed(isTemplateDataPassed).build();

    if (!multiStepReminderTransformer.isMigrationCase(transformationDecisionDTO)) {
      WorkflowLogger.logInfo("Transformation of payload not required for DefinitionId= %s", definitionId);
      return;
    }

    WorkflowLogger.logInfo(
        "Transforming SDEF payload to multi condition workflow=%s-%s, Entity=%s, DefinitionId=%s ,isTemplateDataPassed=%s,isActivityDetailsPresent=%s",
        definition.getName(), definition.getDisplayName(), definition.getRecordType(), definitionId,
        isTemplateDataPassed, isActivityDetailsPresent);
    DefinitionDetails bpmnDetails = definitionServiceHelper.findByDefinitionId(definitionId);
    multiStepReminderTransformer.migrate(definition, bpmnDetails);
  }

  /**
   * Helper method to extract workflow type from definition for access verification
   * @param definition the definition object
   * @return workflow type (action key) or null if not found
   */
  private String getWorkflowTypeFromDefinition(Definition definition) {
    if (definition == null) {
      return null;
    }

    // Try to get workflow type from template name first
    if (definition.getTemplate() != null && definition.getTemplate().getName() != null) {
      return CustomWorkflowType.getActionKey(definition.getTemplate().getName());
    }

    // If template is null, try to get workflow type from workflow steps (for custom workflows)
    if (definition.getWorkflowSteps() != null && !definition.getWorkflowSteps().isEmpty()) {
      return definition.getWorkflowSteps().stream()
              .findFirst()
              .map(workflowStep ->
                      workflowStep.getActions().stream()
                              .findFirst()
                              .map(WorkflowStep.ActionMapper::getActionKey)
                              .orElse(null))
              .orElse(null);
    }

    return null;
  }

}